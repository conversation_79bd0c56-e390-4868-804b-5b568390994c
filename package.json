{"name": "streamliner", "version": "1.2.9", "description": "Streamliner - Job Management", "author": "https://amirreza.com.au", "license": "https://amirreza.com.au/licenses/", "private": true, "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "dependencies": {"@angular/animations": "^20.0.3", "@angular/cdk": "^20.0.3", "@angular/common": "^20.0.3", "@angular/compiler": "^20.0.3", "@angular/core": "^20.0.3", "@angular/fire": "^20.0.1", "@angular/forms": "^20.0.3", "@angular/google-maps": "^20.0.3", "@angular/material": "^20.0.3", "@angular/material-luxon-adapter": "^20.0.3", "@angular/platform-browser": "^20.0.3", "@angular/platform-browser-dynamic": "^20.0.3", "@angular/router": "^20.0.3", "@angular/service-worker": "^20.0.3", "@bryntum/scheduler": "6.0.6", "@bryntum/scheduler-angular": "6.0.5", "@jsverse/transloco": "7.6.1", "@stripe/stripe-js": "^7.3.0", "angularx-flatpickr": "^8.1.0", "apexcharts": "^4.7.0", "appwrite": "^18.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "firebase": "^11.7.1", "flatpickr": "^4.6.13", "highlight.js": "^11.11.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lodash-es": "^4.17.21", "lottie-web": "^5.12.2", "luxon": "^3.6.1", "ng-apexcharts": "^1.15.0", "ng-qrcode": "^20.0.0", "ngx-daterangepicker-material": "^6.0.4", "ngx-lottie": "^20.0.0", "ngx-mask": "^19.0.6", "ngx-material-intl-tel-input": "^20.0.0", "ngx-qrcode-styling": "^1.3.3", "ngx-quill": "^28.0.1", "ngx-stripe": "^20.7.0", "perfect-scrollbar": "^1.5.6", "quill": "^2.0.3", "rxdb": "^16.11.0", "rxjs": "^7.8.2", "tslib": "^2.8.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "zone.js": "^0.15.0"}, "devDependencies": {"@angular/build": "^20.0.2", "@angular/cli": "^20.0.2", "@angular/compiler-cli": "^20.0.3", "@tailwindcss/typography": "^0.5.16", "@types/chroma-js": "^3.1.1", "@types/crypto-js": "^4.2.2", "@types/highlight.js": "^10.1.0", "@types/jasmine": "^5.1.8", "@types/lodash": "^4.17.16", "@types/lodash-es": "^4.17.12", "@types/luxon": "^3.6.2", "autoprefixer": "^10.4.21", "chroma-js": "^3.1.2", "jasmine-core": "^5.7.1", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.1", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "lodash": "^4.17.21", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "typescript": "5.8.3"}}
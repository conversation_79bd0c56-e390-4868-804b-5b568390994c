<div class="absolute inset-0 flex flex-col min-w-0 overflow-hidden">
  <mat-drawer-container
    class="flex-auto h-full bg-card dark:bg-transparent"
    (backdropClick)="onBackdropClicked()"
    >
    <!-- Drawer -->
    <mat-drawer
      class="w-full sm:w-128 dark:bg-gray-900"
      [mode]="drawerMode"
      [opened]="false"
      [position]="'end'"
      [disableClose]="true"
      #matDrawer
      >
      <router-outlet></router-outlet>
    </mat-drawer>

    <mat-drawer-content class="flex flex-col">
      <!-- Main -->
      <div class="flex flex-col flex-auto">
        <!-- Header -->
        <div
          class="flex flex-col sm:flex-row items-start sm:items-center sm:justify-between py-8 px-6 md:px-8"
          >
          <!-- Title -->
          <div>
            <div
              class="text-4xl font-extrabold tracking-tight leading-none"
              >
              Servicing
            </div>
            <div class="ml-0.5 font-medium text-secondary">
              @if (servicingCount.incomplete === 0) {
                <span
                  >All Services completed!</span
                  >
                }
                @if (servicingCount.incomplete !== 0) {
                  <span
                    >{{ servicingCount.incomplete }} remaining
                    Servicing</span
                    >
                  }
                </div>
              </div>
              <!-- Actions -->
              <div class="mt-4 sm:mt-0">
                <!-- Add service button -->
                <button
                  mat-flat-button
                  [color]="'accent'"
                  (click)="createService('newService')"
                  >
                  <mat-icon
                    [svgIcon]="'heroicons_outline:plus'"
                  ></mat-icon>
                  <span class="ml-2 mr-1">Add Service</span>
                </button>
                <!-- Requested services  button -->
                <button
                  class="ml-3"
                  mat-flat-button
                  [color]="'accent'"
                  matBadge="{{ servicingCount.requested }}"
                  matBadgeColor="warn"
                  (click)="openServicingRequestDialog()"
                  >
                  <mat-icon
                    [svgIcon]="'heroicons_outline:wrench'"
                  ></mat-icon>
                  <span class="ml-2 mr-1">Requested Services</span>
                </button>
                <!-- Report button -->
                <!-- <button
                class="ml-3"
                mat-flat-button
                [color]="'primary'"
                >
                <mat-icon
                  [svgIcon]="'heroicons_outline:table-cells'"
                ></mat-icon>
                <span class="ml-2 mr-1">Reports</span>
              </button> -->
            </div>
          </div>

          <!-- Service list -->
          @if (
            servicingList && servicingList.length > 0) {
            <!--Body-->
            <div class="flex m-10">
              <div class="w-full h-full mt-8">
                <!-- Recent transactions table -->
                <div
                  class="xl:col-span-2 flex flex-col flex-auto bg-card shadow rounded-2xl overflow-hidden"
                  >
                  <div
                    class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b"
                    >
                    <!-- Title -->
                    <div
                      class="text-lg font-extrabold tracking-tight"
                      >
                      Member List
                    </div>
                    <!-- Actions -->
                    <div
                      class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4"
                      >
                      <!-- Search -->
                      <mat-form-field
                        class="fuse-mat-dense fuse-mat-rounded min-w-64"
                        [subscriptSizing]="'dynamic'"
                        >
                        <mat-icon
                          class="icon-size-5"
                          matPrefix
                                                [svgIcon]="
                                                    'heroicons_solid:magnifying-glass'
                                                "
                        ></mat-icon>
                        <input
                          matInput
                          (keyup)="applyFilter($event)"
                          [autocomplete]="'off'"
                          [placeholder]="'Search ...'"
                          #input
                          />
                      </mat-form-field>
                      <button
                        mat-icon-button
                        [matMenuTriggerFor]="moreMenu"
                        >
                        <i
                          class="ml-3 icon-size-5 fa-duotone fa-file-export"
                        ></i>
                      </button>
                      <mat-menu #moreMenu="matMenu">
                        <button
                          mat-menu-item
                                                (click)="
                                                    exportMaterialTable('pdf')
                                                "
                          >
                          <i
                            class="fa-duotone fa-file-pdf"
                          ></i>
                          <span class="ml-3">PDF</span>
                        </button>
                        <button
                          mat-menu-item
                                                (click)="
                                                    exportMaterialTable('excel')
                                                "
                          >
                          <i
                            class="fa-duotone fa-file-excel"
                          ></i>
                          <span class="ml-3">EXCEL</span>
                        </button>
                      </mat-menu>
                    </div>
                  </div>
                  <div class="overflow-x-auto mx-6">
                    <div class="w-full bg-transparent">
                      <table
                        mat-table
                        [dataSource]="dataSource"
                        matSort
                        >
                        <!-- Progress Column -->
                        <ng-container
                          matColumnDef="serviceName"
                          >
                          <th
                            mat-header-cell
                            *matHeaderCellDef
                            mat-sort-header
                            >
                            Service Name
                          </th>
                          <td
                            mat-cell
                            *matCellDef="let row"
                            >
                            {{ row.serviceName }}
                          </td>
                        </ng-container>
                        <!-- Name Column -->
                        <ng-container
                          matColumnDef="serviceType"
                          >
                          <th
                            mat-header-cell
                            *matHeaderCellDef
                            mat-sort-header
                            >
                            Service Type
                          </th>
                          <td
                            mat-cell
                            *matCellDef="let row"
                            >
                            {{ row.serviceType }}
                          </td>
                        </ng-container>
                        <!-- phone Column -->
                        <ng-container
                          matColumnDef="selectedVehicles"
                          >
                          <th
                            mat-header-cell
                            *matHeaderCellDef
                            mat-sort-header
                            >
                            Selected Vehicles
                          </th>
                          <td
                            mat-cell
                            *matCellDef="let row"
                            >
                            {{
                            getVehicleName(
                            row.selectedVehicles
                            )
                            }}
                          </td>
                        </ng-container>
                        <!-- Actions Column -->
                        <ng-container
                          matColumnDef="actions"
                          >
                          <th
                            mat-header-cell
                            *matHeaderCellDef
                            >
                            Actions
                          </th>
                          <td
                            mat-cell
                            *matCellDef="let row"
                            >
                            <button
                              mat-icon-button
                                                        (click)="
                                                            createService(
                                                                'editService',
                                                                row
                                                            )
                                                        "
                              >
                              <mat-icon
                                                            [svgIcon]="
                                                                'heroicons_outline:pencil'
                                                            "
                              ></mat-icon>
                            </button>
                            <button
                              mat-icon-button
                                                        [matMenuTriggerFor]="
                                                            moreMenu
                                                        "
                              >
                              <mat-icon
                                                            [svgIcon]="
                                                                'heroicons_outline:ellipsis-vertical'
                                                            "
                              ></mat-icon>
                            </button>
                            <mat-menu
                              #moreMenu="matMenu"
                              >
                              <button
                                mat-menu-item
                                                            (click)="
                                                                deleteItem(row)
                                                            "
                                >
                                <mat-icon
                                                                [svgIcon]="
                                                                    'heroicons_outline:trash'
                                                                "
                                ></mat-icon>
                                <span>Delete</span>
                              </button>
                            </mat-menu>
                          </td>
                        </ng-container>
                        <tr
                          mat-header-row
                                                *matHeaderRowDef="
                                                    displayedColumns
                                                "
                        ></tr>
                        <tr
                          mat-row
                                                *matRowDef="
                                                    let row;
                                                    columns: displayedColumns
                                                "
                        ></tr>
                        <!-- Row shown when there is no matching data. -->
                        <tr class="mat-row" *matNoDataRow>
                          <td
                            class="mat-cell"
                            colspan="4"
                            >
                            No data matching the filter
                            "{{ input.value }}"
                          </td>
                        </tr>
                      </table>
                      <mat-paginator
                        [pageSize]="20"
                                            [pageSizeOptions]="[
                                                5, 10, 20, 50, 100
                                            ]"
                        aria-label="Select page of users"
                      ></mat-paginator>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          } @else {
            @if (isLoading) {
              <div class="flex justify-center w-full m-20">
                <div
                  role="status"
                  class="w-full p-4 space-y-4 border border-gray-200 divide-y divide-gray-200 rounded shadow animate-pulse dark:divide-gray-700 md:p-6 dark:border-gray-700"
                  >
                  <div class="flex items-center justify-between">
                    <div>
                      <div
                        class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"
                      ></div>
                      <div
                        class="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"
                      ></div>
                    </div>
                    <div
                      class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"
                    ></div>
                  </div>
                  <div
                    class="flex items-center justify-between pt-4"
                    >
                    <div>
                      <div
                        class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"
                      ></div>
                      <div
                        class="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"
                      ></div>
                    </div>
                    <div
                      class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"
                    ></div>
                  </div>
                  <div
                    class="flex items-center justify-between pt-4"
                    >
                    <div>
                      <div
                        class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"
                      ></div>
                      <div
                        class="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"
                      ></div>
                    </div>
                    <div
                      class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"
                    ></div>
                  </div>
                  <div
                    class="flex items-center justify-between pt-4"
                    >
                    <div>
                      <div
                        class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"
                      ></div>
                      <div
                        class="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"
                      ></div>
                    </div>
                    <div
                      class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"
                    ></div>
                  </div>
                  <div
                    class="flex items-center justify-between pt-4"
                    >
                    <div>
                      <div
                        class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"
                      ></div>
                      <div
                        class="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"
                      ></div>
                    </div>
                    <div
                      class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"
                    ></div>
                  </div>
                  <span class="sr-only">Loading...</span>
                </div>
              </div>
            } @else {
              <div
                class="flex flex-auto flex-col items-center justify-center bg-gray-100 dark:bg-transparent"
                >
                <mat-icon
                  class="icon-size-24"
                                [svgIcon]="
                                    'heroicons_outline:wrench-screwdriver'
                                "
                ></mat-icon>
                <div
                  class="mt-4 text-2xl font-semibold tracking-tight text-secondary"
                  >
                  Add a Service to start planning!
                </div>
              </div>
            }
          }

        </div>
      </mat-drawer-content>
    </mat-drawer-container>
  </div>

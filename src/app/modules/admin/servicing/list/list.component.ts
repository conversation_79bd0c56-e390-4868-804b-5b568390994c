
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, On<PERSON><PERSON>roy, OnInit, signal, ViewChild, ViewEncapsulation, DOCUMENT } from '@angular/core';
import { MatDrawer, MatSidenavModule } from '@angular/material/sidenav';
import { ActivatedRoute, Router, RouterOutlet } from '@angular/router';
import { FuseMediaWatcherService } from '@fuse/services/media-watcher';
import { catchError, filter, fromEvent, Subject, takeUntil, throwError } from 'rxjs';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatIconModule } from '@angular/material/icon';
import { ServicingService } from 'app/core/databaseModels/servicing/servicing.service';
import { Servicing } from 'app/core/databaseModels/servicing/servicing.type';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { FuseLoadingService } from '@fuse/services/loading';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatMenuModule } from '@angular/material/menu';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TextFieldModule } from '@angular/cdk/text-field';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatBadgeModule } from '@angular/material/badge';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { Vehicle } from 'app/core/databaseModels/vehicle/vehicle.type';
import { VehicleService } from 'app/core/databaseModels/vehicle/vehicle.service';
import { forEach } from 'lodash';
import { ServicingRequest } from 'app/core/databaseModels/servicingRequest/servicingRequest.types';
import { ServicingRequestService } from 'app/core/databaseModels/servicingRequest/servicingRequest.service';
import { MatDialog } from '@angular/material/dialog';
import { ServicingRequestComponent } from '../servicing-request/servicing-request.component';
import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';
import { TeamMembersService } from 'app/core/databaseModels/teammembers/teammembers.service';


@Component({
    selector: 'servicing-list',
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [MatSidenavModule,
        RouterOutlet,
        MatBadgeModule,
        MatTableModule,
        MatPaginatorModule,
        MatSortModule,
        MatButtonModule,
        MatMenuModule,
        MatTooltipModule,
        MatIconModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        TextFieldModule,
        MatSelectModule,
        MatOptionModule,
        MatButtonModule,
    ]
})
export class ServicingListComponent implements OnInit, OnDestroy {
    @ViewChild('matDrawer', { static: true }) matDrawer: MatDrawer;
    @ViewChild(MatPaginator) paginator: MatPaginator;
    @ViewChild(MatSort) sort: MatSort;

    private _unsubscribeAll: Subject<any> = new Subject<any>();
    private _document = inject(DOCUMENT);
    displayedColumns: string[] = ['serviceName', 'serviceType', 'selectedVehicles', 'actions'];
    dataSource: MatTableDataSource<Servicing>;

    vehicles: Vehicle[] = [];

    teamMembers: TeamMember[] = [];

    servicingList: Servicing[];
    servicingRequestList;
    servicing: Servicing;
    isLoading: boolean = false;

    drawerMode: 'side' | 'over';
    servicingCount: any = {
        completed: 0,
        incomplete: 0,
        total: 0,
        overdue: 0,
        requested: '',
    };
    constructor(
        private _activatedRoute: ActivatedRoute,
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _router: Router,
        private _fuseMediaWatcherService: FuseMediaWatcherService,
        private _fuseLoadingService: FuseLoadingService,
        private _servicingService: ServicingService,
        private _servicingRequestService: ServicingRequestService,
        private _vehicleService: VehicleService,
        private _teamMembersService: TeamMembersService,
        private _servicingRequestDialog: MatDialog,
    ) {
    }


    ngOnInit(): void {



        this.dataSource = new MatTableDataSource<Servicing>([]);
        /// Load Vehicles
        this._vehicleService.getVehicles().subscribe((vehicles) => {
            this._vehicleService.vehicles$
                .pipe(
                    takeUntil(this._unsubscribeAll),
                    catchError(error => {
                        console.error('Error:', error);
                        return throwError(error);
                    })
                )
                .subscribe((vehicles) => {
                    this.vehicles = vehicles;
                    // Mark for check
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();

                })
        })

        //load Team Members
        this._teamMembersService.getTeamMembers().subscribe((teamMembers) => {
            this._teamMembersService.teamMembers$
                .pipe(
                    takeUntil(this._unsubscribeAll),
                    catchError(error => {
                        console.error('Error:', error);
                        return throwError(error);
                    })
                )
                .subscribe((teamMembers) => {
                    this.teamMembers = teamMembers;
                    // Mark for check
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();

                })
        });

        this._fuseLoadingService.show();
        this.isLoading = true;
        this.loadServices();
        this.loadServicingRequests();
        // Subscribe to media query change
        this._fuseMediaWatcherService.onMediaQueryChange$('(min-width: 1440px)')
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((state) => {
                // Calculate the drawer mode
                this.drawerMode = state.matches ? 'side' : 'over';

                // Mark for check
                this._changeDetectorRef.markForCheck();
            });

        // Listen for shortcuts
        fromEvent(this._document, 'keydown')
            .pipe(
                takeUntil(this._unsubscribeAll),
                filter<KeyboardEvent>(event =>
                    (event.ctrlKey === true || event.metaKey) // Ctrl or Cmd
                    && (event.key === '/' || event.key === '.'), // '/' or '.' key
                ),
            )
            .subscribe((event: KeyboardEvent) => {
                // If the '/' pressed
                if (event.key === '/') {
                    this.createService('newService');
                }

                // If the '.' pressed
                if (event.key === '.') {
                    // this.createTask('section');
                }
            });


    }
    loadServices() {
        this._servicingService.getServicings().subscribe((servicings) => {

            this._servicingService.servicingList$.pipe(
                takeUntil(this._unsubscribeAll),
                catchError(error => {
                    console.error('Error:', error);
                    return throwError(error);
                })
            ).subscribe((servicingList) => {
                this.servicingList = servicingList;
                this.dataSource.data = this.servicingList;
                this._fuseLoadingService.hide();
                this.isLoading = false;
                if (this.dataSource.data.length > 0) {
                    this.dataSource.paginator = this.paginator;
                    this.dataSource.sort = this.sort;

                }
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
            })
        })
    }

    loadServicingRequests() {
        this._servicingRequestService.getServicingRequests().subscribe((servicingRequests) => {

            this._servicingRequestService.servicingRequests$.pipe(
                takeUntil(this._unsubscribeAll),
                catchError(error => {
                    console.error('Error:', error);
                    return throwError(error);
                })
            ).subscribe((servicingRequestList) => {
                // Find each TeamMember Id and replace ot with name
                servicingRequestList.forEach(x => {
                    const teamMember = this.teamMembers.find(y => y.$id === x.teamMemberID);
                    if (teamMember) {
                        x.teamMemberID = teamMember.name;
                    }

                });
                this.servicingRequestList = signal(servicingRequestList);
                // calculate count of pending requests
                const req = this.servicingRequestList().filter(x => x.requestStatus === 'Pending').length;
                this.servicingCount.requested = req === 0 ? '' : req.toString();

                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
            })
        })
    }
    /**
       * On destroy
       */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._servicingService.unsubscribe();
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    /**
 * On backdrop clicked
 */
    onBackdropClicked(): void {
        // Go back to the list
        this._router.navigate(['./'], { relativeTo: this._activatedRoute });

        // Mark for check
        this._changeDetectorRef.markForCheck();
    }


    createService(type: 'newService' | 'editService' | 'newServiceRequest', servicing?: Servicing): void {
        // Create the task

        if (type === 'newService') {
            this._router.navigate(['./', 0], { relativeTo: this._activatedRoute, queryParams: { type: type } });

        } else if (type === 'newServiceRequest') {
            this._router.navigate(['./', 0], { relativeTo: this._activatedRoute, queryParams: { type: type, servicing: JSON.stringify(servicing) } });

        }
        else {
            //console.log(servicing);

            this._router.navigate(['./', servicing.$id], { relativeTo: this._activatedRoute, queryParams: { type: type, id: servicing.$id } });
        }

        // Mark for check
        this._changeDetectorRef.markForCheck();

    }

    applyFilter(event: Event) {
        const filterValue = (event.target as HTMLInputElement).value;
        this.dataSource.filter = filterValue.trim().toLowerCase();

        if (this.dataSource.paginator) {
            this.dataSource.paginator.firstPage();
        }
    }



    deleteItem(row): void {
        // Open the confirmation dialog
        const _attachedFiles = row.attachedFiles;


        const confirmation = this._fuseConfirmationService.open({
            title: `Delete Service - ${row.serviceName}`,
            message: 'Are you sure you want to delete this service? This action cannot be undone!',
            actions: {
                confirm: {
                    label: 'Delete',
                },
            },
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            // If the confirm button pressed...
            if (result === 'confirmed') {

                try {
                    this.isLoading = true;
                    this._servicingService.deleteServicing(row.$id).subscribe((result) => {
                        this.deleteFiles(_attachedFiles);
                        this.loadServices();
                    });
                } catch (error) {
                    console.error(error);
                }

            }
        });
    }


    deleteFiles(_attachedFiles) {
        try {
            if (_attachedFiles.length > 0) {
                _attachedFiles.forEach((file) => {
                    const fileObject = JSON.parse(file);
                    this._servicingService.deleteFile(fileObject.fileId).subscribe((result) => {
                        // console.log(result);
                    })
                })
            }
        } catch (error) {
            console.error(error);
        }

    }

    // Get vehicles name by ID
    getVehicleName(ids: string[]): string {

        if (!ids) {
            return '';
        }
        let vehicleNames = '';
        forEach(ids, (id) => {
            const vehicle = this.vehicles.find(vehicle => vehicle.$id === id);
            if (vehicle) {
                vehicleNames += ' | ' + vehicle.vehicleName;
            }
        })

        return vehicleNames + ' | ';

    }

    /// Requested Services

    openServicingRequestDialog() {
        let dialogRef = this._servicingRequestDialog.open(ServicingRequestComponent, {
            data: { requestedServicing: this.servicingRequestList },
            width: '1000px'
        });

        dialogRef.afterClosed().subscribe((result) => {
            if (result) {
                const _servicingRequest: ServicingRequest = result;
                const _servicing: Servicing = {
                    organisationID: _servicingRequest.organisationID,
                    serviceName: _servicingRequest.title,
                    servicingRequestId: _servicingRequest.$id,
                    attachedFiles: _servicingRequest.attachedFiles,
                    description: _servicingRequest.description,

                };
                this.createService('newServiceRequest', _servicing);
            }
        });
    }


}

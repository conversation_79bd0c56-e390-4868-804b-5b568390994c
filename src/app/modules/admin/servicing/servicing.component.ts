import { ChangeDetectionStrategy, Component, ViewEncapsulation } from '@angular/core';
import { RouterOutlet } from '@angular/router';

@Component({
    selector: 'servicing',
    templateUrl: './servicing.component.html',
    styleUrls: ['./servicing.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [RouterOutlet]
})
export class ServicingComponent {

    /**
   * Constructor
   */
    constructor() {
    }
}

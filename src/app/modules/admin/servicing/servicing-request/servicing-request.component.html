<h2 mat-dialog-title>Requested Servicing</h2>
<mat-dialog-content>
    @if (!showDetails) {
        <div>
            <div class="grid grid-cols-3 grid-rows-1 gap-4">
                <div class="col-span-2">
                    <mat-form-field class="w-50">
                        <mat-label>Filter</mat-label>
                        <input
                            matInput
                            (keyup)="applyFilter()"
                            placeholder="Ex. Approved"
                            #searchInput
                        />
                    </mat-form-field>
                </div>
                <div class="col-start-3">
                    <button
                        mat-raised-button
                        (click)="searchInput.value = 'Pending'; applyFilter()"
                    >
                        Show Pending Requests
                    </button>
                </div>
            </div>

            <div class="mat-elevation-z8">
                <table mat-table [dataSource]="requestsTableDataSource" matSort>
                    <!-- requestDate Column -->
                    <ng-container matColumnDef="requestDate">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>
                            Request Date
                        </th>
                        <td mat-cell *matCellDef="let row">
                            {{ row.requestDate | date: "dd/MM/yyyy" }}
                        </td>
                    </ng-container>

                    <!-- title Column -->
                    <ng-container matColumnDef="title">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>
                            Title
                        </th>
                        <td mat-cell *matCellDef="let row">{{ row.title }}</td>
                    </ng-container>

                    <!-- description Column -->
                    <ng-container matColumnDef="teamMemberID">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>
                            Request By
                        </th>
                        <td mat-cell *matCellDef="let row">
                            {{ row.teamMemberID }}
                        </td>
                    </ng-container>

                    <!-- requestStatus Column -->
                    <ng-container matColumnDef="requestStatus">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>
                            Status
                        </th>
                        <td mat-cell *matCellDef="let row">
                            {{ row.requestStatus }}
                        </td>
                    </ng-container>

                    <!-- Action Column -->
                    <ng-container matColumnDef="action">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>
                            Action
                        </th>
                        <td mat-cell *matCellDef="let row">
                            <button
                                mat-icon-button
                                (click)="loadServiceRequest(row)"
                            >
                                <i class="fa-duotone fa-wrench"></i>
                            </button>
                        </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr
                        mat-row
                        *matRowDef="let row; columns: displayedColumns"
                    ></tr>

                    <!-- Row shown when there is no matching data. -->
                    <tr class="mat-row" *matNoDataRow>
                        <td class="mat-cell" colspan="4">
                            No data matching the filter "{{ input.value }}"
                        </td>
                    </tr>
                </table>

                <mat-paginator
                    [pageSize]="5"
                    [pageSizeOptions]="[5, 10, 20, 50, 100]"
                    aria-label="Select page of requests"
                >
                </mat-paginator>
            </div>
        </div>
    } @else if (showDetails && selectedRequest) {
        <fuse-card
            class="flex flex-col md:flex-row justify-between w-full p-8 pb-4 filter-listing"
        >
            <div class="flex flex-col flex-auto order-2 md:order-1">
                <div class="text-2xl font-semibold leading-tight">
                    {{ selectedRequest.title }}
                </div>
                <div class="flex items-center leading-none mt-2 -ml-1">
                    <div class="text-secondary ml-2">
                        {{ selectedRequest.requestDate | date: "dd/MM/yyyy" }}
                    </div>
                    <div class="text-secondary mx-2">&bull;</div>
                    <div class="text-secondary">
                        {{ selectedRequest.teamMemberID }}
                    </div>
                </div>
                <div class="mt-6">
                    <div
                        class="flex flex-wrap items-center text-secondary my-1"
                    >
                        {{ selectedRequest.description }}
                    </div>
                </div>
                <div class="flex items-center mt-3 -mx-3">
                    <button
                        class="px-3 mr-5"
                        mat-flat-button
                        [color]="'accent'"
                        (click)="showDetails = false"
                    >
                        <i class="fa-duotone fa-arrow-left"></i> Back
                    </button>
                    @if (selectedRequest.requestStatus !== "Rejected") {
                        <button
                            class="px-3 mr-3"
                            mat-flat-button
                            [color]="'primary'"
                            (click)="createServiceRequest(selectedRequest)"
                        >
                            Create Service
                        </button>
                    }
                    <button
                        class="px-3 mr-3"
                        mat-flat-button
                        [color]="
                            selectedRequest.requestStatus === 'Rejected'
                                ? 'primary'
                                : 'warn'
                        "
                        (click)="unapproveRequest(selectedRequest)"
                    >
                        <i
                            class="mr-2 fa-duotone"
                            [ngClass]="
                                selectedRequest.requestStatus === 'Rejected'
                                    ? 'fa-check'
                                    : 'fa-ban'
                            "
                        ></i>
                        {{
                            selectedRequest.requestStatus === "Rejected"
                                ? "Mark as Pending"
                                : "Unapprove"
                        }}
                    </button>
                    <button
                        class="px-3"
                        mat-flat-button
                        [color]="'warn'"
                        (click)="deleteRequest(selectedRequest)"
                    >
                        <i class="mr-2 fa-duotone fa-trash"></i> Delete
                    </button>
                </div>
            </div>
            @for (file of selectedRequest.attachedFiles; track file) {
                <div
                    class="order-1 md:order-2 w-full md:w-40 md:ml-6 mb-8 md:mb-4 rounded-lg overflow-hidden"
                >
                    <a [href]="fileDetails(file).fileUrl" target="_blank">
                        <img
                            class="w-full h-full object-cover"
                            [src]="fileDetails(file).fileUrl"
                            alt="Card cover image"
                    /></a>
                </div>
            }
        </fuse-card>
    }
</mat-dialog-content>
<mat-dialog-actions>
    <button mat-button mat-dialog-close>Close</button>
</mat-dialog-actions>

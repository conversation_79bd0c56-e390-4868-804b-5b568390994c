import { Component, inject, input, model, After<PERSON>iew<PERSON>nit, OnD<PERSON>roy, OnInit, ViewChild, ElementRef } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import {
    MAT_DIALOG_DATA,
    MatDialogRef,
    MatDialogActions,
    MatDialogClose,
    MatDialogContent,
    MatDialogTitle,
} from '@angular/material/dialog';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { ServicingRequest } from 'app/core/databaseModels/servicingRequest/servicingRequest.types';
import { DatePipe, NgClass } from '@angular/common';
import { FuseCardComponent } from '@fuse/components/card';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { ServicingRequestService } from 'app/core/databaseModels/servicingRequest/servicingRequest.service';


@Component({
    selector: 'app-servicing-request',
    imports: [
        NgClass,
        MatButtonModule,
        MatDialogTitle,
        MatDialogContent,
        FuseCardComponent,
        MatDialogActions,
        MatDialogClose,
        MatButtonModule,
        DatePipe,
        MatFormFieldModule,
        MatInputModule,
        MatTableModule,
        MatSortModule,
        MatPaginatorModule],
    templateUrl: './servicing-request.component.html',
    styleUrl: './servicing-request.component.scss'
})
export class ServicingRequestComponent implements OnInit, OnDestroy, AfterViewInit {
    @ViewChild(MatPaginator) paginator: MatPaginator;
    @ViewChild(MatSort) sort: MatSort;
    @ViewChild('searchInput') searchInput: ElementRef<HTMLInputElement>;


    data = inject(MAT_DIALOG_DATA); // Using inject in a field initializer
    requestedServicing = model.required<ServicingRequest[]>();
    displayedColumns: string[] = ['requestDate', 'title', 'teamMemberID', 'requestStatus', 'action'];
    requestsTableDataSource: MatTableDataSource<ServicingRequest>;
    showDetails = false;
    selectedRequest: ServicingRequest;
    constructor(
        private dialogRef: MatDialogRef<ServicingRequestComponent>,
        private _fuseConfirmationService: FuseConfirmationService,
        private _servicingRequestService: ServicingRequestService
    ) {
        this.requestedServicing = this.data.requestedServicing;
    }
    ngOnInit(): void {
        // Injecting data from the dialog
        this.requestsTableDataSource = new MatTableDataSource(this.requestedServicing());
        // console.log(this.requestedServicing());
        // this.requestedServicing().forEach((request) => {
        //     // request.requestStatus = 'Processing';
        //     console.log(request);
        // })
    }
    ngOnDestroy(): void {

    }
    ngAfterViewInit() {
        // Make sure to set the paginator and sort after the view init
        // This ensures that the paginator is available when we try to assign it
        setTimeout(() => {
            this.requestsTableDataSource.paginator = this.paginator;
            this.requestsTableDataSource.sort = this.sort;
        });
    }

    applyFilter() {
        const filterValue = this.searchInput.nativeElement.value;
        this.requestsTableDataSource.filter = filterValue.trim().toLowerCase();

        if (this.requestsTableDataSource.paginator) {
            this.requestsTableDataSource.paginator.firstPage();
        }
    }



    loadServiceRequest(row) {
        //   console.log(row);
        if (row) {
            this.selectedRequest = row;
            this.showDetails = true;
        } else {

        }

        //
    }
    createServiceRequest(selectedRequest) {

        this.dialogRef.close(selectedRequest);
    }

    fileDetails(file: string): any {

        return JSON.parse(file);
    }

    unapproveRequest(request: ServicingRequest): void {
        // Determine the new status based on current status
        const newStatus = request.requestStatus === 'Rejected' ? 'Pending' : 'Rejected';
        const actionText = newStatus === 'Rejected' ? 'unapprove' : 'mark as pending';

        const confirmation = this._fuseConfirmationService.open({
            title: `${newStatus === 'Rejected' ? 'Unapprove' : 'Mark as Pending'} Service Request - ${request.title}`,
            message: `Are you sure you want to ${actionText} this service request?`,
            actions: {
                confirm: {
                    label: newStatus === 'Rejected' ? 'Unapprove' : 'Mark as Pending',
                    color: 'warn'
                },
            },
            icon: {
                show: true,
                name: newStatus === 'Rejected' ? 'heroicons_outline:ban' : 'heroicons_outline:check',
                color: 'warn'
            }
        });

        confirmation.afterClosed().subscribe((result) => {
            if (result === 'confirmed') {
                // Update the request status
                request.requestStatus = newStatus;
                this._servicingRequestService.updateServicingRequest(request.$id, request).subscribe(() => {
                    // Update the data source
                    this.updateDataSource();
                });
            }
        });
    }

    deleteRequest(request: ServicingRequest): void {
        const confirmation = this._fuseConfirmationService.open({
            title: `Delete Service Request - ${request.title}`,
            message: 'Are you sure you want to delete this service request? This action cannot be undone!',
            actions: {
                confirm: {
                    label: 'Delete',
                    color: 'warn'
                },
            },
            icon: {
                show: true,
                name: 'heroicons_outline:trash',
                color: 'warn'
            }
        });

        confirmation.afterClosed().subscribe((result) => {
            if (result === 'confirmed') {
                // First check if there are attached files and delete them
                if (request.attachedFiles && request.attachedFiles.length > 0) {
                    this.deleteFiles(request.attachedFiles);
                }

                // Then delete the request
                this._servicingRequestService.deleteServicingRequest(request.$id).subscribe(() => {
                    // Remove the request from the array
                    const index = this.requestedServicing().findIndex(item => item.$id === request.$id);
                    if (index !== -1) {
                        const updatedList = [...this.requestedServicing()];
                        updatedList.splice(index, 1);
                        this.requestedServicing.set(updatedList);
                    }
                    // Update the data source
                    this.updateDataSource();
                    // Go back to the list view
                    this.showDetails = false;
                });
            }
        });
    }

    deleteFiles(attachedFiles: string[]): void {
        try {
            if (attachedFiles.length > 0) {
                attachedFiles.forEach((file) => {
                    const fileObject = JSON.parse(file);
                    this._servicingRequestService.deleteFile(fileObject.fileId).subscribe((result) => {
                        // File deleted successfully
                    });
                });
            }
        } catch (error) {
            console.error('Error deleting files:', error);
        }
    }

    updateDataSource(): void {
        this.requestsTableDataSource.data = this.requestedServicing();
    }
}

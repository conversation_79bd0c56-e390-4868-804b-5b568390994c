import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Tag, Servicing } from './servising.types'
import { BehaviorSubject, filter, map, Observable, of, switchMap, take, tap, throwError } from 'rxjs';


@Injectable({ providedIn: 'root' })
export class ServicingService {


    // Private
    private _tags: BehaviorSubject<Tag[] | null> = new BehaviorSubject(null);
    private _servicing: BehaviorSubject<Servicing | null> = new BehaviorSubject(null);
    private _servicings: BehaviorSubject<Servicing[] | null> = new BehaviorSubject(null);

    /**
     * Constructor
     */
    constructor(private _httpClient: HttpClient) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Accessors
    // -----------------------------------------------------------------------------------------------------

    /**
   * Getter for tags
   */
    get tags$(): Observable<Tag[]> {
        return this._tags.asObservable();
    }


    /**
     * Getter for Servicing
     */
    get servicing$(): Observable<Servicing> {
        return this._servicing.asObservable();
    }

    /**
     * Getter for Servicings
     */
    get servicings$(): Observable<Servicing[]> {
        return this._servicings.asObservable();
    }



    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
    * Get tags
    */
    getTags(): Observable<Tag[]> {
        return this._httpClient.get<Tag[]>('api/apps/tasks/tags').pipe(
            tap((response: any) => {
                this._tags.next(response);
            }),
        );
    }

    /**
     * Crate tag
     *
     * @param tag
     */
    createTag(tag: Tag): Observable<Tag> {
        return this.tags$.pipe(
            take(1),
            switchMap(tags => this._httpClient.post<Tag>('api/apps/tasks/tag', { tag }).pipe(
                map((newTag) => {
                    // Update the tags with the new tag
                    this._tags.next([...tags, newTag]);

                    // Return new tag from observable
                    return newTag;
                }),
            )),
        );
    }

    /**
    * Update the tag
    *
    * @param id
    * @param tag
    */
    updateTag(id: string, tag: Tag): Observable<Tag> {
        return this.tags$.pipe(
            take(1),
            switchMap(tags => this._httpClient.patch<Tag>('api/apps/tasks/tag', {
                id,
                tag,
            }).pipe(
                map((updatedTag) => {
                    // Find the index of the updated tag
                    const index = tags.findIndex(item => item.id === id);

                    // Update the tag
                    tags[index] = updatedTag;

                    // Update the tags
                    this._tags.next(tags);

                    // Return the updated tag
                    return updatedTag;
                }),
            )),
        );
    }

    /**
     * Delete the tag
     *
     * @param id
     */
    deleteTag(id: string): Observable<boolean> {
        return this.tags$.pipe(
            take(1),
            switchMap(tags => this._httpClient.delete('api/apps/tasks/tag', { params: { id } }).pipe(
                map((isDeleted: boolean) => {
                    // Find the index of the deleted tag
                    const index = tags.findIndex(item => item.id === id);

                    // Delete the tag
                    tags.splice(index, 1);

                    // Update the tags
                    this._tags.next(tags);

                    // Return the deleted status
                    return isDeleted;
                }),
                filter(isDeleted => isDeleted),
                switchMap(isDeleted => this.servicings$.pipe(
                    take(1),
                    map((servicings) => {
                        // Iterate through the tasks
                        servicings.forEach((servicing) => {
                            const tagIndex = servicing.tags.findIndex(tag => tag === id);

                            // If the task has a tag, remove it
                            if (tagIndex > -1) {
                                servicing.tags.splice(tagIndex, 1);
                            }
                        });

                        // Return the deleted status
                        return isDeleted;
                    }),
                )),
            )),
        );
    }
    /**
      * Get tasks
      */
    getServices(): Observable<Servicing[]> {
        return this._httpClient.get<Servicing[]>('api/apps/tasks/all').pipe(
            tap((response) => {
                this._servicings.next(response);
            }),
        );
    }
    /**
 * Get service by id
 */
    getServiceById(id: string): Observable<Servicing> {
        return this._servicings.pipe(
            take(1),
            map((servicing) => {
                // Find the task
                //const task = servicing.find(item => item.id === id) || null;

                // Update the task
                //  this._servicing.next(task);

                // Return the task
                return null;
            }),
            // switchMap((task) => {
            //     if (!task) {
            //         return throwError('Could not found task with id of ' + id + '!');
            //     }

            //     return of(task);
            // }),
        );
    }
}

import { inject } from "@angular/core";
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot, Routes } from '@angular/router';
import { ServicingComponent } from "./servicing.component";

import { ServicingListComponent } from "./list/list.component"
import { ServicingDetailsComponent } from "./details/details.component"
import { catchError, throwError } from 'rxjs';






/**
 * Can deactivate tasks details
 *
 * @param component
 * @param currentRoute
 * @param currentState
 * @param nextState
 */
const canDeactivateServicingsDetails = (
    component: ServicingDetailsComponent,
    currentRoute: ActivatedRouteSnapshot,
    currentState: RouterStateSnapshot,
    nextState: RouterStateSnapshot) => {
    // Get the next route
    let nextRoute: ActivatedRouteSnapshot = nextState.root;
    while (nextRoute.firstChild) {
        nextRoute = nextRoute.firstChild;
    }

    // If the next state doesn't contain '/tasks'
    // it means we are navigating away from the
    // tasks app
    if (!nextState.url.includes('/servicing')) {
        // Let it navigate
        return true;
    }

    // If we are navigating to another task...
    if (nextRoute.paramMap.get('id')) {
        // Just navigate
        return true;
    }

    // Otherwise, close the drawer first, and then navigate
    return component.closeDrawer().then(() => true);
};
export default [
    {
        path: '',
        component: ServicingComponent,

        children: [
            {
                path: '',
                component: ServicingListComponent,
                children: [{
                    path: ':id',
                    component: ServicingDetailsComponent,
                    canDeactivate: [canDeactivateServicingsDetails],
                }]
            }
        ]
    },
] as Routes;

<div
  class="mb-5 max-w-sm rounded overflow-hidden shadow bg-white p-4 relative hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700 transition duration-300"
  >
  <div (click)="zoomAsset()" class="cursor-pointer">
    <div class="flex items-start">
      @if(asset.imageURL){
        <img
          [src]="asset.imageURL"
          class="rounded-full h-10 w-10 bg-gray-300 mr-4 dark:bg-gray-500"
          alt="Vehicle Image"
          />}@else{
          <div
            class="flex items-center justify-center text-xl font-bold rounded-full h-10 w-10 bg-gray-300 mr-4 dark:bg-gray-500"
            >
          {{ asset.assetName.charAt(0).toUpperCase() }}
        </div>
      }
      <div class="flex-1">
        <div class="text-xl font-bold dark:text-white">
          {{ asset.assetName }}
        </div>
        <div class="text-gray-600 text-sm dark:text-gray-300">
          Update on:
          <span class="font-semibold dark:text-gray-100">{{
            asset.locationDate | relativeTime
          }}</span>
        </div>
      </div>

      <qr-code
        class="absolute top-2 right-2"
        [value]="asset.tagData"
        size="50"
        errorCorrectionLevel="M"
      ></qr-code>
    </div>
    @if (asset.locationDate) {
      <div class="text-left mt-4">
        <div class="text-gray-600 dark:text-gray-300 text-xs">
          <i class="font-bold">reported on:</i>
          <span class="font-semibold dark:text-gray-100">{{
            asset.locationDate | date : "MMM dd, yyyy 'at' hh:mm a"
          }}</span>
        </div>
        <!-- Location, Lat/Lon, and Speed -->
        <div class="text-gray-600 dark:text-gray-300 text-xs">
          <i class="font-bold">Location:</i><br />
          <span class="font-semibold dark:text-gray-100">{{
            asset.currentAddress
          }}</span>
        </div>
        <div class="text-gray-600 dark:text-gray-300 text-xs">
          <i class="font-bold">Lat/Lon:</i>
          <span class="font-semibold dark:text-gray-100">{{
            asset.currentLocation
          }}</span>
        </div>
        <div class="text-gray-600 dark:text-gray-300 text-xs">
          <i class="font-bold">related Job:</i>
          <span class="font-semibold dark:text-gray-100">{{
            asset.relatedJobId || "N/A"
          }}</span>
        </div>
      </div>
    }
  </div>
</div>

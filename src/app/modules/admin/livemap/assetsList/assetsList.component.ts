
import { Component, Input, OnInit, Output, EventEmitter, ViewEncapsulation, OnChanges, SimpleChanges } from '@angular/core';
import { PipesModule } from 'app/pipes/pipes.module';
import { Assets } from 'app/core/databaseModels/assets/assets.types';
import { QrCodeModule } from 'ng-qrcode';
import { DatePipe } from '@angular/common';

@Component({
    selector: 'assets-list',
    templateUrl: './assetsList.component.html',
    encapsulation: ViewEncapsulation.None,
    imports: [PipesModule, QrCodeModule, DatePipe]
})
export class AssetsListComponent implements OnInit, OnChanges {

    @Input() asset: Assets;
    @Output() zoomToAsset: EventEmitter<string> = new EventEmitter();

    constructor() {

    }
    ngOnInit(): void {
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.asset) {
            this.asset = changes.asset.currentValue;
        }
    }
    zoomAsset() {
        this.zoomToAsset.emit(this.asset.$id);
    }

}

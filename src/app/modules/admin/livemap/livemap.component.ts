
import { Compo<PERSON>, On<PERSON><PERSON>roy, OnInit, ViewEncapsulation, ViewChild, AfterViewInit, ChangeDetectorRef } from '@angular/core';
import { GoogleMap, MapInfoWindow, MapMarker, MapTrafficLayer } from '@angular/google-maps';
import { FuseCardComponent } from '@fuse/components/card';
import { FuseScrollbarDirective } from '@fuse/directives/scrollbar';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { MatTabChangeEvent, MatTabsModule } from '@angular/material/tabs';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { Subject, catchError, switchMap, takeUntil, throwError } from 'rxjs';
import { TeamMemberListComponent } from './teamMemberList/teamMemberList.component';
import { TeamMembersService } from 'app/core/databaseModels/teammembers/teammembers.service';
import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';
import { FormsModule } from '@angular/forms';
import { Assets } from 'app/core/databaseModels/assets/assets.types';
import { AssetsService } from 'app/core/databaseModels/assets/assets.service';
import { MatMapsAutocompleteModule } from 'app/modules/widgets/mat-maps-autocomplete/mat-maps-autocomplete.module';
import { AssetsListComponent } from './assetsList/assetsList.component';
import { PendingJobListComponent } from './pendingJobList/pendingJobList.component';
import { ScheduledJob } from 'app/core/databaseModels/scheduledJobs/scheduledJob.types';
import { ScheduledJobsService } from 'app/core/databaseModels/scheduledJobs/scheduledJob.service';
import { DatePipe } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';


// Add Marker interface
interface Marker {
    position: google.maps.LatLngLiteral;
    title: string;
    options: google.maps.MarkerOptions;
    teamMemberId: string;
    infoContent?: string;
}
@Component({
    selector: 'app-liveMap',
    templateUrl: './livemap.component.html',
    styleUrls: ['./livemap.component.scss'],
    encapsulation: ViewEncapsulation.None,
    imports: [FormsModule,
        TeamMemberListComponent,
        AssetsListComponent,
        PendingJobListComponent,
        FuseCardComponent,
        MatButtonModule,
        MatIconModule,
        MatTabsModule,
        FuseScrollbarDirective,
        MatMapsAutocompleteModule,
        GoogleMap,
        MapInfoWindow,
        MapMarker,
        MapTrafficLayer,

    ],
    providers: [DatePipe]
})
export class LiveMapComponent implements OnInit, OnDestroy {
    private _unsubscribeAll: Subject<any> = new Subject();
    @ViewChild(GoogleMap, { static: false }) set imap(m: GoogleMap) {
        if (m) {
            // setTimeout(() => { this.initDrawingManager(m); }, 0);

            Promise.resolve().then(() => {
                this.map = m; // Store reference to map
                if (this.map && this.shouldFitMapToMarkers) {
                    // add delay 3 second
                    setTimeout(() => {
                        console.log('fitMapToMarkers called');
                        this.fitMapToMarkers(this.map); // Fit map to markers conditionally
                    }, 5000);

                }
            });
        }
    }
    @ViewChild(MapInfoWindow) infoWindow: MapInfoWindow;

    selectedTab: number = 0;
    startTrackTeammemberName: string = '';
    toggleTabList: boolean = true;
    map: GoogleMap | null = null; // Reference to the map
    mapId = '9852b6410bcc430e';
    mapTypeId: google.maps.MapTypeId;
    mapTypeIds = ['hybrid', 'roadmap', 'satellite', 'terrain'] as google.maps.MapTypeId[];
    mapTraffic: boolean = false;
    teamMembers: TeamMember[] = [];
    assets: Assets[] = [];
    scheduledJobs: ScheduledJob[] = [];
    filteredTeamMembers: TeamMember[] = []; // List to display
    filteredAssets: Assets[] = [];
    filteredScheduledJobs: ScheduledJob[] = [];
    markers: Marker[] = [];
    searchLocationMarker: Marker | null = null;
    searchLocation: boolean = false;
    assetMarkers: Marker[] = [];
    scheduledMarkers: Marker[] = [];
    trackingMemberId: string | null = null;
    shouldFitMapToMarkers = true; // Flag to control fitting map to markers
    private trackingInterval: any; // For tracking updates
    infoWindowContent;
    showTeamMemberList: boolean = true;
    showAssetList: boolean = false;
    showPendingJobs: boolean = false;

    teamMemberIdQuery: string = '';

    searchText: string = '';

    display: any;
    center: google.maps.LatLngLiteral = {
        lat: -24,
        lng: 134
    };
    zoom = 4;

    options: google.maps.MapOptions = {
        disableDefaultUI: true,
        fullscreenControl: false,
        mapId: this.mapId,
        gestureHandling: 'greedy',
        center: this.center,
        zoom: this.zoom,
        controlSize: 24,
        heading: 0,
        tilt: 65,
        mapTypeControlOptions: {
            position: google.maps.ControlPosition.RIGHT_BOTTOM
        },
        zoomControlOptions: {
            position: google.maps.ControlPosition.RIGHT_BOTTOM
        }
    };


    moveMap(event: google.maps.MapMouseEvent) {
        if (event.latLng != null) this.center = (event.latLng.toJSON());
    }
    move(event: google.maps.MapMouseEvent) {
        //TODO: Load location as move mose show pointer
        //  <div>Latitude: {{ display?.lat }}</div>
        // <div>Longitude: {{ display?.lng }}</div>
        if (event.latLng != null) this.display = event.latLng.toJSON();
    }

    /**
  * Constructor
  */
    constructor(
        private sanitizer: DomSanitizer,
        private _teamMembersService: TeamMembersService,
        private _changeDetectorRef: ChangeDetectorRef,
        private _assetsService: AssetsService,
        private _scheduledJobsService: ScheduledJobsService,
        private datePipe: DatePipe,
        private route: ActivatedRoute,
        private _router: Router,
        private _fuseConfirmationService: FuseConfirmationService,
    ) {
    }

    ngOnInit(): void {
        (window as any).livemapComponent = this;
        // this._teamMembersService.listenToRealTimeData();
        this._scheduledJobsService.listenToNotFinishedRealTimeData();
        this.route.queryParams.subscribe(params => {

            this.teamMemberIdQuery = params['teamMember'];
        });


        this.teamMembers = [];
        this._teamMembersService.teamMembers$.pipe(
            switchMap(() => this._teamMembersService.teamMembers$),
            takeUntil(this._unsubscribeAll),
            catchError(error => {
                console.error('Error:', error);
                return throwError(error);
            })
        ).subscribe(teamMembers => {
            if (!teamMembers) {
                return;
            }
            this.teamMembers = teamMembers;

            if (this.teamMembers.length == 0) {

                const addTeamMember = this._fuseConfirmationService.open({
                    title: `There are no team members available...`,
                    message: 'No team members found, please add team members first.',
                    icon: { show: true, name: "heroicons_outline:user-group", color: "success" },
                    actions: { confirm: { show: true, label: 'Add Team Members', color: 'primary' }, cancel: { show: false } },

                    dismissible: false,
                });
                addTeamMember.afterClosed().subscribe((result) => {

                    if (result === 'confirmed') {
                        this._router.navigate(['/tools']);
                    }
                })
            }
            this.filteredTeamMembers = this.teamMembers;
            this.updateMarkers(); // Update markers with new team member data

            if (this.teamMemberIdQuery && this.teamMemberIdQuery.length > 0) {
                this.startTrackVehicle(this.teamMemberIdQuery);
                this.teamMemberIdQuery = '';
            }
            // if (this.map && this.shouldFitMapToMarkers) {
            //     // add delay 3 second
            //     setTimeout(() => {
            //         this.fitMapToMarkers(this.map); // Fit map to markers conditionally
            //     }, 3000);

            // }

            // Mark for check
            this._changeDetectorRef.markForCheck();
            this._changeDetectorRef.detectChanges();
        });

        this._assetsService.getAssets().pipe(
            switchMap(() => this._assetsService.assets$),
            takeUntil(this._unsubscribeAll),
            catchError(error => {
                console.error('Error:', error);
                return throwError(error);
            })
        ).subscribe(assets => {
            this.assets = assets;
            this.filteredAssets = this.assets;
            this.updateAssetMarkers();
            // Mark for check
            this._changeDetectorRef.markForCheck();
            this._changeDetectorRef.detectChanges();
        })

        this._scheduledJobsService.getNotFinishedScheduledJobs().pipe(
            switchMap(() => this._scheduledJobsService.scheduledJobs$),
            takeUntil(this._unsubscribeAll),
            catchError(error => {
                console.error('Error:', error);
                return throwError(error);
            })
        ).subscribe(scheduledJobs => {
            this.scheduledJobs = scheduledJobs;
            this.filteredScheduledJobs = this.scheduledJobs;
            // Mark for check
            this._changeDetectorRef.markForCheck();
            this._changeDetectorRef.detectChanges();
        })


    }

    filterTeamMembers(): void {
        if (!this.searchText) {
            this.filteredTeamMembers = this.teamMembers;
        } else {
            this.filteredTeamMembers = this.teamMembers.filter(member =>
                member.name.toLowerCase().includes(this.searchText.toLowerCase()));
        }
    }

    filterAssets(): void {
        if (!this.searchText) {
            this.filteredAssets = this.assets;
        } else {
            this.filteredAssets = this.assets.filter(member =>
                member.assetName.toLowerCase().includes(this.searchText.toLowerCase()));
        }
    }

    filterScheduledJobs(): void {
        if (!this.searchText) {
            this.filteredScheduledJobs = this.scheduledJobs;
        } else {
            this.filteredScheduledJobs = this.scheduledJobs.filter(member =>
                member.jobTitle.toLowerCase().includes(this.searchText.toLowerCase()));
        }
    }
    resetSearch(): void {
        this.searchText = '';
        this.filteredTeamMembers = this.teamMembers;
    }


    mapNewControl(controlDiv, map, ButtonText, title) {
        // Set CSS for the control border.
        const controlUI = document.createElement("div");

        controlUI.style.backgroundColor = "#fff";
        controlUI.style.border = "2px solid #fff";
        controlUI.style.borderRadius = "1px";
        controlUI.style.boxShadow = "0 1px 4px rgba(0,0,0,.2)";
        controlUI.style.cursor = "pointer";
        controlUI.style.marginTop = "5px";
        controlUI.style.marginLeft = "0.5px";
        controlUI.style.marginBottom = "22px";
        controlUI.style.textAlign = "center";
        controlUI.title = title;
        controlDiv.appendChild(controlUI);

        // Set CSS for the control interior.
        const controlText = document.createElement("div");

        controlText.style.color = "rgb(25,25,25)";
        controlText.style.fontFamily = "Roboto,Arial,sans-serif";
        controlText.style.fontSize = "12px";
        controlText.style.lineHeight = "20px";
        controlText.style.paddingLeft = "5px";
        controlText.style.paddingRight = "5px";
        controlText.innerHTML = ButtonText;
        controlUI.appendChild(controlText);

        controlUI.onmouseover = function () {
            controlUI.style.backgroundColor = "rgb(235, 235, 235)";
            controlUI.style.border = "2px solid rgb(235, 235, 235)";
        };

        controlUI.onmouseleave = function () {
            controlUI.style.backgroundColor = "#fff";
            controlUI.style.border = "2px solid #fff";
        };

        // Setup the click event listeners: simply set the map to Chicago.
        /*  controlUI.addEventListener("click", () => {
           map.setCenter(chicago);
         }); */
    }

    mapTypeChanged(): void {
        // Find the index of the current mapTypeId in the mapTypeIds array
        const currentIndex = this.mapTypeIds.indexOf(this.mapTypeId);

        // Calculate the index of the next map type, wrapping around if necessary
        const nextIndex = (currentIndex + 1) % this.mapTypeIds.length;

        // Update the mapTypeId to the next map type
        this.mapTypeId = this.mapTypeIds[nextIndex];

        // Update the map's mapTypeId if you have a reference to the map instance
        if (this.map) {
            this.map.googleMap.setMapTypeId(this.mapTypeId);
        }
    }
    updateMarkers(): void {
        const markerPromises = this.teamMembers.map(member => {
            // Check if lastLocation is falsy or an empty string
            if (!member.lastLocation || member.lastLocation.trim() === '') {
                return null;
            }

            // Parse the lastLocation JSON string
            const location = JSON.parse(member.lastLocation);
            return this.createCustomMarkerIcon(member).then(iconUrl => {
                const infoContent = this.getInfoWindowContent(member);
                return {
                    position: { lat: location.Lat, lng: location.Lon },
                    title: member.name,
                    options: { icon: iconUrl },
                    teamMemberId: member.$id,
                    infoContent: infoContent,
                };
            });
        });

        // Filter out null or undefined values and wait for all promises to resolve
        Promise.all(markerPromises.filter(Boolean)).then(resolvedMarkers => {
            this.markers = resolvedMarkers;
        }).catch(error => {
            console.error("Error creating markers:", error);
        });
    }

    updateAssetMarkers(): void {
        const markerPromises = this.assets.map(asset => {
            // Check if lastLocation is falsy or an empty string
            if (!asset.currentLocation || asset.currentLocation.trim() === '') {
                return null;
            }
            return this.createCustomAssetMarkerIcon(asset).then(iconUrl => {
                const infoContent = this.getInfoWindowContentAsset(asset);
                const latLon = asset.currentLocation.split(',');
                return {
                    position: { lat: parseFloat(latLon[0]), lng: parseFloat(latLon[1]) },
                    title: asset.assetName,
                    options: { icon: iconUrl },
                    teamMemberId: asset.$id,
                    infoContent: infoContent,
                };
            })
        });
        Promise.all(markerPromises.filter(Boolean)).then(resolvedMarkers => {
            this.assetMarkers = resolvedMarkers;
        }).catch(error => {
            console.error("Error creating markers:", error);
        })

    }

    updateScheduledJobs(): void {

        const markerPromises = this.scheduledJobs.map(job => {
            return this.createCustomScheduledJobMarkerIcon(job).then(iconUrl => {
                const infoContent = this.getInfoWindowContentScheduledJob(job);
                const latLon = job.latLon.split(',');
                return {
                    position: { lat: parseFloat(latLon[0]), lng: parseFloat(latLon[1]) },
                    title: job.jobTitle,
                    options: { icon: iconUrl },
                    teamMemberId: job.$id,
                    infoContent: infoContent,
                };
            })
        });
        Promise.all(markerPromises.filter(Boolean)).then(resolvedMarkers => {
            this.scheduledMarkers = resolvedMarkers;
        }).catch(error => {
            console.error("Error creating markers:", error);
        })
    }

    openInfo(markerRef: MapMarker, infoContent: string): void {
        // Set the content of the info window

        this.infoWindowContent = this.sanitizer.bypassSecurityTrustHtml(infoContent);
        // Open the info window
        this.infoWindow.open(markerRef);
    }

    startTrackVehicle(vehicleId: string): void {
        this.shouldFitMapToMarkers = false; // Disable fitting map to all markers
        this.trackingMemberId = vehicleId;

        if (this.trackingInterval) {
            clearInterval(this.trackingInterval); // Clear existing interval
        }

        this.trackingInterval = setInterval(() => {
            const member = this.teamMembers.find(m => m.$id === vehicleId);
            this.startTrackTeammemberName = member.name;
            //console.log(member);
            if (!member.lastLocation) {
                this.stopTrackingVehicle();
                return;
            }
            if (member) {
                const location = JSON.parse(member.lastLocation);
                this.map.googleMap.moveCamera({
                    //  center: { lat: location.lat, lng: location.lng },
                    zoom: 18,
                    tilt: 65,
                    //  heading: currentPoint.heading,
                });
                this.center = { lat: location.Lat, lng: location.Lon };

            }
        }, 1000); // Update interval, adjust as needed
    }

    zoomToVehicle(vehicleId: string): void {
        this.stopTrackingVehicle();


        const member = this.teamMembers.find(m => m.$id === vehicleId);
        if (member) {
            if (!member.lastLocation) {
                return;
            }
            const location = JSON.parse(member.lastLocation);
            this.center = { lat: location.Lat, lng: location.Lon };
            setTimeout(() => {
                this.zoom = 18;
            }, 1000); // Adjust zoom level as needed from user options if need
        }
    }


    stopTrackingVehicle(): void {
        if (this.trackingInterval) {
            clearInterval(this.trackingInterval);
            this.trackingInterval = null;
        }

        this.shouldFitMapToMarkers = true; // Allow fitting map to all markers again
        this.trackingMemberId = null;
        // if (this.map) {
        //     this.fitMapToMarkers(this.map); // Optionally re-fit map to all markers
        // }
        // Optionally, reset other tracking-related states or UI elements
    }

    zoomToAsset(assetId: string) {
        this.stopTrackingVehicle();
        this.shouldFitMapToMarkers = false;
        const asset = this.assets.find(m => m.$id === assetId);
        if (asset) {
            if (!asset.currentLocation) {
                return;
            }
            const location = asset.currentLocation.split(',');
            this.center = { lat: parseFloat(location[0]), lng: parseFloat(location[1]) };
            this.zoom = 16; // Adjust zoom level as needed from user options if need
        }
    }
    zoomToJob(jobId: string) {

        this.stopTrackingVehicle();
        const job = this.scheduledJobs.find(m => m.$id === jobId);
        if (job) {
            if (!job.latLon) {
                return;
            }
            const location = job.latLon.split(',');
            this.center = { lat: parseFloat(location[0]), lng: parseFloat(location[1]) };
            // add delay
            setTimeout(() => {
                this.zoom = 16; // Adjust zoom level as needed from user options if need
            }, 3000);


        }
    }
    refreshZoom() {

        if (this.map) {
            if (this.selectedTab == 0) {
                this.fitMapToMarkers(this.map); // Optionally re-fit map to all markers
            }
            if (this.selectedTab == 1) {
                this.fitAssetMapToMarkers(this.map);
            }
            if (this.selectedTab == 2) {
                this.fitScheduledJobMapToMarkers(this.map);
            }

        }
    }
    fitMapToMarkers(map: GoogleMap): void {
        //  console.log('fitMapToMarkers');
        if (this.selectedTab == 0) {


            const bounds = new google.maps.LatLngBounds();
            this.markers.forEach(marker => {
                if (marker.position.lat && marker.position.lng) {
                    bounds.extend(marker.position);
                }
            });

            if (!bounds.isEmpty()) {
                // add delay 3 second
                setTimeout(() => {
                    map.fitBounds(bounds, { top: 50, right: 50, bottom: 50, left: 50 });
                }, 1000);


            }
        }
    }

    fitAssetMapToMarkers(map: GoogleMap): void {

        const bounds = new google.maps.LatLngBounds();
        this.assetMarkers.forEach(marker => {
            if (marker.position.lat && marker.position.lng) {
                bounds.extend(marker.position);
            }
        });
        if (!bounds.isEmpty()) {
            map.fitBounds(bounds, { top: 50, right: 50, bottom: 50, left: 50 });
        }
    }
    fitScheduledJobMapToMarkers(map: GoogleMap): void {
        const bounds = new google.maps.LatLngBounds();
        this.scheduledMarkers.forEach(marker => {
            if (marker.position.lat && marker.position.lng) {
                bounds.extend(marker.position);
            }
        });
        if (!bounds.isEmpty()) {
            map.fitBounds(bounds, { top: 50, right: 50, bottom: 50, left: 50 });
        }
    }

    ngOnDestroy(): void {
        (window as any).livemapComponent = null;
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    createCustomMarkerIcon(member: TeamMember): Promise<string> {
        return new Promise((resolve, reject) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const totalRadius = 18; // Total radius including padding
            const radius = 15; // Radius of the ring
            const imageRadius = radius - 3; // Radius for the image, smaller than the ring
            canvas.width = totalRadius * 2;
            canvas.height = totalRadius * 2;

            // Draw the outer ring with color status and white background
            ctx.beginPath();
            ctx.arc(totalRadius, totalRadius, radius, 0, 2 * Math.PI);
            ctx.fillStyle = 'white';
            ctx.fill();
            ctx.lineWidth = 3;
            ctx.strokeStyle = this.getStatusColor(member.dutyStatus); // Ensure this returns a color value
            ctx.stroke();

            if (member.avatar) {
                const img = new Image();
                img.crossOrigin = "Anonymous";
                img.onload = () => {
                    // Clip to a smaller circle for the image
                    ctx.save();
                    ctx.beginPath();
                    ctx.arc(totalRadius, totalRadius, imageRadius, 0, Math.PI * 2, true);
                    ctx.closePath();
                    ctx.clip();

                    // Draw the image
                    ctx.drawImage(img, totalRadius - imageRadius, totalRadius - imageRadius, imageRadius * 2, imageRadius * 2);
                    ctx.restore();

                    resolve(canvas.toDataURL());
                };
                img.onerror = () => {
                    resolve(canvas.toDataURL()); // Resolve with the ring if image fails to load
                };
                img.src = member.avatar;
            } else {
                // Draw initials if no image
                ctx.fillStyle = "#000"; // Text color
                ctx.font = "13px Arial"; // Adjust font size and style as needed
                ctx.textAlign = "center";
                ctx.textBaseline = "middle";
                const initials = member.name.split(' ').map(n => n[0]).join('').toUpperCase();
                ctx.fillText(initials, totalRadius, totalRadius);

                resolve(canvas.toDataURL());
            }
        });
    }

    createCustomAssetMarkerIcon(asset: Assets): Promise<string> {
        return new Promise((resolve, reject) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const totalRadius = 18; // Total radius including padding
            const radius = 15;
            const imageRadius = radius - 3; // Radius for the image, smaller than the ring
            canvas.width = totalRadius * 2;
            canvas.height = totalRadius * 2;

            // Draw the outer ring with color status and white background
            ctx.beginPath();
            ctx.arc(totalRadius, totalRadius, radius, 0, 2 * Math.PI);
            ctx.fillStyle = 'white';
            ctx.fill();
            ctx.lineWidth = 3;
            ctx.strokeStyle = asset.status === 'Drop' ? 'red' : 'green' // Ensure this returns a color value
            ctx.stroke();

            ctx.stroke();
            if (asset.imageURL) {
                const img = new Image();
                img.crossOrigin = "Anonymous";
                img.onload = () => {
                    // Clip to a smaller circle for the image
                    ctx.save();
                    ctx.beginPath();
                    ctx.arc(totalRadius, totalRadius, imageRadius, 0, Math.PI * 2, true);
                    ctx.closePath();
                    ctx.clip();

                    // Draw the image
                    ctx.drawImage(img, totalRadius - imageRadius, totalRadius - imageRadius, imageRadius * 2, imageRadius * 2);
                    ctx.restore();

                    resolve(canvas.toDataURL());
                };
                img.onerror = () => {
                    resolve(canvas.toDataURL()); // Resolve with the ring if image fails to load
                };
                img.src = asset.imageURL;
            } else {
                // Draw initials if no image
                ctx.fillStyle = "#000"; // Text color
                ctx.font = "13px Arial"; // Adjust font size and style as needed
                ctx.textAlign = "center";
                ctx.textBaseline = "middle";
                const initials = asset.assetName.split(' ').map(n => n[0]).join('').toUpperCase();
                ctx.fillText(initials, totalRadius, totalRadius);

                resolve(canvas.toDataURL());
            }
        }); // Radius of the ring

    }


    createCustomScheduledJobMarkerIcon(job: ScheduledJob): Promise<string> {
        return new Promise((resolve, reject) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const totalRadius = 18; // Total radius including padding
            const radius = 15; // Radius of the ring
            const imageRadius = radius - 3; // Radius for the image, smaller than the ring
            canvas.width = totalRadius * 2;
            canvas.height = totalRadius * 2;

            // Draw the outer ring with color status and white background
            ctx.beginPath();
            ctx.arc(totalRadius, totalRadius, radius, 0, 2 * Math.PI);
            ctx.fillStyle = 'white';
            ctx.fill();
            ctx.lineWidth = 3;
            ctx.strokeStyle = this.getStatusColor(job.jobStatus); // Ensure this returns a color value
            ctx.stroke();


            // Draw initials if no image
            ctx.fillStyle = "#000"; // Text color
            ctx.font = "13px Arial"; // Adjust font size and style as needed
            ctx.textAlign = "center";
            ctx.textBaseline = "middle";
            const initials = job.jobTitle.split(' ').map(n => n[0]).join('').toUpperCase();
            ctx.fillText(initials, totalRadius, totalRadius);

            resolve(canvas.toDataURL());

        });
    }

    getInfoWindowContent(member: TeamMember): string {
        // Parse location data if available
        const locationData = member.lastLocation ? JSON.parse(member.lastLocation) : null;



        // Format relative time directly
        const lastReportTime = locationData?.EventTime ?
            this.formatRelativeTime(new Date(locationData.EventTime)) : 'Unknown';

        // Use Tailwind CSS classes for styling with improved layout
        return `
        <div class="p-3 max-w-sm rounded-lg overflow-hidden shadow-lg bg-white">
            <div class="flex items-center justify-between mb-2">
                <div class="font-bold text-lg">${member.name}</div>
                <div class="px-2 py-1 rounded-full text-xs font-semibold text-white capitalize" 
                     style="background-color: ${this.getStatusColor(member.dutyStatus)}">
                    ${member.dutyStatus || 'Unknown'}
                </div>
            </div>
            
            <div class="space-y-2 text-gray-700">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-phone w-4 text-gray-500"></i>
                    <span>${member.phone || 'No phone'}</span>
                </div>
                
                <div class="flex items-center space-x-2">
                    <i class="fas fa-envelope w-4 text-gray-500"></i>
                    <span>${member.email || 'No email'}</span>
                </div>
                
                ${locationData ? `
                <hr class="my-2 border-gray-200">
                
                <div class="flex items-center space-x-2 text-sm">
                    <i class="fas fa-history w-4 text-gray-500"></i>
                    <span>Last report: ${lastReportTime}</span>
                </div>
                
                <div class="grid grid-cols-2 gap-2 mt-2">
                    <div class="flex items-center space-x-1">
                        <i class="fas fa-tachometer-alt text-gray-500"></i>
                        <span>${locationData.Speed || '0'} KM/Hour</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <i class="fas fa-battery-three-quarters text-gray-500"></i>
                        <span>${locationData.BatteryLevel || '0%'}</span>
                    </div>
                    
                    <div class="flex items-center space-x-1">
                        <i class="fas fa-clock text-gray-500"></i>
                        <span>${locationData.EventTime ? new Date(locationData.EventTime).toLocaleTimeString() : 'Unknown'}</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <i class="fas fa-map-pin text-gray-500"></i>
                        <span>${locationData.Lat?.toFixed(4)}, ${locationData.Lon?.toFixed(4)}</span>
                    </div>
                </div>` : ''}
                
                <div class="mt-3 flex justify-between">
                    <button class="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
                            onclick="window.livemapComponent.startTrackVehicle('${member.$id}')">
                        Track
                    </button>
                    <button class="px-3 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600 transition-colors"
                            onclick="window.open('tel:${member.phone}', '_system')">
                        Call
                    </button>
                </div>
            </div>
        </div>`;
    }
    formatRelativeTime(date: Date): string {
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffSecs = Math.floor(diffMs / 1000);
        const diffMins = Math.floor(diffSecs / 60);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffSecs < 60) {
            return 'just now';
        } else if (diffMins < 60) {
            return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
        } else if (diffHours < 24) {
            return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
        } else if (diffDays < 7) {
            return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
        } else {
            return this.datePipe.transform(date, 'MMM dd, yyyy') || '';
        }
    }

    getInfoWindowContentAsset(asset: Assets): string {
        // Use Tailwind CSS classes for styling
        return `
        <div class="p-4 max-w-sm rounded overflow-hidden shadow-lg bg-white">
            <div class="font-bold text-xl mb-2">${asset.assetName}</div>
            <p class="text-gray-700 text-base">Status: ${asset.status}</p>
            <!-- Add more fields as needed -->

            <div class="bg-white rounded-full w-4 h-4 absolute top-1 left-1"></div>
          </div>
        </div>`;
    }

    getInfoWindowContentScheduledJob(job: ScheduledJob): string {
        // Use Tailwind CSS classes for styling
        return `
        <div class="p-4 max-w-sm rounded-lg overflow-hidden shadow-lg bg-white">
            <div class="flex items-center justify-between mb-2">
                <div class="font-bold text-xl">${job.jobTitle}</div>
                <div class="px-2 py-1 rounded-full text-xs font-semibold text-white capitalize" 
                     style="background-color: ${this.getJobStatusColor(job.jobStatus)}">
                    ${job.jobStatus}
                </div>
            </div>
            
            <div class="space-y-2 text-gray-700">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-calendar-alt w-5 text-gray-500"></i>
                    <span>${this.formatDate(job.dueDate)}</span>
                </div>
                
                <div class="flex items-center space-x-2">
                    <i class="fas fa-map-marker-alt w-5 text-gray-500"></i>
                    <span>${job.jobAddress || 'No address'}</span>
                </div>
                
                ${job.adminNote ? `
                <hr class="my-2 border-gray-200">
                <div class="text-sm">
                    <div class="font-medium mb-1">Description:</div>
                    <p class="text-gray-600">${job.adminNote}</p>
                </div>` : ''}
                
                <div class="mt-3 flex justify-between">
                    <button class="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
                            onclick="window.livemapComponent.zoomToJob('${job.$id}')">
                        Zoom
                    </button>
                    <button class="px-3 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600 transition-colors"
                            onclick="window.livemapComponent._router.navigate(['/scheduler'], { queryParams: { scheduleID: '${job.$id}' } })">
                        Details
                    </button>
                </div>
            </div>
        </div>`;
    }

    // Update the helper method to get color based on job status to match the JobMap component
    getJobStatusColor(status: string): string {
        switch (status?.toLowerCase()) {
            case 'added':
                return '#808080'; // Gray
            case 'pending':
                return '#FFA500'; // Orange
            case 'approved':
                return '#0000ff'; // Blue
            case 'started':
                return '#ffc107'; // Amber
            case 'onmyway':
                return '#fde047'; // Yellow
            case 'finished':
                return '#00ff00'; // Green
            case 'completed':
                return '#4CAF50'; // Green
            case 'cancelled':
                return '#F44336'; // Red
            default:
                return '#808080'; // Gray
        }
    }
    onTabChanged(event: MatTabChangeEvent): void {

        if (event.index == 0) {
            this.selectedTab = 0;
            this.showAssetList = false;
            this.showPendingJobs = false;
            this.showTeamMemberList = true;
            this.fitMapToMarkers(this.map);

        } else if (event.index == 1) {
            this.selectedTab = 1;
            this.stopTrackingVehicle();
            this.showAssetList = true;
            this.showPendingJobs = false;
            this.showTeamMemberList = false;
            this.updateAssetMarkers();
            this.fitAssetMapToMarkers(this.map);

        } else if (event.index == 2) {
            this.selectedTab = 2;
            this.stopTrackingVehicle();
            this.showPendingJobs = true;
            this.showAssetList = false;
            this.showTeamMemberList = false;
            this.updateScheduledJobs();
            this.fitScheduledJobMapToMarkers(this.map);
        }
        // Add your logic here
    }

    refreshMap() {
        if (this.selectedTab == 0) {

            this.fitMapToMarkers(this.map);
        } else if (this.selectedTab == 1) {

            this.updateAssetMarkers();
            this.fitAssetMapToMarkers(this.map);
        } else if (this.selectedTab == 2) {

            this.updateScheduledJobs();
            this.fitScheduledJobMapToMarkers(this.map);
        }
    }
    onAutocompleteSelected(location: any) {
        //  console.log(location.address);
        // console.log(location.latLon);
        this.stopTrackingVehicle();



        if (location.latLon) {
            const latLon = location.latLon.split(',');
            this.center = { lat: parseFloat(latLon[0]), lng: parseFloat(latLon[1]) };
            this.zoom = 18; // Adjust zoom level as needed from user options if need

            this.searchLocationMarker = {

                position: {
                    lat: parseFloat(latLon[0]),
                    lng: parseFloat(latLon[1])
                },
                title: location.address,
                options: {
                    draggable: false,
                    animation: google.maps.Animation.BOUNCE
                },
                teamMemberId: '',

            }
            this.searchLocation = true;


        }
    }
    private formatDate(date: Date): string {
        return this.datePipe.transform(date, 'MMM dd, yyyy \'at\' hh:mm a') || '';
    }
    getStatusColor(status: string): string {
        switch (status) {
            case 'offDuty':
                return '#ff0000'; // Red
            case 'onDuty':
                return '#0000ff'; // Blue
            case 'driving':
                return '#00ff00'; // Green
            case 'breaking':
                return '#ffc107'; // Amber
            default:
                return '#808080'; // Gray
        }
    }

}

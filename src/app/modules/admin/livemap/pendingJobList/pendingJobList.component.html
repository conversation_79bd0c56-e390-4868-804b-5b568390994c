<div
  class="max-w-sm w-full mb-5 rounded-lg overflow-hidden shadow-lg bg-white dark:bg-gray-800 hover:shadow-xl transition-shadow duration-300 cursor-pointer"
  (click)="zoomJob()"
  >
  <div class="p-6">
    <div class="flex items-start justify-between">
      <!-- Job Title and Update Time -->
      <div class="flex-1">
        <h2
          class="text-2xl font-bold text-gray-900 dark:text-white mb-1"
          >
          {{ pendingJob.jobTitle }}
        </h2>
        <p class="text-gray-500 dark:text-gray-400 text-sm">
          Updated on:
          <span class="font-medium">{{
            pendingJob.dueDate | relativeTime
          }}</span>
        </p>
      </div>
      <!-- Status Bubble -->
      <div class="ml-4 flex-shrink-0">
        <span
          class="px-3 py-1 rounded-full text-sm font-semibold text-white capitalize"
                    [ngClass]="{
                        'bg-gray-500':
                            pendingJob.jobStatus === 'pending' ||
                            pendingJob.jobStatus === 'added',
                        'bg-blue-500': pendingJob.jobStatus === 'approved',
                        'bg-green-400': pendingJob.jobStatus === 'started',
                        'bg-yellow-500': pendingJob.jobStatus === 'onMyWay',
                        'bg-green-500': pendingJob.jobStatus === 'finished',
                        'bg-gray-300': !pendingJob.jobStatus
                    }"
          >
          {{ pendingJob.jobStatus || "N/A" }}
        </span>
      </div>
    </div>

    <!-- Job Details -->
    @if (pendingJob.dueDate) {
      <div class="mt-4 space-y-2">
        <div class="text-gray-700 dark:text-gray-300 text-sm">
          <span class="font-semibold">Reported on:</span>
          <span>{{
            pendingJob.dueDate | date: "MMM dd, yyyy 'at' hh:mm a"
          }}</span>
        </div>
        <div class="text-gray-700 dark:text-gray-300 text-sm">
          <span class="font-semibold">Location:</span>
          <span>{{ pendingJob.jobAddress }}</span>
        </div>
        <div class="text-gray-700 dark:text-gray-300 text-sm">
          <span class="font-semibold">Lat/Lon:</span>
          <span>{{ pendingJob.latLon }}</span>
        </div>
      </div>
    }
  </div>
</div>

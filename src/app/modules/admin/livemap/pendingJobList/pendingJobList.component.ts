import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, Output, EventEmitter, ViewEncapsulation, OnChanges, SimpleChanges } from '@angular/core';
import { ScheduledJob } from 'app/core/databaseModels/scheduledJobs/scheduledJob.types';
import { PipesModule } from 'app/pipes/pipes.module';

@Component({
    selector: 'pendingJob-list',
    templateUrl: './pendingJobList.component.html',
    encapsulation: ViewEncapsulation.None,
    imports: [CommonModule, PipesModule,]
})
export class PendingJobListComponent implements OnInit, OnChanges {

    @Input() pendingJob: ScheduledJob;
    @Output() zoomToJob: EventEmitter<string> = new EventEmitter();

    constructor() {

    }

    ngOnInit(): void {
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.pendingJob) {
            this.pendingJob = changes.pendingJob.currentValue;
        }
    }
    zoomJob() {

        this.zoomToJob.emit(this.pendingJob.$id);
    }

}

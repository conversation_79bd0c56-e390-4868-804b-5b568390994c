.mat-mdc-mini-fab {
    border-radius: 25px !important;
}
.search-input {
    outline: none;
    border: none;
    background: #fff;
    width: 0;
    color: #0c0c0c;
    float: left;
    font-size: 14px;
    transition: .3s;
    line-height: 40px;
    border-radius: 25px;
    margin-right: 10px;
    padding: auto;

  }

  .search-input::placeholder {
    color: #dbc5b0;
  }

  .search-input::placeholder {
    color: #dbc5b0;
  }

  /* icon */
  .search-btn {

    transition: .3s;
  }

  .search-input:focus,
  .search-input:not(:placeholder-shown) {
    width: 240px;
    padding: 0 6px;
  }

  .search-box:hover > .search-input {
    width: 240px;
    padding: 0 6px;
  }

  .search-box:hover > .search-btn,
  .search-input:focus + .search-btn,
  .search-input:not(:placeholder-shown) + .search-btn {
    background: #fff;
    color: #cd595a;
  }

  .scroller {
    animation: mask-up;
    animation-timeline: scroll(self);
    animation-range: 0 1rem;
    mask-composite: exclude;
    }
    @keyframes mask-up {
    to { mask-size: 100% 120px, 100% 100%; }
    }

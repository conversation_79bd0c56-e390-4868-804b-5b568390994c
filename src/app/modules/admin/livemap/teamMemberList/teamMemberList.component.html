<div
    class="max-w-sm bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden mb-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-200"
>
    <div (click)="zoomVehicle()" class="cursor-pointer p-3 relative">
        <!-- Status Indicator -->
        <span
            [class]="
                getStatusColor(vehicleData.status) +
                ' absolute top-2 right-2 rounded-full h-3 w-3 border border-white dark:border-gray-800'
            "
        ></span>

        <!-- Row for Image, Name, and Last Update -->
        <div class="flex items-center">
            <!-- Image or Initial -->
            @if (vehicleData.imagePath) {
                <img
                    [src]="vehicleData.imagePath"
                    class="rounded-full h-8 w-8 object-cover mr-3 bg-gray-200 dark:bg-gray-600"
                    alt="Vehicle Image"
                />
            } @else {
                <div
                    class="flex items-center justify-center text-lg font-semibold rounded-full h-8 w-8 bg-gray-200 mr-3 dark:bg-gray-600 text-gray-700 dark:text-gray-300"
                >
                    {{ vehicleData.name.charAt(0).toUpperCase() }}
                </div>
            }

            <!-- Name and Last Update -->
            <div class="flex-1 min-w-0">
                <div
                    class="text-base font-semibold text-gray-900 dark:text-white truncate"
                >
                    {{ vehicleData.name }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                    @if (vehicleData.lastUpdate == "N/A") {
                        <span>{{ vehicleData.lastUpdate }}</span>
                    } @else {
                        <span>{{ vehicleData.lastUpdate | relativeTime }}</span>
                    }
                </div>
            </div>
        </div>

        <!-- Additional Details -->
        @if (vehicleData.location) {
            <div
                class="mt-2 text-xs text-gray-600 dark:text-gray-300 space-y-1"
            >
                <!-- Last Update -->
                <div class="flex items-center space-x-1.5">
                    <i class="fas fa-clock w-3"></i>
                    <span>
                        @if (vehicleData.lastUpdate == "N/A") {
                            {{ vehicleData.lastUpdate }}
                        } @else {
                            {{
                                vehicleData.lastUpdate
                                    | date: "MMM dd, yyyy 'at' hh:mm a"
                            }}
                        }
                    </span>
                </div>

                <!-- Location -->
                <div class="flex items-center space-x-1.5">
                    <i class="fas fa-map-marker-alt w-3"></i>
                    <span class="truncate">{{ vehicleData.location }}</span>
                </div>

                <!-- Lat/Lon -->
                <div class="flex items-center space-x-1.5">
                    <i class="fas fa-compass w-3"></i>
                    <span>{{ vehicleData.latLon }}</span>
                </div>

                <!-- Speed and Battery -->
                <div class="flex items-center space-x-3">
                    <div class="flex items-center space-x-1.5">
                        <i class="fas fa-tachometer-alt w-3"></i>
                        <span>{{ vehicleData.speed }}</span>
                    </div>
                    <div class="flex items-center space-x-1.5">
                        <i class="fas fa-battery-three-quarters w-3"></i>
                        <span>{{ vehicleData.batteryLevel }}</span>
                    </div>
                </div>
            </div>
        }
    </div>

    <!-- Track Car Button -->
    <div class="px-3 pb-2 text-center">
        <button
            (click)="callTracking()"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-1.5 px-3 rounded-md dark:bg-blue-500 dark:hover:bg-blue-600 transition duration-200"
        >
            Track Car
        </button>
    </div>
</div>


// Create team member list component


import { DatePipe } from '@angular/common';
import { Component, Input, OnInit, Output, EventEmitter, ViewEncapsulation, OnChanges, SimpleChanges } from '@angular/core';
import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';
import { PipesModule } from 'app/pipes/pipes.module';

@Component({
    selector: 'team-member-list',
    templateUrl: './teamMemberList.component.html',
    encapsulation: ViewEncapsulation.None,
    imports: [PipesModule, DatePipe]
})
export class TeamMemberListComponent implements OnInit, OnChanges {

    @Input() teamMember: TeamMember;
    @Output() trackVehicle: EventEmitter<string> = new EventEmitter();
    @Output() zoomToVehicle: EventEmitter<string> = new EventEmitter();

    vehicleData: any; // Updated type to any for flexibility, can be typed more specifically if needed

    ngOnInit(): void {
        if (this.teamMember && this.teamMember.lastLocation) {
            try {

                this.updateVehicleData(this.teamMember);
            } catch (error) {
                console.error('Error parsing lastLocation JSON:', error);
            }
            // Parse the lastLocation JSON string
            // const lastLocation = JSON.parse(this.teamMember.lastLocation);

            // Update vehicleData with the parsed information

        }
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.teamMember && changes.teamMember.currentValue) {
            this.updateVehicleData(changes.teamMember.currentValue);
        }
    }

    private updateVehicleData(teamMember: TeamMember) {
        try {


            if (teamMember.lastLocation) {



                const lastLocation = JSON.parse(teamMember.lastLocation);
                this.vehicleData = {
                    id: teamMember.$id,
                    imagePath: teamMember.avatar,
                    name: teamMember.name,
                    status: teamMember.dutyStatus ?? 'offDuty',
                    lastUpdate: lastLocation.EventTime, // Assuming EventTime is in the desired format
                    location: 'N/A Address', // Update this as necessary
                    latLon: `${lastLocation.Lat}, ${lastLocation.Lon}`,
                    speed: `${lastLocation.Speed} KM/Hour`,
                    batteryLevel: `${lastLocation.BatteryLevel ?? '0%'}`,
                };
            } else {
                this.vehicleData = {
                    id: teamMember.$id,
                    imagePath: teamMember.avatar,
                    name: teamMember.name,
                    status: teamMember.dutyStatus ?? 'offDuty',
                    lastUpdate: 'N/A', // Assuming EventTime is in the desired format
                    location: 'N/A Address', // Update this as necessary
                    latLon: `n/A`,
                    speed: `0 KM/Hour`,
                    batteryLevel: `0%`,
                };
            }
        } catch (error) {
            console.error(`Error parsing lastLocation: ${teamMember.lastLocation}`, error);
            return;
        }


    }
    getStatusColor(status: string): string {
        switch (status) {
            case 'offDuty':
                return 'bg-red-500';
            case 'onDuty':
                return 'bg-blue-500';
            case 'driving':
                return 'bg-green-500';
            case 'breaking':
                return 'bg-amber-500';
            default:
                return 'bg-gray-500'; // Default color
        }
    }

    callTracking() {
        this.trackVehicle.emit(this.vehicleData.id);
    }

    zoomVehicle() {
        this.zoomToVehicle.emit(this.vehicleData.id);
    }
}

<div class="flex flex-col flex-auto min-w-0">
    <div class="flex flex-grow">
        <google-map
            class="flex flex-grow"
            width="100%"
            height="100%"
            [center]="center"
            [zoom]="zoom"
            [options]="options"
            (mapClick)="moveMap($event)"
            (mapMousemove)="move($event)"
            [mapTypeId]="mapTypeId"
        >
            @if (mapTraffic) {
                <map-traffic-layer [autoRefresh]="true" />
            }
            @if (searchLocation) {
                <map-marker
                    [position]="searchLocationMarker.position"
                    [title]="searchLocationMarker.title"
                    [options]="searchLocationMarker.options"
                >
                </map-marker>
            }
            @if (showTeamMemberList) {
                @for (marker of markers; track marker.teamMemberId) {
                    <map-marker
                        #markerRef="mapMarker"
                        [position]="marker.position"
                        [title]="marker.title"
                        [options]="marker.options"
                        (mapClick)="openInfo(markerRef, marker.infoContent)"
                    >
                        <map-info-window #infoWindow>
                            <div [innerHTML]="infoWindowContent"></div>
                        </map-info-window>
                    </map-marker>
                }
            } @else if (showAssetList) {
                @for (marker of assetMarkers; track marker.teamMemberId) {
                    <map-marker
                        #markerRef="mapMarker"
                        [position]="marker.position"
                        [title]="marker.title"
                        [options]="marker.options"
                        (mapClick)="openInfo(markerRef, marker.infoContent)"
                    >
                        <map-info-window #infoWindow>
                            <div [innerHTML]="infoWindowContent"></div>
                        </map-info-window>
                    </map-marker>
                }
            } @else if (showPendingJobs) {
                @for (marker of scheduledMarkers; track marker.teamMemberId) {
                    <map-marker
                        #markerRef="mapMarker"
                        [position]="marker.position"
                        [title]="marker.title"
                        [options]="marker.options"
                        (mapClick)="openInfo(markerRef, marker.infoContent)"
                    >
                        <map-info-window #infoWindow>
                            <div [innerHTML]="infoWindowContent"></div>
                        </map-info-window>
                    </map-marker>
                }
            }
        </google-map>
    </div>
    <div>
        @if (toggleTabList) {
            <fuse-card
                class="absolute mt-6 bottom-[4rem] mb-6 ml-6 inset-0 z-10 flex flex-col max-w-80 w-full filter-article p-2"
            >
                <div class="w-full max-w-screen-xl mx-auto">
                    <mat-tab-group
                        mat-stretch-tabs="false"
                        (selectedTabChange)="onTabChanged($event)"
                        [animationDuration]="'0'"
                    >
                        <mat-tab>
                            <ng-template mat-tab-label>
                                <i class="fa-duotone fa-solid fa-car-bus"></i>
                            </ng-template>
                            <!-- teamMember List -->
                            <div class="m-2">
                                <div class="relative mb-2">
                                    <input
                                        type="text"
                                        id="search"
                                        name="search"
                                        placeholder="Search..."
                                        class="w-full pl-10 pr-10 py-2 border-2 border-gray-300 rounded-xl focus:outline-none focus:border-blue-500 dark:bg-gray-700 dark:text-white dark:border-gray-600"
                                        [(ngModel)]="searchText"
                                        (keyup)="filterTeamMembers()"
                                    />
                                    <button
                                        class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                                        (click)="resetSearch()"
                                    >
                                        <i
                                            class="fas fa-times text-gray-500 dark:text-gray-400"
                                        ></i>
                                    </button>
                                    <div
                                        class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                                    >
                                        <i
                                            class="fas fa-search text-gray-500 dark:text-gray-400"
                                        ></i>
                                    </div>
                                </div>

                                <div
                                    class="overflow-y-auto max-h-[calc(100vh-12rem)] md:max-h-[calc(100vh-12rem)] pb-60 scroller"
                                >
                                    <div class="snap-y">
                                        @for (
                                            teamMember of filteredTeamMembers;
                                            track teamMember.$id
                                        ) {
                                            <team-member-list
                                                class="snap-start"
                                                [teamMember]="teamMember"
                                                (trackVehicle)="
                                                    startTrackVehicle($event)
                                                "
                                                (zoomToVehicle)="
                                                    zoomToVehicle($event)
                                                "
                                            />
                                        } @empty {
                                            <div
                                                class="flex flex-col flex-1 gap-5 sm:p-2"
                                            >
                                                <div
                                                    class="flex flex-col flex-1 gap-3"
                                                >
                                                    <div
                                                        class="w-3/5 bg-gray-200 animate-pulse h-14 rounded-2xl"
                                                    ></div>
                                                    <div
                                                        class="w-full h-3 bg-gray-200 animate-pulse rounded-2xl"
                                                    ></div>
                                                    <div
                                                        class="w-4/5 h-3 bg-gray-200 animate-pulse rounded-2xl"
                                                    ></div>
                                                    <div
                                                        class="w-full h-3 bg-gray-200 animate-pulse rounded-2xl"
                                                    ></div>
                                                    <div
                                                        class="w-1/5 h-3 bg-gray-200 animate-pulse rounded-2xl"
                                                    ></div>
                                                </div>
                                                <div class="flex gap-3 mt-auto">
                                                    <div
                                                        class="w-20 h-8 bg-gray-200 rounded-full animate-pulse"
                                                    ></div>
                                                    <div
                                                        class="w-20 h-8 bg-gray-200 rounded-full animate-pulse"
                                                    ></div>
                                                    <div
                                                        class="w-20 h-8 ml-auto bg-gray-200 rounded-full animate-pulse"
                                                    ></div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </mat-tab>
                        <mat-tab>
                            <ng-template mat-tab-label>
                                <i class="fa-duotone fa-qrcode"></i>
                            </ng-template>
                            <!-- Asset Body -->
                            <div class="m-2">
                                <div class="relative mb-2">
                                    <input
                                        type="text"
                                        id="search"
                                        name="search"
                                        placeholder="Search..."
                                        class="w-full pl-10 pr-10 py-2 border-2 border-gray-300 rounded-xl focus:outline-none focus:border-blue-500 dark:bg-gray-700 dark:text-white dark:border-gray-600"
                                        [(ngModel)]="searchText"
                                        (keyup)="filterAssets()"
                                    />
                                    <button
                                        class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                                        (click)="resetSearch()"
                                    >
                                        <i
                                            class="fas fa-times text-gray-500 dark:text-gray-400"
                                        ></i>
                                    </button>
                                    <div
                                        class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                                    >
                                        <i
                                            class="fas fa-search text-gray-500 dark:text-gray-400"
                                        ></i>
                                    </div>
                                </div>
                                <div
                                    class="scroll-auto scroll-ps-6 md:scroll-auto h-full max-h-screen overflow-y-auto overscroll-y-contain sm:overscroll-auto"
                                    fuseScrollbar
                                    [fuseScrollbarOptions]="{
                                        wheelPropagation: true
                                    }"
                                >
                                    <div class="snap-y">
                                        @for (
                                            asset of filteredAssets;
                                            track asset.$id
                                        ) {
                                            <p>
                                                <assets-list
                                                    class="snap-start"
                                                    [asset]="asset"
                                                    (zoomToAsset)="
                                                        zoomToAsset($event)
                                                    "
                                                />
                                            </p>
                                        } @empty {
                                            <div
                                                class="flex flex-col flex-1 gap-5 sm:p-2"
                                            >
                                                <div
                                                    class="flex flex-col flex-1 gap-3"
                                                >
                                                    <div
                                                        class="w-3/5 bg-gray-200 animate-pulse h-14 rounded-2xl"
                                                    ></div>
                                                    <div
                                                        class="w-full h-3 bg-gray-200 animate-pulse rounded-2xl"
                                                    ></div>
                                                    <div
                                                        class="w-4/5 h-3 bg-gray-200 animate-pulse rounded-2xl"
                                                    ></div>
                                                    <div
                                                        class="w-full h-3 bg-gray-200 animate-pulse rounded-2xl"
                                                    ></div>
                                                    <div
                                                        class="w-1/5 h-3 bg-gray-200 animate-pulse rounded-2xl"
                                                    ></div>
                                                </div>
                                                <div class="flex gap-3 mt-auto">
                                                    <div
                                                        class="w-20 h-8 bg-gray-200 rounded-full animate-pulse"
                                                    ></div>
                                                    <div
                                                        class="w-20 h-8 bg-gray-200 rounded-full animate-pulse"
                                                    ></div>
                                                    <div
                                                        class="w-20 h-8 ml-auto bg-gray-200 rounded-full animate-pulse"
                                                    ></div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </mat-tab>
                        <mat-tab>
                            <ng-template mat-tab-label>
                                <i class="fa-duotone fa-list-tree"></i>
                            </ng-template>
                            <!-- ScheduledJobs Body -->
                            <div class="m-2">
                                <div class="relative mb-2">
                                    <input
                                        type="text"
                                        id="search"
                                        name="search"
                                        placeholder="Search..."
                                        class="w-full pl-10 pr-10 py-2 border-2 border-gray-300 rounded-xl focus:outline-none focus:border-blue-500 dark:bg-gray-700 dark:text-white dark:border-gray-600"
                                        [(ngModel)]="searchText"
                                        (keyup)="filterScheduledJobs()"
                                    />
                                    <button
                                        class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                                        (click)="resetSearch()"
                                    >
                                        <i
                                            class="fas fa-times text-gray-500 dark:text-gray-400"
                                        ></i>
                                    </button>
                                    <div
                                        class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                                    >
                                        <i
                                            class="fas fa-search text-gray-500 dark:text-gray-400"
                                        ></i>
                                    </div>
                                </div>
                                <div
                                    class="scroll-auto scroll-ps-6 md:scroll-auto h-full max-h-screen overflow-y-auto overscroll-y-contain sm:overscroll-auto"
                                    fuseScrollbar
                                    [fuseScrollbarOptions]="{
                                        wheelPropagation: true
                                    }"
                                >
                                    <div class="snap-y">
                                        @for (
                                            scheduledJobs of filteredScheduledJobs;
                                            track scheduledJobs.$id
                                        ) {
                                            <p>
                                                <pendingJob-list
                                                    class="snap-start"
                                                    [pendingJob]="scheduledJobs"
                                                    (zoomToJob)="
                                                        zoomToJob($event)
                                                    "
                                                />
                                            </p>
                                        } @empty {
                                            <div
                                                class="flex flex-col flex-1 gap-5 sm:p-2"
                                            >
                                                <div
                                                    class="flex flex-col flex-1 gap-3"
                                                >
                                                    <div
                                                        class="w-3/5 bg-gray-200 animate-pulse h-14 rounded-2xl"
                                                    ></div>
                                                    <div
                                                        class="w-full h-3 bg-gray-200 animate-pulse rounded-2xl"
                                                    ></div>
                                                    <div
                                                        class="w-4/5 h-3 bg-gray-200 animate-pulse rounded-2xl"
                                                    ></div>
                                                    <div
                                                        class="w-full h-3 bg-gray-200 animate-pulse rounded-2xl"
                                                    ></div>
                                                    <div
                                                        class="w-1/5 h-3 bg-gray-200 animate-pulse rounded-2xl"
                                                    ></div>
                                                </div>
                                                <div class="flex gap-3 mt-auto">
                                                    <div
                                                        class="w-20 h-8 bg-gray-200 rounded-full animate-pulse"
                                                    ></div>
                                                    <div
                                                        class="w-20 h-8 bg-gray-200 rounded-full animate-pulse"
                                                    ></div>
                                                    <div
                                                        class="w-20 h-8 ml-auto bg-gray-200 rounded-full animate-pulse"
                                                    ></div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </mat-tab>
                    </mat-tab-group>
                </div>
            </fuse-card>
        }
        <div
            class="absolute left-[22rem] right-0 top-1 mt-6 mr-6 z-10 flex flex-col max-h-14 h-full
            {{ !toggleTabList ? 'left-[1rem]' : 'left-[22rem]' }}
            
            "
        >
            <div class="flex flex-row">
                <div class="grow h-14">
                    <div class="search-box">
                        <button
                            mat-mini-fab
                            class="search-btn"
                            (click)="
                                searchInput.value = ''; searchLocation = false
                            "
                        >
                            <mat-icon
                                svgIcon="mat_solid:travel_explore"
                            ></mat-icon>
                        </button>

                        <input
                            #searchInput
                            type="search"
                            class="search-input"
                            [spellcheck]="false"
                            matMapsAutocomplete
                            [country]="'au'"
                            [placeholder]="'Start Looking For Locations!'"
                            (onAutocompleteSelected)="
                                onAutocompleteSelected($event)
                            "
                        />
                    </div>
                </div>
                <div class="grow-0 h-14"></div>
                <div class="grow h-14">
                    <div
                        class="flex flex-row-reverse space-x-3 space-x-reverse text-black"
                    >
                        <!-- <button mat-mini-fab extended aria-label="Map layer">
                        <i class="fa-duotone fa-layer-group"></i>
                    </button>
                    <button
                        mat-mini-fab
                        (click)="mapTraffic = !mapTraffic"
                        [class]="mapTraffic ? 'text-red-500' : ''"
                    >
                        <mat-icon svgIcon="mat_solid:traffic"></mat-icon>
                    </button>

                    <button mat-mini-fab>
                        <i class="fa-duotone fa-list-timeline"></i>
                    </button>

                    <button mat-mini-fab>
                        <i class="fa-duotone fa-qrcode"></i>
                    </button>
                    <button mat-mini-fab (click)="refreshZoom()">
                        <i class="fa-duotone fa-arrows-spin"></i>
                    </button> -->
                        @if (!shouldFitMapToMarkers) {
                            <button
                                (click)="stopTrackingVehicle()"
                                class="bg-orange-500"
                                mat-mini-fab
                                extended
                                aria-label="Stop Tracking"
                            >
                                <i class="fa-duotone fa-car-on"></i>
                            </button>
                            <span
                                class="rounded-full bg-primary-100 p-2 pl-3 pr-3 text-center text-on-primary-100 shadow-lg"
                                >{{ startTrackTeammemberName }}</span
                            >
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div
            class="fixed mr-1 z-50 w-full h-16 max-w-lg -translate-x-1/2 bg-white border border-gray-200 rounded-full bottom-4 left-1/2 dark:bg-gray-700 dark:border-gray-600"
        >
            <div class="grid h-full max-w-lg grid-cols-5 mx-auto">
                <button
                    type="button"
                    class="inline-flex flex-col items-center justify-center px-5 rounded-s-full hover:bg-gray-50 dark:hover:bg-gray-800 group"
                    (click)="refreshZoom()"
                >
                    <i
                        class="fa-duotone fa-solid fa-magnifying-glass-location group-hover:text-blue-600 dark:group-hover:text-blue-500"
                    ></i>

                    <span class="sr-only">Fit to map</span>
                </button>

                <button
                    type="button"
                    (click)="refreshMap()"
                    class="inline-flex flex-col items-center justify-center px-5 hover:bg-gray-50 dark:hover:bg-gray-800 group"
                >
                    <i
                        class="fa-duotone fa-arrows-spin group-hover:text-blue-600 dark:group-hover:text-blue-500"
                    ></i>

                    <span class="sr-only">refresh</span>
                </button>

                <div class="flex items-center justify-center">
                    <button
                        (click)="toggleTabList = !toggleTabList"
                        type="button"
                        class="inline-flex items-center justify-center w-10 h-10 font-medium bg-blue-600 rounded-full hover:bg-blue-700 group focus:ring-4 focus:ring-blue-300 focus:outline-none dark:focus:ring-blue-800"
                    >
                        <i
                            class="fa-duotone fa-list-timeline w-4 h-4 text-white"
                        ></i>
                        <span class="sr-only">List</span>
                    </button>
                </div>

                <button
                    type="button"
                    class="inline-flex flex-col items-center justify-center px-5 hover:bg-gray-50 dark:hover:bg-gray-800 group"
                    (click)="mapTraffic = !mapTraffic"
                >
                    <mat-icon
                        [class]="mapTraffic ? 'text-red-500' : ''"
                        class="group-hover:text-blue-600 dark:group-hover:text-blue-500"
                        svgIcon="mat_solid:traffic"
                    ></mat-icon>
                </button>

                <button
                    type="button"
                    (click)="mapTypeChanged()"
                    class="inline-flex flex-col items-center justify-center px-5 rounded-e-full hover:bg-gray-50 dark:hover:bg-gray-800 group"
                >
                    <i
                        class="fa-duotone fa-layer-group group-hover:text-blue-600 dark:group-hover:text-blue-500"
                    ></i>
                    <span class="sr-only">Map layer</span>
                </button>
            </div>
        </div>
    </div>
</div>

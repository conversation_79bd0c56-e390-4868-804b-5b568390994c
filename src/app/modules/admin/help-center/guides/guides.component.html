<div class="flex min-w-0 flex-auto flex-col">
    <!-- Main -->
    <div class="flex flex-col items-center p-6 sm:p-10">
        <div class="flex w-full max-w-4xl flex-col">
            <div class="-ml-4 sm:mt-8">
                <a mat-button [routerLink]="['../']" [color]="'primary'">
                    <mat-icon
                        [svgIcon]="'heroicons_outline:arrow-long-left'"
                    ></mat-icon>
                    <span class="ml-2">Back to Help Center</span>
                </a>
            </div>
            <div
                class="mt-2 text-4xl font-extrabold leading-tight tracking-tight sm:text-7xl"
            >
                Guides & Resources
            </div>
            <!-- Guides -->
            <div
                class="mt-8 grid grid-flow-row grid-cols-1 gap-y-12 sm:mt-12 sm:grid-cols-2 sm:gap-x-4"
            >
                @for (
                    guideCategory of guideCategories;
                    track trackByFn($index, guideCategory)
                ) {
                    <div class="flex flex-col items-start">
                        <a
                            class="mb-1 flex items-center text-2xl font-semibold"
                            [routerLink]="[guideCategory.slug]"
                        >
                            {{ guideCategory.title }}
                        </a>
                        @for (
                            guide of guideCategory.guides;
                            track trackByFn($index, guide)
                        ) {
                            <a
                                class="mt-3 font-medium text-primary-500 hover:underline"
                                [routerLink]="[guideCategory.slug, guide.slug]"
                            >
                                {{ guide.title }}
                            </a>
                        }
                        @if (
                            guideCategory.totalGuides >
                            guideCategory.visibleGuides
                        ) {
                            <a
                                class="mt-5 flex cursor-pointer items-center rounded-full bg-gray-200 py-0.5 pl-4 pr-3 hover:bg-gray-300 dark:bg-gray-800 dark:hover:bg-gray-700"
                                [routerLink]="guideCategory.slug"
                            >
                                <span class="text-secondary text-sm font-medium"
                                    >View All</span
                                >
                                <mat-icon
                                    class="ml-2 icon-size-5"
                                    [svgIcon]="
                                        'heroicons_mini:arrow-long-right'
                                    "
                                ></mat-icon>
                            </a>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
</div>

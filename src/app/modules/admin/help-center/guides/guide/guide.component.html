<div class="flex min-w-0 flex-auto flex-col">
    <!-- Main -->
    <div class="flex flex-col items-center p-6 sm:p-10">
        <div class="flex w-full max-w-4xl flex-col">
            <div class="-ml-4 sm:mt-8">
                <a mat-button [routerLink]="['../']" [color]="'primary'">
                    <mat-icon
                        [svgIcon]="'heroicons_outline:arrow-long-left'"
                    ></mat-icon>
                    <span class="ml-2">Back to {{ guideCategory.title }}</span>
                </a>
            </div>
            <div
                class="mt-2 text-4xl font-extrabold leading-tight tracking-tight sm:text-7xl"
            >
                {{ guideCategory.guides[0].title }}
            </div>
            <div class="text-secondary mt-1 tracking-tight sm:text-2xl">
                {{ guideCategory.guides[0].subtitle }}
            </div>

            <!-- Guide -->
            <div
                class="prose prose-sm mt-8 max-w-none sm:mt-12"
                [innerHTML]="guideContent"
            ></div>

            <div
                class="mt-10 flex flex-col border-t pt-8 sm:flex-row sm:items-center sm:justify-between"
            >
                <div class="text-secondary text-sm font-medium">
                    Last updated: {{ lastUpdatedAt }}
                </div>
                <div class="mt-2 flex items-center sm:mt-0">
                    <div class="text-secondary font-medium">
                        Was this page helpful?
                    </div>
                    <div class="ml-4">
                        <button mat-icon-button>
                            <mat-icon
                                [svgIcon]="'heroicons_outline:thumb-up'"
                            ></mat-icon>
                        </button>
                        <button mat-icon-button>
                            <mat-icon
                                [svgIcon]="'heroicons_outline:thumb-down'"
                            ></mat-icon>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Next -->
            @if (nextGuidePath != null) {
                <a
                    class="bg-card mt-8 flex items-center justify-between rounded-2xl p-6 shadow transition-shadow duration-150 ease-in-out hover:shadow-lg sm:px-10"
                    [routerLink]="nextGuidePath"
                >
                    <div>
                        <div class="text-secondary">Next</div>
                        <div class="text-lg font-semibold">
                            {{ nextGuideTitle }}
                        </div>
                    </div>
                    <mat-icon
                        class="ml-3"
                        [svgIcon]="'heroicons_outline:arrow-right'"
                    ></mat-icon>
                </a>
            }
        </div>
    </div>
</div>

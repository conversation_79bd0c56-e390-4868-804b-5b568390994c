import { Component, OnD<PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { RouterLink } from '@angular/router';
import { HelpCenterService } from 'app/modules/admin/help-center/help-center.service';
import { GuideCategory } from 'app/modules/admin/help-center/help-center.type';
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'help-center-guides-guide',
    templateUrl: './guide.component.html',
    encapsulation: ViewEncapsulation.None,
    imports: [MatButtonModule, RouterLink, MatIconModule]
})
export class HelpCenterGuidesGuideComponent implements OnInit, OnDestroy {
    guideCategory: GuideCategory;
    private _unsubscribeAll: Subject<any> = new Subject();
    guideContent: SafeHtml;
    nextGuidePath: string = '';
    lastUpdatedAt: string = 'N/A';
    nextGuideTitle: string = '';

    /**
     * Constructor
     */
    constructor(
        private _helpCenterService: HelpCenterService,
        private _domSanitizer: DomSanitizer) { }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        // Get the Guides
        this._helpCenterService.guide$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((guideCategory) => {
                this.guideCategory = guideCategory;
                this.guideContent = this._domSanitizer.bypassSecurityTrustHtml(this.guideCategory.guides[0].content);
                this.nextGuidePath = this.guideCategory.guides[0].nextGuidePath;
                this.lastUpdatedAt = this.guideCategory.guides[0].lastUpdatedAt;
                this.nextGuideTitle = this.guideCategory.guides[0].nextGuideTitle;
            });
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Track by function for ngFor loops
     *
     * @param index
     * @param item
     */
    trackByFn(index: number, item: any): any {
        return item.id || index;
    }
}

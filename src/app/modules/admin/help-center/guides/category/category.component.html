<div class="flex min-w-0 flex-auto flex-col">
    <!-- Main -->
    <div class="flex flex-col items-center p-6 sm:p-10">
        <div class="flex w-full max-w-4xl flex-col">
            <div class="-ml-4 sm:mt-8">
                <a mat-button [routerLink]="['../']" [color]="'primary'">
                    <mat-icon
                        [svgIcon]="'heroicons_outline:arrow-long-left'"
                    ></mat-icon>
                    <span class="ml-2">Back to Guides & Resources</span>
                </a>
            </div>
            <div
                class="mt-2 text-4xl font-extrabold leading-tight tracking-tight sm:text-7xl"
            >
                {{ guideCategory.title }}
            </div>
            <!-- Guides -->
            <div class="mt-8 flex flex-col items-start space-y-2 sm:mt-12">
                @for (
                    guide of guideCategory.guides;
                    track trackByFn($index, guide)
                ) {
                    <a
                        class="font-medium text-primary-500 hover:underline"
                        [routerLink]="[guide.slug]"
                    >
                        {{ guide.title }}
                    </a>
                }
            </div>
        </div>
    </div>
</div>

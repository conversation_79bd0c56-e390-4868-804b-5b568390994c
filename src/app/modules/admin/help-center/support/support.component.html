<div class="flex min-w-0 flex-auto flex-col">
    <!-- Main -->
    <div class="flex flex-auto flex-col items-center p-6 sm:p-10">
        <div class="flex w-full max-w-4xl flex-col">
            <div class="-ml-4 sm:mt-8">
                <a mat-button [routerLink]="['../']" [color]="'primary'">
                    <mat-icon
                        [svgIcon]="'heroicons_outline:arrow-long-left'"
                    ></mat-icon>
                    <span class="ml-2">Back to Help Center</span>
                </a>
            </div>
            <div
                class="mt-2 text-4xl font-extrabold leading-tight tracking-tight sm:text-7xl"
            >
                Contact support
            </div>
            <!-- Form -->
            <div
                class="bg-card mt-8 rounded-2xl p-6 pb-7 shadow sm:mt-12 sm:p-10 sm:pb-7"
            >
                <!-- Alert -->
                @if (alert) {
                    <fuse-alert
                        class="mb-8"
                        [type]="alert.type"
                        [showIcon]="false"
                    >
                        {{ alert.message }}
                    </fuse-alert>
                }
                <form
                    class="space-y-3"
                    [formGroup]="supportForm"
                    #supportNgForm="ngForm"
                >
                    <div class="mb-6">
                        <div class="text-2xl font-bold tracking-tight">
                            Submit your request
                        </div>
                        <div class="text-secondary">
                            Your request will be processed and our support staff
                            will get back to you in 24 hours.
                        </div>
                    </div>
                    <!-- Name -->
                    <mat-form-field class="w-full">
                        <input
                            matInput
                            [formControlName]="'name'"
                            [required]="true"
                        />
                        <mat-label>Name</mat-label>
                        @if (supportForm.get('name').hasError('required')) {
                            <mat-error> Required </mat-error>
                        }
                    </mat-form-field>
                    <!-- Email -->
                    <mat-form-field class="w-full">
                        <input
                            type="email"
                            matInput
                            [formControlName]="'email'"
                            [required]="true"
                        />
                        <mat-label>Email</mat-label>
                        @if (supportForm.get('email').hasError('required')) {
                            <mat-error> Required </mat-error>
                        }
                        @if (supportForm.get('email').hasError('email')) {
                            <mat-error> Enter a valid email address </mat-error>
                        }
                    </mat-form-field>
                    <!-- Subject -->
                    <mat-form-field class="w-full">
                        <input
                            matInput
                            [formControlName]="'subject'"
                            [required]="true"
                        />
                        <mat-label>Subject</mat-label>
                        @if (supportForm.get('subject').hasError('required')) {
                            <mat-error> Required </mat-error>
                        }
                    </mat-form-field>
                    <!-- Message -->
                    <mat-form-field class="w-full">
                        <textarea
                            matInput
                            [formControlName]="'message'"
                            [required]="true"
                            [rows]="5"
                            cdkTextareaAutosize
                        ></textarea>
                        <mat-label>Message</mat-label>
                        @if (supportForm.get('message').hasError('required')) {
                            <mat-error> Required </mat-error>
                        }
                    </mat-form-field>
                    <!-- Actions -->
                    <div class="flex items-center justify-end">
                        <button
                            mat-button
                            [color]="'accent'"
                            [disabled]="
                                supportForm.pristine || supportForm.untouched
                            "
                            (click)="clearForm()"
                        >
                            Clear
                        </button>
                        <button
                            class="ml-2"
                            mat-flat-button
                            [color]="'primary'"
                            [disabled]="
                                supportForm.pristine || supportForm.invalid
                            "
                            (click)="sendForm()"
                        >
                            Submit
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

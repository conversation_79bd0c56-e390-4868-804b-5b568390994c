import { DatePipe, NgClass } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, effect, input, OnInit, viewChild, ViewChild, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { Customer } from 'app/core/databaseModels/customers/contacts.types';
import { ScheduledJob } from 'app/core/databaseModels/scheduledJobs/scheduledJob.types';
import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';
import { TeamMembersService } from 'app/core/databaseModels/teammembers/teammembers.service';
import { ExportTableService } from 'app/services/export-table.service';

@Component({
    selector: 'report-scheduled-jobs',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        DatePipe,
        NgClass,
        MatIconModule,
        MatTableModule,
        MatPaginatorModule,
        MatMenuModule,
        MatFormFieldModule,
        MatInputModule,
        MatButtonModule,
        MatProgressSpinnerModule,
        MatTooltipModule,
    ],
    templateUrl: './report-scheduled-jobs.component.html',
    styleUrl: './report-scheduled-jobs.component.scss'
})
export class ReportScheduledJobsComponent implements OnInit, AfterViewInit {

    scheduledJobList = input.required<ScheduledJob[]>();
    teammembers = input.required<(TeamMember[])>();
    customers = input.required<(Customer[])>();
    startDate = input.required<string>();
    endDate = input.required<string>();

    displayedColumns: string[] = ['customerId', 'avatar', 'jobTitle', 'dueDate', 'actions'];
    dataSource: MatTableDataSource<ScheduledJob> = new MatTableDataSource<ScheduledJob>();

    paginator = viewChild.required(MatPaginator)
    // Status Lookup objects
    dispatchStatusLookup = {
        '': { color: 'gray', icon: 'fa-solid fa-mobile-signal-out' },
        'null': { color: 'gray', icon: 'fa-solid fa-mobile-signal-out' },
        'dispatched': { color: 'blue', icon: 'fa-solid fa-check' },
        'received': { color: 'blue', icon: 'fa-solid fa-check-double' },
        'onMyWay': { color: 'yellow', icon: 'fa-solid fa-arrows-turn-right' },
        'finished': { color: 'green', icon: 'fa-solid fa-circle-check' },
        'started': { color: '#8FBC8F', icon: 'fa-solid fa-box-circle-check' },
    };

    jobStatusLookup = {
        'pending': { color: 'gray', icon: 'fa-solid fa-clock' },
        'added': { color: 'gray', icon: 'fa-solid fa-circle-dot' },
        'approved': { color: 'blue' },
        'started': { color: '#8FBC8F' },
        'onMyWay': { color: 'yellow' },
        'finished': { color: 'green' }
    };

    constructor(
        private _teamMembersService: TeamMembersService,
        private _router: Router,
        private exportTableService: ExportTableService,

    ) { }
    ngOnInit(): void {
        // console.log(this.scheduledJobList());
        // console.log(this.startDate());
        // console.log(this.endDate());


    }

    ngAfterViewInit() {
        this.dataSource.paginator = this.paginator();
        this.dataSource.data = this.scheduledJobList().sort((a, b) => {
            return new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime();
        });
    }

    getApprovedStatus(approved: string): number {
        return this.scheduledJobList().filter(item => item.jobStatus === approved).length;
    }


    applyFilter(event: Event, filter?: string) {

        const filterValue = filter ? filter : (event.target as HTMLInputElement).value;
        this.dataSource.filter = filterValue.trim().toLowerCase();

        if (this.dataSource.paginator) {
            this.dataSource.paginator.firstPage();
        }
    }

    getTeamMemberInfo(id: string): { name: string, avatar: string | null, fullName: string } {
        const teamMember = this.teammembers().find(item => item.$id === id);

        if (!teamMember) {
            return { name: '', avatar: null, fullName: '' }; // If no team member is found
        }

        // If avatarImageId exists, use the preview URL
        const avatar = teamMember.avatarImageId
            ? this._teamMembersService.getFilePreview(teamMember.avatarImageId, 100, 100)
            : null;

        // Generate initials if avatarImageId or avatar is null
        const name = teamMember.avatar || teamMember.avatarImageId
            ? teamMember.avatar
            : this.generateInitials(teamMember.name);

        return { name: name, avatar: avatar, fullName: teamMember.name };
    }
    // Helper function to generate initials from a name
    private generateInitials(fullName: string): string {
        const nameParts = fullName.split(' ');
        const initials = nameParts.length > 1
            ? `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}` // First letter of first and last part
            : nameParts[0][0]; // First letter if only one part of name
        return initials.toUpperCase();
    }

    getEventAttributes(item) {
        let eventColor = 'gray';
        let icon = 'fa-solid fa-clock';

        const dispatchAttributes = this.dispatchStatusLookup[item.dispatchStatus || 'null'];
        const jobAttributes = this.jobStatusLookup[item.jobStatus];

        if (dispatchAttributes) {
            eventColor = dispatchAttributes.color;
            icon = dispatchAttributes.icon;
        }

        if (jobAttributes) {
            eventColor = jobAttributes.color;
            if (jobAttributes.icon) {
                icon = jobAttributes.icon;
            }
        }

        return { eventColor, icon };
    }



    getCustomerInfo(id: string): { name: string, phone: string | null, email: string | null } {
        const customer = this.customers().find(item => item.$id === id);

        if (!customer) {
            return { name: '', phone: null, email: null }; // If no customer is found
        }

        // Parse the first phone number
        let phone = null;
        if (customer.phoneNumbers && customer.phoneNumbers.length > 0) {
            try {
                // Assuming phoneNumbers is an array of JSON strings
                const phoneObj = typeof customer.phoneNumbers[0] === 'string'
                    ? JSON.parse(customer.phoneNumbers[0])
                    : customer.phoneNumbers[0]; // If already parsed as an object
                phone = phoneObj.phoneNumber || null;
            } catch (error) {
                console.error('Error parsing phone number JSON:', error);
            }
        }

        // Parse the first email
        let email = null;
        if (customer.emails && customer.emails.length > 0) {
            try {
                // Assuming emails is an array of JSON strings
                const emailObj = typeof customer.emails[0] === 'string'
                    ? JSON.parse(customer.emails[0])
                    : customer.emails[0]; // If already parsed as an object
                email = emailObj.email || null;
            } catch (error) {
                console.error('Error parsing email JSON:', error);
            }
        }

        return { name: customer.name, phone, email };
    }





    loadSelectedSchedule(row) {
        this._router.navigate(['/scheduler'], { queryParams: { scheduleID: row.$id } });
    }

    exportMaterialTable(format: 'pdf' | 'excel') {

        const _data = this.scheduledJobList().map(job => {
            let _customer = this.getCustomerInfo(job.customerId);
            let _teamMember = this.getTeamMemberInfo(job.teamMemberID);
            return {
                'Team Member': _teamMember.fullName, // Replace with actual team member name if needed
                'Job Title': job.jobTitle,
                'Job Status': job.jobStatus,
                'Job Address': job.jobAddress,
                'Due Date': job.dueDate.toString().split('T')[0] + ' ' + job.startTime,
                'Customer': _customer.name,
                'Customer Contact': _customer.phone,
                'Customer Email': _customer.email, // Replace with actual customer name if needed

            };
        });

        this.exportTableService.exportMaterialTable(_data, format, 'Scheduled Jobs Report-' + new Date().toISOString(), this.startDate().replace('T', ' '), this.endDate().replace('T', ' '), 'Scheduled Jobs Report');
    }

}

import { CommonModule, DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, input, OnInit, viewChild, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { Router } from '@angular/router';
import { AssetsLocation } from 'app/core/databaseModels/assets/assets.types';
import { PipesModule } from 'app/pipes/pipes.module';
import { ExportTableService } from 'app/services/export-table.service';

@Component({
    selector: 'report-assets-locations',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        CommonModule,
        PipesModule,
        DatePipe,
        MatIconModule,
        MatTableModule,
        MatPaginatorModule,
        MatMenuModule,
        MatFormFieldModule,
        MatInputModule,
        MatButtonModule,
        MatProgressSpinnerModule,
    ],
    templateUrl: './report-assets-locations.component.html',
    styleUrl: './report-assets-locations.component.scss'
})
export class ReportAssetsLocationsComponent implements OnInit, AfterViewInit {
    assetsLocationList = input.required<AssetsLocation[]>();
    startDate = input.required<string>();
    endDate = input.required<string>();


    displayedColumns: string[] = ['avatar', 'assetName', 'locationDate', 'status', 'address', 'actions'];
    dataSource: MatTableDataSource<AssetsLocation> = new MatTableDataSource<AssetsLocation>();

    paginator = viewChild.required(MatPaginator);

    constructor(
        private _router: Router,
        private exportTableService: ExportTableService,
    ) {


    }

    ngOnInit(): void {
        console.log(this.assetsLocationList());
        console.log(this.startDate());
        console.log(this.endDate());


    }

    ngAfterViewInit() {
        this.dataSource.paginator = this.paginator();
        this.dataSource.data = this.assetsLocationList().sort((a, b) => {
            return new Date(b.locationDate).getTime() - new Date(a.locationDate).getTime();
        });
    }

    applyFilter(event: Event, filter?: string) {

        const filterValue = filter ? filter : (event.target as HTMLInputElement).value;
        this.dataSource.filter = filterValue.trim().toLowerCase();

        if (this.dataSource.paginator) {
            this.dataSource.paginator.firstPage();
        }
    }
    loadSelectedSchedule(row) {
        this._router.navigate(['/scheduler'], { queryParams: { scheduleID: row.relatedJobId } });
    }

    exportMaterialTable(format: 'pdf' | 'excel') {

        const _data = this.assetsLocationList().map(assets => {


            return {

                'Asset Name': assets.assetName,
                'Asset Status': assets.status,
                'Asset Address': assets.address,
                'Location Date': assets.locationDate.toString().split('T')[0],
                // Replace with actual customer name if needed

            };
        });

        this.exportTableService.exportMaterialTable(_data, format, 'Assets Locations Report-' + new Date().toISOString(), this.startDate().replace('T', ' '), this.endDate().replace('T', ' '), `Assets Locations`);
    }
}

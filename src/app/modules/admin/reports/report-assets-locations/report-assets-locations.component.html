<div class="flex flex-col flex-auto min-w-0 h-full">
    <div class="p-10">
        <div class="text-2xl font-semibold leading-8 tracking-tight">
            Assets Locations History
        </div>
        <div class="text-secondary font-medium tracking-tigh">
            From: {{ startDate() }} - To: {{ endDate() }}
        </div>
    </div>
    <div class="flex flex-col flex-auto bg-card shadow rounded-2xl">
        <div
            class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b"
        >
            <!-- Actions -->
            <div class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4">
                <!-- Search -->
                <mat-form-field
                    class="fuse-mat-dense fuse-mat-rounded min-w-64"
                    [subscriptSizing]="'dynamic'"
                >
                    <mat-icon
                        class="icon-size-5"
                        matPrefix
                        [svgIcon]="'heroicons_solid:magnifying-glass'"
                    ></mat-icon>
                    <input
                        matInput
                        (keyup)="applyFilter($event)"
                        [autocomplete]="'off'"
                        [placeholder]="'Search ...'"
                        #input
                    />
                </mat-form-field>
                <div class="ml-4">
                    <button mat-icon-button [matMenuTriggerFor]="moreMenu">
                        <i
                            class="ml-3 icon-size-5 fa-duotone fa-file-export"
                        ></i>
                    </button>
                    <mat-menu #moreMenu="matMenu">
                        <button
                            mat-menu-item
                            (click)="exportMaterialTable('pdf')"
                        >
                            <i class="fa-duotone fa-file-pdf"></i>
                            <span class="ml-3">PDF</span>
                        </button>
                        <button
                            mat-menu-item
                            (click)="exportMaterialTable('excel')"
                        >
                            <i class="fa-duotone fa-file-excel"></i>
                            <span class="ml-3">EXCEL</span>
                        </button>
                    </mat-menu>
                </div>
            </div>
        </div>
        <div class="overflow-x-auto mx-6">
            <div class="w-full bg-transparent">
                <table mat-table [dataSource]="dataSource" matSort>
                    <!-- Avatar Column -->
                    <ng-container matColumnDef="avatar">
                        <th mat-header-cell *matHeaderCellDef>
                            <i class="fa-duotone fa-box"></i>
                        </th>
                        <td mat-cell *matCellDef="let row">
                            <div
                                class="relative flex flex-0 items-center justify-center w-10 h-10 rounded-full"
                            >
                                <!-- Team Member Dispatch Status -->

                                <!-- Team Member Avatar Status -->
                                <div
                                    class="w-15 rounded-full overflow-hidden"
                                ></div>
                            </div>
                        </td>
                    </ng-container>
                    <!-- Progress Column -->
                    <ng-container matColumnDef="assetName">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>
                            Asset Name
                        </th>
                        <td mat-cell *matCellDef="let row">
                            {{ row.assetName }}
                        </td>
                    </ng-container>
                    <!-- Status Column -->
                    <ng-container matColumnDef="status">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>
                            Status
                        </th>
                        <td mat-cell *matCellDef="let row">
                            <div
                                class="rounded-full p-2 font-semibold text-center text-sm
                               {{
                                    row.status === 'Pickup'
                                        ? 'bg-green-500 text-white'
                                        : 'bg-red-500 text-white'
                                }}
                                "
                            >
                                <i
                                    class="fa-duotone {{
                                        row.status === 'Pickup'
                                            ? 'fa-up-from-line'
                                            : 'fa-down-to-line'
                                    }}"
                                ></i>
                                {{
                                    row.status === "Pickup"
                                        ? "Picked Up"
                                        : "Dropped"
                                }}
                            </div>
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="address">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>
                            address
                        </th>
                        <td mat-cell *matCellDef="let row">
                            {{ row.address }}
                        </td>
                    </ng-container>

                    <!-- locationDate Column -->
                    <ng-container matColumnDef="locationDate">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header>
                            Location Date
                        </th>
                        <td mat-cell *matCellDef="let row">
                            {{ row.locationDate | date: "EEE, dd MMM yyyy" }}
                        </td>
                    </ng-container>

                    <!-- Actions Column -->
                    <ng-container matColumnDef="actions">
                        <th mat-header-cell *matHeaderCellDef>Related Jobs</th>
                        <td mat-cell *matCellDef="let row">
                            <button
                                mat-icon-button
                                (click)="loadSelectedSchedule(row)"
                            >
                                <i
                                    class="icon-size-5 fa-duotone fa-calendar-lines-pen"
                                ></i>
                            </button>
                        </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr
                        mat-row
                        *matRowDef="let row; columns: displayedColumns"
                    ></tr>

                    <!-- Row shown when there is no matching data.
                    @if (isLoading) {
                        <div class="flex justify-center p-10">
                            <mat-spinner class="spinnr" [diameter]="24">
                            </mat-spinner>
                            <div class="ml-5 text-center">Loading...</div>
                        </div>
                    } @else {
                        <tr class="mat-row" *matNoDataRow>
                            <td class="mat-cell" colspan="4">
                                No data matching the filter "{{ input.value }}"
                            </td>
                        </tr>
                    } -->
                </table>

                <mat-paginator
                    [pageSize]="20"
                    [pageSizeOptions]="[5, 10, 20, 50, 100]"
                    aria-label="Select page of users"
                ></mat-paginator>
            </div>
        </div>
    </div>
</div>

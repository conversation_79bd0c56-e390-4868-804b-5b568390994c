import { ChangeDetectionStrategy, ChangeDetectorRef, Component, effect, On<PERSON><PERSON>roy, OnInit, viewChild, signal, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatNativeDateModule, MatOptionModule } from '@angular/material/core';
import { DateRange, DefaultMatCalendarRangeStrategy, MAT_DATE_RANGE_SELECTION_STRATEGY, MatCalendar, MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FuseMediaWatcherService } from '@fuse/services/media-watcher';
import { FuseVerticalNavigationComponent } from '@fuse/components/navigation';
import { map, Observable, startWith, Subject, switchMap, takeUntil, tap } from 'rxjs';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { AnimationOptions, LottieComponent } from 'ngx-lottie';
import { AnimationItem } from 'lottie-web';

import { ReportsService } from '../../../core/databaseModels/reports/reports.service';

//Import reports Components
import { ReportScheduledJobsComponent } from "./report-scheduled-jobs/report-scheduled-jobs.component";
import { ReportCustomerJobsComponent } from './report-customer-jobs/report-customer-jobs.component';
import { ReportDistanceTraveledComponent } from './report-distance-traveled/report-distance-traveled.component';
import { ReportExpensesComponent } from './report-expenses/report-expenses.component';
import { ReportReplayTeammembersComponent } from './report-replay-teammembers/report-replay-teammembers.component';
import { ReportCostAnalysisComponent } from './report-cost-analysis/report-cost-analysis.component';
import { ReportTeammemberShiftsComponent } from './report-teammember-shifts/report-teammember-shifts.component';
import { ReportAssetsLocationsComponent } from './report-assets-locations/report-assets-locations.component';

import { Customer } from 'app/core/databaseModels/customers/contacts.types';
import { CustomerService } from 'app/core/databaseModels/customers/customers.service';
import { CustomerSelectComponent } from 'app/modules/widgets/customer-select/customer-select.component';
import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';
import { TeamMembersService } from 'app/core/databaseModels/teammembers/teammembers.service';
import { TeamMemberSelectComponent } from 'app/modules/widgets/team-member-select/team-member-select.component';

@Component({
    selector: 'app-reports',
    templateUrl: './reports.component.html',
    styleUrls: ['./reports.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [
        {
            provide: MAT_DATE_RANGE_SELECTION_STRATEGY,
            useClass: DefaultMatCalendarRangeStrategy,
        }
    ],
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatDatepickerModule,
        MatNativeDateModule,
        MatInputModule,
        MatSidenavModule,
        MatButtonModule,
        MatIconModule,
        MatButtonToggleModule,
        MatSelectModule,
        MatOptionModule,
        MatChipsModule,
        MatProgressSpinnerModule,
        LottieComponent,
        ReportScheduledJobsComponent,
        ReportCustomerJobsComponent,
        ReportDistanceTraveledComponent,
        ReportExpensesComponent,
        ReportReplayTeammembersComponent,
        ReportCostAnalysisComponent,
        ReportTeammemberShiftsComponent,
        ReportAssetsLocationsComponent,
        CustomerSelectComponent,
        TeamMemberSelectComponent,
    ]
})
export class ReportsComponent implements OnInit, OnDestroy {
    drawerMode: 'over' | 'side' = 'side';
    drawerOpened: boolean = true;
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    reportCalendar = viewChild.required(MatCalendar<Date>)
    selectedOrganisationId = this._reportsService.organisationID();

    isLoading = signal<boolean>(false);  // Signal for loading state
    reportResultReady = this._reportsService.dataAvailableSignal;  // Signal for result state
    errorMessage = signal<string | null>(null);  // Signal for error message

    customers: Customer[];
    teammembers: TeamMember[];



    selectedCustomer = signal<Customer | null>(null);
    selectedTeammember = signal<TeamMember | null>(null);

    // Signals for data inputs
    scheduledJobsData = this._reportsService.reportScheduledJob;  // Signal for scheduled jobs
    teamMemberShiftsData = this._reportsService.teamMemberShifts;  // Signal for team member shifts
    teamMemberJobsData = this._reportsService.teamMemberJobs;  // Signal for team member replay
    assetsLocationsData = this._reportsService.assetsLocations;  // Signal for assets locations
    generateReportDialog;

    selectedReportType: string | null = null;



    calenderSelectedDate: DateRange<Date> | undefined;





    options: AnimationOptions = {
        path: '/lottie/report.json',
        autoplay: true,
        loop: true,

    };

    reportType: any[] = [
        { label: 'Scheduled Jobs', value: 'scheduled_jobs', icon: 'fa-calendar-day' },
        { label: 'Customer Jobs', value: 'customer_jobs', icon: 'fa-building' },
        { label: 'Team Member Shifts', value: 'teammember_shifts', icon: 'fa-people-group' },
        { label: 'Replay Team Members', value: 'replay', icon: 'fa-reply-clock' },
        { label: 'Assets Locations', value: 'assets_locations', icon: 'fa-map-location-dot' },
        // { label: 'Distance Traveled', value: 'distance_traveled', icon: 'fa-location-dot' },
        // { label: 'Cost Analysis', value: 'cost_analysis', icon: 'fa-calculator' },
        // { label: 'Expenses', value: 'expenses', icon: 'fa-file-invoice' },
    ];

    constructor(
        private _fuseMediaWatcherService: FuseMediaWatcherService,
        private _fuseConfirmationService: FuseConfirmationService,
        private _reportsService: ReportsService,
        private _customerService: CustomerService,
        private _teamMembersService: TeamMembersService,
        private _changeDetectorRef: ChangeDetectorRef,

    ) {
        this.selectedOrganisationId = _reportsService.organisationID();
        //console.log(this.selectedOrganisationId);
        // Use `effect()` to close the dialog when the data is ready or failed
        effect(() => {
            if (this.reportResultReady()) {
                this.isLoading.set(false);
                console.log('dialog closed');
                this.generateReportDialog.close();
            }
        }, {
            allowSignalWrites: true,
        });

    }

    ngOnInit(): void {
        // Subscribe to media changes
        this._fuseMediaWatcherService.onMediaChange$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe(({ matchingAliases }) => {
                // Set the drawerMode and drawerOpened
                if (matchingAliases.includes('lg')) {
                    this.drawerMode = 'side';
                    this.drawerOpened = true;
                } else {
                    this.drawerMode = 'over';
                    this.drawerOpened = false;
                }
            });


        // Load Customers
        //TODO: Make better performance not Load All customers, hint use pagination ...
        this._customerService.getCustomers().pipe(
            takeUntil(this._unsubscribeAll),

        ).subscribe(customers => {
            this.customers = customers;
            // console.log(customers);
            // Mark for check
            this._changeDetectorRef.markForCheck();
        }

        );

        //Load Team Members
        this._teamMembersService.getTeamMembers().pipe(
            takeUntil(this._unsubscribeAll),

        ).subscribe(teammembers => {
            this.teammembers = teammembers;
            //  console.log(teammembers);
            // Mark for check
            this._changeDetectorRef.markForCheck();
        });

        // set calender selected date
        this.selectTabCalendarPreset({ start: new Date(), end: new Date() });


    }

    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }



    onCustomerSelected(customer: Customer): void {
        this.selectedCustomer.set(customer);  // Set the selected customer
        console.log('Selected Customer:', this.selectedCustomer());
    }

    onTeamMemberSelected(teamMember: TeamMember): void {
        this.selectedTeammember.set(teamMember);

        console.log('Selected Team Member:', teamMember);

    }

    generateReport(): void {
        this.reportResultReady.set(false);
        this.isLoading.set(true);

        this.generateReportDialog = this._fuseConfirmationService.open({
            title: "Generating...",
            message: "Please wait while we generate your report.",
            icon: { show: true, name: "heroicons_outline:presentation-chart-bar", color: "success" },
            actions: { confirm: { show: false }, cancel: { show: false } },
            dismissible: false,
        });

        console.log('Report Type:', this.selectedReportType);
        console.log('Start Date:', this.calenderSelectedDate.start);
        console.log('End Date:', this.calenderSelectedDate.end);

        if (!this.selectedReportType || !this.calenderSelectedDate.start || !this.calenderSelectedDate.end) {
            this.isLoading.set(false);
            this.reportResultReady.set(true);
            this.generateReportDialog.close();
            return;

        }

        switch (this.selectedReportType) {
            case 'scheduled_jobs':
                this.scheduledJobs();
                break;
            case 'customer_jobs':
                this.customerJobs();
                break;
            case 'teammember_shifts':
                this.teammemberShifts();
                break;
            case 'replay':
                this.replayTeammembers();
                break;
            case 'assets_locations':
                this.assetsLocations();
                break;
            case 'distance_traveled':
                this.distanceTraveled();
                break;
            case 'cost_analysis':
                this.costAnalysis();
                break;
            case 'expenses':
                this.expenses();
                break;

            default:
                this.isLoading.set(false);
                this.generateReportDialog.close();
                break;


        }

    }



    scheduledJobs(): void {
        // Start fetching the scheduled jobs
        this._reportsService.getScheduledJobs(this.calenderSelectedDate.start, this.calenderSelectedDate.end);

    }
    customerJobs(): void {
        // Start fetching the scheduled jobs
        if (this.selectedCustomer() && this.selectedCustomer().$id) {
            this._reportsService.getScheduledJobs(this.calenderSelectedDate.start, this.calenderSelectedDate.end, this.selectedCustomer().$id);
        }
    }
    teammemberShifts(): void {
        if (this.selectedTeammember() && this.selectedTeammember().$id) {
            this._reportsService.getTeamMemberShifts(this.calenderSelectedDate.start, this.calenderSelectedDate.end, this.selectedTeammember().$id);
        }
    }
    replayTeammembers(): void {
        if (this.selectedTeammember() && this.selectedTeammember().$id) {
            this._reportsService.getTeamMemberShifts(this.calenderSelectedDate.start, this.calenderSelectedDate.end, this.selectedTeammember().$id);
        }
    }
    assetsLocations(): void {
        this._reportsService.getAssetsLocations(this.calenderSelectedDate.start, this.calenderSelectedDate.end);
    }
    distanceTraveled(): void { } // TODO: Get distance traveled
    costAnalysis(): void { } // TODO: Get cost analysis
    expenses(): void { } // TODO: Get expenses


    animationCreated(animationItem: AnimationItem): void {
        //   console.log(animationItem);
    }


    rangeChanged(selectedDate: Date) {

        if (
            this.calenderSelectedDate &&
            this.calenderSelectedDate.start &&
            selectedDate > this.calenderSelectedDate.start &&
            !this.calenderSelectedDate.end
        ) {
            this.calenderSelectedDate = new DateRange(
                this.calenderSelectedDate.start,
                selectedDate
            );
        } else {
            this.calenderSelectedDate = new DateRange(selectedDate, null);
        }
        console.log(this.calenderSelectedDate.start, this.calenderSelectedDate.end)

    }
    filterByRangeDate(selectedItem: string) {
        const today = new Date();
        const startOfThisWeek = new Date(today.setDate(today.getDate() - today.getDay()));
        const startOfThisMonth = new Date(today.getFullYear(), today.getMonth(), 1);

        switch (selectedItem) {
            case 'today':
                this.selectTabCalendarPreset({ start: new Date(), end: new Date() });
                break;
            case 'yesterday':
                const yesterday = new Date();
                yesterday.setDate(yesterday.getDate() - 1);
                this.selectTabCalendarPreset({ start: yesterday, end: yesterday });
                break;
            case 'tomorrow':
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                this.selectTabCalendarPreset({ start: tomorrow, end: tomorrow });
                break;
            case 'thisweek':
                const endOfThisWeek = new Date(today.setDate(today.getDate() - today.getDay() + 6));
                this.selectTabCalendarPreset({ start: startOfThisWeek, end: endOfThisWeek });
                break;
            case 'lastweek':
                const startOfLastWeek = new Date(startOfThisWeek);
                startOfLastWeek.setDate(startOfLastWeek.getDate() - 7);
                const endOfLastWeek = new Date(startOfLastWeek);
                endOfLastWeek.setDate(endOfLastWeek.getDate() + 6);
                this.selectTabCalendarPreset({ start: startOfLastWeek, end: endOfLastWeek });
                break;
            case 'nextweek':
                const startOfNextWeek = new Date(startOfThisWeek);
                startOfNextWeek.setDate(startOfNextWeek.getDate() + 7);
                const endOfNextWeek = new Date(startOfNextWeek);
                endOfNextWeek.setDate(endOfNextWeek.getDate() + 6);
                this.selectTabCalendarPreset({ start: startOfNextWeek, end: endOfNextWeek });
                break;
            case 'thismonth':
                const endOfThisMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                this.selectTabCalendarPreset({ start: startOfThisMonth, end: endOfThisMonth });
                break;
            case 'lastmonth':
                const startOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
                this.selectTabCalendarPreset({ start: startOfLastMonth, end: endOfLastMonth });
                break;
            default:
                this.selectTabCalendarPreset({ start: new Date(), end: new Date() });
                break;
        }
    }

    selectTabCalendarPreset(presetDateRange: { start: Date; end: Date }) {
        this.calenderSelectedDate = new DateRange<Date>(presetDateRange.start, presetDateRange.end);

        if (presetDateRange.start && this.reportCalendar()) {
            this.reportCalendar()._goToDateInView(presetDateRange.start, 'month');
        }

        this._changeDetectorRef.markForCheck();
        this._changeDetectorRef.detectChanges();
    }

}

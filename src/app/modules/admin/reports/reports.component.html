<div class="absolute inset-0 flex flex-col min-w-0 overflow-hidden">
    <mat-drawer-container class="flex-auto h-full">
        <!-- Drawer -->
        <mat-drawer
            class="w-72 dark:bg-gray-900"
            [autoFocus]="false"
            [mode]="drawerMode"
            [opened]="drawerOpened"
            #matDrawer
        >
            <!-- Report sidebar options -->
            <div class="">
                <div class="mx-6 mt-10 text-3xl font-bold tracking-tighter">
                    Report Options
                </div>
                <div class="p-4">
                    <mat-form-field class="w-full">
                        <mat-label>Select Report Type</mat-label>
                        <mat-select
                            [(ngModel)]="selectedReportType"
                            [placeholder]="'Report Type'"
                            required
                        >
                            <!-- Loop through report types -->
                            @for (report of reportType; track report) {
                                <mat-option [value]="report.value">
                                    <i
                                        class="fa-duotone fa-solid mr-5"
                                        [ngClass]="report.icon"
                                    ></i>
                                    {{ report.label }}
                                </mat-option>
                            }
                        </mat-select>
                        <i
                            matPrefix
                            class="mr-2 fa-duotone fa-solid fa-square-sliders"
                        ></i>
                    </mat-form-field>
                </div>
                @if (selectedReportType == "customer_jobs") {
                    <div class="p-4">
                        <customer-select
                            [customers]="customers"
                            (customerSelected)="onCustomerSelected($event)"
                        />
                    </div>
                }

                @if (
                    selectedReportType == "teammember_shifts" ||
                    selectedReportType == "replay"
                ) {
                    <div class="p-4">
                        <team-member-select
                            [teamMembers]="teammembers"
                            (teamMemberSelected)="onTeamMemberSelected($event)"
                        ></team-member-select>
                    </div>
                }

                <div class="p-4">
                    <mat-calendar
                        #reportCalendar
                        [selected]="calenderSelectedDate"
                        (selectedChange)="rangeChanged($event)"
                    ></mat-calendar>
                    <div class="text-sm text-center">
                        @if (
                            calenderSelectedDate && calenderSelectedDate.start
                        ) {
                            {{
                                calenderSelectedDate.start
                                    | date: "EEE, dd MMM yyyy"
                            }}
                            @if (calenderSelectedDate.end) {
                                To:
                                {{
                                    calenderSelectedDate.end
                                        | date: "EEE, dd MMM yyyy"
                                }}
                            }
                        }
                    </div>
                </div>
                <div class="p-4">
                    <mat-form-field
                        class="w-full"
                        [subscriptSizing]="'dynamic'"
                    >
                        <mat-label class="text-sm ml-5">Time frame</mat-label>
                        <mat-select
                            class="mt-2 w-200"
                            [value]="'today'"
                            (selectionChange)="filterByRangeDate($event.value)"
                        >
                            <mat-option value="today">Today</mat-option>
                            <mat-option value="yesterday">Yesterday</mat-option>
                            <mat-option value="tomorrow">Tomorrow</mat-option>
                            <mat-option value="thisweek">This week</mat-option>
                            <mat-option value="lastweek">Last week</mat-option>
                            <mat-option value="nextweek">Next week</mat-option>
                            <mat-option value="thismonth"
                                >This month</mat-option
                            >
                            <mat-option value="lastmonth"
                                >Last month</mat-option
                            >
                        </mat-select>
                    </mat-form-field>
                </div>

                <div class="p-4">
                    <button
                        class="w-full h-10 rounded-lg text-gray-100 dark:text-gray-300"
                        [disabled]="
                            !selectedReportType ||
                            !calenderSelectedDate.start ||
                            !calenderSelectedDate.end
                        "
                        [ngClass]="{
                            'bg-slate-900 dark:bg-slate-700':
                                selectedReportType &&
                                calenderSelectedDate.start &&
                                calenderSelectedDate.end,
                            'bg-gray-300 dark:bg-gray-600 cursor-not-allowed':
                                !selectedReportType ||
                                !calenderSelectedDate.start ||
                                !calenderSelectedDate.end
                        }"
                        (click)="generateReport()"
                    >
                        <!-- Conditional loading spinner -->
                        @if (isLoading()) {
                            <div class="flex justify-center">
                                <mat-spinner
                                    class="spinnr"
                                    [diameter]="24"
                                ></mat-spinner>
                                <div class="ml-5 text-center">
                                    Generating...
                                </div>
                            </div>
                        } @else {
                            Generate Report
                        }
                    </button>
                </div>
            </div>
        </mat-drawer>

        <!-- Drawer content -->
        <mat-drawer-content class="flex flex-col">
            <!-- Main content -->
            <div class="flex-auto p-6 sm:p-10">
                <!-- Drawer toggle button -->
                <button class="" mat-icon-button (click)="matDrawer.toggle()">
                    <mat-icon [svgIcon]="'heroicons_outline:bars-3'"></mat-icon>
                </button>

                <!-- Report Content -->
                <div
                    class="h-full w-full border-[1px] bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600 rounded-2xl"
                >
                    <!-- If the report is not ready, show message -->
                    @if (!reportResultReady()) {
                        <div class="flex flex-auto flex-col items-center">
                            <ng-lottie
                                width="400px"
                                [options]="options"
                                (animationCreated)="animationCreated($event)"
                            />
                            <div
                                class="mt-6 font-bold text-4xl text-center dark:text-gray-300"
                            >
                                Please select a report type and dates.
                            </div>
                        </div>
                    } @else {
                        <!-- Show report content based on selected report type -->
                        <div class="p-4 flex h-full">
                            @switch (selectedReportType) {
                                @case ("scheduled_jobs") {
                                    <report-scheduled-jobs
                                        [scheduledJobList]="scheduledJobsData()"
                                        [teammembers]="teammembers"
                                        [startDate]="
                                            calenderSelectedDate.start.toLocaleDateString()
                                        "
                                        [endDate]="
                                            calenderSelectedDate.end.toLocaleDateString()
                                        "
                                        [customers]="customers"
                                    />
                                }
                                @case ("customer_jobs") {
                                    <report-customer-jobs
                                        class="w-full h-full"
                                        [scheduledJobList]="scheduledJobsData()"
                                        [teammembers]="teammembers"
                                        [startDate]="
                                            calenderSelectedDate.start.toLocaleDateString()
                                        "
                                        [endDate]="
                                            calenderSelectedDate.end.toLocaleDateString()
                                        "
                                        [selectedCustomer]="selectedCustomer()"
                                    />
                                }
                                @case ("teammember_shifts") {
                                    <report-teammember-shifts
                                        [teamMemberShiftList]="
                                            teamMemberShiftsData()
                                        "
                                        [teamMemberJobsList]="
                                            teamMemberJobsData()
                                        "
                                        [startDate]="
                                            calenderSelectedDate.start.toLocaleDateString()
                                        "
                                        [endDate]="
                                            calenderSelectedDate.end.toLocaleDateString()
                                        "
                                        [selectedTeamMember]="
                                            selectedTeammember()
                                        "
                                        [customers]="customers"
                                    />
                                }
                                @case ("replay") {
                                    <report-replay-teammembers
                                        class="w-full h-full"
                                        [teamMemberShiftList]="
                                            teamMemberShiftsData()
                                        "
                                        [startDate]="
                                            calenderSelectedDate.start.toLocaleDateString()
                                        "
                                        [endDate]="
                                            calenderSelectedDate.end.toLocaleDateString()
                                        "
                                        [selectedTeamMember]="
                                            selectedTeammember()
                                        "
                                    />
                                }
                                @case ("assets_locations") {
                                    <report-assets-locations
                                        class="w-full h-full"
                                        [assetsLocationList]="
                                            assetsLocationsData()
                                        "
                                        [startDate]="
                                            calenderSelectedDate.start.toLocaleDateString()
                                        "
                                        [endDate]="
                                            calenderSelectedDate.end.toLocaleDateString()
                                        "
                                    />
                                }
                                @case ("distance_traveled") {
                                    <report-distance-traveled />
                                }
                                @case ("cost_analysis") {
                                    <report-cost-analysis />
                                }
                                @case ("expenses") {
                                    <report-expenses />
                                }

                                @default {
                                    <div
                                        class="flex flex-auto flex-col items-center"
                                    >
                                        <ng-lottie
                                            width="400px"
                                            [options]="options"
                                            (animationCreated)="
                                                animationCreated($event)
                                            "
                                        />
                                        <div
                                            class="mt-6 font-bold text-4xl text-center dark:text-gray-300"
                                        >
                                            Please select a report type and
                                            dates.
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                    }
                </div>
            </div>
        </mat-drawer-content>
    </mat-drawer-container>
</div>

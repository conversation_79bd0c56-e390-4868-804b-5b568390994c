<div class="flex flex-auto w-full h-full relative">
    <google-map
        class="flex flex-grow rounded-lg"
        width="100%"
        height="100%"
        [options]="options"
        style="border-radius: inherit"
    >
        @for (marker of markers; track trackByFn($index, marker)) {
            <map-advanced-marker
                #secondMarker="mapAdvancedMarker"
                (mapClick)="clickAdvancedMarker(secondMarker)"
                [title]="marker.title"
                [gmpDraggable]="false"
                [content]="marker.content"
                [position]="marker.position"
            >
            </map-advanced-marker>
        }
        @for (dot of redDots; track trackByFn($index, dot)) {
            <map-advanced-marker
                [title]="dot.title"
                [gmpDraggable]="false"
                [content]="dot.content"
                [position]="dot.position"
            ></map-advanced-marker>
        }
        <map-info-window></map-info-window>

        <map-polyline [options]="polylineOptions"></map-polyline>
    </google-map>
    <!-- Bottom Navigation -->
    @if (playBackOptions()) {
        @if (currentPointInfo) {
            <div class="absolute z-50 m-5 p-2 bg-card rounded-lg w-80 h-25">
                <strong>Event Details:</strong>
                <p>
                    <strong>Event Time:</strong>
                    {{ currentPointInfo.eventTime | date: "medium" }}
                </p>
                <p>
                    <strong>Latitude:</strong>
                    {{ currentPointInfo.lat | number: "1.6-6" }}
                </p>
                <p>
                    <strong>Longitude:</strong>
                    {{ currentPointInfo.lng | number: "1.6-6" }}
                </p>
            </div>
        }

        <div
            class="absolute z-50 w-full h-16 max-w-lg -translate-x-1/2 bg-white border border-gray-200 rounded-full bottom-4 left-1/2 dark:bg-gray-700 dark:border-gray-600"
        >
            <div class="grid h-full max-w-lg grid-cols-5 mx-auto">
                <button
                    type="button"
                    class="inline-flex flex-col items-center justify-center px-5 rounded-s-full hover:bg-gray-50 dark:hover:bg-gray-800 group"
                    (click)="fitBoundsToPolyline()"
                >
                    <i
                        class="fa-duotone fa-arrows-spin group-hover:text-blue-600 dark:group-hover:text-blue-500"
                    ></i>

                    <span class="sr-only">refresh</span>
                </button>

                <button
                    (click)="backwardPoint()"
                    type="button"
                    class="inline-flex flex-col items-center justify-center px-5 hover:bg-gray-50 dark:hover:bg-gray-800 group"
                >
                    <i
                        class="fa-duotone fa-backward group-hover:text-blue-600 dark:group-hover:text-blue-500"
                    ></i>
                    <span class="sr-only">Backward</span>
                </button>

                <div class="flex items-center justify-center">
                    <button
                        (click)="togglePlay()"
                        type="button"
                        class="inline-flex items-center justify-center w-10 h-10 font-medium bg-blue-600 rounded-full hover:bg-blue-700 group focus:ring-4 focus:ring-blue-300 focus:outline-none dark:focus:ring-blue-800"
                    >
                        <i
                            class="icon-size-5 w-4 h-4 text-white"
                            [class]="
                                isPlaying
                                    ? 'fa-duotone fa-pause'
                                    : 'fa-duotone fa-circle-play'
                            "
                        ></i>
                        <span class="sr-only">Play/Pause</span>
                    </button>
                </div>

                <button
                    (click)="forwardPoint()"
                    type="button"
                    class="inline-flex flex-col items-center justify-center px-5 hover:bg-gray-50 dark:hover:bg-gray-800 group"
                >
                    <i
                        class="fa-duotone fa-forward group-hover:text-blue-600 dark:group-hover:text-blue-500"
                    ></i>
                    <span class="sr-only">Forward</span>
                </button>

                <button
                    (click)="changeReplaySpeed()"
                    type="button"
                    class="inline-flex flex-col items-center justify-center px-5 rounded-e-full hover:bg-gray-50 dark:hover:bg-gray-800 group"
                >
                    <i
                        class="fa-duotone fa-tachometer-alt-fast group-hover:text-blue-600 dark:group-hover:text-blue-500"
                    ></i>
                    <span class="text-xs">{{ replaySpeed }}x</span>
                    <span class="sr-only">Replay Speed</span>
                </button>
            </div>
        </div>
    }
</div>

import { DatePipe } from '@angular/common';
import { Component, input, viewChild, OnChanges, ChangeDetectorRef } from '@angular/core';
import { GoogleMap, MapAdvancedMarker, MapInfoWindow, MapPolyline } from '@angular/google-maps';
import { MatIconModule } from '@angular/material/icon';
import { PipesModule } from 'app/pipes/pipes.module';

@Component({
    selector: 'replay-map-widget',
    templateUrl: './replay-map-widget.component.html',
    imports: [
    PipesModule,
    DatePipe,
    GoogleMap,
    MapAdvancedMarker,
    MapPolyline,
    MapInfoWindow,
    MatIconModule
]
})
export class ReplayMapWidgetComponent implements OnChanges {
    locationGPSData = input.required<string[]>();
    infoWindow = viewChild.required(MapInfoWindow);
    map = viewChild.required(GoogleMap);
    playBackOptions = input<boolean>(false);

    currentPointInfo: { eventTime: string; lat: number; lng: number } | null = null;

    isPlaying = false;
    currentIndex = 0;
    playInterval: any = null;
    redDots: any[] = [];
    replaySpeed = 1; // Playback speed multiplier

    mapId = '9852b6410bcc430e';
    pathWithHeading: Array<{ lat: number; lng: number; heading: number; eventTime: string }> = [];
    center: google.maps.LatLngLiteral = {
        lat: -37.823452789202925,
        lng: 144.96169004109495,
    };
    zoom = 4;
    options: google.maps.MapOptions = {
        disableDefaultUI: true,
        mapId: this.mapId,
        gestureHandling: 'greedy',
        center: this.center,
        zoom: 19,
        heading: 324,
        tilt: 65,
    };
    markers = [];
    polylineOptions = {
        path: [],
        strokeColor: '#32a1d0',
        strokeOpacity: 1.0,
        strokeWeight: 2,
    };

    constructor(private _changeDetectorRef: ChangeDetectorRef) { }

    ngOnChanges(): void {
        if (this.locationGPSData().length > 0) {
            this.loadSelectedShift();
        }
    }

    loadSelectedShift() {
        // Clean previous data
        this.markers = [];
        this.pathWithHeading = [];
        this.redDots = [];
        this.currentIndex = 0;
        this.currentPointInfo = null; // Reset currentPointInfo
        this.pausePlayback(); // Ensure any existing playback is stopped

        setTimeout(() => {
            if (this.locationGPSData().length > 0) {
                // Parse the location data
                this.pathWithHeading = this.parseLocationData(this.locationGPSData());
                // Extract positions for the polyline
                const polylinePath = this.pathWithHeading.map(loc => ({ lat: loc.lat, lng: loc.lng }));
                this.polylineOptions = { ...this.polylineOptions, path: polylinePath };

                if (this.pathWithHeading.length > 0) {
                    this.markers = [];
                    this.markers.push(
                        {
                            id: Math.floor(Math.random() * 100) + 1,
                            position: { lat: this.pathWithHeading[0].lat, lng: this.pathWithHeading[0].lng },
                            title: 'Start',
                            content: this.buildContent('fa-map-marker-alt', 'red-400', 'Start'),
                        },
                        {
                            id: Math.floor(Math.random() * 100) + 1,
                            position: {
                                lat: this.pathWithHeading[this.pathWithHeading.length - 1].lat,
                                lng: this.pathWithHeading[this.pathWithHeading.length - 1].lng,
                            },
                            title: 'End',
                            content: this.buildContent('fa-flag', 'blue-800', 'End'),
                        }
                    );

                    this.fitBoundsToPolyline();
                    this._changeDetectorRef.detectChanges();
                }
            }
        }, 10);
    }

    parseLocationData(locationGPSData: string[]): Array<{ lat: number; lng: number; heading: number; eventTime: string }> {
        try {
            return locationGPSData
                .map((item: string) => {
                    const loc = JSON.parse(item);
                    return {
                        lat: loc.Lat,
                        lng: loc.Lon,
                        heading: loc.heading || 0,
                        eventTime: loc.EventTime,
                    };
                })
                .filter(item => item !== null);
        } catch (error) {
            console.error('Error parsing JSON:', error);
            return [];
        }
    }

    fitBoundsToPolyline() {
        // Clean markers, red dots, and reset currentIndex
        this.markers = [];
        this.redDots = [];
        this.currentIndex = 0;

        // Reset speed to default
        this.replaySpeed = 1;

        // Pause playback and clear intervals
        this.pausePlayback();

        // Recreate Start and End markers
        if (this.pathWithHeading.length > 0) {
            this.markers.push(
                {
                    id: Math.floor(Math.random() * 100) + 1,
                    position: { lat: this.pathWithHeading[0].lat, lng: this.pathWithHeading[0].lng },
                    title: 'Start',
                    content: this.buildContent('fa-map-marker-alt', 'red-400', 'Start'),
                },
                {
                    id: Math.floor(Math.random() * 100) + 1,
                    position: {
                        lat: this.pathWithHeading[this.pathWithHeading.length - 1].lat,
                        lng: this.pathWithHeading[this.pathWithHeading.length - 1].lng,
                    },
                    title: 'End',
                    content: this.buildContent('fa-flag', 'blue-800', 'End'),
                }
            );
        }

        // Fit bounds as before
        const bounds = new google.maps.LatLngBounds();
        this.polylineOptions.path.forEach((latLng: google.maps.LatLngLiteral) => {
            bounds.extend(latLng);
        });

        setTimeout(() => {
            this.map().fitBounds(bounds);
        }, 1000);

        // Trigger change detection
        this._changeDetectorRef.detectChanges();
    }

    buildContent(iconClass: string, color: string, title: string): HTMLElement {
        const content = document.createElement('div');
        content.className = 'text-center p-2';
        content.innerHTML = `
      <div class="text-${color} text-xl">
        <span class="fa-stack" style="vertical-align: top;">
          <i class="fa-solid fa-circle fa-stack-2x"></i>
          <i class="fa-solid ${iconClass} fa-stack-1x fa-inverse"></i>
        </span>
      </div>
    `;
        return content;
    }

    clickAdvancedMarker(advancedMarker: any) {
        this.infoWindow().openAdvancedMarkerElement(
            advancedMarker.advancedMarker,
            advancedMarker.advancedMarker.title
        );
    }

    togglePlay() {
        if (this.isPlaying) {
            this.pausePlayback();
        } else {
            this.startPlayback();
        }
    }

    startPlayback() {
        // Set isPlaying to true
        this.isPlaying = true;

        // Clear any existing interval
        if (this.playInterval) {
            clearInterval(this.playInterval);
            this.playInterval = null;
        }

        if (this.currentIndex >= this.pathWithHeading.length) {
            this.currentIndex = 0;
        }

        this.playInterval = setInterval(() => {
            if (this.currentIndex >= this.pathWithHeading.length) {
                this.pausePlayback();
                return;
            }

            const currentPoint = this.pathWithHeading[this.currentIndex];

            // Update currentPointInfo
            this.currentPointInfo = {
                eventTime: currentPoint.eventTime,
                lat: currentPoint.lat,
                lng: currentPoint.lng,
            };

            // Move the camera to the current position with heading
            this.map().googleMap.moveCamera({
                center: { lat: currentPoint.lat, lng: currentPoint.lng },
                zoom: 18,
                tilt: 65,
                heading: currentPoint.heading,
            });

            // Add a directional arrow marker at the current position
            this.addArrowMarker(
                { lat: currentPoint.lat, lng: currentPoint.lng },
                currentPoint.heading
            );

            this.currentIndex++;
            this._changeDetectorRef.detectChanges();
        }, 2000 / this.replaySpeed);
    }


    pausePlayback() {
        // Always clear the interval
        if (this.playInterval) {
            clearInterval(this.playInterval);
            this.playInterval = null;
        }
        // Set isPlaying to false
        this.isPlaying = false;
    }

    forwardPoint() {
        if (this.isPlaying) {
            this.pausePlayback();
        }

        if (this.currentIndex < this.pathWithHeading.length - 1) {
            this.currentIndex++;
            this.moveCameraToCurrentPoint();

            // Add marker to the next point
            const currentPoint = this.pathWithHeading[this.currentIndex];
            this.addArrowMarker(
                { lat: currentPoint.lat, lng: currentPoint.lng },
                currentPoint.heading
            );
        }
    }

    backwardPoint() {
        if (this.isPlaying) {
            this.pausePlayback();
        }

        if (this.currentIndex > 0) {
            // Decrement the current index
            this.currentIndex--;

            // Remove markers beyond the current index
            this.redDots = this.redDots.slice(0, this.currentIndex);

            // Move the camera to the current point
            this.moveCameraToCurrentPoint();

            // Ensure change detection picks up the changes
            this._changeDetectorRef.detectChanges();
        }
    }

    moveCameraToCurrentPoint() {
        const currentPoint = this.pathWithHeading[this.currentIndex];

        // Update currentPointInfo
        this.currentPointInfo = {
            eventTime: currentPoint.eventTime,
            lat: currentPoint.lat,
            lng: currentPoint.lng,
        };

        this.map().googleMap.moveCamera({
            center: { lat: currentPoint.lat, lng: currentPoint.lng },
            zoom: 18,
            tilt: 65,
            heading: currentPoint.heading,
        });

        // Do not add or remove markers here; it's handled in forwardPoint and backwardPoint
        this._changeDetectorRef.detectChanges();
    }


    changeReplaySpeed() {
        const speeds = [1, 2, 4, 8, 16];
        const currentSpeedIndex = speeds.indexOf(this.replaySpeed);
        this.replaySpeed = speeds[(currentSpeedIndex + 1) % speeds.length];

        if (this.isPlaying) {
            this.pausePlayback();
            this.startPlayback();
        }
    }

    addArrowMarker(position: google.maps.LatLngLiteral, heading: number) {
        const arrowMarker = {
            position: position,
            title: 'Movement point',
            content: this.buildArrowIcon(heading),
        };

        this.redDots.push(arrowMarker);
    }

    buildArrowIcon(heading: number): HTMLElement {
        const content = document.createElement('div');
        content.innerHTML = `
      <div style="position: relative; transform: translate(-50%, -50%);">
        <!-- Red Dot -->
        <div style="
          width: 10px;
          height: 10px;
          background-color: red;
          border-radius: 50%;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        "></div>
        <!-- Blue Arrow -->
        <div style="
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) rotate(${heading}deg);
        ">
          <i class="fa-solid fa-location-arrow" style="color: blue; font-size: 16px;"></i>
        </div>
      </div>
    `;
        return content;
    }

    trackByFn(index: number, item: any): any {
        return item.id || index;
    }
}

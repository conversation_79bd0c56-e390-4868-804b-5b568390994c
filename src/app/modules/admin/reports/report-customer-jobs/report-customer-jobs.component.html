<div class="flex flex-col flex-auto min-w-0">
    @if (!scheduledJobList().length) {
        <div class="flex-auto bg-gray-100 p-4 w-full">
            <div
                class="xl:col-span-2 flex flex-col flex-auto bg-card shadow rounded-2xl overflow-hidden"
            >
                <div
                    class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b"
                >
                    <div class="text-lg font-extrabold tracking-tight">
                        No Data
                    </div>
                </div>
            </div>
        </div>
    } @else {
        <div class="flex-auto p-4 w-full">
            <div
                class="xl:col-span-2 flex flex-col flex-auto bg-card shadow rounded-2xl"
            >
                <div
                    class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b"
                >
                    <!-- Title -->
                    <div class="text-lg font-extrabold tracking-tight">
                        <div class="p-10">
                            <div
                                class="text-3xl font-semibold leading-8 tracking-tight"
                            >
                                Scheduled Job Report -
                                {{ selectedCustomer().name }}
                            </div>
                            <div
                                class="text-secondary font-medium tracking-tight"
                            >
                                From: {{ startDate() }} - To:
                                {{ endDate() }}
                            </div>
                        </div>
                        <div class="text-secondary font-medium">
                            <button
                                class="mr-5"
                                (click)="applyFilter(null, 'pending')"
                            >
                                <div
                                    class="inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-gray-500 border-2 border-gray rounded-full dark:border-gray-900"
                                >
                                    {{ getApprovedStatus("pending") }}
                                </div>
                                pending
                            </button>
                            <button
                                class="mr-5"
                                (click)="applyFilter(null, 'approved')"
                            >
                                <div
                                    class="inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-blue-500 border-2 border-gray rounded-full dark:border-gray-900"
                                >
                                    {{ getApprovedStatus("approved") }}
                                </div>
                                approved
                            </button>

                            <button
                                class="mr-5"
                                (click)="applyFilter(null, 'finished')"
                            >
                                <div
                                    class="inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-green-500 border-2 border-gray rounded-full dark:border-gray-900"
                                >
                                    {{ getApprovedStatus("finished") }}
                                </div>
                                finished
                            </button>
                        </div>
                    </div>
                    <!-- Actions -->
                    <div
                        class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4"
                    >
                        <!-- Search -->
                        <mat-form-field
                            class="fuse-mat-dense fuse-mat-rounded min-w-64"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-icon
                                class="icon-size-5"
                                matPrefix
                                [svgIcon]="'heroicons_solid:magnifying-glass'"
                            ></mat-icon>
                            <input
                                matInput
                                (keyup)="applyFilter($event)"
                                [autocomplete]="'off'"
                                [placeholder]="'Search ...'"
                                #input
                            />
                        </mat-form-field>
                        <div class="ml-4">
                            <button
                                mat-icon-button
                                [matMenuTriggerFor]="moreMenu"
                            >
                                <i
                                    class="ml-3 icon-size-5 fa-duotone fa-file-export"
                                ></i>
                            </button>
                            <mat-menu #moreMenu="matMenu">
                                <button
                                    mat-menu-item
                                    (click)="exportMaterialTable('pdf')"
                                >
                                    <i class="fa-duotone fa-file-pdf"></i>
                                    <span class="ml-3">PDF</span>
                                </button>
                                <button
                                    mat-menu-item
                                    (click)="exportMaterialTable('excel')"
                                >
                                    <i class="fa-duotone fa-file-excel"></i>
                                    <span class="ml-3">EXCEL</span>
                                </button>
                            </mat-menu>
                        </div>
                    </div>
                </div>

                <div class="overflow-x-auto mx-6">
                    <div class="w-full bg-transparent">
                        <table mat-table [dataSource]="dataSource" matSort>
                            <!-- Avatar Column -->
                            <ng-container matColumnDef="avatar">
                                <th mat-header-cell *matHeaderCellDef>
                                    Team Member
                                </th>
                                <td mat-cell *matCellDef="let row">
                                    <div
                                        class="relative flex flex-0 items-center justify-center w-10 h-10 rounded-full"
                                    >
                                        <!-- Team Member Dispatch Status -->
                                        @if (row.dispatchStatus) {
                                            <div
                                                class="flex absolute bottom-0 right-0 flex-0 w-4 h-4 -ml-0.5 rounded-full ring-2 ring-bg-card items-center justify-center text-on-primary"
                                                [ngClass]="{
                                                    'bg-blue-200':
                                                        getEventAttributes(row)
                                                            .eventColor ===
                                                        'blue',
                                                    'bg-gray-500':
                                                        getEventAttributes(row)
                                                            .eventColor ===
                                                        'gray',
                                                    'bg-green-500':
                                                        getEventAttributes(row)
                                                            .eventColor ===
                                                        'green'
                                                }"
                                            >
                                                <i
                                                    class="fa-xs"
                                                    [ngClass]="
                                                        getEventAttributes(row)
                                                            .icon
                                                    "
                                                ></i>
                                            </div>
                                        } @else {
                                            <span
                                                class="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10"
                                            >
                                                Pending
                                            </span>
                                        }

                                        <!-- Team Member Avatar Status -->
                                        <div
                                            class="w-15 rounded-full overflow-hidden"
                                        >
                                            @if (
                                                getTeamMemberInfo(
                                                    row.teamMemberID
                                                ).avatar
                                            ) {
                                                <img
                                                    class="object-cover w-full h-full"
                                                    [src]="
                                                        getTeamMemberInfo(
                                                            row.teamMemberID
                                                        ).avatar
                                                    "
                                                    alt="Team Member Avatar"
                                                />
                                            } @else {
                                                <div
                                                    class="flex items-center justify-center w-full h-full rounded-full text-lg uppercase bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                                >
                                                    {{
                                                        getTeamMemberInfo(
                                                            row.teamMemberID
                                                        ).name
                                                    }}
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </td>
                            </ng-container>
                            <!-- Progress Column -->
                            <ng-container matColumnDef="jobTitle">
                                <th
                                    mat-header-cell
                                    *matHeaderCellDef
                                    mat-sort-header
                                >
                                    Job Title
                                </th>
                                <td mat-cell *matCellDef="let row">
                                    <div class="flex flex-col mt-2 mb-2">
                                        <div class="font-medium">
                                            {{ row.jobTitle }}
                                        </div>
                                        <div
                                            class="flex flex-col sm:flex-row sm:items-center -ml-0.5 mt-2 sm:mt-1 space-y-1 sm:space-y-0 sm:space-x-3"
                                        >
                                            @if (row.startTime) {
                                                <div class="flex items-center">
                                                    <mat-icon
                                                        class="icon-size-5 text-hint"
                                                        [svgIcon]="
                                                            'heroicons_solid:clock'
                                                        "
                                                    ></mat-icon>
                                                    <div
                                                        class="ml-1.5 text-md text-secondary"
                                                    >
                                                        {{ row.startTime }}
                                                    </div>
                                                </div>
                                            }
                                            @if (row.jobAddress) {
                                                <div class="flex items-center">
                                                    <mat-icon
                                                        class="icon-size-5 text-hint"
                                                        [svgIcon]="
                                                            'heroicons_solid:map-pin'
                                                        "
                                                    ></mat-icon>
                                                    <div
                                                        class="ml-1.5 text-md text-secondary"
                                                    >
                                                        {{ row.jobAddress }}
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </td>
                            </ng-container>
                            <!-- Status Column -->
                            <ng-container matColumnDef="jobStatus">
                                <th
                                    mat-header-cell
                                    *matHeaderCellDef
                                    mat-sort-header
                                >
                                    Status
                                </th>
                                <td mat-cell *matCellDef="let row">
                                    <div
                                        class="rounded-full p-2 font-semibold text-center text-sm"
                                        [ngClass]="{
                                            'bg-blue-200':
                                                getEventAttributes(row)
                                                    .eventColor === 'blue',
                                            'text-blue-600':
                                                getEventAttributes(row)
                                                    .eventColor === 'blue',
                                            'bg-gray-500':
                                                getEventAttributes(row)
                                                    .eventColor === 'gray',
                                            'text-gray-200':
                                                getEventAttributes(row)
                                                    .eventColor === 'gray',
                                            'bg-green-200':
                                                getEventAttributes(row)
                                                    .eventColor === 'green',
                                            'text-green-800':
                                                getEventAttributes(row)
                                                    .eventColor === 'green'
                                        }"
                                    >
                                        {{ row.jobStatus }}
                                    </div>
                                </td>
                            </ng-container>

                            <!-- dueDate Column -->
                            <ng-container matColumnDef="dueDate">
                                <th
                                    mat-header-cell
                                    *matHeaderCellDef
                                    mat-sort-header
                                >
                                    Due Date
                                </th>
                                <td mat-cell *matCellDef="let row">
                                    {{ row.dueDate | date: "EEE, dd MMM yyyy" }}
                                </td>
                            </ng-container>

                            <!-- Actions Column -->
                            <ng-container matColumnDef="actions">
                                <th mat-header-cell *matHeaderCellDef>
                                    Actions
                                </th>
                                <td mat-cell *matCellDef="let row">
                                    <button
                                        mat-icon-button
                                        (click)="loadSelectedSchedule(row)"
                                    >
                                        <i
                                            class="icon-size-5 fa-duotone fa-calendar-lines-pen"
                                        ></i>
                                    </button>
                                </td>
                            </ng-container>

                            <tr
                                mat-header-row
                                *matHeaderRowDef="displayedColumns"
                            ></tr>
                            <tr
                                mat-row
                                *matRowDef="let row; columns: displayedColumns"
                            ></tr>

                            <!-- Row shown when there is no matching data. -->
                            @if (isLoading) {
                                <div class="flex justify-center p-10">
                                    <mat-spinner class="spinnr" [diameter]="24">
                                    </mat-spinner>
                                    <div class="ml-5 text-center">
                                        Loading...
                                    </div>
                                </div>
                            } @else {
                                <tr class="mat-row" *matNoDataRow>
                                    <td class="mat-cell" colspan="4">
                                        No data matching the filter "{{
                                            input.value
                                        }}"
                                    </td>
                                </tr>
                            }
                        </table>

                        <mat-paginator
                            [pageSize]="20"
                            [pageSizeOptions]="[5, 10, 20, 50, 100]"
                            aria-label="Select page of users"
                        ></mat-paginator>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

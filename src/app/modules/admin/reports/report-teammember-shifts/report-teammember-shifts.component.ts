import { DatePipe, DecimalPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, input, OnInit, Signal, signal, viewChild, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTableModule } from '@angular/material/table';
import { DriverShift } from 'app/core/databaseModels/driverShifts/driverShifts.types';
import { ScheduledJob } from 'app/core/databaseModels/scheduledJobs/scheduledJob.types';
import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';
import { ApexOptions, NgApexchartsModule } from 'ng-apexcharts';
import { ReportScheduledJobsComponent } from '../report-scheduled-jobs/report-scheduled-jobs.component';
import { Customer } from 'app/core/databaseModels/customers/contacts.types';
import { MatAccordion, MatExpansionModule } from '@angular/material/expansion';
import { PipesModule } from 'app/pipes/pipes.module';
import { ReplayMapWidgetComponent } from '../widget/replay-map-widget.component';
import { DistanceCalculatorService } from 'app/utils/distanceCalculator';
@Component({
    selector: 'report-teammember-shifts',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        PipesModule,
        DatePipe,
        DecimalPipe,
        MatIconModule,
        MatTableModule,
        MatPaginatorModule,
        MatMenuModule,
        MatFormFieldModule,
        MatInputModule,
        MatButtonModule,
        MatProgressSpinnerModule,
        NgApexchartsModule,
        ReportScheduledJobsComponent,
        MatExpansionModule,
        ReplayMapWidgetComponent,
    ],
    templateUrl: './report-teammember-shifts.component.html',
    styleUrl: './report-teammember-shifts.component.scss'
})
export class ReportTeammemberShiftsComponent implements OnInit, AfterViewInit {

    accordion = viewChild.required(MatAccordion);
    distanceCalculator = new DistanceCalculatorService();

    teamMemberShiftList = input.required<DriverShift[]>();
    teamMemberJobsList = input.required<ScheduledJob[]>();
    selectedTeamMember = input.required<TeamMember>();
    startDate = input.required<string>();
    endDate = input.required<string>();
    teammembers = [];
    customers = input.required<(Customer[])>();
    // Local 
    totalShifts: Signal<number> = computed(() => this.teamMemberShiftList().length);
    totalJobs: Signal<number> = computed(() => this.teamMemberJobsList().length);

    totalHours: Signal<number> = computed(() =>
        this.teamMemberShiftList().reduce((total, shift) => total + shift.shiftDuration, 0)
    );
    totalBrakes: Signal<number> = computed(() =>
        this.teamMemberShiftList().reduce((total, shift) => total + shift.totalBreakDuration, 0))

    completedJobs: Signal<number> = computed(() =>
        this.teamMemberJobsList().reduce((total, shift) => total + (shift.jobStatus === 'finished' ? 1 : 0), 0)
    )
    // charts

    chartFinishVsPending: ApexOptions;


    ngOnInit(): void {
        // console.log(this.teamMemberShiftList());
        // console.log(this.teamMemberJobsList());
        // console.log(this.startDate());
        // console.log(this.endDate());
        // console.log('totalShifts', this.totalShifts());
        // console.log('totalJobs', this.totalJobs());
        // console.log('totalHours', this.totalHours());
        // console.log('totalBrakes', this.totalBrakes());
        // console.log('completedJobs', this.completedJobs());

        this.teammembers.push(this.selectedTeamMember());

        this.chartFinishVsPending = {
            chart: {
                animations: {
                    speed: 400,
                    animateGradually: {
                        enabled: false,
                    },
                },
                fontFamily: 'inherit',
                foreColor: 'inherit',
                height: '100%',
                type: 'donut',
                sparkline: {
                    enabled: true,
                },
            },
            colors: ['#3182CE', '#63B3ED'],
            labels: ['Finished', 'Not Finished'],
            plotOptions: {
                pie: {
                    customScale: 0.9,
                    expandOnClick: false,
                    donut: {
                        size: '70%',
                    },
                },
            },
            series: [Math.round((this.completedJobs() / this.totalJobs()) * 100), Math.round(((this.totalJobs() - this.completedJobs()) / this.totalJobs()) * 100)],
            states: {
                hover: {
                    filter: {
                        type: 'none',
                    },
                },
                active: {
                    filter: {
                        type: 'none',
                    },
                },
            },
            tooltip: {
                enabled: true,
                fillSeriesColor: false,
                theme: 'dark',
                custom: ({
                    seriesIndex,
                    w,
                }): string => `<div class="flex items-center h-8 min-h-8 max-h-8 px-3">
                                                    <div class="w-3 h-3 rounded-full" style="background-color: ${w.config.colors[seriesIndex]};"></div>
                                                    <div class="ml-2 text-md leading-none">${w.config.labels[seriesIndex]}:</div>
                                                    <div class="ml-2 text-md font-bold leading-none">${w.config.series[seriesIndex]}%</div>
                                                </div>`,
            },
        };


    }
    ngAfterViewInit() {

    }

    trackByFn(index: number, item: any): any {

        return item.id || index;
    }
    totalDistance(path: string[]): number {
        const pathList = this.parseLocationData(path);
        // return Round number
        return Math.round(this.distanceCalculator.calculateTotalDistance(pathList));


    }
    parseLocationData(locationGPSData: string[]): google.maps.LatLngLiteral[] {
        try {
            // Map over the array of stringified JSON objects
            const parsedArray = locationGPSData.map((item: string) => {
                try {
                    return JSON.parse(item);
                } catch (error) {
                    console.error("Error parsing item:", item, error);
                    return null; // Return null for items that couldn't be parsed
                }
            }).filter(item => item !== null); // Filter out null values

            // Map over the parsed array to extract lat and lng
            return parsedArray.map((loc: any) => ({ lat: loc.Lat, lng: loc.Lon }));
        } catch (error) {
            console.error("Error parsing JSON:", error);
            return [];
        }
    }

    exportMaterialTable(format: 'pdf' | 'excel') { }

}

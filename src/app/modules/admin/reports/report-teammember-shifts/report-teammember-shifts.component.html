<div class="flex flex-col flex-auto min-w-0">
    @if (!teamMemberShiftList().length) {
        <!-- No Data Available -->
        <div class="flex-auto bg-gray-100 p-4 w-full">
            <div class="text-lg font-extrabold tracking-tight">No Data</div>
        </div>
    } @else {
        <div class="p-10">
            <div class="text-3xl font-semibold leading-8 tracking-tight">
                Team Member Shift Report - {{ selectedTeamMember().name }}
            </div>
            <div class="text-secondary font-medium tracking-tight">
                From: {{ startDate() }} - To: {{ endDate() }}
            </div>
        </div>
        <div
            class="bg-card flex flex-auto flex-col overflow-hidden rounded-2xl p-6 shadow w-full"
        >
            <div class="flex items-start justify-between">
                <div class="text-2xl font-semibold leading-8 tracking-tight">
                    Total Shifts :
                    <span
                        class="rounded bg-primary p-2 text-white dark:bg-primary-dark"
                    >
                        {{ totalShifts() }}
                    </span>
                    <span class="ml-5 text-hint text-lg">
                        Total Duration : {{ totalHours() | minutesToTime }} -
                        Total Brakes : {{ totalBrakes() | minutesToTime }}</span
                    >
                </div>
                <div class="ml-4 justify-end">
                    <button mat-icon-button [matMenuTriggerFor]="moreMenu">
                        <i
                            class="ml-3 icon-size-5 fa-duotone fa-file-export"
                        ></i>
                    </button>
                    <mat-menu #moreMenu="matMenu">
                        <button
                            mat-menu-item
                            (click)="exportMaterialTable('pdf')"
                        >
                            <i class="fa-duotone fa-file-pdf"></i>
                            <span class="ml-3">PDF</span>
                        </button>
                        <button
                            mat-menu-item
                            (click)="exportMaterialTable('excel')"
                        >
                            <i class="fa-duotone fa-file-excel"></i>
                            <span class="ml-3">EXCEL</span>
                        </button>
                    </mat-menu>
                </div>
            </div>
            <mat-accordion class="w-full mt-5">
                @for (shift of teamMemberShiftList(); track shift.$id) {
                    <mat-expansion-panel>
                        <mat-expansion-panel-header>
                            <mat-panel-title>
                                <i class="fa-duotone fa-calendar mr-5"></i>
                                {{ shift.startShift | date: "short" }} -
                                {{ shift.endShift | date: "short" }}
                            </mat-panel-title>
                            <mat-panel-description>
                                <i class="fa-duotone fa-clock mr-5"></i>

                                Duration:
                                <span class="text-green-700">
                                    {{
                                        shift.shiftDuration | minutesToTime
                                    }}</span
                                >
                                &nbsp;-&nbsp; Brakes:
                                <span class="text-orange-700 mr-5">
                                    {{
                                        shift.totalBreakDuration | minutesToTime
                                    }}</span
                                >
                                Distance:
                                <span class="text-blue-700 ml-1">
                                    {{ totalDistance(shift.locationGPSData) }}
                                    KM
                                </span>
                            </mat-panel-description>
                        </mat-expansion-panel-header>
                        <div class="flex w-full flex-auto flex-col">
                            <div class="w-full h-10">Shift Details</div>
                            <div
                                class="grid w-full grid-cols-1 gap-8 xl:grid-cols-5"
                            >
                                <div
                                    class="bg-card flex flex-auto flex-col overflow-hidden rounded-2xl shadow xl:col-span-4"
                                >
                                    <replay-map-widget
                                        class="w-full h-full"
                                        [locationGPSData]="
                                            shift.locationGPSData
                                        "
                                    />
                                </div>
                                <div
                                    class="flex flex-auto flex-col rounded-2xl p-6 shadow bg-gray-100"
                                >
                                    <video
                                        class="w-full object-cover rounded-lg"
                                        controls
                                    >
                                        <source
                                            [src]="shift.videoFileUrl"
                                            type="video/mp4"
                                        />
                                        Your browser does not support the video
                                        tag.
                                    </video>
                                    <div
                                        class="flex-col mt-3 p-5 rounded-lg shadow bg-white"
                                    >
                                        <span class="m-5">Signature</span>
                                        <img
                                            [src]="shift.signatureFileImageUrl"
                                            alt="signature image"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </mat-expansion-panel>
                }
            </mat-accordion>
        </div>
        <div
            class="grid w-full grid-cols-1 gap-8 sm:grid-cols-2 md:mt-8 lg:grid-cols-4"
        >
            <div
                class="bg-card flex flex-auto flex-col overflow-hidden rounded-2xl p-6 shadow"
            >
                <div class="flex items-start justify-between">
                    <div
                        class="truncate text-lg font-medium leading-6 tracking-tight"
                    >
                        Finished Jobs vs. Not Finished Jobs
                    </div>
                </div>
                <div class="mt-6 flex h-44 flex-auto flex-col">
                    <apx-chart
                        class="flex h-full w-full flex-auto items-center justify-center"
                        [chart]="chartFinishVsPending.chart"
                        [colors]="chartFinishVsPending.colors"
                        [labels]="chartFinishVsPending.labels"
                        [plotOptions]="chartFinishVsPending.plotOptions"
                        [series]="chartFinishVsPending.series"
                        [states]="chartFinishVsPending.states"
                        [tooltip]="chartFinishVsPending.tooltip"
                    ></apx-chart>
                </div>

                <div class="mt-8">
                    <div class="-my-3 divide-y">
                        @for (
                            dataset of chartFinishVsPending.series;
                            track trackByFn(i, dataset);
                            let i = $index
                        ) {
                            <div class="grid grid-cols-3 py-3">
                                <div class="flex items-center">
                                    <div
                                        class="h-2 w-2 flex-0 rounded-full"
                                        [style.backgroundColor]="
                                            chartFinishVsPending.colors[i]
                                        "
                                    ></div>
                                    <div class="ml-3 truncate">
                                        {{ chartFinishVsPending.labels[i] }}
                                    </div>
                                </div>
                                <div class="text-right font-medium">
                                    {{
                                        (totalJobs() * dataset) / 100
                                            | number: "1.0-0"
                                    }}
                                </div>
                                <div class="text-secondary text-right">
                                    {{ dataset }}%
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
            <div
                class="bg-card flex flex-auto flex-col overflow-hidden lg:col-span-3"
            >
                <report-scheduled-jobs
                    [scheduledJobList]="teamMemberJobsList()"
                    [teammembers]="teammembers"
                    [startDate]="startDate()"
                    [endDate]="endDate()"
                    [customers]="customers()"
                />
            </div>
        </div>
    }
</div>

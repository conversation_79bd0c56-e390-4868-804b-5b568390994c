import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, input, OnInit, signal, Signal, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTableModule } from '@angular/material/table';
import { PipesModule } from 'app/pipes/pipes.module';
import { NgApexchartsModule } from 'ng-apexcharts';
import { ReplayMapWidgetComponent } from '../widget/replay-map-widget.component';
import { DriverShift } from 'app/core/databaseModels/driverShifts/driverShifts.types';
import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';
import { DistanceCalculatorService } from 'app/utils/distanceCalculator';

@Component({
    selector: 'report-replay-teammembers',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        PipesModule,
        DatePipe,
        MatIconModule,
        MatTableModule,
        MatPaginatorModule,
        MatMenuModule,
        MatFormFieldModule,
        MatInputModule,
        MatButtonModule,
        MatProgressSpinnerModule,
        NgApexchartsModule,
        MatExpansionModule,
        ReplayMapWidgetComponent,
    ],
    templateUrl: './report-replay-teammembers.component.html',
    styleUrl: './report-replay-teammembers.component.scss'
})
export class ReportReplayTeammembersComponent implements OnInit {
    teamMemberShiftList = input.required<DriverShift[]>();
    selectedTeamMember = input.required<TeamMember>();
    startDate = input.required<string>();
    endDate = input.required<string>();
    teammembers = [];
    totalShifts: Signal<number> = computed(() => this.teamMemberShiftList().length);
    totalHours: Signal<number> = computed(() =>
        this.teamMemberShiftList().reduce((total, shift) => total + shift.shiftDuration, 0)
    );
    totalBrakes: Signal<number> = computed(() =>
        this.teamMemberShiftList().reduce((total, shift) => total + shift.totalBreakDuration, 0))

    selectedShift = signal<string[]>([]);

    isGenerating = signal<boolean>(false);

    distanceCalculator = new DistanceCalculatorService();



    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
    ) { }

    ngOnInit(): void {

        // console.log(this.teamMemberShiftList());
        // console.log(this.startDate());
        // console.log(this.endDate());
        // console.log('totalShifts', this.totalShifts());
        // console.log('totalHours', this.totalHours());
        // console.log('totalBrakes', this.totalBrakes());
    }

    loadShiftDetails(selected: DriverShift) {
        this.isGenerating.set(true);
        this.selectedShift.set([]);
        //delay for the map to load
        setTimeout(() => {
            this.selectedShift.set(selected.locationGPSData);

            this._changeDetectorRef.detectChanges();
            this.isGenerating.set(false);
        }, 1000);

    }
    totalDistance(path: string[]): number {
        const pathList = this.parseLocationData(path);
        // return Round number
        return Math.round(this.distanceCalculator.calculateTotalDistance(pathList));
    }

    parseLocationData(locationGPSData: string[]): google.maps.LatLngLiteral[] {
        try {
            // Map over the array of stringified JSON objects
            const parsedArray = locationGPSData.map((item: string) => {
                try {
                    return JSON.parse(item);
                } catch (error) {
                    console.error("Error parsing item:", item, error);
                    return null; // Return null for items that couldn't be parsed
                }
            }).filter(item => item !== null); // Filter out null values

            // Map over the parsed array to extract lat and lng
            return parsedArray.map((loc: any) => ({ lat: loc.Lat, lng: loc.Lon }));
        } catch (error) {
            console.error("Error parsing JSON:", error);
            return [];
        }
    }

    exportMaterialTable(format: 'pdf' | 'excel') { }

}

<div class="flex flex-col flex-auto min-w-0 h-full">
    @if (!teamMemberShiftList().length) {
        <!-- No Data Available -->
        <div class="flex-auto bg-gray-100 p-4 w-full">
            <div class="text-lg font-extrabold tracking-tight">No Data</div>
        </div>
    } @else {
        <div class="p-10">
            <div class="text-3xl font-semibold leading-8 tracking-tight">
                Team Member Replay Report - {{ selectedTeamMember().name }}
            </div>
            <div class="text-secondary font-medium tracking-tight">
                From: {{ startDate() }} - To: {{ endDate() }}
            </div>
        </div>

        <!-- Main Grid Container -->
        <div class="grid w-full h-full grid-cols-1 gap-8 xl:grid-cols-5">
            <!-- List Section with max-height and Scroll -->
            <div
                class="flex flex-col rounded-2xl p-6 shadow bg-gray-100 xl:col-span-1"
                style="max-height: 50vh"
            >
                <!-- Inner Scrollable List -->
                <div class="flex-1 overflow-y-auto">
                    <!-- Shift List -->
                    <span
                        class="text-2xl font-semibold leading-8 tracking-tight"
                    >
                        Total Shifts : {{ totalShifts() }}</span
                    >
                    @for (shift of teamMemberShiftList(); track shift.$id) {
                        <div
                            class="cursor-pointer bg-card flex-none flex-col overflow-hidden rounded-2xl p-6 shadow mb-5"
                            (click)="loadShiftDetails(shift)"
                        >
                            <div class="flex items-start justify-between">
                                <div
                                    class="text-2xl font-semibold leading-8 tracking-tight"
                                >
                                    Shift Details
                                </div>
                            </div>
                            <div class="mt-5">
                                <div
                                    class="text-secondary font-medium tracking-tight"
                                >
                                    Start Time : {{ shift.startShift | date }}
                                </div>
                                <div
                                    class="text-secondary font-medium tracking-tight"
                                >
                                    End Time : {{ shift.endShift | date }}
                                </div>
                                <div
                                    class="text-secondary font-medium tracking-tight"
                                >
                                    Duration :
                                    {{ shift.shiftDuration | minutesToTime }}
                                </div>
                                <div
                                    class="text-secondary font-medium tracking-tight"
                                >
                                    Brakes :
                                    {{
                                        shift.totalBreakDuration | minutesToTime
                                    }}
                                </div>
                                <div
                                    class="text-secondary font-medium tracking-tight"
                                >
                                    Distance :
                                    {{ totalDistance(shift.locationGPSData) }}
                                    KM
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>

            <!-- MAP Section with Full Height -->
            <div
                class="bg-card flex flex-auto flex-col overflow-hidden rounded-2xl shadow xl:col-span-4 h-full"
            >
                <replay-map-widget
                    [class]="isGenerating() ? 'blur-sm' : ''"
                    class="w-full h-full"
                    [locationGPSData]="selectedShift()"
                    [playBackOptions]="'true'"
                />
            </div>
        </div>
    }
</div>

import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    OnDestroy,
    OnInit,
    Renderer2,
    TemplateRef,
    ViewChild,
    ViewContainerRef,
    ViewEncapsulation
} from '@angular/core';

import { TextFieldModule } from '@angular/cdk/text-field';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDrawerToggleResult } from '@angular/material/sidenav';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatRippleModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { filter, Subject, takeUntil, tap } from 'rxjs';
import { ExpensesListComponent } from '../expensesList/expensesList.component';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { FuseFindByKeyPipe } from '@fuse/pipes/find-by-key/find-by-key.pipe';
import { MatChipsModule } from '@angular/material/chips';
import { CustomMatselectModule } from 'app/modules/widgets/custom-matselect/custom-matslsct.module';
import { FileInputDropzoneModule } from 'app/modules/widgets/file-input-dropzone/file-input-dropzone.module';
import { FileInputDropzoneComponent } from 'app/modules/widgets/file-input-dropzone/file-input-dropzone.component';
import { FuseLoadingService } from '@fuse/services/loading';
import { assign } from 'lodash-es';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { ExpenseCategory, Expense } from 'app/core/databaseModels/expense/expense.types';
import { ExpenseService } from 'app/core/databaseModels/expense/expense.service';
import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';
import { MatMapsAutocompleteModule } from 'app/modules/widgets/mat-maps-autocomplete/mat-maps-autocomplete.module';

@Component({
    selector: 'expenses-details',
    templateUrl: './expensesDetails.component.html',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [FormsModule,
        MatChipsModule,
        ReactiveFormsModule,
        NgxMaskDirective,
        MatMapsAutocompleteModule,
        MatProgressSpinnerModule,
        FileInputDropzoneModule,
        FuseFindByKeyPipe,
        CustomMatselectModule,
        MatRadioModule,
        MatSelectModule,
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
        RouterLink,
        MatDividerModule,
        MatFormFieldModule,
        MatInputModule,
        TextFieldModule,
        MatRippleModule,
        MatCheckboxModule,
        MatDatepickerModule,
    ],
    providers: [provideNgxMask()]
})
export class ExpensesDetailsComponent implements OnInit, AfterViewInit, OnDestroy {


    private _unsubscribeAll: Subject<any> = new Subject<any>();
    @ViewChild('fileInputDropzone') fileInputDropzone: FileInputDropzoneComponent;
    @ViewChild('tagsPanel') private _tagsPanel: TemplateRef<any>;
    @ViewChild('tagsPanelOrigin') private _tagsPanelOrigin: ElementRef;

    private _tagsPanelOverlayRef: OverlayRef;
    _organisationID = '';
    isLoading: boolean = false;
    isSaving: boolean = false;
    editExpense: boolean = false;
    tagsEditMode: boolean = false;



    filteredTags: ExpenseCategory[] = [];
    expenseCategory: ExpenseCategory[] = [];
    expense: Expense;
    selectedExpenseId: string = '';
    expenseForm: FormGroup;
    selectedFiles: Array<{ filename: string, file: File, url: string }> = [];
    preloadedFiles: Array<{ filename: string, fileUrl: string, fileId: string }> = [];


    constructor(
        private _expenseListComponent: ExpensesListComponent,
        private _router: Router,
        private _overlay: Overlay,
        private _renderer2: Renderer2,
        private _viewContainerRef: ViewContainerRef,
        private _activatedRoute: ActivatedRoute,
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseLoadingService: FuseLoadingService,
        private _expenseService: ExpenseService

    ) {

        this.expenseForm = new FormGroup({
            $id: new FormControl(''),
            organisationID: new FormControl('', [Validators.required]),
            expenseName: new FormControl('', [Validators.required]),
            categoryTags: new FormControl(''),
            date: new FormControl('', [Validators.required]),
            description: new FormControl(''),
            amount: new FormControl('', [Validators.required]),
            expenseBy: new FormControl(''),
            attachedFiles: new FormControl([]),
            address: new FormControl(''),
            addressLatLon: new FormControl(''),
            status: new FormControl(true),
        })

    }


    ngOnInit(): void {
        this._expenseService.getOrganisationID().subscribe((organisationID) => {
            this._organisationID = organisationID;
            //console.log(this._organisationID);
        })

        this._activatedRoute.queryParamMap
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((params) => {

                const type = params.get('type');
                if (type === 'newExpense') {
                    this.editExpense = false;
                    this.expense = this.initializeToNull<Expense>();
                    this.expenseForm.get('organisationID').patchValue(this._organisationID);


                    this.expense.categoryTags = [];
                } else {
                    this.selectedExpenseId = params.get('id');
                    if (this.selectedExpenseId && this.selectedExpenseId.length > 0) {
                        this.editExpense = true;
                        this.loadExpense(this.selectedExpenseId);
                    }
                }
            })

        this._expenseListComponent.matDrawer.open();

        this.loadExpenseCategory();

    }
    ngOnDestroy(): void {

        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }
    ngAfterViewInit(): void {
        this._expenseListComponent.matDrawer.openedChange
            .pipe(
                takeUntil(this._unsubscribeAll),
                filter(opened => opened),
            )
            .subscribe(() => {
                // Focus on the title element

            });
    }

    closeDrawer(): Promise<MatDrawerToggleResult> {
        this._expenseListComponent.loadExpenses();
        return this._expenseListComponent.matDrawer.close();
    }

    clearForm(): void {
        this.expenseForm.reset();
        this.closeDrawer();
        this._router.navigate(['../'], { relativeTo: this._activatedRoute });
    }

    handleSelectedFiles(files: File[]): void {
        // process the selected files

        if (files.length > 0) {
            this.selectedFiles = files.map(file => {
                const url = URL.createObjectURL(file);
                return { filename: file.name, file: file, url: url };
            });
            //  console.log(this.selectedFiles);
        } else {
            this.selectedFiles = [];
        }
    }

    onRemovePreloadedFile(fileId: string): void {
        this._fuseLoadingService.show()
        this.isSaving = true;
        if (this.expense.attachedFiles) {

        }
        this._expenseService.deleteFile(fileId).subscribe({
            next: () => {
                this.expense.attachedFiles = this.expense.attachedFiles
                    .map(fileString => JSON.parse(fileString))
                    .filter(fileObject => fileObject.fileId !== fileId)
                    .map(fileObject => JSON.stringify(fileObject)); //
                this._expenseService.updateExpense(this.expense.$id, this.expense).subscribe({
                    next: () => {
                        this._fuseLoadingService.hide();
                        this.isSaving = false;
                        this.loadExpense(this.selectedExpenseId);
                    },
                    error: (error) => {
                        this._fuseLoadingService.hide();
                        console.error('Error saving expense', error);
                        // Handle errors here
                    },
                })
            },
            error: (error) => {
                this._fuseLoadingService.hide();
                console.error('Error saving expense', error);
                // Handle errors here
            }
        })
    }
    modifyExpense(): void {

        if (this.expenseForm.valid) {
            if (this.editExpense) {
                this.updateExpense();
            } else {
                this.createExpense();
            }
        }

    }



    createExpense(): void {
        this._fuseLoadingService.show();
        this.isSaving = true;
        try {
            this.expense = assign(this.expense, this.expenseForm.value);
            if (this.selectedFiles.length > 0) {
                const _attachedFiles = [];
                this.selectedFiles.forEach(file => {
                    this._expenseService.uploadFile(file.file).subscribe((result) => {
                        let fileObject = { fileName: file.filename, fileUrl: result.fileUrl, fileId: result.fileId };
                        let fileString = JSON.stringify(fileObject);
                        this.expense.attachedFiles.push(fileString);
                        _attachedFiles.push(fileString);
                        if (_attachedFiles.length == this.selectedFiles.length) {
                            this.saveData();
                        }

                    })
                })
            }
            else {
                this.saveData();
            }
        } catch (error) {
            console.error(error);
        }
    }

    updateExpense(): void {
        this._fuseLoadingService.show();
        this.isSaving = true;
        try {
            this.expense = assign(this.expense, this.expenseForm.value);

            if (this.selectedFiles.length > 0) {
                const _attachedFiles = [];
                this.selectedFiles.forEach(file => {
                    this._expenseService.uploadFile(file.file).subscribe((result) => {
                        let fileObject = { fileName: file.filename, fileUrl: result.fileUrl, fileId: result.fileId };
                        let fileString = JSON.stringify(fileObject);
                        this.expense.attachedFiles.push(fileString);
                        _attachedFiles.push(fileString);
                        if (_attachedFiles.length == this.selectedFiles.length) {
                            this.updateData();
                        }
                    })
                })
            } else {
                this.updateData();
            }
        } catch (error) {
            console.error(error);
        }
    }

    saveData(): void {
        try {
            this.expense.$id = null;
            this._expenseService.createExpense(this.expense).subscribe((expense) => {
                this.expense = expense;
                this._fuseLoadingService.hide();
                this.expenseForm.patchValue(expense, { emitEvent: false });
                this.isSaving = false;
                this.closeDrawer();
                this._changeDetectorRef.markForCheck();
                this._router.navigate(['../'], { relativeTo: this._activatedRoute });
            })

        } catch (error) {
            console.error(error);
        }
    }

    updateData(): void {
        try {
            this._expenseService.updateExpense(this.expense.$id, this.expense).subscribe((expense) => {
                this.expense = expense;
                this._fuseLoadingService.hide();

                this.expenseForm.patchValue(expense, { emitEvent: false });
                this.isSaving = false;
                this.closeDrawer();
                this._changeDetectorRef.markForCheck();
                this._router.navigate(['../'], { relativeTo: this._activatedRoute });
            })
        } catch (error) {
            console.error(error);
        }

    }

    loadExpenseCategory(): void {

        this._expenseService.getExpenseCategories().subscribe((data) => {
            this.expenseCategory = data;
            this.filteredTags = data;
            this._changeDetectorRef.markForCheck();
        })
    }


    loadExpense(expenseId: string): void {
        try {
            this.preloadedFiles = [];
            this.editExpense = true;
            this.isLoading = true;
            this._expenseService.getExpense(expenseId)
                .pipe(takeUntil(this._unsubscribeAll))
                .subscribe((expense) => {
                    this.expense = expense;
                    this.expenseForm.patchValue(expense, { emitEvent: false });
                    this.isLoading = false;
                    if (this.expense.attachedFiles) {
                        this.preloadedFiles = this.expense.attachedFiles.map((fileString) => {
                            let fileObject = JSON.parse(fileString);
                            return { filename: fileObject.fileName, fileUrl: fileObject.fileUrl, fileId: fileObject.fileId };
                        })
                    }
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();
                })
        } catch (error) {

        }
    }





    /**
   * Open tags panel
   */
    openTagsPanel(): void {
        // Create the overlay
        this._tagsPanelOverlayRef = this._overlay.create({
            backdropClass: '',
            hasBackdrop: true,
            scrollStrategy: this._overlay.scrollStrategies.block(),
            positionStrategy: this._overlay.position()
                .flexibleConnectedTo(this._tagsPanelOrigin.nativeElement)
                .withFlexibleDimensions(true)
                .withViewportMargin(64)
                .withLockedPosition(true)
                .withPositions([
                    {
                        originX: 'start',
                        originY: 'bottom',
                        overlayX: 'start',
                        overlayY: 'top',
                    },
                ]),
        });

        // Subscribe to the attachments observable
        this._tagsPanelOverlayRef.attachments().subscribe(() => {
            // Add a class to the origin
            this._renderer2.addClass(this._tagsPanelOrigin.nativeElement, 'panel-opened');

            // Focus to the search input once the overlay has been attached
            this._tagsPanelOverlayRef.overlayElement.querySelector('input').focus();
        });

        // Create a portal from the template
        const templatePortal = new TemplatePortal(this._tagsPanel, this._viewContainerRef);

        // Attach the portal to the overlay
        this._tagsPanelOverlayRef.attach(templatePortal);

        // Subscribe to the backdrop click
        this._tagsPanelOverlayRef.backdropClick().subscribe(() => {
            // Remove the class from the origin
            this._renderer2.removeClass(this._tagsPanelOrigin.nativeElement, 'panel-opened');

            // If overlay exists and attached...
            if (this._tagsPanelOverlayRef && this._tagsPanelOverlayRef.hasAttached()) {
                // Detach it
                this._tagsPanelOverlayRef.detach();

                // Reset the tag filter
                this.filteredTags = this.expenseCategory;

                // Toggle the edit mode off
                this.tagsEditMode = false;
            }

            // If template portal exists and attached...
            if (templatePortal && templatePortal.isAttached) {
                // Detach it
                templatePortal.detach();
            }
        });
    }
    /**
     * Toggle the tags edit mode
     */
    toggleTagsEditMode(): void {
        this.tagsEditMode = !this.tagsEditMode;
    }
    /**
       * Filter tags
       *
       * @param event
       */
    filterTags(event): void {
        // Get the value
        const value = event.target.value.toLowerCase();

        // Filter the tags
        this.filteredTags = this.expenseCategory.filter(tag => tag.title.toLowerCase().includes(value));
    }

    /**
   * Filter tags input key down event
   *
   * @param event
   */
    filterTagsInputKeyDown(event): void {
        // Return if the pressed key is not 'Enter'
        if (event.key !== 'Enter') {
            return;
        }

        // If there is no tag available...
        if (this.filteredTags.length === 0) {
            // Create the tag
            this.createExpenseCategory(event.target.value);

            // Clear the input
            event.target.value = '';

            // Return
            return;
        }

        // If there is a tag...
        const tag = this.filteredTags[0];
        const isTagApplied = this.expense.categoryTags.find(id => id === tag.$id);

        // If the found tag is already applied to the contact...
        if (isTagApplied) {
            // Remove the tag from the contact
            this.removeCategoryFromExpense(tag);
        }
        else {
            // Otherwise add the tag to the contact
            this.addCategoryToExpense(tag);
        }
    }

    updateTagTitle(tag: ExpenseCategory, event): void {
        const title = event.target.value;
        this._expenseService.updateExpenseCategory(tag.$id, title).subscribe((response) => {
            // Update the tag
            this._changeDetectorRef.markForCheck();

            this._changeDetectorRef.detectChanges();
            this._changeDetectorRef.markForCheck();
        })
    }

    deleteExpenseCategory(tag: ExpenseCategory): void {
        // Delete the tag
        //TODO: check if this is used in any other place then Delete
        console.log(tag);
        this._expenseService.deleteExpenseCategory(tag.$id).subscribe(() => {
            // Remove the tag
            this.removeCategoryFromExpense(tag);

            this._changeDetectorRef.detectChanges();
            this._changeDetectorRef.markForCheck();
        });
    }

    createExpenseCategory(title: string): void {
        const expenseCategory = {
            $id: null,
            organisationID: this._organisationID,
            title: title,
        }

        this._expenseService.createExpenseCategory(expenseCategory).subscribe((response) => {
            // Add the tag to the contact
            this.addCategoryToExpense(response);

            this._changeDetectorRef.detectChanges();
            this._changeDetectorRef.markForCheck();
        })
    }

    removeCategoryFromExpense(tag: ExpenseCategory): void {
        // Remove the tag from the contact
        this.expense.categoryTags = this.expense.categoryTags.filter(id => id !== tag.$id);
        this.expenseCategory = this.expenseCategory.filter(id => id !== tag);

        this.expenseForm.get('categoryTags').patchValue(this.expense.categoryTags);

        // Mark for check
        this._changeDetectorRef.markForCheck();
        this.loadExpenseCategory();
    }

    addCategoryToExpense(tag: ExpenseCategory): void {
        // Add the tag
        this.expense.categoryTags = [...this.expense.categoryTags, tag.$id];
        this.expenseCategory = [...this.expenseCategory, tag];
        // Update the contact form
        this.expenseForm.get('categoryTags').patchValue(this.expense.categoryTags);
        // Mark for check
        this._changeDetectorRef.markForCheck();
        this.loadExpenseCategory();

    }

    toggleExpenseTag(tag: ExpenseCategory): void {
        if (this.expense.categoryTags.includes(tag.$id)) {
            this.removeCategoryFromExpense(tag);
        }
        else {
            this.addCategoryToExpense(tag);
        }

    }

    /**
    * Should the create tag button be visible
    *
    * @param inputValue
    */
    shouldShowCreateTagButton(inputValue: string): boolean {
        return !!!(inputValue === '' || this.expenseCategory.findIndex(tag => tag.title.toLowerCase() === inputValue.toLowerCase()) > -1);
    }

    trackByFn(index: number, item: any): any {

        return item.$id || index;
    }

    onAutocompleteSelected(location: any) {
        this.expenseForm.controls.address.setValue(location.address);
        this.expenseForm.controls.addressLatLon.setValue(location.latLon);
    }

    //initializeToNull
    initializeToNull<T>(): T {
        const proxy = new Proxy({}, {
            get: () => null
        });
        // Create a new object with null values
        const obj = Object.assign({}, proxy);
        return obj as T;
    }


}

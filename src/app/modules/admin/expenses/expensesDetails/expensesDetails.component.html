<div class="flex flex-auto">
    @if (isLoading) {
        <div
            class="absolute bg-white bg-opacity-60 z-10 h-full w-full flex items-center justify-center"
        >
            <div class="flex items-center">
                <span class="text-3xl mr-4"> Loading...</span>
                <!-- loading icon -->
                <svg
                    class="animate-spin h-5 w-5 text-gray-600"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                >
                    <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                    ></circle>
                    <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                </svg>
                <!-- end loading icon -->
            </div>
        </div>
    }
    <form
        [formGroup]="expenseForm"
        (ngSubmit)="modifyExpense()"
        (keydown.enter)="$event.preventDefault()"
        class="flex flex-col flex-auto p-6 pt-10 sm:p-8 sm:pt-10 overflow-y-auto"
    >
        <!-- Header -->
        <div class="flex items-center justify-between -mt-3 -ml-4">
            <div class="pr-4 pl-3.5">
                <p
                    class="flex items-center justify-center text-2xl font-extrabold"
                >
                    @if (!editExpense) {
                        Add new Expense
                    } @else {
                        Edit Expense
                    }
                </p>
            </div>
            <div class="flex items-center">
                <!-- Close button -->
                <a mat-icon-button [routerLink]="['../']">
                    <mat-icon [svgIcon]="'heroicons_outline:x-mark'"></mat-icon>
                </a>
            </div>
        </div>
        <mat-divider class="mt-6 mb-8"></mat-divider>
        <div>
            <p class="text-lg font-medium">Expense Details</p>
            <p class="text-secondary mb-6">
                To ensure accurate and efficient processing of your expense
                report, please provide comprehensive details for each expense
                incurred. This should include a clear description of the nature
                of the expense, along with the specific location where it was
                incurred.
            </p>
            <div class="flex">
                <mat-form-field class="flex-auto">
                    <input
                        matInput
                        formControlName="expenseName"
                        [placeholder]="'Expense Name'"
                    />

                    <i
                        matPrefix
                        class="icon-size-5 mr-3 fa-duotone fa-solid fa-comment-dollar"
                    ></i>
                </mat-form-field>
            </div>
            <div class="flex flex-col">
                <span class="font-semibold">Expense Category:</span>
                <!-- Tags -->
                <div class="flex flex-wrap items-center -m-1.5 mt-3 mb-3">
                    <!-- Tags -->

                    @if (
                        expenseCategory &&
                        expense &&
                        expense.categoryTags.length &&
                        expenseCategory.length
                    ) {
                        @for (
                            tag of expense.categoryTags
                                | fuseFindByKey: "$id" : expenseCategory;
                            track trackByFn($index, tag)
                        ) {
                            <div
                                class="flex items-center justify-center px-4 m-1.5 rounded-full leading-9 text-gray-500 bg-gray-100 dark:text-gray-300 dark:bg-gray-700"
                            >
                                <span
                                    class="text-md font-medium whitespace-nowrap"
                                    >{{ tag.title }}</span
                                >
                            </div>
                        }
                    }
                    <!-- Tags panel and its button -->
                    <div
                        class="flex items-center justify-center px-4 m-1.5 rounded-full leading-9 cursor-pointer text-gray-500 bg-gray-100 dark:text-gray-300 dark:bg-gray-700"
                        (click)="openTagsPanel()"
                        #tagsPanelOrigin
                    >
                        @if (expense && expense.categoryTags.length) {
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_solid:pencil-square'"
                            ></mat-icon>
                            <span
                                class="ml-1.5 text-md font-medium whitespace-nowrap"
                                >Edit</span
                            >
                        }

                        @if (!expense || !expense.categoryTags.length) {
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_solid:plus-circle'"
                            ></mat-icon>
                            <span
                                class="ml-1.5 text-md font-medium whitespace-nowrap"
                                >Add</span
                            >
                        }

                        <!-- Tags panel -->
                        <ng-template #tagsPanel>
                            <div class="w-60 rounded border shadow-md bg-card">
                                <!-- Tags panel header -->
                                <div class="flex items-center m-3 mr-2">
                                    <div class="flex items-center">
                                        <mat-icon
                                            class="icon-size-5"
                                            [svgIcon]="
                                                'heroicons_solid:magnifying-glass'
                                            "
                                        ></mat-icon>
                                        <div class="ml-2">
                                            <input
                                                class="w-full min-w-0 py-1 border-0"
                                                type="text"
                                                placeholder="Enter tag name"
                                                (input)="filterTags($event)"
                                                (keydown)="
                                                    filterTagsInputKeyDown(
                                                        $event
                                                    )
                                                "
                                                [maxLength]="30"
                                                #newTagInput
                                            />
                                        </div>
                                    </div>
                                    <button
                                        class="ml-1"
                                        mat-icon-button
                                        (click)="toggleTagsEditMode()"
                                    >
                                        @if (!tagsEditMode) {
                                            <mat-icon
                                                class="icon-size-5"
                                                [svgIcon]="
                                                    'heroicons_solid:pencil-square'
                                                "
                                            ></mat-icon>
                                        }
                                        @if (tagsEditMode) {
                                            <mat-icon
                                                class="icon-size-5"
                                                [svgIcon]="
                                                    'heroicons_solid:check'
                                                "
                                            ></mat-icon>
                                        }
                                    </button>
                                </div>
                                <div
                                    class="flex flex-col max-h-64 py-2 border-t overflow-y-auto"
                                >
                                    <!-- Tags -->
                                    @if (!tagsEditMode) {
                                        @for (
                                            tag of filteredTags;
                                            track trackByFn($index, tag)
                                        ) {
                                            <div
                                                class="flex items-center h-10 min-h-10 pl-1 pr-4 cursor-pointer hover:bg-hover"
                                                (click)="toggleExpenseTag(tag)"
                                                matRipple
                                            >
                                                <mat-checkbox
                                                    class="flex items-center h-10 min-h-10 pointer-events-none"
                                                    [checked]="
                                                        expense.categoryTags.includes(
                                                            tag.$id
                                                        )
                                                    "
                                                    [color]="'primary'"
                                                    [disableRipple]="true"
                                                >
                                                </mat-checkbox>
                                                <div>{{ tag.title }}</div>
                                            </div>
                                        }
                                    }
                                    <!-- Tags editing -->
                                    @if (tagsEditMode) {
                                        <div class="py-2 space-y-2">
                                            @for (
                                                tag of filteredTags;
                                                track trackByFn($index, tag)
                                            ) {
                                                <div class="flex items-center">
                                                    <mat-form-field
                                                        class="fuse-mat-dense w-full mx-4"
                                                        [subscriptSizing]="
                                                            'dynamic'
                                                        "
                                                    >
                                                        <input
                                                            matInput
                                                            [value]="tag.title"
                                                            (input)="
                                                                updateTagTitle(
                                                                    tag,
                                                                    $event
                                                                )
                                                            "
                                                        />
                                                        <button
                                                            mat-icon-button
                                                            (click)="
                                                                deleteExpenseCategory(
                                                                    tag
                                                                )
                                                            "
                                                            matSuffix
                                                        >
                                                            <mat-icon
                                                                class="icon-size-5 ml-2"
                                                                [svgIcon]="
                                                                    'heroicons_solid:trash'
                                                                "
                                                            ></mat-icon>
                                                        </button>
                                                    </mat-form-field>
                                                </div>
                                            }
                                        </div>
                                    }
                                    <!-- Create tag -->
                                    @if (
                                        shouldShowCreateTagButton(
                                            newTagInput.value
                                        )
                                    ) {
                                        <div
                                            class="flex items-center h-10 min-h-10 -ml-0.5 pl-4 pr-3 leading-none cursor-pointer hover:bg-hover"
                                            (click)="
                                                createExpenseCategory(
                                                    newTagInput.value
                                                );
                                                newTagInput.value = ''
                                            "
                                            matRipple
                                        >
                                            <mat-icon
                                                class="mr-2 icon-size-5"
                                                [svgIcon]="
                                                    'heroicons_solid:plus-circle'
                                                "
                                            ></mat-icon>
                                            <div class="break-all">
                                                Create "<b>{{
                                                    newTagInput.value
                                                }}</b
                                                >"
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        </ng-template>
                    </div>
                </div>
            </div>
            <div class="flex">
                <mat-form-field class="flex-auto w-full">
                    <mat-label>Expense Date</mat-label>

                    <i
                        matPrefix
                        class="hidden sm:flex icon-size-5 mr-4 fa-duotone fa-duotone fa-solid fa-calendar-check"
                    ></i>
                    <input
                        matInput
                        [matDatepicker]="ExpenseDatepicker"
                        [formControlName]="'date'"
                        [placeholder]="'Expense Date'"
                    />
                    <mat-datepicker-toggle matSuffix [for]="ExpenseDatepicker">
                    </mat-datepicker-toggle>
                    <mat-datepicker #ExpenseDatepicker></mat-datepicker>
                </mat-form-field>
            </div>
            <div class="flex">
                <mat-form-field class="flex-auto">
                    <input
                        prefix="$ "
                        mask="separator.2"
                        thousandSeparator=","
                        decimalMarker="."
                        [allowNegativeNumbers]="false"
                        class="text-right"
                        matInput
                        formControlName="amount"
                        [placeholder]="'Amount'"
                    />
                    <i
                        class="icon-size-5 mr-3 fa-duotone fa-solid fa-square-dollar"
                        matPrefix
                    ></i>
                </mat-form-field>
            </div>
            <div class="flex">
                <mat-form-field class="flex-auto">
                    <textarea
                        matInput
                        formControlName="description"
                        [placeholder]="'Expense description'"
                        [rows]="3"
                    ></textarea>
                    <mat-icon
                        class="icon-size-5"
                        matPrefix
                        [svgIcon]="'heroicons_solid:bars-3-bottom-left'"
                    ></mat-icon>
                </mat-form-field>
            </div>
            <div class="flex">
                <mat-form-field class="flex-auto">
                    <input
                        matInput
                        formControlName="expenseBy"
                        [placeholder]="'Expense By'"
                    />
                    <i
                        matPrefix
                        class="icon-size-5 mr-3 fa-duotone fa-solid fa-image-polaroid-user"
                    ></i>
                </mat-form-field>
            </div>
            <div class="flex">
                <mat-form-field class="flex-auto">
                    <input
                        matInput
                        [formControlName]="'address'"
                        [spellcheck]="false"
                        matMapsAutocomplete
                        [country]="'au'"
                        [placeholder]="'Enter expense location'"
                        (onAutocompleteSelected)="
                            onAutocompleteSelected($event)
                        "
                    />
                    <mat-icon
                        class="icon-size-5"
                        matPrefix
                        [svgIcon]="'heroicons_solid:map-pin'"
                    ></mat-icon>
                </mat-form-field>
            </div>
            <!-- Attach Files -->
            <div class="mt-8">
                <label class="font-medium">Attach Files:</label>
                <file-input-dropzone
                    [multiple]="true"
                    (selectedFiles)="handleSelectedFiles($event)"
                    [preloadedFiles]="preloadedFiles"
                    (removePreloadedFile)="onRemovePreloadedFile($event)"
                    #fileInputDropzone
                ></file-input-dropzone>
                <mat-divider class="mt-6 mb-10"></mat-divider>
            </div>

            <!-- Actions -->
            @if (isSaving) {
                <div
                    class="flex justify-center items-center mt-10 -mx-6 sm:-mx-12 py-4 pr-4 pl-1 sm:pr-12 sm:pl-7 border-t bg-gray-50 dark:bg-transparent"
                >
                    <mat-progress-spinner
                        [diameter]="24"
                        [mode]="'indeterminate'"
                    ></mat-progress-spinner>
                </div>
            }
            @if (!isSaving) {
                <div
                    class="flex items-center justify-end border-t -mx-8 mt-8 px-8 py-5 bg-gray-50 dark:bg-gray-700"
                >
                    <button mat-button (click)="clearForm()">Cancel</button>
                    <button
                        class="px-6 ml-3"
                        mat-flat-button
                        [color]="'primary'"
                        [disabled]="expenseForm.invalid"
                        type="submit"
                    >
                        Save
                    </button>
                </div>
            }
        </div>
    </form>
</div>

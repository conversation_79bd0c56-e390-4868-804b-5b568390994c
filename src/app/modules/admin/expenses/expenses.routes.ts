import { ActivatedRouteSnapshot, RouterStateSnapshot, Routes } from '@angular/router';
import { ExpensesComponent } from './expenses.component';
import { ExpensesService } from './expenses.service';
import { inject } from '@angular/core';
import { ExpensesListComponent } from './expensesList/expensesList.component';
import { ExpensesDetailsComponent } from './expensesDetails/expensesDetails.component';

const canDeactivateExpenseDetails = (
    component: ExpensesDetailsComponent,
    currentRoute: ActivatedRouteSnapshot,
    currentState: RouterStateSnapshot,
    nextState: RouterStateSnapshot) => {
    // Get the next route
    let nextRoute: ActivatedRouteSnapshot = nextState.root;
    while (nextRoute.firstChild) {
        nextRoute = nextRoute.firstChild;
    }

    // If the next state doesn't contain '/tasks'
    // it means we are navigating away from the
    // tasks app
    if (!nextState.url.includes('/expenses')) {
        // Let it navigate
        return true;
    }

    // If we are navigating to another task...
    if (nextRoute.paramMap.get('id')) {
        // Just navigate
        return true;
    }

    // Otherwise, close the drawer first, and then navigate
    return component.closeDrawer().then(() => true);
};


export default [
    {
        path: '',
        component: ExpensesComponent,
        resolve: {
            data: () => inject(ExpensesService).getData(),
        },
        children: [
            {
                path: '',
                component: ExpensesListComponent,
                resolve: {
                    data: () => inject(ExpensesService).getData(),
                },
                children: [
                    {
                        path: ':id',
                        component: ExpensesDetailsComponent,
                        canDeactivate: [canDeactivateExpenseDetails],
                    }
                ]
            },
        ],
    },
] as Routes;

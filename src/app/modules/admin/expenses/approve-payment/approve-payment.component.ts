import { DatePipe } from '@angular/common';
import { Component, OnInit, Signal, ViewEncapsulation, inject } from '@angular/core';
import {
    FormsModule,
    ReactiveFormsModule,
    UntypedFormBuilder,
    UntypedFormGroup,
    Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { FuseLoadingService } from '@fuse/services/loading';
import { ExpenseService } from 'app/core/databaseModels/expense/expense.service';
import { Expense } from 'app/core/databaseModels/expense/expense.types';
import { assign } from 'lodash';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';


@Component({
    selector: 'app-approve-payment',
    encapsulation: ViewEncapsulation.None,
    imports: [
        MatButtonModule,
        MatIconModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        DatePipe,
        MatSlideToggleModule,
        MatDatepickerModule,
        MatSelectModule,
        NgxMaskDirective,
        MatProgressSpinnerModule,
    ],
    providers: [provideNgxMask()],
    templateUrl: './approve-payment.component.html',
    styleUrl: './approve-payment.component.scss'
})
export class ApprovePaymentComponent implements OnInit {

    paymentForm: UntypedFormGroup;
    data: { expense: Expense } = inject(MAT_DIALOG_DATA);
    selectedExpense: Expense;
    isSaving: boolean = false;

    constructor(
        public matDialogRef: MatDialogRef<ApprovePaymentComponent>,
        private _expenseService: ExpenseService,
        private _formBuilder: UntypedFormBuilder,
        private _fuseLoadingService: FuseLoadingService,
    ) { }

    ngOnInit(): void {
        // Create the form
        this.paymentForm = this._formBuilder.group({
            paymentStatus: [''],
            paymentDate: [''],
            paymentAmount: [''],
            paymentMethod: [''],
            paymentRef: [''],
            status: [''],
        });
        if (this.data && this.data.expense) {
            this.fillPaymentForm(this.data.expense);
            // set selected expense

        }
        // console.log(this.selectedExpense());
    }

    fillPaymentForm(expense: Expense): void {
        // Map the relevant properties from the expense object to the form controls
        this.paymentForm.patchValue({
            paymentStatus: expense.paymentStatus,         // Adjust property names as per your Expense model
            paymentDate: expense.paymentDate,      // Ensure this is in a compatible format (e.g., Date object)
            paymentAmount: expense.paymentAmount,         // Adjust property names accordingly
            paymentMethod: expense.paymentMethod,         // Adjust property names accordingly
            paymentRef: expense.paymentRef,
            status: expense.status,         // Adjust property names accordingly
        });
        this.selectedExpense = expense;

        console.log(this.selectedExpense);


    }
    fileDetails(file: string): void {
        return JSON.parse(file);
    }
    discard(): void {
        this.matDialogRef.close();
    }

    updateExpense(): void {
        this._fuseLoadingService.show();
        this.isSaving = true;
        this.paymentForm.disable();
        try {
            this.selectedExpense = assign(this.selectedExpense, this.paymentForm.value);

            this._expenseService.updateExpense(this.selectedExpense.$id, this.selectedExpense).subscribe((expense) => {
                this.isSaving = false;
                this._fuseLoadingService.hide();
                this.matDialogRef.close();
            });

        } catch (error) {
            console.error(error);
        }
    }


}

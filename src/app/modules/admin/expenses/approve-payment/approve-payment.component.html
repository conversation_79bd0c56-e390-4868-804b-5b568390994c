<div class="-m-6 flex max-h-screen max-w-240 flex-col md:min-w-160">
    <!-- Header -->
    <div
        class="flex h-16 flex-0 items-center justify-between bg-primary pl-6 pr-3 text-on-primary sm:pl-8 sm:pr-5"
    >
        <div class="text-lg font-medium">Approve Payment & Payment Details</div>
        <button mat-icon-button (click)="discard()" [tabIndex]="-1">
            <mat-icon
                class="text-current"
                [svgIcon]="'heroicons_outline:x-mark'"
            ></mat-icon>
        </button>
    </div>

    <!-- Payment form -->

    <form
        class="flex flex-auto flex-col overflow-y-auto p-6 sm:p-8"
        [formGroup]="paymentForm"
    >
        @if (!selectedExpense) {
            <div
                class="flex flex-auto flex-col items-center justify-center bg-gray-100 dark:bg-transparent"
            >
                <mat-spinner
                    color="primary"
                    mode="indeterminate"
                    [diameter]="40"
                ></mat-spinner>
            </div>
        } @else {
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <div
                        class="flex flex-col flex-auto p-6 bg-card rounded-2xl shadow"
                    >
                        <div class="mt-6">Expense bill details.</div>
                        <hr class="w-full border-t my-6" />
                        <div class="flex flex-col">
                            <div class="flex items-center">
                                <i
                                    class="icon-size-5 mr-3 fa-duotone fa-file-invoice"
                                ></i>
                                <span class="leading-none">{{
                                    selectedExpense.expenseName
                                }}</span>
                            </div>
                            <div class="flex items-center mt-4">
                                <i
                                    class="icon-size-5 mr-3 fa-duotone fa-calendar-days"
                                ></i>
                                <span class="leading-none">{{
                                    selectedExpense.date
                                        | date: "EEE, dd MMM yyyy"
                                }}</span>
                            </div>
                            <div class="flex items-center mt-4">
                                <i
                                    class="icon-size-5 mr-3 fa-duotone fa-circle-dollar"
                                ></i>
                                <span class="leading-none">{{
                                    selectedExpense.amount
                                }}</span>
                            </div>
                            <div class="flex items-center mt-4">
                                <i
                                    class="icon-size-5 mr-3 fa-duotone fa-align-left"
                                ></i>
                                <p class="leading-none">
                                    {{ selectedExpense.description }}
                                </p>
                            </div>
                            <div class="flex items-center mt-4">
                                <i
                                    class="icon-size-5 mr-3 fa-duotone fa-image-polaroid-user"
                                ></i>
                                <p class="leading-none">
                                    {{ selectedExpense.expenseBy }}
                                </p>
                            </div>
                            @if (selectedExpense.address) {
                                <div class="flex items-center mt-4">
                                    <i
                                        class="icon-size-5 mr-3 fa-duotone fa-location-dot"
                                    ></i>
                                    <span class="leading-none">{{
                                        selectedExpense.address
                                    }}</span>
                                </div>
                            }
                        </div>

                        <!-- Attached files -->
                        <div>
                            @if (selectedExpense.attachedFiles.length > 0) {
                                @for (
                                    file of selectedExpense.attachedFiles;
                                    track file
                                ) {
                                    <div class="flex flex-wrap -m-3 mt-3">
                                        <span></span>

                                        <div
                                            class="flex items-center m-3 ng-star-inserted"
                                        >
                                            @if (
                                                fileDetails(file)
                                                    .fileName.split(".")
                                                    .pop() == "pdf"
                                            ) {
                                                <div
                                                    class="flex items-center justify-center w-10 h-10 rounded-md overflow-hidden bg-primary-100 ng-star-inserted"
                                                >
                                                    <div
                                                        class="flex items-center justify-center text-sm font-semibold text-primary-500-800"
                                                    >
                                                        PDF
                                                    </div>
                                                </div>
                                            } @else {
                                                <img
                                                    class="w-10 h-10 rounded-md overflow-hidden ng-star-inserted"
                                                    [src]="
                                                        fileDetails(file)
                                                            .fileUrl
                                                    "
                                                />
                                            }

                                            <div class="ml-3">
                                                <div
                                                    class="text-md font-medium overflow-hidden"
                                                    [title]="
                                                        fileDetails(file)
                                                            .fileName
                                                    "
                                                >
                                                    {{
                                                        fileDetails(file)
                                                            .fileName
                                                    }}
                                                </div>
                                                <div
                                                    class="text-sm font-medium truncate text-secondary"
                                                    title="Preview file"
                                                >
                                                    <a
                                                        [href]="
                                                            fileDetails(file)
                                                                .fileUrl
                                                        "
                                                        target="_blank"
                                                        >Preview</a
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            } @else {
                                <div
                                    class="flex flex-auto flex-col items-center"
                                >
                                    <div class="mt-6 font-bold text-lg">
                                        No receipt attached.
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
                <div>
                    <div class="mt-8 grid w-full grid-cols-4 gap-6">
                        <!-- Approval payment -->

                        <div
                            class="flex items-center justify-between sm:col-span-4"
                        >
                            <div
                                class="flex-auto cursor-pointer"
                                (click)="approvalToggle.toggle()"
                            >
                                <div class="font-medium leading-6">
                                    Payment Approved Status
                                </div>
                                @if (paymentForm.get("status").value) {
                                    <div class="text-secondary text-md">
                                        The payment is approved. If you want to
                                        disapprove the payment, check the
                                        toggle.
                                    </div>
                                } @else {
                                    <div class="text-secondary text-md">
                                        By default, the payment is not approved.
                                        If you want to approve the payment,
                                        check the toggle.
                                    </div>
                                }
                            </div>
                            <mat-slide-toggle
                                class="ml-4"
                                [color]="'primary'"
                                [formControlName]="'status'"
                                #approvalToggle
                            >
                            </mat-slide-toggle>
                        </div>
                        <!-- Payment Status -->
                        <div class="sm:col-span-2">
                            <mat-form-field
                                class="w-full"
                                [subscriptSizing]="'dynamic'"
                            >
                                <mat-label>Payment Status</mat-label>

                                <i
                                    matPrefix
                                    class="icon-size-5 mr-3 fa-duotone fa-solid fa-money-bill-transfer"
                                ></i>

                                <mat-select [formControlName]="'paymentStatus'">
                                    <mat-option [value]="'usa'"
                                        >Paid</mat-option
                                    >
                                    <mat-option [value]="'canada'"
                                        >Not Paid</mat-option
                                    >
                                </mat-select>
                            </mat-form-field>
                        </div>
                        <!-- Payment Method -->
                        <div class="sm:col-span-2">
                            <mat-form-field
                                class="w-full"
                                [subscriptSizing]="'dynamic'"
                            >
                                <mat-label>Payment Method</mat-label>

                                <i
                                    matPrefix
                                    class="icon-size-5 mr-3 fa-sharp-duotone fa-solid fa-money-check-dollar-pen"
                                ></i>

                                <mat-select [formControlName]="'paymentMethod'">
                                    <mat-option [value]="'moneyTransfer'"
                                        >Money Transfer</mat-option
                                    >
                                    <mat-option [value]="'debit'"
                                        >Debit</mat-option
                                    >
                                    <mat-option [value]="'cash'"
                                        >Cash</mat-option
                                    >
                                </mat-select>
                            </mat-form-field>
                        </div>
                        <!-- payment ref -->
                        <div class="col-span-4">
                            <mat-form-field
                                class="w-full"
                                [subscriptSizing]="'dynamic'"
                            >
                                <mat-label>Payment Reference</mat-label>

                                <i
                                    matPrefix
                                    class="icon-size-5 mr-3 fa-duotone fa-solid fa-file-invoice-dollar"
                                ></i>
                                <input
                                    [formControlName]="'paymentRef'"
                                    matInput
                                />
                            </mat-form-field>
                        </div>

                        <!-- payment date -->
                        <div class="sm:col-span-2">
                            <mat-form-field
                                class="w-full"
                                [subscriptSizing]="'dynamic'"
                            >
                                <mat-label>Payment Date</mat-label>
                                <i
                                    matPrefix
                                    class="icon-size-5 mr-3 fa-duotone fa-solid fa-calendar"
                                ></i>
                                <input
                                    [formControlName]="'paymentDate'"
                                    matInput
                                    [matDatepicker]="picker"
                                />
                                <mat-datepicker-toggle
                                    matSuffix
                                    [for]="picker"
                                ></mat-datepicker-toggle>
                                <mat-datepicker
                                    #picker
                                    [startView]="'multi-year'"
                                    [view]="'multi-year'"
                                ></mat-datepicker>
                            </mat-form-field>
                        </div>
                        <!-- payment Amount -->
                        <div class="sm:col-span-2">
                            <mat-form-field
                                class="w-full"
                                [subscriptSizing]="'dynamic'"
                            >
                                <mat-label>Payment Amount</mat-label>
                                <i
                                    matPrefix
                                    class="icon-size-5 mr-3 fa-duotone fa-solid fa-money-bill"
                                ></i>

                                <input
                                    prefix="$ "
                                    mask="separator.2"
                                    thousandSeparator=","
                                    decimalMarker="."
                                    [allowNegativeNumbers]="false"
                                    class="text-right"
                                    matInput
                                    [formControlName]="'paymentAmount'"
                                    [placeholder]="'Amount'"
                                />
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </div>
        }
        <!-- Actions -->
        <div
            class="mt-4 flex flex-col justify-between sm:mt-6 sm:flex-row sm:items-center"
        >
            <div class="-ml-2"></div>

            <div class="mt-4 flex items-center sm:mt-0">
                <!-- Discard -->
                <button
                    class="ml-auto sm:ml-0"
                    mat-stroked-button
                    (click)="discard()"
                >
                    Discard
                </button>

                <!-- Send -->
                <button
                    class="order-first sm:order-last ml-5"
                    mat-flat-button
                    [color]="'primary'"
                    (click)="updateExpense()"
                >
                    @if (isSaving) {
                        <mat-spinner diameter="20"></mat-spinner>
                        <span class="ml-2"> Saving...</span>
                    } @else {
                        Save
                    }
                </button>
            </div>
        </div>
    </form>
</div>

<div class="absolute inset-0 flex flex-col min-w-0 overflow-hidden">
    <mat-drawer-container
        class="flex-auto h-full bg-card dark:bg-transparent"
        (backdropClick)="onBackdropClicked()"
    >
        <!-- Drawer -->
        <mat-drawer
            class="w-full sm:w-128 dark:bg-gray-900"
            [mode]="drawerMode"
            [opened]="false"
            [position]="'end'"
            [disableClose]="true"
            #matDrawer
        >
            <router-outlet></router-outlet>
        </mat-drawer>
        <mat-drawer-content class="flex flex-col">
            <div
                class="flex flex-wrap w-full max-w-screen-xl mx-auto p-6 md:p-8"
            >
                <!-- Title and action buttons -->
                <div class="flex items-center justify-between w-full">
                    <div>
                        <h2
                            class="text-3xl font-semibold tracking-tight leading-8"
                        >
                            Expenses
                        </h2>
                        <div class="font-medium tracking-tight text-secondary">
                            Keep track of your expenses status
                        </div>
                    </div>
                    <div class="flex items-center ml-6">
                        <button
                            (click)="createExpense('newExpense')"
                            class="hidden sm:inline-flex"
                            mat-flat-button
                            [color]="'primary'"
                        >
                            <i
                                class="icon-size-5 fa-duotone fa-file-invoice-dollar"
                            ></i>
                            <span class="ml-2">Add new expense</span>
                        </button>

                        <!-- Actions menu (visible on xs) -->
                        <div class="sm:hidden">
                            <button
                                [matMenuTriggerFor]="actionsMenu"
                                mat-icon-button
                            >
                                <mat-icon
                                    [svgIcon]="
                                        'heroicons_mini:ellipsis-vertical'
                                    "
                                ></mat-icon>
                            </button>
                            <mat-menu #actionsMenu="matMenu">
                                <button mat-menu-item>Add new expense</button>
                            </mat-menu>
                        </div>
                    </div>
                </div>
                <!-- Expenses -->

                <div
                    class="grid grid-cols-1 gap-8 w-full mt-8"
                    [ngClass]="
                        selectedExpense ? 'xl:grid-cols-3' : 'xl:grid-cols-1'
                    "
                >
                    <!-- Recent transactions table -->
                    <div
                        class="xl:col-span-2 flex flex-col flex-auto bg-card shadow rounded-2xl overflow-hidden"
                    >
                        @if (expenses && expenses.length > 0) {
                            <!-- Recent transactions table -->
                            <div
                                class="xl:col-span-2 flex flex-col flex-auto bg-card shadow rounded-2xl overflow-hidden"
                            >
                                <div
                                    class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b"
                                >
                                    <!-- Title -->
                                    <div
                                        class="text-lg font-extrabold tracking-tight"
                                    >
                                        Expenses List
                                    </div>
                                    <!-- Actions -->
                                    <div
                                        class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4"
                                    >
                                        <!-- Search -->
                                        <mat-form-field
                                            class="fuse-mat-dense fuse-mat-rounded min-w-64"
                                            [subscriptSizing]="'dynamic'"
                                        >
                                            <mat-icon
                                                class="icon-size-5"
                                                matPrefix
                                                [svgIcon]="
                                                    'heroicons_solid:magnifying-glass'
                                                "
                                            ></mat-icon>
                                            <input
                                                matInput
                                                (keyup)="applyFilter($event)"
                                                [autocomplete]="'off'"
                                                [placeholder]="'Search ...'"
                                                #input
                                            />
                                        </mat-form-field>
                                        <button
                                            mat-icon-button
                                            [matMenuTriggerFor]="moreMenu"
                                        >
                                            <i
                                                class="ml-3 icon-size-5 fa-duotone fa-file-export"
                                            ></i>
                                        </button>
                                        <mat-menu #moreMenu="matMenu">
                                            <button
                                                mat-menu-item
                                                (click)="
                                                    exportMaterialTable('pdf')
                                                "
                                            >
                                                <i
                                                    class="fa-duotone fa-file-pdf"
                                                ></i>
                                                <span class="ml-3">PDF</span>
                                            </button>
                                            <button
                                                mat-menu-item
                                                (click)="
                                                    exportMaterialTable('excel')
                                                "
                                            >
                                                <i
                                                    class="fa-duotone fa-file-excel"
                                                ></i>
                                                <span class="ml-3">EXCEL</span>
                                            </button>
                                        </mat-menu>
                                    </div>
                                </div>

                                <div class="overflow-x-auto mx-6">
                                    <div class="w-full bg-transparent">
                                        <table
                                            mat-table
                                            [dataSource]="expensesDataSource"
                                            matSort
                                        >
                                            <!-- Progress Column -->
                                            <ng-container
                                                matColumnDef="expenseName"
                                            >
                                                <th
                                                    mat-header-cell
                                                    *matHeaderCellDef
                                                    mat-sort-header
                                                >
                                                    Expense Name
                                                </th>
                                                <td
                                                    mat-cell
                                                    *matCellDef="let row"
                                                    (click)="
                                                        previewExpense(row)
                                                    "
                                                    class="cursor-pointer"
                                                >
                                                    {{ row.expenseName }}
                                                </td>
                                            </ng-container>

                                            <!-- date Column -->
                                            <ng-container matColumnDef="date">
                                                <th
                                                    mat-header-cell
                                                    *matHeaderCellDef
                                                    mat-sort-header
                                                >
                                                    Date
                                                </th>
                                                <td
                                                    mat-cell
                                                    *matCellDef="let row"
                                                    (click)="
                                                        previewExpense(row)
                                                    "
                                                    class="cursor-pointer"
                                                >
                                                    {{
                                                        row.date
                                                            | date
                                                                : "EEE, dd MMM yyyy"
                                                    }}
                                                </td>
                                            </ng-container>

                                            <!-- amount Column -->
                                            <ng-container matColumnDef="amount">
                                                <th
                                                    mat-header-cell
                                                    *matHeaderCellDef
                                                    mat-sort-header
                                                >
                                                    Amount
                                                </th>
                                                <td
                                                    mat-cell
                                                    *matCellDef="let row"
                                                    class="text-right font-bold"
                                                    (click)="
                                                        previewExpense(row)
                                                    "
                                                    class="cursor-pointer"
                                                >
                                                    ${{ row.amount }}
                                                </td>
                                            </ng-container>
                                            <!-- categoryTags -->
                                            <ng-container
                                                matColumnDef="categoryTags"
                                            >
                                                <th
                                                    mat-header-cell
                                                    *matHeaderCellDef
                                                    mat-sort-header
                                                >
                                                    Category
                                                </th>
                                                <td
                                                    mat-cell
                                                    *matCellDef="let row"
                                                    (click)="
                                                        previewExpense(row)
                                                    "
                                                    class="cursor-pointer"
                                                >
                                                    @for (
                                                        categoryName of getExpenseCategoryName(
                                                            row.categoryTags
                                                        );
                                                        track categoryName
                                                    ) {
                                                        <span
                                                            class="m-1 py-0.5 px-3 rounded-full text-sm font-medium text-gray-500 bg-gray-100 dark:text-gray-300 dark:bg-gray-700 ng-star-inserted"
                                                            >{{
                                                                categoryName
                                                            }}</span
                                                        >
                                                    }
                                                </td>
                                            </ng-container>
                                            <!--Status-->
                                            <ng-container matColumnDef="status">
                                                <th
                                                    mat-header-cell
                                                    *matHeaderCellDef
                                                    mat-sort-header
                                                >
                                                    Status
                                                </th>
                                                <td
                                                    mat-cell
                                                    *matCellDef="let row"
                                                    (click)="
                                                        previewExpense(row)
                                                    "
                                                >
                                                    <span
                                                        class="inline-flex items-center font-bold text-xs px-2.5 py-0.5 rounded-full tracking-wide uppercase"
                                                        [ngClass]="{
                                                            'bg-red-200 text-red-800 dark:bg-red-600 dark:text-red-50':
                                                                row.status ===
                                                                false,
                                                            'bg-green-200 text-green-800 dark:bg-green-600 dark:text-green-50':
                                                                row.status ===
                                                                true
                                                        }"
                                                    >
                                                        <span
                                                            class="leading-relaxed whitespace-nowrap"
                                                            >{{
                                                                row.status
                                                                    ? "Approved"
                                                                    : "Pending"
                                                            }}</span
                                                        >
                                                    </span>
                                                </td>
                                            </ng-container>
                                            <!-- Actions Column -->
                                            <ng-container
                                                matColumnDef="actions"
                                            >
                                                <th
                                                    mat-header-cell
                                                    *matHeaderCellDef
                                                >
                                                    Actions
                                                </th>
                                                <td
                                                    mat-cell
                                                    *matCellDef="let row"
                                                >
                                                    <button
                                                        mat-icon-button
                                                        (click)="
                                                            createExpense(
                                                                'editService',
                                                                row
                                                            )
                                                        "
                                                    >
                                                        <mat-icon
                                                            [svgIcon]="
                                                                'heroicons_outline:pencil'
                                                            "
                                                        ></mat-icon>
                                                    </button>
                                                    <button
                                                        mat-icon-button
                                                        [matMenuTriggerFor]="
                                                            moreMenu
                                                        "
                                                    >
                                                        <mat-icon
                                                            [svgIcon]="
                                                                'heroicons_outline:ellipsis-vertical'
                                                            "
                                                        ></mat-icon>
                                                    </button>
                                                    <mat-menu
                                                        #moreMenu="matMenu"
                                                    >
                                                        <button
                                                            mat-menu-item
                                                            (click)="
                                                                previewExpense(
                                                                    row
                                                                )
                                                            "
                                                        >
                                                            <i
                                                                class="mr-3 icon-size-5 fa-duotone fa-eye"
                                                            ></i>
                                                            <span>Preview</span>
                                                        </button>
                                                        <button
                                                            mat-menu-item
                                                            (click)="
                                                                approvePayment(
                                                                    row
                                                                )
                                                            "
                                                        >
                                                            <i
                                                                class="mr-3 icon-size-5 fa-duotone fa-file-invoice"
                                                            ></i>
                                                            <span
                                                                >Approve /
                                                                Payment</span
                                                            >
                                                        </button>
                                                        <button
                                                            mat-menu-item
                                                            (click)="
                                                                deleteItem(row)
                                                            "
                                                        >
                                                            <mat-icon
                                                                [svgIcon]="
                                                                    'heroicons_outline:trash'
                                                                "
                                                            ></mat-icon>
                                                            <span>Delete</span>
                                                        </button>
                                                    </mat-menu>
                                                </td>
                                            </ng-container>

                                            <tr
                                                mat-header-row
                                                *matHeaderRowDef="
                                                    expensesTableColumns
                                                "
                                            ></tr>
                                            <tr
                                                mat-row
                                                *matRowDef="
                                                    let row;
                                                    columns: expensesTableColumns
                                                "
                                            ></tr>

                                            <!-- Row shown when there is no matching data. -->
                                            <tr class="mat-row" *matNoDataRow>
                                                <td
                                                    class="mat-cell"
                                                    colspan="4"
                                                >
                                                    No data matching the filter
                                                    "{{ input.value }}"
                                                </td>
                                            </tr>
                                        </table>

                                        <mat-paginator
                                            [pageSize]="20"
                                            [pageSizeOptions]="[
                                                5, 10, 20, 50, 100
                                            ]"
                                            aria-label="Select page of users"
                                        ></mat-paginator>
                                    </div>
                                </div>
                            </div>
                        } @else {
                            <div>
                                @if (isLoading) {
                                    <div
                                        class="flex justify-center w-full m-20"
                                    >
                                        <div
                                            role="status"
                                            class="w-full p-4 space-y-4 border border-gray-200 divide-y divide-gray-200 rounded shadow animate-pulse dark:divide-gray-700 md:p-6 dark:border-gray-700"
                                        >
                                            <div
                                                class="flex items-center justify-between"
                                            >
                                                <div>
                                                    <div
                                                        class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"
                                                    ></div>
                                                    <div
                                                        class="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"
                                                    ></div>
                                                </div>
                                                <div
                                                    class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"
                                                ></div>
                                            </div>
                                            <div
                                                class="flex items-center justify-between pt-4"
                                            >
                                                <div>
                                                    <div
                                                        class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"
                                                    ></div>
                                                    <div
                                                        class="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"
                                                    ></div>
                                                </div>
                                                <div
                                                    class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"
                                                ></div>
                                            </div>
                                            <div
                                                class="flex items-center justify-between pt-4"
                                            >
                                                <div>
                                                    <div
                                                        class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"
                                                    ></div>
                                                    <div
                                                        class="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"
                                                    ></div>
                                                </div>
                                                <div
                                                    class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"
                                                ></div>
                                            </div>
                                            <div
                                                class="flex items-center justify-between pt-4"
                                            >
                                                <div>
                                                    <div
                                                        class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"
                                                    ></div>
                                                    <div
                                                        class="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"
                                                    ></div>
                                                </div>
                                                <div
                                                    class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"
                                                ></div>
                                            </div>
                                            <div
                                                class="flex items-center justify-between pt-4"
                                            >
                                                <div>
                                                    <div
                                                        class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"
                                                    ></div>
                                                    <div
                                                        class="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"
                                                    ></div>
                                                </div>
                                                <div
                                                    class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"
                                                ></div>
                                            </div>
                                            <span class="sr-only"
                                                >Loading...</span
                                            >
                                        </div>
                                    </div>
                                } @else {
                                    <div
                                        class="flex flex-auto flex-col items-center justify-center bg-gray-100 dark:bg-transparent"
                                    >
                                        <mat-icon
                                            class="icon-size-24"
                                            [svgIcon]="
                                                'heroicons_outline:wrench-screwdriver'
                                            "
                                        ></mat-icon>
                                        <div
                                            class="mt-4 text-2xl font-semibold tracking-tight text-secondary"
                                        >
                                            Add a Expense to Keep track of your
                                            expenses status!
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                    </div>
                    <!-- Expenses Preview -->
                    @if (selectedExpense) {
                        <div
                            class="flex flex-col flex-auto p-6 bg-card rounded-2xl shadow"
                        >
                            <div class="flex items-center">
                                <div class="flex flex-col">
                                    <div
                                        class="mr-4 text-lg font-medium tracking-tight leading-6 truncate"
                                    >
                                        Preview
                                    </div>
                                    <div
                                        class="text-secondary font-medium"
                                    ></div>
                                </div>
                                <div class="ml-auto -mt-2 -mr-2">
                                    <button
                                        mat-icon-button
                                        [matMenuTriggerFor]="budgetMenu"
                                    >
                                        <mat-icon
                                            class="icon-size-5"
                                            [svgIcon]="
                                                'heroicons_mini:ellipsis-vertical'
                                            "
                                        ></mat-icon>
                                    </button>
                                    <mat-menu #budgetMenu="matMenu">
                                        <button mat-menu-item>
                                            Save as PDF
                                        </button>
                                        <button mat-menu-item>Edit</button>
                                        <button mat-menu-item>Delete</button>
                                        <mat-divider class="my-2"></mat-divider>
                                        <button mat-menu-item>
                                            <span class="flex items-center">
                                                <mat-icon
                                                    class="icon-size-5 mr-3"
                                                    [svgIcon]="
                                                        'heroicons_solid:printer'
                                                    "
                                                ></mat-icon>
                                                <span>Print summary</span>
                                            </span>
                                        </button>
                                        <button mat-menu-item>
                                            <span class="flex items-center">
                                                <mat-icon
                                                    class="icon-size-5 mr-3"
                                                    [svgIcon]="
                                                        'heroicons_solid:envelope'
                                                    "
                                                ></mat-icon>
                                                <span>Email summary</span>
                                            </span>
                                        </button>
                                    </mat-menu>
                                    <button
                                        mat-icon-button
                                        (click)="selectedExpense = null"
                                    >
                                        <mat-icon
                                            [svgIcon]="
                                                'heroicons_outline:x-mark'
                                            "
                                        ></mat-icon>
                                    </button>
                                </div>
                            </div>
                            <div class="mt-6">Expense bill details.</div>
                            <hr class="w-full border-t my-6" />
                            <div class="flex flex-col">
                                <div class="flex items-center">
                                    <i
                                        class="icon-size-5 mr-3 fa-duotone fa-file-invoice"
                                    ></i>
                                    <span class="leading-none">{{
                                        selectedExpense.expenseName
                                    }}</span>
                                </div>
                                <div class="flex items-center mt-4">
                                    <i
                                        class="icon-size-5 mr-3 fa-duotone fa-calendar-days"
                                    ></i>
                                    <span class="leading-none">{{
                                        selectedExpense.date
                                            | date: "EEE, dd MMM yyyy"
                                    }}</span>
                                </div>
                                <div class="flex items-center mt-4">
                                    <i
                                        class="icon-size-5 mr-3 fa-duotone fa-circle-dollar"
                                    ></i>
                                    <span class="leading-none">{{
                                        selectedExpense.amount
                                    }}</span>
                                </div>
                                <div class="flex items-center mt-4">
                                    <i
                                        class="icon-size-5 mr-3 fa-duotone fa-align-left"
                                    ></i>
                                    <p class="leading-none">
                                        {{ selectedExpense.description }}
                                    </p>
                                </div>
                                <div class="flex items-center mt-4">
                                    <i
                                        class="icon-size-5 mr-3 fa-duotone fa-image-polaroid-user"
                                    ></i>
                                    <p class="leading-none">
                                        {{ selectedExpense.expenseBy }}
                                    </p>
                                </div>
                                @if (selectedExpense.address) {
                                    <div class="flex items-center mt-4">
                                        <i
                                            class="icon-size-5 mr-3 fa-duotone fa-location-dot"
                                        ></i>
                                        <span class="leading-none">{{
                                            selectedExpense.address
                                        }}</span>
                                    </div>
                                }

                                @if (selectedExpense.addressLatLon) {
                                    <div
                                        class="flex items-center mt-4 w-full h-50 flex-grow bg-slate-400 rounded-2xl overflow-hidden"
                                    >
                                        <google-map
                                            class="flex flex-grow w-full h-full"
                                            [width]="'100%'"
                                            [height]="'200px'"
                                            [center]="center"
                                            [zoom]="zoom"
                                            [options]="mapOptions"
                                        >
                                            <map-marker [position]="center" />
                                        </google-map>
                                    </div>
                                }
                            </div>

                            <!-- Attached files -->
                            <div>
                                @if (selectedExpense.attachedFiles.length > 0) {
                                    @for (
                                        file of selectedExpense.attachedFiles;
                                        track file
                                    ) {
                                        <div class="flex flex-wrap -m-3 mt-3">
                                            <span></span>

                                            <div
                                                class="flex items-center m-3 ng-star-inserted"
                                            >
                                                @if (
                                                    fileDetails(file)
                                                        .fileName.split(".")
                                                        .pop() == "pdf"
                                                ) {
                                                    <div
                                                        class="flex items-center justify-center w-10 h-10 rounded-md overflow-hidden bg-primary-100 ng-star-inserted"
                                                    >
                                                        <div
                                                            class="flex items-center justify-center text-sm font-semibold text-primary-500-800"
                                                        >
                                                            PDF
                                                        </div>
                                                    </div>
                                                } @else {
                                                    <img
                                                        class="w-10 h-10 rounded-md overflow-hidden ng-star-inserted"
                                                        [src]="
                                                            fileDetails(file)
                                                                .fileUrl
                                                        "
                                                    />
                                                }

                                                <div class="ml-3">
                                                    <div
                                                        class="text-md font-medium overflow-hidden"
                                                        [title]="
                                                            fileDetails(file)
                                                                .fileName
                                                        "
                                                    >
                                                        {{
                                                            fileDetails(file)
                                                                .fileName
                                                        }}
                                                    </div>
                                                    <div
                                                        class="text-sm font-medium truncate text-secondary"
                                                        title="Preview file"
                                                    >
                                                        <a
                                                            [href]="
                                                                fileDetails(
                                                                    file
                                                                ).fileUrl
                                                            "
                                                            target="_blank"
                                                            >Preview</a
                                                        >
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                } @else {
                                    <div
                                        class="flex flex-auto flex-col items-center"
                                    >
                                        <div class="mt-6 font-bold text-lg">
                                            No receipt attached.
                                        </div>
                                        <ng-lottie
                                            width="150px"
                                            [options]="options"
                                            (animationCreated)="
                                                animationCreated($event)
                                            "
                                        />
                                    </div>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        </mat-drawer-content>
    </mat-drawer-container>
</div>

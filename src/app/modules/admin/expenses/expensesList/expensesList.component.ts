import { DatePipe, NgClass } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, Inject, OnDestroy, OnInit, ViewChild, ViewEncapsulation, DOCUMENT } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, MatSidenavModule } from '@angular/material/sidenav';
import { ActivatedRoute, Router, RouterOutlet } from '@angular/router';
import { FuseMediaWatcherService } from '@fuse/services/media-watcher';
import { catchError, Subject, takeUntil, throwError } from 'rxjs';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatIconModule } from '@angular/material/icon';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { FuseLoadingService } from '@fuse/services/loading';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatMenuModule } from '@angular/material/menu';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TextFieldModule } from '@angular/cdk/text-field';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { Expense, ExpenseCategory } from 'app/core/databaseModels/expense/expense.types';
import { ExpenseService } from 'app/core/databaseModels/expense/expense.service';

import { forEach } from 'lodash';

import { MatDividerModule } from '@angular/material/divider';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTabsModule } from '@angular/material/tabs';

import { AnimationItem } from 'lottie-web';
import { LottieComponent, AnimationOptions } from 'ngx-lottie';
import { GoogleMap, MapMarker } from '@angular/google-maps';
import { ExportTableService } from 'app/services/export-table.service';
import { MatDialog } from '@angular/material/dialog';
import { ApprovePaymentComponent } from '../approve-payment/approve-payment.component';

@Component({
    selector: 'expenses-list',
    templateUrl: './expensesList.component.html',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        MatSidenavModule,
        RouterOutlet,
        NgClass,
        LottieComponent,
        GoogleMap,
        MapMarker,
        MatTableModule,
        MatPaginatorModule,
        MatSortModule,
        MatButtonModule,
        MatMenuModule,
        MatTooltipModule,
        MatIconModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        TextFieldModule,
        MatSelectModule,
        MatOptionModule,
        MatButtonModule,
        DatePipe,
        MatSortModule,
        MatTableModule,
        MatDividerModule,
        MatProgressBarModule,
        MatMenuModule,
        MatButtonModule,
        MatIconModule,
        MatTabsModule,
        DatePipe,
    ]
})
export class ExpensesListComponent implements OnInit, OnDestroy, AfterViewInit {
    @ViewChild('matDrawer', { static: true }) matDrawer: MatDrawer;

    @ViewChild(MatPaginator) paginator: MatPaginator;

    isLoading: boolean = false;
    expenses: Expense[] = [];
    expenseCategory: ExpenseCategory[] = [];
    selectedExpense: Expense;

    expensesDataSource: MatTableDataSource<any> = new MatTableDataSource();
    expensesTableMatSort: MatSort;
    expensesTableColumns: string[] = ['expenseName', 'date', 'amount', 'categoryTags', 'status', 'actions'];
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    private _document = inject(DOCUMENT);

    drawerMode: 'side' | 'over';

    options: AnimationOptions = {
        path: '/lottie/invoice-receipt-validating-ticket.json',
        autoplay: true,
        loop: true,

    };

    map: GoogleMap | null = null; // Reference to the map
    center: google.maps.LatLngLiteral = {
        lat: -24,
        lng: 134
    };
    zoom = 4;
    mapOptions: google.maps.MapOptions = {
        controlSize: 24,
        fullscreenControl: false,
        mapTypeControl: false,
        zoomControl: false,
        streetViewControl: false,


    };

    constructor(
        private _activatedRoute: ActivatedRoute,
        private _changeDetectorRef: ChangeDetectorRef,

        private _fuseConfirmationService: FuseConfirmationService,
        private _router: Router,
        private _fuseMediaWatcherService: FuseMediaWatcherService,
        private _fuseLoadingService: FuseLoadingService,
        private _expensesService: ExpenseService,
        private exportTableService: ExportTableService,
        private _approvalDialog: MatDialog,

    ) {
    }


    ngOnInit(): void {


        // Subscribe to media query change
        this._fuseMediaWatcherService.onMediaQueryChange$('(min-width: 1440px)')
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((state) => {
                // Calculate the drawer mode
                this.drawerMode = state.matches ? 'side' : 'over';

                // Mark for check
                this._changeDetectorRef.markForCheck();
            });
        this._fuseLoadingService.show();
        this.isLoading = true;
        this.loadExpenseCategories();
        this.loadExpenses();



    }

    /**
   * After view init
   */
    ngAfterViewInit(): void {
        // Make the data source sortable
        this.expensesDataSource.sort = this.expensesTableMatSort;
    }

    /**
  /**
   * On destroy
   */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }
    /**
   * Track by function for ngFor loops
   *
   * @param index
   * @param item
   */
    trackByFn(index: number, item: any): any {
        return item.$id || index;
    }


    onBackdropClicked(): void {
        // Go back to the list
        this._router.navigate(['./'], { relativeTo: this._activatedRoute });

        // Mark for check
        this._changeDetectorRef.markForCheck();
    }

    createExpense(type: 'newExpense' | 'editExpense', expense?: Expense): void {

        this.selectedExpense = null;
        if (type === 'newExpense') {

            this._router.navigate(['./', 0], { relativeTo: this._activatedRoute, queryParams: { type: type } });
        } else {
            this._router.navigate(['./', expense.$id], { relativeTo: this._activatedRoute, queryParams: { type: type, id: expense.$id } });
        }
        // Mark for check
        this._changeDetectorRef.markForCheck();
    }

    applyFilter(event: Event) {
        const filterValue = (event.target as HTMLInputElement).value;
        this.expensesDataSource.filter = filterValue.trim().toLowerCase();

        if (this.expensesDataSource.paginator) {
            this.expensesDataSource.paginator.firstPage();
        }
    }

    loadExpenseCategories() {

        try {
            this._expensesService.getExpenseCategories().subscribe((categories) => {
                this.expenseCategory = categories;
                this._changeDetectorRef.markForCheck();
            })
        } catch (error) {
            console.error(error);
        }
    }

    loadExpenses() {
        try {
            this._expensesService.getExpenses().subscribe((expenses) => {

                this._expensesService.expense$.pipe(
                    takeUntil(this._unsubscribeAll),
                    catchError(error => {
                        console.error('Error:', error);
                        return throwError(error);
                    })
                ).subscribe((expense) => {
                    this.expenses = expenses;
                    this.expensesDataSource.data = expenses;
                    this.isLoading = false;
                    this._changeDetectorRef.markForCheck();
                    if (this.expensesDataSource.data.length > 0) {
                        this.expensesDataSource.paginator = this.paginator;
                        this.expensesDataSource.sort = this.expensesTableMatSort;
                    }
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();
                })
            })

        } catch (error) {
            console.error(error);
        }
    }
    deleteItem(row): void {
        const _attachedFiles = row.attachedFiles;

        const confirmation = this._fuseConfirmationService.open({
            title: `Delete Service - ${row.expenseName}`,
            message: 'Are you sure you want to delete this service? This action cannot be undone!',
            actions: {
                confirm: {
                    label: 'Delete',
                },
            },
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            // If the confirm button pressed...
            if (result === 'confirmed') {

                try {
                    this.isLoading = true;
                    this._fuseLoadingService.show();
                    this._expensesService.deleteExpense(row.$id).subscribe((result) => {
                        this.deleteFiles(_attachedFiles);
                        this.isLoading = false;
                        this._fuseLoadingService.hide();
                        this.loadExpenses();
                    });
                } catch (error) {
                    console.error(error);
                }

            }
        });
    }
    deleteFiles(_attachedFiles) {
        try {
            if (_attachedFiles.length > 0) {
                _attachedFiles.forEach((file) => {
                    const fileObject = JSON.parse(file);
                    this._expensesService.deleteFile(fileObject.fileId).subscribe((result) => {
                        // console.log(result);
                    })
                })
            }
        } catch (error) {
            console.error(error);
        }

    }

    getExpenseCategoryName(ids: string[]): string[] {

        if (!ids) {
            return [];
        }
        let expenseCategoryName = [];
        forEach(ids, (id) => {
            const expenseCategory = this.expenseCategory.find((category) => category.$id === id);
            if (expenseCategory) {
                expenseCategoryName.push(expenseCategory.title);
            }
        })
        return expenseCategoryName;
    }


    previewExpense(expense: Expense): void {
        // console.log(expense);
        if (expense.addressLatLon) {
            const location = expense.addressLatLon.split(',');
            this.center = { lat: parseFloat(location[0]), lng: parseFloat(location[1]) };
            this.zoom = 16; // Adjust zoom level as needed from user options if need
        }
        this.selectedExpense = expense;


    }
    fileDetails(file: string): void {
        return JSON.parse(file);
    }

    exportMaterialTable(format: 'pdf' | 'excel') {

        const _data = this.expenses.map(expense => {

            return {

                'Expense Title': expense.expenseName,
                'Due Date': expense.date.toString().replace('T', ' '),
                'Expense Amount': '$' + expense.amount,
                'Expense Category': this.getExpenseCategoryName(expense.categoryTags).toString(),
                'Expense Address': expense.address,
                'Expense By': expense.expenseBy,
                'Status': expense.status ? 'Paid' : 'Unpaid',
            };
        });

        this.exportTableService.exportMaterialTable(_data, format, 'expenses-' + new Date().toISOString());
    }
    animationCreated(animationItem: AnimationItem): void {
        //   console.log(animationItem);
    }

    approvePayment(expense: Expense) {
        // console.log(row);
        let dialogRef = this._approvalDialog.open(ApprovePaymentComponent, {
            data: {
                expense
            }
        });

        dialogRef.afterClosed().subscribe(result => {
            if (result) {
                this.loadExpenses();
            }
        });

    }

}

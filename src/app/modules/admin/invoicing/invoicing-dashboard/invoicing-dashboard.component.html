<div class="mx-auto flex w-full max-w-screen-xl flex-wrap p-6 md:p-8">
    <!-- Title and action buttons -->
    <div class="flex w-full items-center justify-between">
        <div>
            <h2 class="text-3xl font-semibold leading-8 tracking-tight">
                Invoicing dashboard
            </h2>
            <div class="text-secondary font-medium tracking-tight">
                Keep track of your invoicing status
            </div>
        </div>
        <div class="ml-6 flex items-center">
            <button
                class="hidden sm:inline-flex"
                mat-stroked-button
                [routerLink]="'/invoicing/reports'"
            >
                <mat-icon
                    class="icon-size-5"
                    [svgIcon]="'heroicons_solid:document-chart-bar'"
                ></mat-icon>
                <span class="ml-2">Reports</span>
            </button>
            <button
                class="ml-3 hidden sm:inline-flex"
                mat-stroked-button
                [routerLink]="'/invoicing/settings'"
            >
                <mat-icon
                    class="icon-size-5"
                    [svgIcon]="'heroicons_solid:cog-8-tooth'"
                ></mat-icon>
                <span class="ml-2">Settings</span>
            </button>
            <button
                class="ml-3 hidden sm:inline-flex"
                mat-flat-button
                [color]="'primary'"
                [routerLink]="'/invoicing/create'"
            >
                <mat-icon
                    class="icon-size-5"
                    [svgIcon]="'heroicons_solid:plus'"
                ></mat-icon>
                <span class="ml-2">Create invoice</span>
            </button>
            <button
                class="ml-3 hidden sm:inline-flex"
                mat-flat-button
                [color]="'primary'"
                [routerLink]="'/invoicing/awaiting'"
            >
                <i class="icon-size-5 fa-duotone fa-file-invoice-dollar"></i>
                <span class="ml-2">Awaiting Invoicing</span>
            </button>

            <!-- Actions menu (visible on xs) -->
            <div class="sm:hidden">
                <button [matMenuTriggerFor]="actionsMenu" mat-icon-button>
                    <mat-icon
                        [svgIcon]="'heroicons_mini:ellipsis-vertical'"
                    ></mat-icon>
                </button>
                <mat-menu #actionsMenu="matMenu">
                    <button [routerLink]="'/invoicing/awaiting'" mat-menu-item>
                        Awaiting Invoicing
                    </button>
                    <button [routerLink]="'/invoicing/create'" mat-menu-item>
                        Create invoice
                    </button>
                    <button mat-menu-item>Reports</button>
                    <button [routerLink]="'/invoicing/settings'" mat-menu-item>
                        Settings
                    </button>
                </mat-menu>
            </div>
        </div>
    </div>

    <div class="mt-8 grid w-full grid-cols-1 gap-8 xl:grid-cols-2">
        <!-- Previous statement -->
        <div class="grid gap-8 sm:grid-flow-col xl:grid-flow-row">
            <div
                class="bg-card relative flex flex-auto flex-col overflow-hidden rounded-2xl p-6 pb-3 pr-3 shadow"
            >
                <div class="absolute bottom-0 right-0 -m-6 h-24 w-24">
                    <mat-icon
                        class="text-green-500 opacity-25 icon-size-24 dark:text-green-400"
                        [svgIcon]="'heroicons_outline:check-circle'"
                    ></mat-icon>
                </div>
                <div class="flex items-center">
                    <div class="flex flex-col">
                        <div
                            class="truncate text-lg font-medium leading-6 tracking-tight"
                        >
                            Invoice Statement
                        </div>
                        <div class="text-sm font-medium text-green-600">
                            Financial Year: {{ currentFY.displayText }}
                        </div>
                    </div>
                    <!-- <div class="-mt-2 ml-auto">
                        <button
                            mat-icon-button
                            [matMenuTriggerFor]="previousStatementMenu"
                        >
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_mini:ellipsis-vertical'"
                            ></mat-icon>
                        </button>
                        <mat-menu #previousStatementMenu="matMenu">
                            <button mat-menu-item>
                                <span class="flex items-center">
                                    <mat-icon
                                        class="mr-3 icon-size-5"
                                        [svgIcon]="
                                            'heroicons_solid:credit-card'
                                        "
                                    ></mat-icon>
                                    <span>View statement</span>
                                </span>
                            </button>
                            <button mat-menu-item>
                                <span class="flex items-center">
                                    <mat-icon
                                        class="mr-3 icon-size-5"
                                        [svgIcon]="'heroicons_solid:banknotes'"
                                    ></mat-icon>
                                    <span>Spending breakdown</span>
                                </span>
                            </button>
                            <button mat-menu-item>
                                <span class="flex items-center">
                                    <mat-icon
                                        class="mr-3 icon-size-5"
                                        [svgIcon]="
                                            'heroicons_solid:receipt-percent'
                                        "
                                    ></mat-icon>
                                    <span>Tax breakdown</span>
                                </span>
                            </button>
                            <mat-divider class="my-2"></mat-divider>
                            <button mat-menu-item>
                                <span class="flex items-center">
                                    <mat-icon
                                        class="mr-3 icon-size-5"
                                        [svgIcon]="'heroicons_solid:printer'"
                                    ></mat-icon>
                                    <span>Print statement</span>
                                </span>
                            </button>
                            <button mat-menu-item>
                                <span class="flex items-center">
                                    <mat-icon
                                        class="mr-3 icon-size-5"
                                        [svgIcon]="'heroicons_solid:envelope'"
                                    ></mat-icon>
                                    <span>Email statement</span>
                                </span>
                            </button>
                        </mat-menu>
                    </div> -->
                </div>
                <div class="-mx-6 mt-4 flex flex-row flex-wrap">
                    <div class="mx-6 my-3 flex flex-col">
                        <div
                            class="text-secondary text-sm font-medium leading-none"
                        >
                            Total amount
                        </div>
                        <div class="mt-2 text-3xl font-medium leading-none">
                            {{ invoiceStatement.totalAmount | currency }}
                        </div>
                    </div>
                    <div class="mx-6 my-3 flex flex-col">
                        <div
                            class="text-secondary text-sm font-medium leading-none"
                        >
                            Balance Due
                        </div>
                        <div class="mt-2 text-3xl font-medium leading-none">
                            {{ invoiceStatement.balanceDue | currency }}
                        </div>
                    </div>
                    <div class="mx-6 my-3 flex flex-col">
                        <div
                            class="text-secondary text-sm font-medium leading-none"
                        >
                            Total Paid
                        </div>
                        <div class="mt-2 text-3xl font-medium leading-none">
                            {{ invoiceStatement.totalPaid | currency }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Current statement -->
        <div
            class="bg-card flex flex-auto flex-col overflow-hidden rounded-2xl shadow"
        >
            <div
                class="bg-card relative flex flex-auto flex-col overflow-hidden rounded-2xl p-6 pb-3 pr-3 shadow"
            >
                <div class="absolute bottom-0 right-0 -m-6 h-24 w-24">
                    <mat-icon
                        class="text-red-500 opacity-25 icon-size-24 dark:text-red-400"
                        [svgIcon]="'heroicons_outline:exclamation-circle'"
                    ></mat-icon>
                </div>
                <div class="flex items-center">
                    <div class="flex flex-col">
                        <div
                            class="truncate text-lg font-medium leading-6 tracking-tight"
                        >
                            Unallocated transaction
                        </div>
                        <div class="text-sm font-medium text-red-600">
                            Approved invoices not allocated to any transaction
                        </div>
                    </div>
                    <!-- <div class="-mt-2 ml-auto">
                        <button
                            mat-icon-button
                            [matMenuTriggerFor]="currentStatementMenu"
                        >
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_mini:ellipsis-vertical'"
                            ></mat-icon>
                        </button>
                        <mat-menu #currentStatementMenu="matMenu">
                            <button mat-menu-item>
                                <span class="flex items-center">
                                    <mat-icon
                                        class="mr-3 icon-size-5"
                                        [svgIcon]="
                                            'heroicons_solid:credit-card'
                                        "
                                    ></mat-icon>
                                    <span>View statement</span>
                                </span>
                            </button>
                            <button mat-menu-item>
                                <span class="flex items-center">
                                    <mat-icon
                                        class="mr-3 icon-size-5"
                                        [svgIcon]="'heroicons_solid:banknotes'"
                                    ></mat-icon>
                                    <span>Spending breakdown</span>
                                </span>
                            </button>
                            <button mat-menu-item>
                                <span class="flex items-center">
                                    <mat-icon
                                        class="mr-3 icon-size-5"
                                        [svgIcon]="
                                            'heroicons_solid:receipt-percent'
                                        "
                                    ></mat-icon>
                                    <span>Tax breakdown</span>
                                </span>
                            </button>
                            <mat-divider class="my-2"></mat-divider>
                            <button mat-menu-item>
                                <span class="flex items-center">
                                    <mat-icon
                                        class="mr-3 icon-size-5"
                                        [svgIcon]="'heroicons_solid:printer'"
                                    ></mat-icon>
                                    <span>Print statement</span>
                                </span>
                            </button>
                            <button mat-menu-item>
                                <span class="flex items-center">
                                    <mat-icon
                                        class="mr-3 icon-size-5"
                                        [svgIcon]="'heroicons_solid:envelope'"
                                    ></mat-icon>
                                    <span>Email statement</span>
                                </span>
                            </button>
                        </mat-menu>
                    </div> -->
                </div>
                <div class="-mx-6 mt-4 flex flex-row flex-wrap">
                    <div class="mx-6 my-3 flex flex-col">
                        <div
                            class="text-secondary text-sm font-medium leading-none"
                        >
                            Total unallocated
                        </div>
                        <div class="mt-2 text-3xl font-medium leading-none">
                            {{
                                unallocatedTransactions.totalUnallocated
                                    | currency
                            }}
                        </div>
                    </div>
                    <div class="mx-6 my-3 flex flex-col">
                        <div
                            class="text-secondary text-sm font-medium leading-none"
                        >
                            Invoices
                        </div>
                        <div class="mt-2 text-3xl font-medium leading-none">
                            {{ unallocatedTransactions.totalInvoices }}
                        </div>
                    </div>
                    <div class="mx-6 my-3 flex flex-col">
                        <button
                            [routerLink]="'/invoicing/payment'"
                            class="mt-2"
                            mat-stroked-button
                        >
                            Allocate Payment
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-8 grid w-full grid-cols-1 gap-8 xl:grid-cols-4">
        <!-- Recent transactions table -->
        <div
            class="bg-card flex flex-auto flex-col overflow-hidden rounded-2xl shadow xl:col-span-3"
        >
            <div class="p-6">
                <div
                    class="mr-4 truncate text-lg font-medium leading-6 tracking-tight"
                >
                    Recent transactions
                </div>
                <div class="text-secondary font-medium">
                    1 pending, 4 completed
                </div>
            </div>
            <div class="mx-6 overflow-x-auto">
                <div
                    class="border-b pb-4 border-gray-200 dark:border-gray-700 w-full p-6"
                >
                    <invoice-list [limited]="true" />
                </div>
                <div class="flex justify-center p-6">
                    <button mat-stroked-button [routerLink]="'/invoicing/list'">
                        See all transactions
                    </button>
                </div>
            </div>
        </div>

        <!-- Budget -->
        <div class="bg-card flex flex-auto flex-col rounded-2xl p-6 shadow">
            <div class="flex items-center">
                <div class="flex flex-col">
                    <div
                        class="mr-4 truncate text-lg font-medium leading-6 tracking-tight"
                    >
                        Summary
                    </div>
                    <div class="text-secondary font-medium">
                        Monthly invoicing summary
                    </div>
                </div>
                <!-- <div class="-mr-2 -mt-2 ml-auto">
                    <button mat-icon-button [matMenuTriggerFor]="budgetMenu">
                        <mat-icon
                            class="icon-size-5"
                            [svgIcon]="'heroicons_mini:ellipsis-vertical'"
                        ></mat-icon>
                    </button>
                    <mat-menu #budgetMenu="matMenu">
                        <button mat-menu-item>Expenses breakdown</button>
                        <button mat-menu-item>Savings breakdown</button>
                        <button mat-menu-item>Bills breakdown</button>
                        <mat-divider class="my-2"></mat-divider>
                        <button mat-menu-item>
                            <span class="flex items-center">
                                <mat-icon
                                    class="mr-3 icon-size-5"
                                    [svgIcon]="'heroicons_solid:printer'"
                                ></mat-icon>
                                <span>Print budget summary</span>
                            </span>
                        </button>
                        <button mat-menu-item>
                            <span class="flex items-center">
                                <mat-icon
                                    class="mr-3 icon-size-5"
                                    [svgIcon]="'heroicons_solid:envelope'"
                                ></mat-icon>
                                <span>Email budget summary</span>
                            </span>
                        </button>
                    </mat-menu>
                </div> -->
            </div>
            <div class="mt-6">
                This month so far you had
                <strong>{{ monthlyInvoicingSummary.totalInvoices }}</strong>
                invoices,
                <strong>{{ monthlyInvoicingSummary.totalPaidInvoices }}</strong>
                transactions entries and
                <strong>{{
                    monthlyInvoicingSummary.totalInvoices -
                        monthlyInvoicingSummary.totalPaidInvoices
                }}</strong>
                unpaid invoices.
            </div>
            <div class="my-8 space-y-8">
                <div class="flex flex-col">
                    <div class="flex items-center">
                        <div
                            class="flex h-14 w-14 items-center justify-center rounded bg-red-100 text-red-800 dark:bg-red-600 dark:text-red-50"
                        >
                            <mat-icon
                                class="text-current"
                                [svgIcon]="'heroicons_outline:credit-card'"
                            ></mat-icon>
                        </div>
                        <div class="ml-4 flex-auto leading-none">
                            <div class="text-secondary text-sm font-medium">
                                Unpaid Invoices
                            </div>
                            <div class="mt-2 text-2xl font-medium">
                                {{
                                    monthlyInvoicingSummary.totalUnallocated
                                        | currency
                                }}
                            </div>
                            <mat-progress-bar
                                class="mt-3 rounded-full"
                                [color]="'warn'"
                                [mode]="'determinate'"
                                [value]="
                                    percentageMonthlyInvoicing.totalUnallocated
                                "
                            ></mat-progress-bar>
                        </div>
                        <div
                            class="ml-6 mt-auto flex min-w-18 items-end justify-end"
                        >
                            <div class="text-lg leading-none">
                                {{
                                    percentageMonthlyInvoicing.totalUnallocated
                                }}%
                            </div>
                            @if (
                                percentageMonthlyInvoicing.totalUnallocatedUpDown ==
                                "up"
                            ) {
                                <mat-icon
                                    class="ml-1 text-green-600 icon-size-4"
                                    [svgIcon]="'heroicons_mini:arrow-long-up'"
                                ></mat-icon>
                            } @else if (
                                percentageMonthlyInvoicing.totalUnallocatedUpDown ==
                                "down"
                            ) {
                                <mat-icon
                                    class="ml-1 text-red-600 icon-size-4"
                                    [svgIcon]="'heroicons_mini:arrow-long-down'"
                                ></mat-icon>
                            } @else {
                                <mat-icon
                                    class="ml-1 text-indigo-600 icon-size-4"
                                    [svgIcon]="
                                        'heroicons_mini:arrow-long-right'
                                    "
                                ></mat-icon>
                            }
                        </div>
                    </div>
                </div>
                <div class="flex flex-col">
                    <div class="flex items-center">
                        <div
                            class="flex h-14 w-14 items-center justify-center rounded bg-indigo-100 text-indigo-800 dark:bg-indigo-600 dark:text-indigo-50"
                        >
                            <mat-icon
                                class="text-current"
                                [svgIcon]="'heroicons_outline:banknotes'"
                            ></mat-icon>
                        </div>
                        <div class="ml-4 flex-auto leading-none">
                            <div class="text-secondary text-sm font-medium">
                                Paid Invoices
                            </div>
                            <div class="mt-2 text-2xl font-medium">
                                {{
                                    monthlyInvoicingSummary.totalPaid | currency
                                }}
                            </div>
                            <mat-progress-bar
                                class="mt-3 rounded-full"
                                [mode]="'determinate'"
                                [value]="percentageMonthlyInvoicing.totalPaid"
                            ></mat-progress-bar>
                        </div>
                        <div
                            class="ml-6 mt-auto flex min-w-18 items-end justify-end"
                        >
                            <div class="text-lg leading-none">
                                {{ percentageMonthlyInvoicing.totalPaid }}%
                            </div>
                            @if (
                                percentageMonthlyInvoicing.totalPaidUpDown ==
                                "up"
                            ) {
                                <mat-icon
                                    class="ml-1 text-green-600 icon-size-4"
                                    [svgIcon]="'heroicons_mini:arrow-long-up'"
                                ></mat-icon>
                            } @else if (
                                percentageMonthlyInvoicing.totalPaidUpDown ==
                                "down"
                            ) {
                                <mat-icon
                                    class="ml-1 text-red-600 icon-size-4"
                                    [svgIcon]="'heroicons_mini:arrow-long-down'"
                                ></mat-icon>
                            } @else {
                                <mat-icon
                                    class="ml-1 text-indigo-600 icon-size-4"
                                    [svgIcon]="
                                        'heroicons_mini:arrow-long-right'
                                    "
                                ></mat-icon>
                            }
                        </div>
                    </div>
                </div>
                <div class="flex flex-col">
                    <div class="flex items-center">
                        <div
                            class="flex h-14 w-14 items-center justify-center rounded bg-teal-100 text-teal-800 dark:bg-teal-600 dark:text-teal-50"
                        >
                            <mat-icon
                                class="text-current"
                                [svgIcon]="'heroicons_outline:light-bulb'"
                            ></mat-icon>
                        </div>
                        <div class="ml-4 flex-auto leading-none">
                            <div class="text-secondary text-sm font-medium">
                                Total Income
                            </div>
                            <div class="mt-2 text-2xl font-medium">
                                {{
                                    monthlyInvoicingSummary.totalAmount
                                        | currency
                                }}
                            </div>
                            <mat-progress-bar
                                class="mt-3 rounded-full"
                                [mode]="'determinate'"
                                [value]="percentageMonthlyInvoicing.totalAmount"
                            ></mat-progress-bar>
                        </div>
                        <div
                            class="ml-6 mt-auto flex min-w-18 items-end justify-end"
                        >
                            <div class="text-lg leading-none">
                                {{ percentageMonthlyInvoicing.totalAmount }}%
                            </div>
                            @if (
                                percentageMonthlyInvoicing.totalAmountUpDown ==
                                "up"
                            ) {
                                <mat-icon
                                    class="ml-1 text-green-600 icon-size-4"
                                    [svgIcon]="'heroicons_mini:arrow-long-up'"
                                ></mat-icon>
                            } @else if (
                                percentageMonthlyInvoicing.totalAmountUpDown ==
                                "down"
                            ) {
                                <mat-icon
                                    class="ml-1 text-red-600 icon-size-4"
                                    [svgIcon]="'heroicons_mini:arrow-long-down'"
                                ></mat-icon>
                            } @else {
                                <mat-icon
                                    class="ml-1 text-indigo-600 icon-size-4"
                                    [svgIcon]="
                                        'heroicons_mini:arrow-long-right'
                                    "
                                ></mat-icon>
                            }
                        </div>
                    </div>
                    <div class="text-secondary mt-3 text-md">
                        Last month you had
                        <strong>{{
                            lastMonthlyInvoicingSummary.totalInvoices
                        }}</strong>
                        invoices,
                        <strong>{{
                            lastMonthlyInvoicingSummary.totalPaidInvoices
                        }}</strong>
                        transactions entries and
                        <strong>{{
                            lastMonthlyInvoicingSummary.totalInvoices -
                                lastMonthlyInvoicingSummary.totalPaidInvoices
                        }}</strong>
                        unpaid invoices.
                    </div>
                </div>
            </div>
            <div class="mt-auto flex items-center"></div>
        </div>
    </div>
</div>

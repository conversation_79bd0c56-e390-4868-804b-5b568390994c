import { <PERSON><PERSON><PERSON>cyPipe, DatePipe, NgClass } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    OnDestroy,
    OnInit,
    ViewChild,
    ViewEncapsulation,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { RouterLink } from '@angular/router';
import { ApexOptions, NgApexchartsModule } from 'ng-apexcharts';
import { combineLatest, Subject } from 'rxjs';
import { InvoiceListComponent } from '../invoice-list/invoice-list.component';
import { InvoicingService } from 'app/core/databaseModels/invoicing/invoicing.service';
import { FinancialYear, FinancialYearUtil } from '../util/financial-year.util';

interface PercentageComparison {
    totalUnallocated: number;
    totalUnallocatedUpDown: 'up' | 'down' | 'same';
    totalPaid: number;
    totalPaidUpDown: 'up' | 'down' | 'same';
    totalAmount: number;
    totalAmountUpDown: 'up' | 'down' | 'same';
    totalInvoices: number;
    totalInvoicesUpDown: 'up' | 'down' | 'same';
    totalPaidInvoices: number;
    totalPaidInvoicesUpDown: 'up' | 'down' | 'same';
}
@Component({
    selector: 'app-invoicing-dashboard',
    imports: [
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
        MatDividerModule,
        NgApexchartsModule,
        MatSortModule,
        MatProgressBarModule,
        RouterLink,
        InvoiceListComponent,
        CurrencyPipe,
    ],
    templateUrl: './invoicing-dashboard.component.html',
    styleUrl: './invoicing-dashboard.component.scss'

})

export class InvoicingDashboardComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('recentTransactionsTable', { read: MatSort })
    recentTransactionsTableMatSort: MatSort;
    currentFY: FinancialYear
    data: any;
    accountBalanceOptions: ApexOptions;
    invoiceStatement = {
        totalAmount: 0,
        balanceDue: 0,
        totalPaid: 0,
    };

    unallocatedTransactions = {
        totalUnallocated: 0,
        totalInvoices: 0,
    };

    monthlyInvoicingSummary = {
        totalInvoices: 0,
        totalPaidInvoices: 0,
        totalAmount: 0,
        totalPaid: 0,
        totalUnallocated: 0,
    };

    lastMonthlyInvoicingSummary = {
        totalInvoices: 0,
        totalPaidInvoices: 0,
        totalAmount: 0,
        totalPaid: 0,
        totalUnallocated: 0,
    };

    percentageMonthlyInvoicing: PercentageComparison = {
        totalUnallocated: 0,
        totalUnallocatedUpDown: 'same',
        totalPaid: 0,
        totalPaidUpDown: 'same',
        totalAmount: 0,
        totalAmountUpDown: 'same',
        totalInvoices: 0,
        totalInvoicesUpDown: 'same',
        totalPaidInvoices: 0,
        totalPaidInvoicesUpDown: 'same',
    };

    private _unsubscribeAll: Subject<any> = new Subject<any>();

    /**
     * Constructor
     */
    constructor(
        private invoicingService: InvoicingService,
    ) { }

    getInvoiceStatement(): void {
        // GEt this financial year
        this.currentFY = FinancialYearUtil.getCurrentFinancialYear(7, 1);
        this.invoicingService.getInvoiceStatement(this.currentFY.startDate, this.currentFY.endDate).subscribe({
            next: (statement) => {
                // console.log('statement', statement, this.currentFY);
                this.invoiceStatement = statement;
            },
            error: (error) => {
                console.error('Error fetching data:', error);
            }
        });
    }

    getUnallocatedTransactions(): void {
        this.invoicingService.getUnallocatedTransaction().subscribe({
            next: (unallocatedTransactions) => {
                this.unallocatedTransactions = unallocatedTransactions;
            },
            error: (error) => {
                console.error('Error fetching data:', error);
            }
        });
    }

    getMonthlyInvoicingSummary(): void {
        const currentMonth = FinancialYearUtil.getCurrentMonth();
        const lastMonth = FinancialYearUtil.getLastMonth();

        // Using combineLatest to handle both API calls together
        combineLatest([
            this.invoicingService.getMonthlyInvoicingSummary(currentMonth.startDate, currentMonth.endDate),
            this.invoicingService.getMonthlyInvoicingSummary(lastMonth.startDate, lastMonth.endDate)
        ]).subscribe({
            next: ([currentMonthData, lastMonthData]) => {
                this.monthlyInvoicingSummary = currentMonthData;
                this.lastMonthlyInvoicingSummary = lastMonthData;

                // Calculate percentage changes
                this.calculatePercentageChanges();
            },
            error: (error) => {
                console.error('Error fetching data:', error);
            }
        });
    }

    getInvoiseSettings(): void {
        this.invoicingService.getInvoiceSettings().subscribe({
            next: (settings) => {
                /// TODO: GET Opening moth and year
            },
            error: (error) => {
                console.error('Error fetching data:', error);
            }
        });
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        this.getInvoiceStatement();
        this.getUnallocatedTransactions();
        this.getMonthlyInvoicingSummary();
    }

    /**
     * After view init
     */
    ngAfterViewInit(): void {


    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Track by function for ngFor loops
     *
     * @param index
     * @param item
     */
    trackByFn(index: number, item: any): any {
        return item.id || index;
    }

    calculatePercentageChange(current: number, previous: number): { percentage: number; direction: 'up' | 'down' | 'same' } {
        if (previous === 0) {
            return current === 0
                ? { percentage: 0, direction: 'same' }
                : { percentage: 100, direction: 'up' };
        }

        const percentageChange = ((current - previous) / previous) * 100;
        return {
            percentage: Math.abs(Math.round(percentageChange)),
            direction: percentageChange > 0 ? 'up' : percentageChange < 0 ? 'down' : 'same'
        };
    }

    private calculatePercentageChanges(): void {
        const unallocatedChange = this.calculatePercentageChange(
            this.monthlyInvoicingSummary.totalUnallocated,
            this.lastMonthlyInvoicingSummary.totalUnallocated
        );

        const paidChange = this.calculatePercentageChange(
            this.monthlyInvoicingSummary.totalPaid,
            this.lastMonthlyInvoicingSummary.totalPaid
        );

        const amountChange = this.calculatePercentageChange(
            this.monthlyInvoicingSummary.totalAmount,
            this.lastMonthlyInvoicingSummary.totalAmount
        );

        const invoicesChange = this.calculatePercentageChange(
            this.monthlyInvoicingSummary.totalInvoices,
            this.lastMonthlyInvoicingSummary.totalInvoices
        );

        const paidInvoicesChange = this.calculatePercentageChange(
            this.monthlyInvoicingSummary.totalPaidInvoices,
            this.lastMonthlyInvoicingSummary.totalPaidInvoices
        );

        this.percentageMonthlyInvoicing = {
            totalUnallocated: unallocatedChange.percentage,
            totalUnallocatedUpDown: unallocatedChange.direction,
            totalPaid: paidChange.percentage,
            totalPaidUpDown: paidChange.direction,
            totalAmount: amountChange.percentage,
            totalAmountUpDown: amountChange.direction,
            totalInvoices: invoicesChange.percentage,
            totalInvoicesUpDown: invoicesChange.direction,
            totalPaidInvoices: paidInvoicesChange.percentage,
            totalPaidInvoicesUpDown: paidInvoicesChange.direction
        };
    }


}

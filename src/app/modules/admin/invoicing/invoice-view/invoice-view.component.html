<div
    class="absolute inset-0 min-w-0 overflow-auto text-center print:overflow-visible"
    cdkScrollable
>
    <div class="inline-block p-6 text-left sm:p-10 print:p-0">
        <!-- Back to Dashboard Link -->
        <div class="mb-4">
            <a
                [routerLink]="'/invoicing/list'"
                class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
            >
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Invoices
            </a>
        </div>

        <!-- Invoice -->
        @if (isLoading) {
            <div class="flex justify-center gap-2">
                <mat-progress-spinner
                    [diameter]="24"
                    [mode]="'indeterminate'"
                ></mat-progress-spinner>
                <p>Loading invoice details...</p>
            </div>
        } @else if (invoice && isOrganizationLoaded) {
            <div
                class="bg-card w-240 rounded-2xl p-16 shadow print:w-auto print:rounded-none print:bg-transparent print:shadow-none"
            >
                <div class="flex gap-4 -mt-10 mb-10 justify-end">
                    <button
                        (click)="editInvoice()"
                        class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        <span class="material-icons mr-2">edit</span>
                        Edit
                    </button>

                    <button
                        (click)="openEmailDialog()"
                        [disabled]="isSendingEmail"
                        class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        @if (isSendingEmail) {
                            <mat-progress-spinner
                                [diameter]="24"
                                [mode]="'indeterminate'"
                                [color]="'accent'"
                            ></mat-progress-spinner>
                            <span class="ml-2">Sending...</span>
                        } @else {
                            <span class="material-icons mr-2">email</span>
                            Send Email
                        }
                    </button>
                    <button
                        (click)="printInvoice()"
                        class="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                    >
                        <span class="material-icons mr-2">print</span>
                        Print
                    </button>
                </div>
                <div class="flex items-start justify-between">
                    <div class="grid grid-cols-2 gap-x-4 gap-y-1">
                        <div class="text-secondary text-4xl tracking-tight">
                            INVOICE
                        </div>
                        <div class="text-4xl">
                            #{{ invoicePrefix }}{{ invoice.invoiceNumber }}
                        </div>
                        <div class="text-secondary font-medium tracking-tight">
                            INVOICE DATE
                        </div>
                        <div class="font-medium">
                            {{ invoice.invoiceDate | date }}
                        </div>
                        <div class="text-secondary font-medium tracking-tight">
                            DUE DATE
                        </div>
                        <div class="font-medium">
                            {{ invoice.dueDate | date }}
                        </div>
                        <div class="text-secondary font-medium tracking-tight">
                            TOTAL DUE
                        </div>
                        <div class="font-medium">
                            {{
                                invoice.netTotal
                                    | currency
                                        : "USD"
                                        : "symbol-narrow"
                                        : "1.2-2"
                            }}
                        </div>
                    </div>
                    <div
                        class="dark -mr-16 grid auto-cols-max grid-flow-col gap-x-8 rounded-l-2xl bg-accent px-8 py-6"
                    >
                        <div class="w-24 place-self-center">
                            @if (selectedOrganisation?.avatar) {
                                <!-- Add code to display avatar -->
                                <img
                                    [src]="selectedOrganisation.avatar"
                                    alt="Organization Avatar"
                                    class="w-full h-full object-cover"
                                />
                            } @else if (
                                selectedOrganisation?.organisationName
                            ) {
                                <div
                                    class="flex items-center justify-center w-full h-full rounded overflow-hidden uppercase text-8xl font-bold leading-none bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                >
                                    {{
                                        selectedOrganisation.organisationName.charAt(
                                            0
                                        )
                                    }}
                                </div>
                            }
                        </div>
                        <div
                            class="border-l pl-10 text-md max-w-[200px] break-words overflow-hidden"
                        >
                            @if (selectedOrganisation) {
                                <div class="font-medium">
                                    {{ selectedOrganisation.organisationName }}
                                </div>
                                <div class="line-clamp-2">
                                    {{ selectedOrganisation.address }}
                                </div>

                                <div>ABN: {{ selectedOrganisation.abn }}</div>
                                <div>
                                    {{ email }}
                                </div>
                                <div>
                                    {{ phoneNumber }}
                                </div>
                                <div>
                                    {{ selectedOrganisation.website }}
                                </div>
                            } @else {
                                <div>
                                    Organization information not available
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <div class="text-md mt-8">
                    @if (customer) {
                        <div class="text-xl font-medium">
                            {{ customer.name }}
                        </div>
                        <div>
                            {{ customer.addresses?.[0]?.address || "" }}
                        </div>
                        <div>{{ getCustomerEmail() }}</div>
                        <div>{{ getCustomerPhone() }}</div>
                    }
                </div>
                @if (selectedJob) {
                    <div
                        class="mt-8 p-8 bg-card flex flex-auto flex-col overflow-hidden rounded-2xl shadow"
                    >
                        <h1 class="font-bold">JOB Details</h1>
                        <div class="mt-4 flex items-start space-x-4">
                            <strong class="mr-2">Job Title:</strong
                            >{{ selectedJob.jobTitle }}
                            <strong class="mr-2">Job Number:</strong
                            >{{ selectedJob.jobNumber }}
                            <strong class="mr-2">Job Status:</strong
                            ><span class="ml-2">{{
                                selectedJob.jobStatus
                            }}</span>
                        </div>
                    </div>
                }

                <div class="mt-12 grid grid-cols-12 gap-x-1">
                    <!-- Columns -->
                    <div class="text-secondary col-span-6 text-md font-medium">
                        SERVICE
                    </div>
                    <div class="text-secondary text-right text-md font-medium">
                        RATE
                    </div>
                    <div class="text-secondary text-right text-md font-medium">
                        QTY
                    </div>
                    <div class="text-secondary text-right text-md font-medium">
                        DISCOUNT
                    </div>
                    <div
                        class="text-secondary col-span-2 text-right text-md font-medium"
                    >
                        TOTAL
                    </div>

                    <!-- Divider -->
                    <div class="col-span-12 my-4 border-b"></div>

                    @for (item of parsedItems; track item) {
                        <!-- Item -->
                        <div class="col-span-6 text-lg font-medium">
                            {{ item.description }}
                        </div>
                        <div class="self-center text-right">
                            {{
                                item.price
                                    | currency
                                        : "USD"
                                        : "symbol-narrow"
                                        : "1.2-2"
                            }}
                        </div>
                        <div class="self-center text-right">
                            {{ item.quantity }}
                        </div>
                        <div class="self-center text-right">
                            {{
                                item.discount
                                    | currency
                                        : "USD"
                                        : "symbol-narrow"
                                        : "1.2-2"
                            }}
                        </div>
                        <div class="col-span-2 self-center text-right">
                            {{
                                item.total
                                    | currency
                                        : "USD"
                                        : "symbol-narrow"
                                        : "1.2-2"
                            }}
                        </div>

                        <!-- Divider -->
                        <div class="col-span-12 my-4 border-b"></div>
                    }

                    <!-- Spacer -->
                    <div class="col-span-12 mt-16"></div>

                    <!-- Subtotal -->
                    <div
                        class="text-secondary col-span-10 self-center font-medium tracking-tight"
                    >
                        SUBTOTAL
                    </div>
                    <div class="col-span-2 text-right text-lg">
                        {{
                            invoice.total
                                | currency: "USD" : "symbol-narrow" : "1.2-2"
                        }}
                    </div>

                    <!-- Divider -->
                    <div class="col-span-12 my-3 border-b"></div>

                    <!-- Tax -->
                    <div
                        class="text-secondary col-span-10 self-center font-medium tracking-tight"
                    >
                        TAX
                    </div>
                    <div class="col-span-2 text-right text-lg">
                        {{
                            invoice.tax
                                | currency: "USD" : "symbol-narrow" : "1.2-2"
                        }}
                    </div>

                    <!-- Divider -->
                    <div class="col-span-12 my-3 border-b"></div>

                    <!-- Total -->
                    <div
                        class="text-secondary col-span-10 self-center text-2xl font-medium tracking-tight"
                    >
                        TOTAL
                    </div>
                    <div class="col-span-2 text-right text-2xl font-medium">
                        {{
                            invoice.netTotal
                                | currency: "USD" : "symbol-narrow" : "1.2-2"
                        }}
                    </div>
                </div>

                <div class="mt-16">
                    <div class="font-medium">
                        Please pay within {{ invoice.paymentTerms }} days. Thank
                        you for your business.
                    </div>
                    <div class="mt-4 flex items-start">
                        @if (selectedOrganisation?.avatar) {
                            <!-- Add code to display avatar -->
                            <img
                                [src]="selectedOrganisation.avatar"
                                alt="Organization Avatar"
                                class="mt-2 w-10 flex-0"
                            />
                        } @else if (selectedOrganisation?.organisationName) {
                            <div
                                class="flex items-center justify-center w-10 h-10 rounded overflow-hidden uppercase text-8xl font-bold leading-none bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                            >
                                {{
                                    selectedOrganisation.organisationName.charAt(
                                        0
                                    )
                                }}
                            </div>
                        }
                        <div class="text-secondary ml-6 text-sm">
                            <p [innerHTML]="invoice.description"></p>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

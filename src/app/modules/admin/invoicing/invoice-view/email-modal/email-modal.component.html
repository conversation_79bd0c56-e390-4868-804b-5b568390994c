<div class="-m-6 flex max-h-screen max-w-240 flex-col md:min-w-160">
    <!-- Header -->
    <div
        class="flex h-16 flex-0 items-center justify-between bg-primary pl-6 pr-3 text-on-primary sm:pl-8 sm:pr-5"
    >
        <div class="text-lg font-medium">Send Invoice Email</div>
        <button mat-icon-button (click)="close()" [tabIndex]="-1">
            <mat-icon
                class="text-current"
                [svgIcon]="'heroicons_outline:x-mark'"
            ></mat-icon>
        </button>
    </div>

    <form
        class="flex flex-auto flex-col overflow-y-auto p-6 sm:p-8"
        [formGroup]="emailForm"
        (ngSubmit)="onSend()"
    >
        <div class="mt-4 flex flex-col gap-4">
            <mat-form-field class="w-full" appearance="outline">
                <mat-label>Email Recipients</mat-label>
                <mat-chip-grid #chipGrid formArrayName="emails">
                    @for (email of emailsArray.controls; track email) {
                        <mat-chip-row
                            [removable]="true"
                            (removed)="removeEmail($index)"
                            [aria-label]="'Email ' + email.value"
                        >
                            {{ email.value }}
                            <button
                                matChipRemove
                                [attr.aria-label]="'Remove ' + email.value"
                                type="button"
                            >
                                <mat-icon>cancel</mat-icon>
                            </button>
                        </mat-chip-row>
                    }
                    <input
                        placeholder="Add email"
                        [matChipInputFor]="chipGrid"
                        [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                        [matChipInputAddOnBlur]="true"
                        (matChipInputTokenEnd)="addEmail($event)"
                        aria-label="Enter email address"
                    />
                </mat-chip-grid>
                <mat-hint>Press Enter or comma to add email</mat-hint>
            </mat-form-field>

            <quill-editor
                formControlName="message"
                [styles]="{ height: '200px' }"
                placeholder="Compose your message..."
                class="mt-4"
                [bounds]="'self'"
                [modules]="quillModules"
            >
            </quill-editor>
        </div>

        <div
            class="mt-4 flex flex-col justify-between sm:mt-6 sm:flex-row sm:items-center"
        >
            <div class="flex gap-3 p-4">
                <button mat-button type="button" [mat-dialog-close]="undefined">
                    Cancel
                </button>
                <button
                    mat-raised-button
                    color="primary"
                    type="submit"
                    [disabled]="!emailForm.valid || emailsArray.length === 0"
                >
                    Send
                </button>
            </div>
        </div>
    </form>
</div>

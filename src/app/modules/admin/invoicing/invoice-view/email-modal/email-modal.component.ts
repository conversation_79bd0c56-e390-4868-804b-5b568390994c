import { Component, Inject, OnInit, ViewEncapsulation } from '@angular/core';
import {
    FormBuilder,
    FormGroup,
    FormArray,
    FormsModule,
    ReactiveFormsModule
} from '@angular/forms';
import {
    MatDialogRef,
    MAT_DIALOG_DATA,
    MatDialogModule
} from '@angular/material/dialog';
import { MatChipsModule, MatChipInputEvent } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { QuillEditorComponent } from 'ngx-quill';

@Component({
    selector: 'app-email-modal-compose',
    templateUrl: './email-modal.component.html',
    styleUrls: ['./email-modal.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: true,
    imports: [
        MatButtonModule,
        MatIconModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatChipsModule,
        MatDialogModule,
        QuillEditorComponent,

    ],
})
export class EmailModalComponent implements OnInit {
    emailForm: FormGroup;
    separatorKeysCodes: number[] = [ENTER, COMMA];
    quillModules: any = {
        toolbar: [
            ['bold', 'italic', 'underline'],
            [{ align: [] }, { list: 'ordered' }, { list: 'bullet' }],
            ['clean'],
        ],
    };

    constructor(
        private fb: FormBuilder,
        public dialogRef: MatDialogRef<EmailModalComponent>,
        @Inject(MAT_DIALOG_DATA) public data: { customerEmail: string }
    ) {
        this.emailForm = this.fb.group({
            emails: this.fb.array([]),
            message: ['']
        });

        if (data?.customerEmail) {
            this.emailsArray.push(this.fb.control(data.customerEmail));
        }
    }

    ngOnInit(): void {
        // Any additional initialization if needed
    }

    get emailsArray(): FormArray {
        return this.emailForm.get('emails') as FormArray;
    }

    addEmail(event: MatChipInputEvent): void {
        const value = (event.value || '').trim();

        if (value && this.isValidEmail(value)) {
            this.emailsArray.push(this.fb.control(value));
            event.chipInput!.clear();
        }
    }

    removeEmail(index: number): void {
        this.emailsArray.removeAt(index);
    }

    isValidEmail(email: string): boolean {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }

    onSend(): void {
        if (this.emailForm.valid && this.emailsArray.length > 0) {
            this.dialogRef.close({
                emails: this.emailsArray.value,
                message: this.emailForm.get('message')?.value
            });
        }

    }
    close(): void {
        this.dialogRef.close();
    }
}

import { Cur<PERSON>cyPipe, DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { InvoicingService } from 'app/core/databaseModels/invoicing/invoicing.service';
import { Invoicing, InvoicingItem } from 'app/core/databaseModels/invoicing/invoicing.type';
import { CustomerService } from 'app/core/databaseModels/customers/customers.service';
import { Customer } from 'app/core/databaseModels/customers/contacts.types';
import { finalize, switchMap } from 'rxjs/operators';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ScheduledJobsService } from 'app/core/databaseModels/scheduledJobs/scheduledJob.service';
import { firstValueFrom, forkJoin, of } from 'rxjs';
import { ScheduledJob } from 'app/core/databaseModels/scheduledJobs/scheduledJob.types';
import { OrganisationsService } from 'app/core/databaseModels/organisations/organisations.service';
import { Organisation } from 'app/core/databaseModels/organisations/organisations.types';
import { MatDialog } from '@angular/material/dialog';
import { InvoicePdfService } from 'app/services/invoice-pdf.service';
import { EmailModalComponent } from './email-modal/email-modal.component';
import { EmailService } from 'app/core/databaseModels/email.service';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

@Component({
    selector: 'app-invoice-view',
    standalone: true,
    imports: [DatePipe,
        CurrencyPipe,
        RouterModule,
        MatProgressSpinnerModule,
        MatSnackBarModule
    ],
    templateUrl: './invoice-view.component.html',
    styleUrls: ['./invoice-view.component.scss']
})
export class InvoiceViewComponent implements OnInit {
    invoice: Invoicing | null = null;
    customer: Customer | null = null;
    parsedItems: InvoicingItem[] = [];
    invoicePrefix: string = '';
    dueDays: number = 0;
    selectedJob: ScheduledJob | null = null;
    selectedOrganisation: Organisation | null = null;
    isLoading: boolean = true;
    isOrganizationLoaded: boolean = false;
    isSendingEmail: boolean = false;
    phoneNumber: string = '';
    email: string = '';

    constructor(
        private route: ActivatedRoute,
        private invoicingService: InvoicingService,
        private customerService: CustomerService,
        private scheduledJobsService: ScheduledJobsService,
        private _organisationsService: OrganisationsService,
        private dialog: MatDialog,
        private invoicePdfService: InvoicePdfService,
        private _emailService: EmailService,
        private http: HttpClient,
        private _snackBar: MatSnackBar,
        private router: Router,
    ) { }

    ngOnInit(): void {
        const id = this.route.snapshot.paramMap.get('id');
        if (id) {
            this.loadInvoiceAndCustomer(id);
        } else {
            this.isLoading = false;
        }
    }

    loadInvoiceAndCustomer(id: string): void {
        this.isLoading = true;
        forkJoin({
            invoice: this.invoicingService.getInvoicing(id),
            settings: this.invoicingService.getNextInvoiceNumber()
        }).pipe(
            switchMap(({ invoice, settings }) => {
                this.invoice = invoice;
                this.invoicePrefix = settings.prefix;
                this.dueDays = settings.dueDays;
                this.parseInvoiceItems();
                return forkJoin({
                    customer: this.customerService.getCustomer(invoice.customerId),
                    job: invoice.scheduledJobId ? this.scheduledJobsService.getScheduledJob(invoice.scheduledJobId) : of(null),
                    organisation: invoice.organisationID ? this._organisationsService.getOrganisation(invoice.organisationID) : of(null)
                });
            }),
            finalize(() => {
                this.isLoading = false;
                this.isOrganizationLoaded = true;
            })
        ).subscribe({
            next: ({ customer, job, organisation }) => {
                this.customer = customer;
                this.selectedJob = job;
                this.selectedOrganisation = organisation;
                this.phoneNumber = this.getOrganisationPhone();
                this.email = this.organisationEmail();
                // console.log(this.selectedOrganisation);
            },
            error: (error) => {
                console.error('Error fetching invoice, customer, and job:', error);
                this.isOrganizationLoaded = false;
            }
        });
    }

    parseInvoiceItems(): void {
        if (this.invoice && this.invoice.items) {
            this.parsedItems = this.invoice.items.map(item => {
                if (typeof item === 'string') {
                    try {
                        return JSON.parse(item) as InvoicingItem;
                    } catch (error) {
                        console.error('Error parsing invoice item:', error);
                        return null;
                    }
                } else if (typeof item === 'object' && item !== null) {
                    return item as InvoicingItem;
                } else {
                    console.error('Invalid invoice item format:', item);
                    return null;
                }
            }).filter((item): item is InvoicingItem => item !== null);
        }
    }

    getCustomerPhone(): string {
        if (this.customer && this.customer.phoneNumbers && this.customer.phoneNumbers.length > 0) {
            const phoneNumber = this.customer.phoneNumbers[0];
            if (typeof phoneNumber === 'string') {
                try {
                    const parsed = JSON.parse(phoneNumber);
                    return parsed.phoneNumber || '';
                } catch {
                    return phoneNumber;
                }
            } else if (typeof phoneNumber === 'object' && phoneNumber.phoneNumber) {
                return phoneNumber.phoneNumber;
            }
        }
        return '';
    }

    getCustomerEmail(): string {
        if (this.customer && this.customer.emails && this.customer.emails.length > 0) {
            const email = this.customer.emails[0];
            if (typeof email === 'string') {
                try {
                    const parsed = JSON.parse(email);
                    return parsed.email || '';
                } catch {
                    return email;
                }
            } else if (typeof email === 'object' && email.email) {
                return email.email;
            }
        }
        return '';
    }
    getOrganisationPhone(): string {
        if (this.selectedOrganisation && this.selectedOrganisation.phoneNumbers && this.selectedOrganisation.phoneNumbers.length > 0) {
            const phoneNumber = this.selectedOrganisation.phoneNumbers[0];
            if (typeof phoneNumber === 'string') {
                try {
                    const parsed = JSON.parse(phoneNumber);
                    return parsed.phoneNumber || '';
                } catch {
                    return phoneNumber;
                }
            } else if (typeof phoneNumber === 'object' && phoneNumber.phoneNumber) {
                return phoneNumber.phoneNumber;
            }
        }
        return '';
    }

    organisationEmail(): string {
        if (this.selectedOrganisation && this.selectedOrganisation.emails && this.selectedOrganisation.emails.length > 0) {
            const email = this.selectedOrganisation.emails[0];
            if (typeof email === 'string') {
                try {
                    const parsed = JSON.parse(email);
                    return parsed.email || '';
                } catch {
                    return email;
                }
            } else if (typeof email === 'object' && email.email) {
                return email.email;
            }
        }
        return '';
    }
    // invoice-view.component.ts
    openEmailDialog(): void {
        const dialogRef = this.dialog.open(EmailModalComponent, {
            data: { customerEmail: this.getCustomerEmail() },
            autoFocus: 'dialog',
            restoreFocus: true
        });

        dialogRef.afterClosed().subscribe(async result => {
            if (result) {
                try {
                    this.isSendingEmail = true;
                    // Load and convert organization logo to base64 if it exists
                    let organisationWithBase64Logo = { ...this.selectedOrganisation };
                    if (this.selectedOrganisation?.avatar) {
                        const logoBase64 = await this.loadImage(this.selectedOrganisation.avatar);
                        if (logoBase64) {
                            organisationWithBase64Logo.avatar = logoBase64;
                        }
                    }

                    const payloadData = {
                        to: result.emails,
                        subject: `🧾 Invoice - #${this.invoicePrefix}${this.invoice.invoiceNumber} from ${this.selectedOrganisation.organisationName}`,
                        type: 'invoice',
                        message: result.message,
                        email: this.getCustomerEmail(),
                        fullName: this.customer.name,
                        invoice: this.invoice,
                        customer: this.customer,
                        organisation: organisationWithBase64Logo,
                        invoicePrefix: '',
                        dueDays: this.dueDays,
                        currency: '$'
                    };



                    this._emailService.sendInvoiceEmail(payloadData).subscribe(
                        response => {
                            this._snackBar.open('Email sent successfully', 'Close', {
                                duration: 3000,
                            });

                            this.isSendingEmail = false;

                        },
                        error => {
                            this.isSendingEmail = false;

                            this._snackBar.open('Failed to send email', 'Close', {
                                duration: 3000,
                            });
                            console.error('Failed to send email:', error);
                        }
                    );
                } catch (error) {
                    this.isSendingEmail = false;

                    this._snackBar.open('Error preparing email data', 'Close', {
                        duration: 3000,
                    });
                    console.error('Error preparing email data:', error);
                }
            }
        });
    }

    private async loadImage(path: string): Promise<string | null> {
        try {
            const imageBlob = await firstValueFrom(this.http.get(path, { responseType: 'blob' }));
            return new Promise<string | null>((resolve, reject) => {
                const reader = new FileReader();
                reader.onloadend = () => resolve(reader.result as string);
                reader.onerror = reject;
                reader.readAsDataURL(imageBlob);
            });
        } catch (error) {
            console.error('Error loading image:', error);
            return null;
        }
    }

    async printInvoice(): Promise<void> {
        if (this.invoice && this.customer && this.selectedOrganisation) {
            this.invoicePdfService.generateInvoicePdf(
                this.invoice,
                this.customer,
                this.selectedOrganisation,
                '',
                this.dueDays
            );
        }
        // let organisationWithBase64Logo = { ...this.selectedOrganisation };
        // if (this.selectedOrganisation?.avatar) {
        //     const logoBase64 = await this.loadImage(this.selectedOrganisation.avatar);
        //     if (logoBase64) {
        //         organisationWithBase64Logo.avatar = logoBase64;
        //     }
        // }
        // const payloadData = {
        //     to: '<EMAIL>',
        //     subject: `🧾 Invoice - #${this.invoicePrefix}${this.invoice.invoiceNumber} from ${this.selectedOrganisation.organisationName}`,
        //     type: 'downloadInvoice', // Make sure this matches your server branch
        //     message: 'Please find attached the invoice for your reference.',
        //     email: this.getCustomerEmail(),
        //     fullName: this.customer.name,
        //     invoice: this.invoice, // Ensure this object contains invoiceNumber, dueDate, netTotal, etc.
        //     customer: this.customer,
        //     organisation: organisationWithBase64Logo,
        //     invoicePrefix: this.invoicePrefix,
        //     dueDays: this.dueDays,
        //     currency: '$'
        // };
        // console.log('payloadData', payloadData);
        // // Call the download method from your EmailService
        // this._emailService.downloadInvoicePDF(payloadData).subscribe(
        //     (blob: Blob) => {
        //         // Create a temporary URL for the blob
        //         const fileURL = window.URL.createObjectURL(blob);
        //         const link = document.createElement('a');
        //         link.href = fileURL;
        //         // Set the filename using your invoicePrefix and invoice number
        //         link.download = `Invoice-${this.invoicePrefix}${this.invoice.invoiceNumber}.pdf`;
        //         // Append to the document and trigger a click to start the download
        //         document.body.appendChild(link);
        //         link.click();
        //         // Clean up the temporary link and URL
        //         document.body.removeChild(link);
        //         window.URL.revokeObjectURL(fileURL);
        //     },
        //     error => {
        //         this._snackBar.open('Failed to download invoice', 'Close', { duration: 3000 });
        //         console.error('Failed to download invoice:', error);
        //     }
        // );
    }
    editInvoice(): void {
        this.router.navigate(['/invoicing/edit', this.invoice.$id]);
    }
}

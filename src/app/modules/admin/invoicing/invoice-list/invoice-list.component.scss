// invoice-list.component.scss
.mat-mdc-footer-row {
    font-weight: bold;
    background-color: #f9fafb;
    border-top: 2px solid #e5e7eb;
}

.mat-mdc-footer-cell {
    padding: 1rem !important;
}

// Ensure footer text is visible
.mat-mdc-footer-row td {
    color: rgba(0, 0, 0, 0.87);
    font-weight: 500;
}

// Style for the customer filter
mat-form-field.customer-filter {
    width: 100%;
    .mat-form-field-wrapper {
        padding-bottom: 0;
    }
}

.invoice-summary {
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04), 
                0 8px 16px rgba(148, 163, 184, 0.1);

    .summary-item {
        flex: 1;
        text-align: center;
        
        .item-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #64748b;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .item-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;

            &.success {
                color: #059669;
            }

            &.warning {
                color: #dc2626;
            }
        }
    }

    .summary-divider {
        width: 1px;
        height: 40px;
        background: #e2e8f0;
        margin: 0 2rem;
    }

    &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 
                    0 10px 20px rgba(148, 163, 184, 0.12);
        transition: all 0.2s ease;
    }
}

// Responsive design
@media (max-width: 768px) {
    .invoice-summary {
        flex-direction: column;
        padding: 1rem;

        .summary-item {
            padding: 1rem 0;
        }

        .summary-divider {
            width: 80%;
            height: 1px;
            margin: 0.5rem 0;
        }
    }
}

.customer-filter-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .filter-button {
        color: #64748b;
        width: 32px;
        height: 32px;
        line-height: 32px;
        
        &:hover {
            color: #1e293b;
            background-color: #f1f5f9;
        }
    }

    .filter-dropdown {
        position: relative;
        
        .customer-search {
            width: 180px;
            
            ::ng-deep {
                .mat-mdc-form-field-flex {
                    padding-right: 4px;
                }
                
                .mat-mdc-form-field-wrapper {
                    padding: 0;
                }

                .mat-mdc-text-field-wrapper {
                    height: 36px;
                    border-radius: 18px;
                    padding: 0 12px;
                }

                .mat-mdc-form-field-infix {
                    padding: 8px 0;
                    min-height: unset;
                }

                .mat-mdc-form-field-icon-suffix {
                    padding: 4px;
                    
                    button {
                        width: 20px;
                        height: 20px;
                        line-height: 20px;
                        
                        i {
                            font-size: 12px;
                        }
                    }
                }
            }
        }
    }

    .active-filter {
        .filter-chip {
            display: inline-flex;
            align-items: center;
            padding: 4px 10px;
            background: #f1f5f9;
            border-radius: 16px;
            font-size: 13px;
            color: #1e293b;
            border: 1px solid #e2e8f0;

            .clear-button {
                width: 18px;
                height: 18px;
                line-height: 18px;
                margin-left: 4px;
                
                i {
                    font-size: 10px;
                }
                
                &:hover {
                    color: #dc2626;
                    background-color: #fee2e2;
                }
            }
        }
    }
}

import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { InvoicingService } from 'app/core/databaseModels/invoicing/invoicing.service';
import { Invoicing, InvoiceStatus } from 'app/core/databaseModels/invoicing/invoicing.type';
import { CustomerService } from 'app/core/databaseModels/customers/customers.service';
import { Customer } from 'app/core/databaseModels/customers/contacts.types';
import { Router, RouterModule } from '@angular/router';
import { AsyncPipe, CurrencyPipe, DatePipe, KeyValuePipe, NgClass } from '@angular/common';
import { catchError, map, startWith, switchMap } from 'rxjs/operators';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { forkJoin, Observable, of } from 'rxjs';
import { ScheduledJobsService } from 'app/core/databaseModels/scheduledJobs/scheduledJob.service';
import { MatTooltip } from '@angular/material/tooltip';
import { MatSelectModule } from '@angular/material/select';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatIconButton } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';

@Component({
    selector: 'invoice-list',
    standalone: true,
    imports: [
        NgClass,
        MatTableModule,
        MatPaginatorModule,
        MatSortModule,
        MatButtonToggleModule,
        MatProgressSpinnerModule,
        DatePipe,
        KeyValuePipe,
        RouterModule,
        MatTooltip,
        CurrencyPipe,
        MatSelectModule,
        FormsModule,
        MatAutocompleteModule,
        ReactiveFormsModule,
        MatIconButton,
        AsyncPipe,
        MatAutocompleteModule,
        ReactiveFormsModule,
        AsyncPipe,
        MatFormFieldModule,
        MatInputModule
    ],
    templateUrl: './invoice-list.component.html',
    styleUrls: ['./invoice-list.component.scss']
})
export class InvoiceListComponent implements OnInit {
    @Input() limited: boolean = false;
    //displayedColumns: string[] = ['invoiceNumber', 'customerDetails', 'invoiceDate', 'dueDate', 'jobStatus', 'netTotal', 'amountPaid', 'invoiceStatus', 'actions'];
    dataSource: MatTableDataSource<Invoicing> = new MatTableDataSource([]);
    invoiceStatusEnum = InvoiceStatus;
    activeFilters: InvoiceStatus[] = [];
    customers: Customer[] = [];
    isLoading: boolean = false;
    invoicePrefix: string = '';
    jobDetails: { [key: string]: { status: string, title: string } } = {};




    displayedColumns: string[] = [
        'invoiceNumber', 'customerDetails', 'invoiceDate', 'dueDate',
        'jobStatus', 'netTotal', 'amountPaid', 'invoiceStatus', 'actions'
    ];

    selectedCustomerId: string = '';

    filteredCustomers$: Observable<Customer[]>;
    customerFilterActive = false;
    customerSearchCtrl = new FormControl('');
    selectedCustomerName: string = '';


    footerData = {
        totalPaid: 0,
        totalAmount: 0,
        totalOutstanding: 0
    };

    @ViewChild(MatPaginator) paginator!: MatPaginator;
    @ViewChild(MatSort) sort!: MatSort;

    constructor(
        private invoicingService: InvoicingService,
        private scheduledJobsService: ScheduledJobsService,
        private customerService: CustomerService,
        private router: Router
    ) { }

    ngOnInit(): void {
        this.getInvoiseSettings();
        this.loadCustomersAndInvoices();
        this.filteredCustomers$ = this.customerSearchCtrl.valueChanges.pipe(
            startWith(''),
            map(value => this._filterCustomers(value || ''))
        );
    }

    private _filterCustomers(value: string): Customer[] {
        const filterValue = value.toLowerCase();
        return this.customers.filter(customer =>
            customer.name.toLowerCase().includes(filterValue)
        );
    }

    toggleCustomerFilter(): void {
        this.customerFilterActive = !this.customerFilterActive;
        if (this.customerFilterActive) {
            setTimeout(() => {
                // Focus the input when dropdown opens
                document.getElementById('customerSearch')?.focus();
            });
        }
    }

    clearCustomerFilter(): void {
        this.selectedCustomerId = '';
        this.selectedCustomerName = '';
        this.customerFilterActive = false;
        this.customerSearchCtrl.setValue('');
        this.filterByCustomer('');
    }

    selectCustomer(customer: Customer): void {
        this.selectedCustomerId = customer.$id;
        this.selectedCustomerName = customer.name;
        this.customerFilterActive = false;
        this.customerSearchCtrl.setValue('');
        this.filterByCustomer(customer.$id);
    }

    getInvoiseSettings(): void {
        this.invoicingService.getInvoiceSettings().subscribe({
            next: (settings) => {
                this.invoicePrefix = settings.prefix;
            },
            error: (error) => {
                console.error('Error fetching data:', error);
            }
        });
    }

    loadCustomersAndInvoices(): void {
        this.isLoading = true;
        this.customerService.getCustomers().pipe(
            switchMap((customers) => {
                this.customers = customers;
                return this.invoicingService.getInvoicings(this.limited);
            }),
            switchMap((invoices) => {
                const uniqueJobIds = [...new Set(invoices.map(inv => inv.scheduledJobId).filter(id => id))];
                if (uniqueJobIds.length === 0) {
                    // No job IDs to fetch, proceed with invoices
                    this.jobDetails = {};
                    return of(invoices);
                }

                // Create observables for each job with comprehensive error handling
                const jobObservables = uniqueJobIds.map(jobId =>
                    this.scheduledJobsService.getScheduledJob(jobId).pipe(
                        map(job => ({
                            jobId,
                            status: job.jobStatus,
                            title: job.jobTitle,
                            found: true
                        })),
                        catchError(error => {
                            // Handle different types of errors
                            if (error?.code === 404 || error?.status === 404) {
                                console.warn(`Job ${jobId} was deleted or not found`);
                            } else {
                                console.warn(`Error fetching job ${jobId}:`, error);
                            }

                            // Return a placeholder for deleted/missing jobs
                            return of({
                                jobId,
                                status: 'Deleted',
                                title: 'Job Not Found',
                                found: false
                            });
                        })
                    )
                );

                // Use forkJoin - all observables will emit a value
                return forkJoin(jobObservables).pipe(
                    map(jobDetails => {
                        // Build job details map
                        this.jobDetails = jobDetails.reduce((acc, job) => {
                            acc[job.jobId] = {
                                status: job.status,
                                title: job.title
                            };
                            return acc;
                        }, {} as { [key: string]: { status: string, title: string } });

                        return invoices;
                    }),
                    catchError(forkJoinError => {
                        // This should not happen with our error handling above, but just in case
                        console.error('Unexpected error in forkJoin:', forkJoinError);
                        this.jobDetails = {};
                        return of(invoices);
                    })
                );
            }),
            catchError(mainError => {
                // Handle errors in the main chain (customers or invoices fetch)
                console.error('Error in main data fetch:', mainError);
                this.isLoading = false;
                return of([]); // Return empty array to prevent complete failure
            })
        ).subscribe({
            next: (invoices) => {
                this.dataSource.data = invoices;
                this.dataSource.paginator = this.paginator;
                this.dataSource.sort = this.sort;
                this.calculateTotals();
                this.isLoading = false;
            },
            error: (error) => {
                // This should rarely happen now with our error handling
                this.isLoading = false;
                console.error('Unexpected error in subscription:', error);
            }
        });
    }

    getJobStatus(scheduledJobId: string): string {
        return this.jobDetails[scheduledJobId]?.status || 'N/A';
    }

    getJobTitle(scheduledJobId: string): string {
        return this.jobDetails[scheduledJobId]?.title || 'N/A';
    }

    getCustomerName(id: string): { name: string, contact: string } {
        try {
            if (this.customers == null) {
                return { name: '', contact: '' };
            }

            const customer = this.customers.find(item => item.$id === id);
            if (customer) {
                let number;

                if (typeof customer.phoneNumbers[0] === 'string') {
                    try {
                        number = JSON.parse(customer.phoneNumbers[0]);
                    } catch (error) {
                        console.error('Error parsing phone number', error);
                        return { name: customer.name, contact: 'Invalid Contact' };
                    }
                } else {
                    number = customer.phoneNumbers[0];
                }

                return { name: customer.name, contact: number.phoneNumber };
            } else {
                return { name: '', contact: '' };
            }
        } catch (error) {
            console.error('Error parsing customer ID', error);
            return { name: '', contact: '' };
        }
    }

    applyFilter(event: Event): void {
        const filterValue = (event.target as HTMLInputElement).value.trim().toLowerCase();

        this.dataSource.filterPredicate = (data: Invoicing, filter: string) => {
            // Customer filter
            const customerMatch = !this.selectedCustomerId || data.customerId === this.selectedCustomerId;

            // Text filter - check against all object values
            const textMatch = !filter || Object.values(data).some(value => {
                if (value === null || value === undefined) return false;
                return value.toString().toLowerCase().includes(filter);
            });

            const result = customerMatch && textMatch;

            // If this row matches the filter, it will be included in filteredData
            return result;
        };

        // Apply the filter
        this.dataSource.filter = filterValue;

        // Reset to first page if using paginator
        if (this.dataSource.paginator) {
            this.dataSource.paginator.firstPage();
        }

        // Calculate totals based on filtered data
        this.calculateTotals(); // Remove setTimeout
    }

    filterByCustomer(customerId: string): void {
        this.selectedCustomerId = customerId;
        // Update filterPredicate for both customer and text filtering
        this.dataSource.filterPredicate = (data: Invoicing, filter: string) => {
            const customerMatch = !this.selectedCustomerId || data.customerId === this.selectedCustomerId;
            const searchText = this.dataSource.filter?.toLowerCase() || '';
            const textMatch = !searchText || Object.values(data).some(value =>
                value?.toString().toLowerCase().includes(searchText)
            );
            return customerMatch && textMatch;
        };
        // Trigger the filter
        this.dataSource.filter = this.dataSource.filter || ' ';
        this.calculateTotals();
    }

    toggleStatusFilter(status: InvoiceStatus): void {
        const index = this.activeFilters.indexOf(status);
        if (index === -1) {
            this.activeFilters.push(status);
        } else {
            this.activeFilters.splice(index, 1);
        }
        this.applyStatusFilter();
    }

    applyStatusFilter(): void {
        this.dataSource.filterPredicate = (data: Invoicing) => {
            return this.activeFilters.length === 0 || this.activeFilters.includes(data.invoiceStatus);
        };
        this.dataSource.filter = 'dummy'; // Trigger filter
        this.calculateTotals(); // Add this line
    }

    viewInvoice(id: string): void {
        this.router.navigate(['/invoicing/view', id]);
    }

    editInvoice(id: string): void {
        this.router.navigate(['/invoicing/edit', id]);
    }

    calculateTotals(): void {
        this.footerData = this.dataSource.filteredData.reduce((totals, invoice) => ({
            totalPaid: totals.totalPaid + invoice.amountPaid,
            totalAmount: totals.totalAmount + invoice.netTotal,
            totalOutstanding: totals.totalOutstanding + (invoice.netTotal - invoice.amountPaid)
        }), {
            totalPaid: 0,
            totalAmount: 0,
            totalOutstanding: 0
        });
    }
}

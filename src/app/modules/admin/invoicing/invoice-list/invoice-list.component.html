<div>
    @if (!limited) {
        <!-- Add this before your table -->
        <div class="invoice-summary mb-10">
            <div class="summary-item">
                <div class="item-label">Total Amount</div>
                <div class="item-value">
                    {{ footerData.totalAmount | currency }}
                </div>
            </div>
            <div class="summary-divider"></div>
            <div class="summary-item">
                <div class="item-label">Total Paid</div>
                <div class="item-value success">
                    {{ footerData.totalPaid | currency }}
                </div>
            </div>
            <div class="summary-divider"></div>
            <div class="summary-item">
                <div class="item-label">Outstanding</div>
                <div class="item-value warning">
                    {{ footerData.totalOutstanding | currency }}
                </div>
            </div>
        </div>

        <div class="mb-6 flex justify-between items-center">
            <mat-button-toggle-group multiple>
                @for (
                    status of invoiceStatusEnum | keyvalue;
                    track status.key
                ) {
                    <mat-button-toggle
                        (change)="toggleStatusFilter(status.value)"
                        [checked]="activeFilters.includes(status.value)"
                    >
                        {{ status.value }}
                    </mat-button-toggle>
                }
            </mat-button-toggle-group>
            <!-- Add customer filter -->

            <div class="customer-filter-container">
                @if (!customerFilterActive) {
                    <button
                        mat-icon-button
                        matTooltip="Filter by Customer"
                        (click)="toggleCustomerFilter()"
                    >
                        <i
                            class="fa-solid fa-user-magnifying-glass icon-size-3"
                        ></i>
                    </button>
                } @else {
                    <div class="filter-dropdown">
                        <mat-form-field
                            appearance="outline"
                            class="customer-search"
                        >
                            <input
                                type="text"
                                matInput
                                id="customerSearch"
                                [formControl]="customerSearchCtrl"
                                [matAutocomplete]="auto"
                                placeholder="Search customers..."
                            />
                            <button
                                mat-icon-button
                                matSuffix
                                (click)="toggleCustomerFilter()"
                            >
                                <i class="fas fa-times"></i>
                            </button>
                        </mat-form-field>

                        <mat-autocomplete #auto="matAutocomplete">
                            @for (
                                customer of filteredCustomers$ | async;
                                track customer.$id
                            ) {
                                <mat-option (click)="selectCustomer(customer)">
                                    {{ customer.name }}
                                </mat-option>
                            }
                        </mat-autocomplete>
                    </div>
                }

                @if (selectedCustomerName) {
                    <div class="active-filter">
                        <span class="filter-chip">
                            {{ selectedCustomerName }}
                            <button
                                mat-icon-button
                                class="clear-button"
                                (click)="clearCustomerFilter()"
                            >
                                <i class="fas fa-times"></i>
                            </button>
                        </span>
                    </div>
                }
            </div>

            <!-- Existing search input -->
            <div class="w-1/4 ml-5">
                <input
                    (keyup)="applyFilter($event)"
                    placeholder="Search invoices..."
                    class="w-full px-4 py-2 rounded-full border focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
            </div>
        </div>
    }
    @if (dataSource.data.length === 0 && isLoading) {
        <div class="flex justify-center gap-2">
            <mat-progress-spinner
                [diameter]="24"
                [mode]="'indeterminate'"
            ></mat-progress-spinner>
            <p>Loading invoices...</p>
        </div>
    } @else {
        <table
            mat-table
            [dataSource]="dataSource"
            matSort
            class="w-full"
            [ngClass]="{ 'text-xs': limited }"
        >
            <ng-container matColumnDef="invoiceNumber">
                <th
                    mat-header-cell
                    *matHeaderCellDef
                    mat-sort-header
                    [ngClass]="{ 'text-xs': limited }"
                >
                    Invoice Number
                </th>
                <td
                    mat-cell
                    *matCellDef="let invoice"
                    [ngClass]="{ 'text-xs': limited }"
                >
                    {{ invoice.invoiceNumber }}
                </td>
            </ng-container>

            <ng-container matColumnDef="customerDetails">
                <th
                    mat-header-cell
                    *matHeaderCellDef
                    [ngClass]="{ 'text-xs': limited }"
                >
                    Customer Details
                </th>
                <td
                    mat-cell
                    *matCellDef="let invoice"
                    [ngClass]="{ 'text-xs': limited }"
                >
                    @if (invoice.customerId) {
                        @let customerInfo = getCustomerName(invoice.customerId);
                        <div>{{ customerInfo.name }}</div>
                        <div>{{ customerInfo.contact }}</div>
                    }
                </td>
            </ng-container>

            <ng-container matColumnDef="invoiceDate">
                <th
                    mat-header-cell
                    *matHeaderCellDef
                    mat-sort-header
                    [ngClass]="{ 'text-xs': limited }"
                >
                    Invoice Date
                </th>
                <td
                    mat-cell
                    *matCellDef="let invoice"
                    [ngClass]="{ 'text-xs': limited }"
                >
                    {{ invoice.invoiceDate | date }}
                </td>
            </ng-container>

            <ng-container matColumnDef="dueDate">
                <th
                    mat-header-cell
                    *matHeaderCellDef
                    mat-sort-header
                    [ngClass]="{ 'text-xs': limited }"
                >
                    Due Date
                </th>
                <td
                    mat-cell
                    *matCellDef="let invoice"
                    [ngClass]="{ 'text-xs': limited }"
                >
                    {{ invoice.dueDate | date }}
                </td>
            </ng-container>

            <ng-container matColumnDef="jobStatus">
                <th
                    mat-header-cell
                    *matHeaderCellDef
                    [ngClass]="{ 'text-xs': limited }"
                >
                    Job Status
                </th>
                <td
                    mat-cell
                    *matCellDef="let invoice"
                    [ngClass]="{ 'text-xs': limited }"
                >
                    <div>Title: {{ getJobTitle(invoice.scheduledJobId) }}</div>
                    <div class="capitalize">
                        Status:
                        <span
                            class="inline-flex text-[8px] items-center rounded-full px-1.5 py-0.1 font-bold uppercase tracking-wide"
                            [ngClass]="{
                                'bg-red-200 text-red-800 dark:bg-red-600 dark:text-red-50':
                                    getJobStatus(invoice.scheduledJobId) ===
                                    'Deleted',
                                'bg-green-100 text-green-800 dark:bg-green-600 dark:text-green-50':
                                    getJobStatus(invoice.scheduledJobId) ===
                                    'finished',

                                'bg-yellow-100 text-yellow-800 dark:bg-yellow-600 dark:text-yellow-50':
                                    getJobStatus(invoice.scheduledJobId) ===
                                    'pending'
                            }"
                        >
                            {{ getJobStatus(invoice.scheduledJobId) }}
                        </span>
                    </div>
                </td>
            </ng-container>

            <ng-container matColumnDef="netTotal">
                <th
                    mat-header-cell
                    *matHeaderCellDef
                    mat-sort-header
                    [ngClass]="{ 'text-xs': limited }"
                >
                    Total Amount
                </th>
                <td
                    mat-cell
                    *matCellDef="let invoice"
                    [ngClass]="{ 'text-xs': limited }"
                    class="text-right"
                >
                    <strong>{{ invoice.netTotal | currency }}</strong>
                </td>
            </ng-container>
            <ng-container matColumnDef="amountPaid">
                <th
                    mat-header-cell
                    *matHeaderCellDef
                    mat-sort-header
                    [ngClass]="{ 'text-xs': limited }"
                >
                    Total Paid
                </th>
                <td
                    mat-cell
                    *matCellDef="let invoice"
                    [ngClass]="{ 'text-xs': limited }"
                >
                    <div class="text-left">
                        Paid:
                        <strong class="text-right">
                            {{ invoice.amountPaid | currency }}</strong
                        >
                    </div>
                    <div>
                        Outstanding:
                        <strong>
                            {{
                                invoice.netTotal - invoice.amountPaid | currency
                            }}</strong
                        >
                    </div>
                </td>
            </ng-container>

            <ng-container matColumnDef="invoiceStatus">
                <th
                    mat-header-cell
                    *matHeaderCellDef
                    [ngClass]="{ 'text-xs': limited }"
                >
                    Invoice Status
                </th>
                <td
                    mat-cell
                    *matCellDef="let invoice"
                    class="text-center"
                    [ngClass]="{ 'text-xs': limited }"
                >
                    <span
                        class="inline-flex text-[8px] w-full items-center rounded-full px-1.5 py-0.1 font-bold uppercase tracking-wide"
                        [ngClass]="{
                            'bg-red-200 text-red-800 dark:bg-red-600 dark:text-red-50':
                                invoice.invoiceStatus === 'Pending',
                            'bg-green-200 text-green-800 dark:bg-green-600 dark:text-green-50':
                                invoice.invoiceStatus === 'Approved',
                            'bg-blue-200 text-blue-800 dark:bg-blue-600 dark:text-blue-50':
                                invoice.invoiceStatus === 'Paid',
                            'bg-yellow-200 text-yellow-800 dark:bg-yellow-600 dark:text-yellow-50':
                                invoice.invoiceStatus === 'Outstanding'
                        }"
                    >
                        {{ invoice.invoiceStatus }}
                    </span>
                </td>
            </ng-container>

            <ng-container matColumnDef="actions">
                <th
                    mat-header-cell
                    *matHeaderCellDef
                    [ngClass]="{ 'text-xs': limited }"
                >
                    Actions
                </th>
                <td
                    mat-cell
                    *matCellDef="let invoice"
                    [ngClass]="{ 'text-xs': limited }"
                >
                    <button
                        matTooltip="View Invoice"
                        (click)="viewInvoice(invoice.$id)"
                        class="text-black-500 hover:text-black-700 mr-2"
                    >
                        <i class="fas fa-eye"></i>
                    </button>
                    <button
                        matTooltip="Edit Invoice"
                        (click)="editInvoice(invoice.$id)"
                        class="text-green-500 hover:text-green-700"
                    >
                        <i class="fas fa-edit"></i>
                    </button>
                    @if (
                        invoice.invoiceStatus === "Approved" ||
                        invoice.invoiceStatus === "Paid" ||
                        invoice.invoiceStatus === "Outstanding"
                    ) {
                        <button
                            matTooltip="Payment"
                            [routerLink]="'/invoicing/payment/' + invoice.$id"
                            class="text-blue-500 hover:text-blue-700"
                        >
                            <i
                                class="fa-duotone fa-solid fa-badge-dollar ml-5"
                            ></i>
                        </button>
                    }
                </td>
            </ng-container>
            <!-- Footer row -->

            <!-- Row definitions -->
            <tr
                mat-footer-row
                *matHeaderRowDef="displayedColumns"
                [ngClass]="{ 'text-xs': limited }"
            ></tr>
            <tr
                mat-row
                *matRowDef="let row; columns: displayedColumns"
                [ngClass]="{ 'text-xs': limited }"
            ></tr>
        </table>

        @if (!limited) {
            <mat-paginator
                [pageSizeOptions]="[10, 25, 100]"
                showFirstLastButtons
            ></mat-paginator>
        }
    }
    @if (dataSource.data.length === 0 && !isLoading) {
        <div class="flex justify-center gap-2">
            <i class="fa-duotone fa-file-invoice-dollar icon-size-10"></i>
            <p class="font-bold text-lg">No invoices found.</p>
        </div>
    }
</div>

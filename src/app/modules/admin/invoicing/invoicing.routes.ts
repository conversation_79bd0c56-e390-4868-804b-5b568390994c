import { Routes } from "@angular/router";
import { InvoicingComponent } from "./invoicing.component";
import { InvoiceDetailsComponent } from "./invoice-details/invoice-details.component";
import { InvoicingReportsComponent } from "./invoicing-reports/invoicing-reports.component";
import { InvoicingSettingsComponent } from "./invoicing-settings/invoicing-settings.component";
import { InvoicingDashboardComponent } from "./invoicing-dashboard/invoicing-dashboard.component";
import { InvoiceViewComponent } from "./invoice-view/invoice-view.component";
import { InvoicePaymentComponent } from "./invoice-payment/invoice-payment.component";
import { InvoiceListViewComponent } from "./invoice-list-view/invoice-list-view.component";
import { InvoicingAwaitingComponent } from "./invoicing-awaiting/invoicing-awaiting.component";

export default [
    {
        path: '',
        component: InvoicingComponent,
        children: [
            {
                path: '',
                component: InvoicingDashboardComponent,
            },
            {
                path: 'list',
                component: InvoiceListViewComponent,
            },
            {
                path: 'reports',
                component: InvoicingReportsComponent,
            },
            {
                path: 'awaiting',
                component: InvoicingAwaitingComponent,
            },
            {
                path: 'settings',
                component: InvoicingSettingsComponent,
            },
            {
                path: 'view/:id',
                component: InvoiceViewComponent,
            },
            {
                path: 'edit/:id',
                component: InvoiceDetailsComponent,
            },
            {
                path: 'create',
                component: InvoiceDetailsComponent,
            },
            {
                path: 'create/:id',
                component: InvoiceDetailsComponent,
            },
            {
                path: 'payment',
                component: InvoicePaymentComponent,
            },
            {
                path: 'payment/:id',
                component: InvoicePaymentComponent,
            },

            {
                path: '**',
                redirectTo: '',
            },
        ],
    },
] as Routes;

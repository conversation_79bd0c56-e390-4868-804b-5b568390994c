import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { CustomerSelectComponent } from 'app/modules/widgets/customer-select/customer-select.component';
import { Customer } from 'app/core/databaseModels/customers/contacts.types';
import { InvoiceStatus, Invoicing, InvoicingItem } from 'app/core/databaseModels/invoicing/invoicing.type';
import { InvoicingService } from 'app/core/databaseModels/invoicing/invoicing.service';
import { CustomerService } from 'app/core/databaseModels/customers/customers.service';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatNativeDateModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { QuillModule } from 'ngx-quill';

@Component({
    selector: 'app-invoice-payment',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        MatInputModule,
        MatFormFieldModule,
        MatButtonModule,
        MatDatepickerModule,
        MatNativeDateModule,
        MatCardModule,
        MatIconModule,
        MatSelectModule,
        MatSnackBarModule,
        MatDialogModule,
        MatAutocompleteModule,
        MatTooltipModule,
        MatProgressSpinnerModule,
        CustomerSelectComponent,
        RouterLink,
        QuillModule,
    ],
    templateUrl: './invoice-payment.component.html',
    styleUrls: ['./invoice-payment.component.scss']
})
export class InvoicePaymentComponent implements OnInit {
    paymentForm: FormGroup;
    customers: Customer[] = [];
    selectedCustomer: Customer | null = null;
    invoices: Invoicing[] = [];
    parsedItems: InvoicingItem[] = [];
    selectedInvoice: Invoicing | null = null;
    isEditMode: boolean = false;
    isLoading: boolean = false;
    quillModules = {
        toolbar: [
            ['bold', 'italic', 'underline'],
            [{ 'list': 'ordered' }, { 'list': 'bullet' }],
            ['clean']
        ]
    };

    constructor(
        private fb: FormBuilder,
        private invoicingService: InvoicingService,
        private customerService: CustomerService,
        private dialog: MatDialog,
        private route: ActivatedRoute,
        private _snackBar: MatSnackBar,
        private fuseConfirmationService: FuseConfirmationService
    ) {
        this.paymentForm = this.fb.group({
            paidDate: [null, Validators.required],
            amountPaid: [0, [Validators.required, Validators.min(0)]],
            paymentRef: ['']
        });
    }

    ngOnInit(): void {
        const id = this.route.snapshot.paramMap.get('id');
        if (id) {

            this.loadInvoiceById(id);
        } else {
            this.loadCustomers();
        }
    }

    loadInvoiceById(id: string): void {
        this.isEditMode = true;
        this.isLoading = true;
        this.invoicingService.getInvoicing(id).subscribe({
            next: (invoice) => {
                this.selectedInvoice = invoice;
                this.parseInvoiceItems();
                this.getCustomerName(invoice.customerId);
                this.paymentForm.patchValue({
                    paidDate: invoice.paidDate ? new Date(invoice.paidDate) : null,
                    amountPaid: invoice.amountPaid || 0,
                    paymentRef: invoice.paymentRef || ''
                });
                this.isEditMode = true;

                this.isLoading = false;
            },
            error: (error) => {
                this._snackBar.open('Error loading invoice', 'Close', { duration: 3000 });
                console.error('Error loading invoice:', error);
                this.isLoading = false;
            }
        });
    }

    parseInvoiceItems(): void {
        if (!this.selectedInvoice || !Array.isArray(this.selectedInvoice.items)) {
            return;
        }
        this.parsedItems = this.selectedInvoice.items.map(item => {
            try {
                let parsed: unknown = item;

                // Keep parsing as long as parsed is a string
                while (typeof parsed === 'string') {
                    parsed = JSON.parse(parsed);
                }

                return parsed as InvoicingItem;
            } catch (error) {
                console.error('Error parsing invoice item:', error, item);
                return null;
            }
        }).filter((item): item is InvoicingItem => item !== null);
    }





    loadCustomers(): void {
        this.customerService.getCustomers().subscribe(customers => {
            this.customers = customers;
        });
    }

    getCustomerName(customerId: string): void {
        this.customerService.getCustomer(customerId).subscribe(customer => {
            this.selectedCustomer = customer;
        })
    }

    onCustomerSelected(customer: Customer): void {
        this.selectedCustomer = customer;
        this.loadInvoices(customer.$id);
    }

    loadInvoices(customerId: string): void {
        this.invoicingService.getInvoicings().subscribe(invoices => {
            this.invoices = invoices.filter(invoice =>
                invoice.customerId === customerId && invoice.invoiceStatus === InvoiceStatus.Approved
            );
        });
    }

    onInvoiceSelected(invoice: Invoicing): void {
        this.selectedInvoice = invoice;
        this.parseInvoiceItems();

        if (invoice.paidDate) {
            this.isEditMode = true;
            this.paymentForm.patchValue({
                paidDate: new Date(invoice.paidDate),
                amountPaid: invoice.amountPaid,
                paymentRef: invoice.paymentRef
            });
        } else {
            this.isEditMode = false;
            this.paymentForm.reset({
                paidDate: null,
                amountPaid: 0,
                paymentRef: ''
            });
        }
        this.paymentForm.get('amountPaid')?.setValidators([
            Validators.required,
            Validators.min(0),
            Validators.max(invoice.netTotal)
        ]);
        this.paymentForm.get('amountPaid')?.updateValueAndValidity();
    }

    savePayment(): void {
        if (this.paymentForm.valid && this.selectedInvoice) {
            // check if amountPaid is less than netTotal then set in InvoiceStatus as outstanding
            //console.log(this.paymentForm.get('amountPaid')?.value, this.selectedInvoice.netTotal);
            const invoiceStatus = this.paymentForm.get('amountPaid')?.value < this.selectedInvoice.netTotal
                ? InvoiceStatus.Outstanding
                : InvoiceStatus.Paid;


            const updatedInvoice: Invoicing = {
                ...this.selectedInvoice,
                paidDate: this.paymentForm.get('paidDate')?.value,
                amountPaid: this.paymentForm.get('amountPaid')?.value,
                paymentRef: this.paymentForm.get('paymentRef')?.value,
                invoiceStatus: invoiceStatus,

            };

            this.invoicingService.updateInvoicing(this.selectedInvoice.$id, updatedInvoice, true).subscribe({
                next: () => {
                    console.log('Payment saved successfully');

                    this._snackBar.open('Payment saved successfully', 'Close', { duration: 3000 });
                    this.loadInvoices(this.selectedCustomer?.$id || '');
                },
                error: (error) => console.error('Error saving payment:', error)
            });
        }
    }

    removePayment(): void {
        if (this.selectedInvoice) {
            const confirmation = this.fuseConfirmationService.open({
                title: 'Remove Payment',
                message: 'Are you sure you want to remove this payment? This action cannot be undone.',
                actions: {
                    confirm: {
                        label: 'Remove'
                    }
                }
            });

            confirmation.afterClosed().subscribe(result => {
                if (result === 'confirmed') {
                    const updatedInvoice: Invoicing = {
                        ...this.selectedInvoice,
                        paidDate: null,
                        amountPaid: 0,
                        paymentRef: null,
                        invoiceStatus: InvoiceStatus.Approved
                    };

                    this.invoicingService.updateInvoicing(this.selectedInvoice.$id, updatedInvoice).subscribe({
                        next: () => {
                            console.log('Payment removed successfully');
                            this.paymentForm.reset({
                                paidDate: null,
                                amountPaid: 0,
                                paymentRef: ''
                            });
                            this.isEditMode = false;
                            this.loadInvoices(this.selectedCustomer?.$id || '');
                        },
                        error: (error) => console.error('Error removing payment:', error)
                    });
                }
            });
        }
    }
}

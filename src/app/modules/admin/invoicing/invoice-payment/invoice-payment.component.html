<div class="container mx-auto px-4 py-8 w-full max-w-4xl">
    <div class="mb-4">
        <a
            [routerLink]="'/invoicing/'"
            class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
        >
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Dashboard
        </a>
        <a
            [routerLink]="'/invoicing/list'"
            class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
        >
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Invoices
        </a>
    </div>

    <mat-card class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full">
        <div
            class="border-b pb-4 border-gray-200 dark:border-gray-700 w-full p-6"
        >
            <div class="flex items-center justify-between w-full">
                <div class="flex items-center">
                    <div
                        class="bg-blue-600 p-2 rounded-lg w-10 h-10 text-white text-center flex items-center justify-center"
                    >
                        <i class="fa-duotone fa-money-bill"></i>
                    </div>
                    <div
                        class="ml-4 text-2xl font-bold text-gray-900 dark:text-gray-100"
                    >
                        Invoice Payment
                    </div>
                </div>
            </div>
        </div>

        <mat-card-content class="p-6">
            @if (isLoading) {
                <div class="flex justify-center gap-2">
                    <mat-progress-spinner
                        [diameter]="24"
                        [mode]="'indeterminate'"
                    ></mat-progress-spinner>
                    <p>Loading invoice details...</p>
                </div>
            } @else {
                <form
                    [formGroup]="paymentForm"
                    (ngSubmit)="savePayment()"
                    class="space-y-6"
                >
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @if (isEditMode) {
                            <p
                                class="text-gray-600 dark:text-gray-400 text-xl font-bold"
                            >
                                @if (selectedCustomer) {
                                    Customer: {{ selectedCustomer.name }}
                                }
                            </p>
                            <p
                                class="text-gray-600 dark:text-gray-400 text-xl font-bold"
                            >
                                Invoice: {{ selectedInvoice.invoiceNumber }}
                            </p>
                        } @else {
                            <customer-select
                                [customers]="customers"
                                (customerSelected)="onCustomerSelected($event)"
                            ></customer-select>
                            @if (selectedCustomer) {
                                <mat-form-field class="w-full">
                                    <mat-label>Select Invoice</mat-label>
                                    <mat-select
                                        (selectionChange)="
                                            onInvoiceSelected($event.value)
                                        "
                                    >
                                        <mat-option [value]="null"
                                            >Select an invoice</mat-option
                                        >
                                        @for (
                                            invoice of invoices;
                                            track invoice.$id
                                        ) {
                                            <mat-option [value]="invoice">
                                                {{ invoice.invoiceNumber }} -
                                                {{
                                                    invoice.netTotal
                                                        | currency
                                                            : "USD"
                                                            : "symbol-narrow"
                                                            : "1.2-2"
                                                }}
                                            </mat-option>
                                        }
                                    </mat-select>
                                </mat-form-field>
                            }
                        }
                    </div>

                    @if (selectedInvoice) {
                        <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
                            <h2 class="text-xl font-semibold mb-4">
                                Invoice Details
                            </h2>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <p>
                                        <strong>Invoice Number:</strong>
                                        {{ selectedInvoice.invoiceNumber }}
                                    </p>
                                    <p>
                                        <strong>Invoice Date:</strong>
                                        {{ selectedInvoice.invoiceDate | date }}
                                    </p>
                                    <p>
                                        <strong>Due Date:</strong>
                                        {{ selectedInvoice.dueDate | date }}
                                    </p>
                                </div>
                                <div>
                                    <p>
                                        <strong>Total:</strong>
                                        {{
                                            selectedInvoice.total
                                                | currency
                                                    : "USD"
                                                    : "symbol-narrow"
                                                    : "1.2-2"
                                        }}
                                    </p>
                                    <p>
                                        <strong>Tax:</strong>
                                        {{
                                            selectedInvoice.tax
                                                | currency
                                                    : "USD"
                                                    : "symbol-narrow"
                                                    : "1.2-2"
                                        }}
                                    </p>
                                    <p>
                                        <strong>Net Total:</strong>
                                        {{
                                            selectedInvoice.netTotal
                                                | currency
                                                    : "USD"
                                                    : "symbol-narrow"
                                                    : "1.2-2"
                                        }}
                                    </p>
                                </div>
                            </div>

                            <h3 class="text-lg font-semibold mt-4 mb-2">
                                Items
                            </h3>
                            <table class="w-full">
                                <thead>
                                    <tr>
                                        <th class="text-left">Description</th>
                                        <th class="text-right">Quantity</th>
                                        <th class="text-right">Price</th>
                                        <th class="text-right">Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (item of parsedItems; track item) {
                                        <tr>
                                            <td>{{ item.description }}</td>
                                            <td class="text-right">
                                                {{ item.quantity }}
                                            </td>
                                            <td class="text-right">
                                                {{
                                                    item.price
                                                        | currency
                                                            : "USD"
                                                            : "symbol-narrow"
                                                            : "1.2-2"
                                                }}
                                            </td>
                                            <td class="text-right">
                                                {{
                                                    item.total
                                                        | currency
                                                            : "USD"
                                                            : "symbol-narrow"
                                                            : "1.2-2"
                                                }}
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <div
                            class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow"
                        >
                            <h2 class="text-xl font-semibold mb-4">
                                Record Payment
                            </h2>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <mat-form-field class="w-full">
                                    <mat-label>Payment Date</mat-label>
                                    <input
                                        matInput
                                        [matDatepicker]="picker"
                                        formControlName="paidDate"
                                    />
                                    <mat-datepicker-toggle
                                        matIconSuffix
                                        [for]="picker"
                                    ></mat-datepicker-toggle>
                                    <mat-datepicker #picker></mat-datepicker>
                                </mat-form-field>

                                <mat-form-field class="w-full">
                                    <mat-label>Amount Paid</mat-label>
                                    <input
                                        matInput
                                        type="number"
                                        formControlName="amountPaid"
                                    />
                                    @if (
                                        paymentForm
                                            .get("amountPaid")
                                            ?.hasError("required")
                                    ) {
                                        <mat-error
                                            >Amount is required</mat-error
                                        >
                                    }
                                    @if (
                                        paymentForm
                                            .get("amountPaid")
                                            ?.hasError("max")
                                    ) {
                                        <mat-error
                                            >Amount cannot exceed the invoice
                                            total</mat-error
                                        >
                                    }
                                </mat-form-field>
                            </div>

                            <div class="w-full">
                                <label
                                    class="text-sm font-medium text-gray-700 dark:text-gray-300"
                                    >Payment Reference</label
                                >
                                <quill-editor
                                    class="quill-editor bg-white dark:bg-gray-900 min-h-[200px] mt-2 rounded-md border border-gray-300 dark:border-gray-600"
                                    formControlName="paymentRef"
                                    [modules]="editorConfig"
                                >
                                </quill-editor>
                            </div>

                            <div class="flex justify-end space-x-4 mt-4">
                                @if (selectedInvoice.paidDate && isEditMode) {
                                    <button
                                        mat-raised-button
                                        color="warn"
                                        type="button"
                                        (click)="removePayment()"
                                    >
                                        Remove Payment
                                    </button>
                                }
                                <button
                                    mat-raised-button
                                    color="primary"
                                    type="submit"
                                    [disabled]="paymentForm.invalid"
                                >
                                    {{
                                        isEditMode
                                            ? "Update Payment"
                                            : "Save Payment"
                                    }}
                                </button>
                            </div>
                        </div>
                    }
                </form>
            }
        </mat-card-content>
    </mat-card>
</div>

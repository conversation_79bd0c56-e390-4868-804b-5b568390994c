// financial-year.util.ts

export interface FinancialYear {
    startYear: number;
    endYear: number;
    displayText: string;
    startDate: Date;
    endDate: Date;
}

export interface MonthRange {
    startDate: Date;
    endDate: Date;
    displayText: string;
}

export class FinancialYearUtil {
    /**
     * Get current financial year details
     * @param startMonth - Month when financial year starts (1-12)
     * @param startDay - Day of month when financial year starts (1-31)
     * @param format - Output format ('short' | 'long' | 'full')
     * @returns FinancialYear object with year details
     */
    static getCurrentFinancialYear(
        startMonth: number = 4,
        startDay: number = 1,
        format: 'short' | 'long' | 'full' = 'short'
    ): FinancialYear {
        const today = new Date();
        const currentMonth = today.getMonth() + 1;
        const currentYear = today.getFullYear();

        let startYear = currentYear;
        if (currentMonth < startMonth || (currentMonth === startMonth && today.getDate() < startDay)) {
            startYear = currentYear - 1;
        }

        const endYear = startYear + 1;

        const startDate = new Date(startYear, startMonth - 1, startDay);
        const endDate = new Date(endYear, startMonth - 1, startDay - 1);

        let displayText: string;
        switch (format) {
            case 'long':
                displayText = `FY ${startYear}-${endYear}`;
                break;
            case 'full':
                displayText = `Financial Year ${startYear}-${endYear}`;
                break;
            case 'short':
            default:
                displayText = `${startYear}-${endYear.toString().slice(-2)}`;
                break;
        }

        return {
            startYear,
            endYear,
            displayText,
            startDate,
            endDate
        };
    }

    /**
     * Get current month date range
     * @returns MonthRange object with start and end dates
     */
    static getCurrentMonth(): MonthRange {
        const today = new Date();
        const startDate = new Date(today.getFullYear(), today.getMonth(), 1);
        const endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);

        const displayText = startDate.toLocaleString('default', { month: 'long', year: 'numeric' });

        return {
            startDate,
            endDate,
            displayText
        };
    }

    /**
     * Get last month date range
     * @returns MonthRange object with start and end dates
     */
    static getLastMonth(): MonthRange {
        const today = new Date();
        const startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const endDate = new Date(today.getFullYear(), today.getMonth(), 0);

        const displayText = startDate.toLocaleString('default', { month: 'long', year: 'numeric' });

        return {
            startDate,
            endDate,
            displayText
        };
    }

    /**
     * Get next month date range
     * @returns MonthRange object with start and end dates
     */
    static getNextMonth(): MonthRange {
        const today = new Date();
        const startDate = new Date(today.getFullYear(), today.getMonth() + 1, 1);
        const endDate = new Date(today.getFullYear(), today.getMonth() + 2, 0);

        const displayText = startDate.toLocaleString('default', { month: 'long', year: 'numeric' });

        return {
            startDate,
            endDate,
            displayText
        };
    }

    /**
     * Get list of financial years
     * @param numberOfYears - Number of years to return (including current)
     * @param startMonth - Month when financial year starts (1-12)
     * @param startDay - Day of month when financial year starts (1-31)
     * @param format - Output format ('short' | 'long' | 'full')
     * @returns Array of FinancialYear objects
     */
    static getFinancialYearsList(
        numberOfYears: number = 5,
        startMonth: number = 4,
        startDay: number = 1,
        format: 'short' | 'long' | 'full' = 'short'
    ): FinancialYear[] {
        const currentFY = this.getCurrentFinancialYear(startMonth, startDay, format);
        const years: FinancialYear[] = [currentFY];

        for (let i = 1; i < numberOfYears; i++) {
            const startYear = currentFY.startYear - i;
            const endYear = startYear + 1;
            const startDate = new Date(startYear, startMonth - 1, startDay);
            const endDate = new Date(endYear, startMonth - 1, startDay - 1);

            let displayText: string;
            switch (format) {
                case 'long':
                    displayText = `FY ${startYear}-${endYear}`;
                    break;
                case 'full':
                    displayText = `Financial Year ${startYear}-${endYear}`;
                    break;
                case 'short':
                default:
                    displayText = `${startYear}-${endYear.toString().slice(-2)}`;
                    break;
            }

            years.push({
                startYear,
                endYear,
                displayText,
                startDate,
                endDate
            });
        }

        return years.sort((a, b) => b.startYear - a.startYear);
    }

    /**
     * Check if a date falls within the current financial year
     * @param date - Date to check
     * @param startMonth - Month when financial year starts (1-12)
     * @param startDay - Day of month when financial year starts (1-31)
     * @returns boolean
     */
    static isCurrentFinancialYear(
        date: Date,
        startMonth: number = 4,
        startDay: number = 1
    ): boolean {
        const fy = this.getCurrentFinancialYear(startMonth, startDay);
        return date >= fy.startDate && date <= fy.endDate;
    }
}

<div class="container mx-auto px-4 py-8 max-w-3xl">
    <!-- Back to Dashboard Link -->
    <div class="mb-4">
        <a
            [routerLink]="'/invoicing/'"
            class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
        >
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Dashboard
        </a>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2
            class="text-3xl font-extrabold text-gray-900 dark:text-white mb-8 flex items-center justify-center"
        >
            <i class="fas fa-file-invoice text-blue-500 mr-4"></i>
            Invoicing Settings
        </h2>

        @if (isLoading) {
            <div class="flex justify-center items-center py-10">
                <mat-progress-spinner
                    mode="indeterminate"
                    [diameter]="50"
                    strokeWidth="3"
                    class="text-blue-600"
                ></mat-progress-spinner>
            </div>
        } @else {
            <form
                [formGroup]="invoicingSettingsForm"
                (ngSubmit)="onSubmit()"
                class="space-y-8"
            >
                <!-- Invoice Start Number and Prefix -->
                <div class="grid grid-cols-2 gap-4">
                    <mat-form-field appearance="outline" class="w-full">
                        <mat-label class="text-gray-700 dark:text-gray-300"
                            >Invoice Start Number</mat-label
                        >
                        <input
                            matInput
                            type="number"
                            formControlName="invoiceStartNumber"
                            placeholder="Start"
                            class="text-gray-700 dark:text-white"
                        />
                        @if (
                            invoicingSettingsForm
                                .get("invoiceStartNumber")
                                ?.hasError("min") &&
                            invoicingSettingsForm.get("invoiceStartNumber")
                                ?.touched
                        ) {
                            <mat-error class="text-red-500"
                                >Must be at least 1</mat-error
                            >
                        }
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="w-full">
                        <mat-label class="text-gray-700 dark:text-gray-300"
                            >Invoice Prefix</mat-label
                        >
                        <input
                            matInput
                            formControlName="invoicePrefix"
                            placeholder="e.g., INV"
                            class="text-gray-700 dark:text-white"
                        />
                    </mat-form-field>
                </div>

                <!-- Opening Month and GST -->
                <div class="grid grid-cols-2 gap-4">
                    <mat-form-field appearance="outline" class="w-full">
                        <mat-label class="text-gray-700 dark:text-gray-300"
                            >Opening Month</mat-label
                        >
                        <mat-select
                            formControlName="openingMonth"
                            placeholder="Select"
                            class="text-gray-700 dark:text-white"
                        >
                            @for (month of months; track month) {
                                <mat-option
                                    [value]="month"
                                    class="dark:bg-gray-700"
                                    >{{ month }}</mat-option
                                >
                            }
                        </mat-select>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="w-full">
                        <mat-label class="text-gray-700 dark:text-gray-300"
                            >GST (%)</mat-label
                        >
                        <input
                            matInput
                            type="number"
                            formControlName="gst"
                            placeholder="GST"
                            class="text-gray-700 dark:text-white"
                        />
                        <span
                            matTextSuffix
                            class="text-gray-700 dark:text-gray-300"
                            >%</span
                        >
                        @if (
                            invoicingSettingsForm.get("gst")?.hasError("min")
                        ) {
                            <mat-error class="text-red-500"
                                >Cannot be negative</mat-error
                            >
                        } @else if (
                            invoicingSettingsForm.get("gst")?.hasError("max")
                        ) {
                            <mat-error class="text-red-500"
                                >Cannot exceed 100%</mat-error
                            >
                        }
                    </mat-form-field>
                    <mat-form-field appearance="outline" class="w-full">
                        <mat-label class="text-gray-700 dark:text-gray-300"
                            >Payment is Due in a number of days after the issue
                            date
                        </mat-label>
                        <input
                            matInput
                            type="number"
                            formControlName="dueDays"
                            placeholder="Due Days"
                            class="text-gray-700 dark:text-white"
                        />
                        @if (
                            invoicingSettingsForm
                                .get("dueDays")
                                ?.hasError("min")
                        ) {
                            <mat-error class="text-red-500"
                                >Cannot be negative</mat-error
                            >
                        }
                    </mat-form-field>
                </div>

                <!-- Description Template -->
                <div>
                    <label
                        class="text-sm font-medium text-gray-700 dark:text-gray-300"
                        >Description Template</label
                    >
                    <quill-editor
                        class="quill-editor bg-white dark:bg-gray-900 min-h-[200px] mt-2 rounded-md border border-gray-300 dark:border-gray-600"
                        formControlName="detailsTemplate"
                        [modules]="editorConfig"
                    >
                    </quill-editor>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-4 mt-6">
                    <button
                        mat-button
                        type="button"
                        (click)="loadInvoicingSettings()"
                        class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
                    >
                        Reset
                    </button>
                    <button
                        mat-raised-button
                        color="primary"
                        type="submit"
                        [disabled]="invoicingSettingsForm.invalid"
                        class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
                    >
                        Save Settings
                    </button>
                </div>
            </form>
        }
    </div>
</div>

// invoicing-settings.component.ts
import { Component, <PERSON><PERSON>nit, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef, ChangeDetectionStrategy } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { QuillModule } from 'ngx-quill';
import { Subject } from 'rxjs';
import { switchMap, take, takeUntil } from 'rxjs/operators';

import { InvoicingSettings, months } from 'app/core/databaseModels/invoicingSettings/invoicingSettings.type';
import { InvoicingSettingsService } from 'app/core/databaseModels/invoicingSettings/invoicingSettings.service';
import { FuseLoadingService } from '@fuse/services/loading';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { RouterLink } from '@angular/router';

@Component({
    selector: 'app-invoicing-settings',
    templateUrl: './invoicing-settings.component.html',
    styleUrls: ['./invoicing-settings.component.scss'],
    standalone: true,
    imports: [
        FormsModule,
        ReactiveFormsModule,
        MatInputModule,
        MatFormFieldModule,
        MatButtonModule,
        MatSelectModule,
        MatSnackBarModule,
        MatProgressSpinner,
        RouterLink,
        QuillModule
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class InvoicingSettingsComponent implements OnInit, OnDestroy {
    invoicingSettingsForm!: FormGroup;
    private destroy$ = new Subject<void>();
    isLoading = true;
    settingsExist = false;
    months = Object.values(months);


    editorConfig = {
        toolbar: [
            ['bold', 'italic', 'underline', 'strike'],
            ['blockquote', 'code-block'],
            [{ 'header': 1 }, { 'header': 2 }],
            [{ 'list': 'ordered' }, { 'list': 'bullet' }],
            [{ 'script': 'sub' }, { 'script': 'super' }],
            [{ 'indent': '-1' }, { 'indent': '+1' }],
            [{ 'direction': 'rtl' }],
            [{ 'size': ['small', false, 'large', 'huge'] }],
            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
            [{ 'color': [] }, { 'background': [] }],
            [{ 'font': [] }],
            [{ 'align': [] }],
            ['clean']
        ]
    };

    constructor(
        private fb: FormBuilder,
        private invoicingSettingsService: InvoicingSettingsService,
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseLoadingService: FuseLoadingService,
        private _snackBar: MatSnackBar,
        private _userService: UserService
    ) { }

    ngOnInit(): void {
        this.initForm();
        this.loadInvoicingSettings();
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    private initForm(): void {
        this.invoicingSettingsForm = this.fb.group({
            $id: [''],
            organisationID: [''],
            invoiceStartNumber: [1, [Validators.min(1)]],
            invoicePrefix: ['',],
            openingMonth: ['',],
            detailsTemplate: [''],
            gst: [0, [Validators.min(0), Validators.max(100)]],
            dueDays: [15],
        });
    }

    private loadInvoicingSettings(): void {
        this._fuseLoadingService.show();
        this.invoicingSettingsService
            .getInvoicingSettings()
            .pipe(takeUntil(this.destroy$))
            .subscribe({

                next: (settings) => {
                    if (settings) {
                        this.settingsExist = true;

                        this.invoicingSettingsForm.patchValue({
                            $id: settings.$id,
                            organisationID: settings.organisationID,
                            invoiceStartNumber: Number(settings.invoiceStartNumber),
                            invoicePrefix: settings.invoicePrefix,
                            openingMonth: settings.openingMonth,
                            detailsTemplate: settings.detailsTemplate,
                            gst: Number(settings.gst),
                            dueDays: Number(settings.dueDays)
                        }, { emitEvent: false });
                    }
                    this.isLoading = false;
                    this._changeDetectorRef.markForCheck();
                    this._fuseLoadingService.hide();
                },
                error: (error) => {
                    console.error('Error loading invoicing settings:', error);
                    this.isLoading = false;
                    this._fuseLoadingService.hide();
                    this._snackBar.open('Error loading settings', 'Close', { duration: 3000 });
                    this._changeDetectorRef.markForCheck();
                }
            });
    }

    onSubmit(): void {
        if (this.invoicingSettingsForm.invalid) {
            this.invoicingSettingsForm.markAllAsTouched();
            return;
        }

        const formData: InvoicingSettings = this.invoicingSettingsForm.value;

        if (this.settingsExist) {
            this.updateInvoicingSettings(formData);
        } else {
            this.createInvoicingSettings(formData);
        }
    }

    private createInvoicingSettings(data: InvoicingSettings): void {
        data.$id = null;
        this._fuseLoadingService.show();

        // First get the organization ID from user service
        this._userService.user$
            .pipe(
                take(1),
                switchMap(user => {
                    // Set the organization ID from user
                    data.organisationID = user.organisationID;

                    // Then create the settings
                    return this.invoicingSettingsService.createInvoicingSettings(data);
                }),
                takeUntil(this.destroy$)
            )
            .subscribe({
                next: (newSettings) => {
                    this.settingsExist = true;
                    this._snackBar.open('Settings created successfully', 'Close', { duration: 3000 });
                    this._fuseLoadingService.hide();
                    this._changeDetectorRef.markForCheck();
                },
                error: (error) => {
                    console.error('Error creating settings:', error);
                    this._snackBar.open('Error creating settings', 'Close', { duration: 3000 });
                    this._fuseLoadingService.hide();
                    this._changeDetectorRef.markForCheck();
                }
            });
    }

    private updateInvoicingSettings(data: InvoicingSettings): void {
        if (!data.$id) {
            console.error('No ID provided for update');
            return;
        }

        this._fuseLoadingService.show();
        this.invoicingSettingsService
            .updateInvoicingSettings(data.$id, data)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: () => {
                    this._snackBar.open('Settings updated successfully', 'Close', { duration: 3000 });
                    this._fuseLoadingService.hide();
                    this._changeDetectorRef.markForCheck();
                },
                error: (error) => {
                    console.error('Error updating settings:', error);
                    this._snackBar.open('Error updating settings', 'Close', { duration: 3000 });
                    this._fuseLoadingService.hide();
                    this._changeDetectorRef.markForCheck();
                }
            });
    }


}

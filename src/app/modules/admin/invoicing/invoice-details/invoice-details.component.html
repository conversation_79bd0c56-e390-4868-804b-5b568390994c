<div class="container mx-auto px-4 py-8 max-w-4xl">
    <!-- Back to Dashboard Link -->
    <div class="mb-4">
        <a
            [routerLink]="'/invoicing/'"
            class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
        >
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Dashboard
        </a>
    </div>

    <mat-card class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full">
        <div
            class="border-b pb-4 border-gray-200 dark:border-gray-700 w-full p-6"
        >
            <div class="flex items-center justify-between w-full">
                <div class="flex items-center">
                    <div
                        class="bg-blue-600 p-2 rounded-lg w-10 h-10 text-white text-center flex items-center justify-center"
                    >
                        <i class="fa-duotone fa-file-invoice"></i>
                    </div>
                    <div
                        class="ml-4 text-2xl font-bold text-gray-900 dark:text-gray-100"
                    >
                        @if (isCreateMode) {
                            Create New Invoice
                        } @else if (isEditMode) {
                            Edit Invoice
                        } @else {
                            View Invoice
                        }
                    </div>
                </div>
                @if (isEditMode) {
                    <button
                        mat-flat-button
                        [color]="'primary'"
                        (click)="changeInvoiceStatus()"
                    >
                        <i class="fa-duotone fa-thumbs-up"></i>
                        <span class="ml-2">
                            {{
                                invoiceForm.get("invoiceStatus").value ===
                                "Pending"
                                    ? "Mark as Approved"
                                    : "Mark as Pending"
                            }}
                        </span>
                    </button>
                }
            </div>
        </div>

        <mat-card-content class="p-6">
            @if (isLoading) {
                <div class="flex justify-center gap-2">
                    <mat-progress-spinner
                        [diameter]="24"
                        [mode]="'indeterminate'"
                    ></mat-progress-spinner>
                    <p>Loading invoice details...</p>
                </div>
            } @else {
                <form
                    [formGroup]="invoiceForm"
                    (ngSubmit)="onSubmit()"
                    class="space-y-6"
                >
                    <!-- Customer Information Section -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Select Customer -->
                        @if (isEditMode || isCreateByIdMode) {
                            <mat-form-field class="w-full">
                                <mat-label>Customer</mat-label>
                                <input
                                    matInput
                                    [value]="selectedCustomer?.name || ''"
                                    readonly
                                />
                            </mat-form-field>
                        } @else {
                            <customer-select
                                [customers]="customers"
                                (customerSelected)="onCustomerSelected($event)"
                                (onCustomerCleared)="onCustomerCleared()"
                            />
                        }
                        @if (selectedCustomer && isJobLoaded) {
                            <mat-form-field class="w-full">
                                <mat-label>Scheduled Job</mat-label>
                                <input
                                    type="text"
                                    matInput
                                    [formControl]="jobControl"
                                    [matAutocomplete]="jobAuto"
                                />
                                <mat-autocomplete
                                    #jobAuto="matAutocomplete"
                                    (optionSelected)="
                                        onJobSelected($event.option.value)
                                    "
                                    [displayWith]="displayJobFn"
                                >
                                    @for (
                                        job of filteredJobs;
                                        track job;
                                        let i = $index
                                    ) {
                                        <mat-option
                                            [value]="job"
                                            class="w-full"
                                        >
                                            {{ displayJobFn(job) }}
                                        </mat-option>
                                    }
                                </mat-autocomplete>
                                @if (jobControl.value) {
                                    <button
                                        mat-icon-button
                                        matSuffix
                                        (click)="clearSelectedJob()"
                                    >
                                        <i
                                            class="fa-duotone fa-circle-xmark"
                                        ></i>
                                    </button>
                                }
                            </mat-form-field>
                        }
                    </div>

                    <!-- Invoice Information Section -->
                    <div
                        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                    >
                        <!-- Invoice Number -->
                        <mat-form-field class="fuse-mat-dense flex-auto">
                            <mat-label>Invoice Number</mat-label>

                            <input matInput formControlName="invoiceNumber" />
                            @if (
                                invoiceForm
                                    .get("invoiceNumber")
                                    .hasError("required")
                            ) {
                                <mat-error>
                                    Invoice Number is required
                                </mat-error>
                            }
                        </mat-form-field>

                        <!-- Invoice Date -->
                        <mat-form-field class="fuse-mat-dense flex-auto">
                            <mat-label>Invoice Date</mat-label>
                            <input
                                matInput
                                [matDatepicker]="invoiceDatePicker"
                                formControlName="invoiceDate"
                            />
                            <mat-datepicker-toggle
                                matSuffix
                                [for]="invoiceDatePicker"
                            ></mat-datepicker-toggle>
                            <mat-datepicker #invoiceDatePicker></mat-datepicker>
                            @if (
                                invoiceForm
                                    .get("invoiceDate")
                                    .hasError("required")
                            ) {
                                <mat-error>
                                    Invoice Date is required
                                </mat-error>
                            }
                        </mat-form-field>

                        <!-- Due Date -->
                        <mat-form-field class="fuse-mat-dense flex-auto">
                            <mat-label>Due Date</mat-label>
                            <input
                                matInput
                                [matDatepicker]="dueDatePicker"
                                formControlName="dueDate"
                            />
                            <mat-datepicker-toggle
                                matSuffix
                                [for]="dueDatePicker"
                            ></mat-datepicker-toggle>
                            <mat-datepicker #dueDatePicker></mat-datepicker>
                            @if (
                                invoiceForm.get("dueDate").hasError("required")
                            ) {
                                <mat-error> Due Date is required </mat-error>
                            }
                        </mat-form-field>
                    </div>

                    <!-- Items Section -->
                    <div class="space-y-4" formArrayName="items">
                        <div class="flex justify-between items-center">
                            <h3
                                class="text-xl font-semibold text-gray-900 dark:text-gray-100"
                            >
                                Invoice Items
                            </h3>
                            <button
                                mat-raised-button
                                color="primary"
                                type="button"
                                (click)="addInvoiceItem()"
                                [disabled]="isViewMode"
                            >
                                <mat-icon>add</mat-icon>
                                Add Item
                            </button>
                        </div>

                        <div
                            class="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700"
                        >
                            <table
                                class="min-w-full divide-y divide-gray-200 dark:divide-gray-700"
                            >
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th
                                            class="px-4 py-3 text-left text-xs font-medium uppercase text-gray-500 dark:text-gray-300"
                                        >
                                            Description
                                        </th>
                                        <th
                                            class="px-2 py-3 text-left text-xs font-medium uppercase w-20 text-gray-500 dark:text-gray-300"
                                        >
                                            Qty
                                        </th>
                                        <th
                                            class="px-2 py-3 text-left text-xs font-medium uppercase w-24 text-gray-500 dark:text-gray-300"
                                        >
                                            Price
                                        </th>
                                        <th
                                            class="px-2 py-3 text-left text-xs font-medium uppercase w-24 text-gray-500 dark:text-gray-300"
                                        >
                                            Discount
                                        </th>
                                        <th
                                            class="px-4 py-3 text-left text-xs font-medium uppercase w-28 text-gray-500 dark:text-gray-300"
                                        >
                                            Total
                                        </th>
                                        <th
                                            class="px-4 py-3 text-left text-xs font-medium uppercase text-gray-500 dark:text-gray-300"
                                        >
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody
                                    class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"
                                >
                                    <!-- Using the new @for syntax with track expression -->
                                    @for (
                                        item of itemsFormArray.controls;
                                        track item;
                                        let i = $index
                                    ) {
                                        <tr
                                            [formGroupName]="i"
                                            [ngClass]="{
                                                'bg-gray-50 dark:bg-gray-700':
                                                    i % 2 === 0
                                            }"
                                        >
                                            <!-- Description -->
                                            <td class="p-2">
                                                <mat-form-field
                                                    class="fuse-mat-dense flex-auto w-full"
                                                >
                                                    <input
                                                        matInput
                                                        formControlName="description"
                                                        placeholder="Item description"
                                                    />
                                                    @if (
                                                        item
                                                            .get("description")
                                                            .hasError(
                                                                "required"
                                                            )
                                                    ) {
                                                        <mat-error>
                                                            Description is
                                                            required
                                                        </mat-error>
                                                    }
                                                </mat-form-field>
                                            </td>
                                            <!-- Quantity -->
                                            <td class="p-2">
                                                <mat-form-field
                                                    class="fuse-mat-dense flex-auto w-20"
                                                >
                                                    <input
                                                        matInput
                                                        type="number"
                                                        formControlName="quantity"
                                                        min="1"
                                                    />
                                                    @if (
                                                        item
                                                            .get("quantity")
                                                            .hasError("min")
                                                    ) {
                                                        <mat-error>
                                                            Minimum quantity is
                                                            1
                                                        </mat-error>
                                                    }
                                                </mat-form-field>
                                            </td>
                                            <!-- Price -->
                                            <td class="p-2">
                                                <mat-form-field
                                                    class="fuse-mat-dense flex-auto"
                                                >
                                                    <span matPrefix
                                                        >$&nbsp;</span
                                                    >
                                                    <input
                                                        matInput
                                                        type="number"
                                                        formControlName="price"
                                                        min="0.00"
                                                        step="0.01"
                                                        (focus)="
                                                            selectAllText(
                                                                $event
                                                            )
                                                        "
                                                    />
                                                    @if (
                                                        item
                                                            .get("price")
                                                            .hasError("min")
                                                    ) {
                                                        <mat-error>
                                                            Price cannot be
                                                            negative
                                                        </mat-error>
                                                    }
                                                </mat-form-field>
                                            </td>
                                            <!-- Discount -->
                                            <td class="p-2">
                                                <mat-form-field
                                                    class="fuse-mat-dense flex-auto"
                                                >
                                                    <span matPrefix
                                                        >$&nbsp;</span
                                                    >
                                                    <input
                                                        matInput
                                                        type="number"
                                                        formControlName="discount"
                                                        min="0"
                                                        step="0.01"
                                                        (focus)="
                                                            selectAllText(
                                                                $event
                                                            )
                                                        "
                                                    />
                                                    @if (
                                                        item
                                                            .get("discount")
                                                            .hasError("min")
                                                    ) {
                                                        <mat-error>
                                                            Discount cannot be
                                                            negative
                                                        </mat-error>
                                                    }
                                                </mat-form-field>
                                            </td>
                                            <!-- Total -->
                                            <td class="p-2">
                                                <mat-form-field
                                                    class="fuse-mat-dense flex-auto"
                                                >
                                                    <span matPrefix
                                                        >$&nbsp;</span
                                                    >
                                                    <input
                                                        matInput
                                                        type="number"
                                                        formControlName="total"
                                                        readonly
                                                    />
                                                </mat-form-field>
                                            </td>
                                            <!-- Actions -->
                                            <td
                                                class="text-center items-center"
                                            >
                                                <button
                                                    mat-icon-button
                                                    color="warn"
                                                    type="button"
                                                    (click)="
                                                        removeInvoiceItem(i)
                                                    "
                                                    [disabled]="isViewMode"
                                                    matTooltip="Remove Item"
                                                    class="mb-5"
                                                >
                                                    <i
                                                        class="fa-duotone fa-trash icon-size-5 text-red-600 hover:text-red-800 transition-colors dark:text-red-400 dark:hover:text-red-600"
                                                    ></i>
                                                </button>
                                            </td>
                                        </tr>
                                    } @empty {
                                        <tr>
                                            <td
                                                colspan="6"
                                                class="text-center py-4 text-gray-500 dark:text-gray-300"
                                            >
                                                There are no items.
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Totals and Description Section -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                        <!-- Description -->
                        <div class="fuse-mat-dense flex-auto w-full">
                            <quill-editor
                                class="quill-editor w-full bg-white dark:bg-gray-900 min-h-[200px] mt-2 rounded-md border border-gray-300 dark:border-gray-600"
                                formControlName="description"
                                [modules]="editorConfig"
                            >
                            </quill-editor>
                        </div>
                        <!-- Totals -->
                        <div class="space-y-4 mt-2">
                            <div
                                class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700"
                            >
                                <!-- Subtotal -->
                                <div class="flex justify-between items-center">
                                    <span
                                        class="text-gray-600 dark:text-gray-300"
                                        >Subtotal:</span
                                    >
                                    <span
                                        class="font-semibold text-gray-900 dark:text-gray-100"
                                        >{{
                                            invoiceForm.get("total").value
                                                | currency
                                                    : "USD"
                                                    : "symbol-narrow"
                                                    : "1.2-2"
                                        }}</span
                                    >
                                </div>
                                <!-- Tax Rate -->
                                <div
                                    class="flex justify-between items-center mt-2"
                                >
                                    <span
                                        class="text-gray-600 dark:text-gray-300"
                                        >Tax Rate:</span
                                    >
                                    <mat-form-field class="fuse-mat-dense w-20">
                                        <input
                                            matInput
                                            type="number"
                                            formControlName="taxPercent"
                                            min="0"
                                            step="0.01"
                                        />
                                        <span matSuffix>%&nbsp;</span>
                                        @if (
                                            invoiceForm
                                                .get("taxPercent")
                                                .hasError("min")
                                        ) {
                                            <mat-error>
                                                Tax rate cannot be negative
                                            </mat-error>
                                        }
                                    </mat-form-field>
                                </div>
                                <!-- Tax Amount -->
                                <div class="flex justify-between items-center">
                                    <span
                                        class="text-gray-600 dark:text-gray-300"
                                        >Tax Amount:</span
                                    >
                                    <span
                                        class="font-semibold text-gray-900 dark:text-gray-100"
                                        >{{
                                            invoiceForm.get("tax").value
                                                | currency
                                                    : "USD"
                                                    : "symbol-narrow"
                                                    : "1.2-2"
                                        }}</span
                                    >
                                </div>
                                <!-- Net Total -->
                                <div
                                    class="flex justify-between items-center text-lg font-bold mt-2 pt-4 border-t border-gray-200 dark:border-gray-700"
                                >
                                    <span>Total:</span>
                                    <span
                                        class="text-gray-900 dark:text-gray-100"
                                        >{{
                                            invoiceForm.get("netTotal").value
                                                | currency
                                                    : "USD"
                                                    : "symbol-narrow"
                                                    : "1.2-2"
                                        }}</span
                                    >
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div
                        class="flex justify-end space-x-4 mt-8 pt-4 border-t border-gray-200 dark:border-gray-700"
                    >
                        @if (!isSaving) {
                            <button
                                mat-button
                                type="button"
                                (click)="cancelEdit()"
                            >
                                Cancel
                            </button>
                        }

                        @if (!isViewMode) {
                            @if (!isCreateMode && !isSaving) {
                                <button
                                    mat-raised-button
                                    color="warn"
                                    type="button"
                                    (click)="deleteInvoice()"
                                    matTooltip="Delete Invoice"
                                >
                                    <mat-icon>delete</mat-icon>
                                    Delete
                                </button>
                            }

                            <button
                                mat-raised-button
                                color="primary"
                                type="submit"
                                [disabled]="invoiceForm.invalid || isSaving"
                                matTooltip="Save Invoice"
                            >
                                <div
                                    class="flex items-center justify-between gap-2"
                                >
                                    @if (isSaving) {
                                        <mat-progress-spinner
                                            [diameter]="24"
                                            [mode]="'indeterminate'"
                                        ></mat-progress-spinner>
                                    } @else {
                                        <mat-icon>save</mat-icon>
                                    }

                                    {{
                                        isCreateMode
                                            ? "Create Invoice"
                                            : "Update Invoice"
                                    }}
                                </div>
                            </button>
                        }
                    </div>
                </form>
            }
        </mat-card-content>
    </mat-card>
</div>

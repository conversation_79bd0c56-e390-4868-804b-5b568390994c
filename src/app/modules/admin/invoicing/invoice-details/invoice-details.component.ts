import { Compo<PERSON>, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormBuilder, FormArray, FormGroup, ReactiveFormsModule, Validators, FormControl } from '@angular/forms';
import { of, Subject, Subscription } from 'rxjs';
import { takeUntil, switchMap, tap, map } from 'rxjs/operators';

// Angular Material Imports
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule } from '@angular/material/dialog';
import { MatTooltip } from '@angular/material/tooltip';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';


//components 
import { CustomerSelectComponent } from 'app/modules/widgets/customer-select/customer-select.component';
// Services and Types

import { InvoicingService } from 'app/core/databaseModels/invoicing/invoicing.service';
import { InvoiceStatus, Invoicing } from 'app/core/databaseModels/invoicing/invoicing.type';
import { CustomerService } from 'app/core/databaseModels/customers/customers.service';
import { ScheduledJobsService } from 'app/core/databaseModels/scheduledJobs/scheduledJob.service';
import { Customer } from 'app/core/databaseModels/customers/contacts.types';
import { ScheduledJob } from 'app/core/databaseModels/scheduledJobs/scheduledJob.types';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { QuillModule } from 'ngx-quill';

@Component({
    selector: 'app-invoice-details',
    imports: [
        CommonModule,
        RouterModule,
        ReactiveFormsModule,
        MatInputModule,
        MatFormFieldModule,
        MatButtonModule,
        MatDatepickerModule,
        MatNativeDateModule,
        MatCardModule,
        MatIconModule,
        MatSelectModule,
        MatSnackBarModule,
        MatDialogModule,
        MatTooltip,
        MatAutocompleteModule,
        MatProgressSpinnerModule,
        CustomerSelectComponent,
        QuillModule
    ],
    templateUrl: './invoice-details.component.html',
    styleUrls: ['./invoice-details.component.scss']
})
export class InvoiceDetailsComponent implements OnInit, OnDestroy {
    private destroy$ = new Subject<void>();
    private currentJobSubscription?: Subscription; // A

    invoiceForm: FormGroup;
    jobControl = new FormControl();
    filteredJobs: ScheduledJob[] = [];

    isEditMode = false;
    isCreateByIdMode = false;
    isViewMode = false;
    isCreateMode = false;
    isJobLoaded = false;
    isLoading = false;
    isSaving = false;
    invoiceId: string | null = null;
    detailsTemplate: string = '';

    customers: Customer[] = [];
    selectedCustomer: Customer | null = null;
    scheduledJobList: ScheduledJob[] = [];
    selectedScheduledJob: ScheduledJob | null = null;

    editorConfig = {
        toolbar: [
            ['bold', 'italic', 'underline', 'strike'],

            ['clean']
        ]
    };





    constructor(
        private fb: FormBuilder,
        private router: Router,
        private route: ActivatedRoute,
        private _changeDetectorRef: ChangeDetectorRef,
        private _snackBar: MatSnackBar,
        private _invoicingService: InvoicingService,
        private _customerService: CustomerService,
        private _scheduledJobsService: ScheduledJobsService,
        private _fuseConfirmationService: FuseConfirmationService,
    ) {
        this.initForm();

    }

    ngOnInit(): void {

        this.initializeComponent();
        this.initJobAutocomplete();
    }

    private initializeComponent(): void {
        // First determine mode and load customers

        this.isLoading = true;
        this.route.params.pipe(
            takeUntil(this.destroy$),
            tap(params => {
                this.invoiceId = params['id'] || null;
                this.determineMode();
            }),
            switchMap(() => this._customerService.getCustomers()),
            tap(customers => {
                this.customers = customers;
                this._changeDetectorRef.markForCheck();
            }),
            switchMap(() => {
                if (this.isCreateMode) {
                    if (this.invoiceId) {
                        // If an id is present in the create route, load the scheduled job
                        return this._scheduledJobsService.getScheduledJob(this.invoiceId).pipe(
                            switchMap(job => {
                                // Load the customer based on the job's customerId
                                this.isCreateByIdMode = true;
                                return this._customerService.getCustomer(job.customerId).pipe(
                                    map(customer => ({ job, customer }))
                                );
                            })
                        );
                    } else {
                        return this._invoicingService.getNextInvoiceNumber();
                    }
                } else if (this.invoiceId) {
                    return this._invoicingService.getInvoicing(this.invoiceId);
                }
                return of(null);
            })
        ).subscribe({
            next: (result) => {
                if (result) {
                    this.isLoading = false;
                    if (this.isCreateMode) {
                        if (result instanceof Object && 'job' in result && 'customer' in result) {
                            // If the result contains job and customer, set up the new invoice with the loaded data
                            const { job, customer } = result;
                            this._invoicingService.getNextInvoiceNumber().subscribe(settings => {
                                this.setupNewInvoice(settings);
                                this.onCustomerSelected(customer);
                                this.onJobSelected(job);

                                // Set the value of the customer autocomplete field
                                this.selectedCustomer = customer;
                                this.invoiceForm.patchValue({ customerId: customer.$id });

                                // Set the value of the job autocomplete field
                                this.selectedScheduledJob = job;
                                this.jobControl.setValue(job);
                            });
                        } else {
                            const settings = result as { nextNumber: string; prefix: string; gst: number, detailsTemplate: string, dueDays: number };
                            this.setupNewInvoice(settings);
                        }
                    } else {
                        const invoice = result as Invoicing;
                        this.populateForm(invoice);
                        if (invoice.customerId) {
                            this.loadCustomerJobs(invoice.customerId);
                        }
                    }
                }
                this._changeDetectorRef.markForCheck();
            },
            error: (error) => {
                console.error('Error initializing invoice:', error);
                this._snackBar.open('Error loading invoice data', 'Close', { duration: 3000 });
            }
        });

        // Subscribe to tax percent changes
        this.invoiceForm.get('taxPercent').valueChanges
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => this.calculateTotals());
    }

    private initJobAutocomplete(): void {
        this.jobControl.valueChanges
            .pipe(takeUntil(this.destroy$))
            .subscribe(value => {
                this.filteredJobs = this._filterJobs(value);
                this._changeDetectorRef.markForCheck();
            });
    }



    displayJobFn(job: ScheduledJob | string | null): string {
        if (!job) return '';
        if (typeof job === 'string') return job;

        const jobNumber = `${(job.jobNumber || null)}`;
        // const jobTitle = job.jobTitle || 'No Title';

        return job.jobNumber ? `${job.jobNumber} - ${job.jobTitle}` : job.jobTitle;
    }
    private _filterJobs(value: string | ScheduledJob | null): ScheduledJob[] {
        if (!value) return this.scheduledJobList;

        let filterValue = '';
        if (typeof value === 'string') {
            filterValue = value.toLowerCase();
        } else if (value && typeof value === 'object') {
            filterValue = ((value.jobNumber || '') + (value.jobTitle || '')).toLowerCase();
        }

        return this.scheduledJobList.filter(job =>
            (job.jobTitle?.toLowerCase() || '').includes(filterValue) ||
            (job.jobNumber?.toLowerCase() || '').includes(filterValue)
        );

    }


    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }



    private initForm(): void {
        this.invoiceForm = this.fb.group({
            $id: [null],
            organisationID: [null],
            customerId: [null, Validators.required],
            scheduledJobId: [null],
            invoiceNumber: [null, [Validators.required, Validators.min(1)]],
            invoiceDate: [new Date(), Validators.required],
            invoiceStatus: ['Pending'],
            total: [0],
            taxPercent: [0, [Validators.min(0), Validators.max(100)]],
            tax: [0],
            netTotal: [0],
            dueDate: [null, Validators.required],
            amountPaid: [0],
            paidDate: [null],
            paymentRef: [''],
            description: [''],

            items: this.fb.array([], Validators.required)
        });
    }

    private setupNewInvoice(settings: { nextNumber: string; prefix: string; gst: number, detailsTemplate: string, dueDays: number }): void {
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + settings.dueDays); // 15 days from now
        this.detailsTemplate = settings.detailsTemplate;
        this.invoiceForm.patchValue({
            invoiceNumber: settings.nextNumber,
            taxPercent: settings.gst,
            invoiceDate: new Date(),
            description: settings.detailsTemplate,
            dueDate: dueDate
        }, { emitEvent: false });
    }


    onCustomerSelected(customer: Customer): void {
        if (!customer) {
            this.onCustomerCleared();
            return;
        }

        if (this.selectedCustomer?.$id !== customer.$id) {
            this.selectedCustomer = customer;
            this.invoiceForm.patchValue({
                customerId: customer.$id,
                organisationID: customer.organisationID
            }, { emitEvent: false });
            this.loadCustomerJobs(customer.$id);
        }
    }


    onCustomerCleared(): void {
        this.selectedCustomer = null;
        this.invoiceForm.patchValue({
            customerId: '',
            organisationID: '',
            scheduledJobId: ''
        }, { emitEvent: false });
        this.isJobLoaded = false;
        this.scheduledJobList = [];
        this.filteredJobs = [];
        this.jobControl.setValue('');
        this.clearInvoiceItems();
    }
    onJobSelected(job: ScheduledJob): void {
        this.selectedScheduledJob = job;
        this.invoiceForm.patchValue({
            scheduledJobId: job.$id
        });
        this.clearInvoiceItems(); // Clear existing items before adding new one
        this.addJobItemToInvoice(job);
    }

    private addJobItemToInvoice(job: ScheduledJob): void {


        const newItem = this.createInvoiceItemForm();

        newItem.patchValue({
            description: job.jobTitle || '',
            quantity: 1,
            price: job.jobCost || 0,
            total: job.jobCost || 0
        });

        this.itemsFormArray.push(newItem);
        this.calculateTotals();
    }
    clearSelectedJob(): void {
        this.jobControl.setValue(null);
        this.selectedScheduledJob = null;
        this.invoiceForm.patchValue({
            scheduledJobId: null
        });
        this.clearInvoiceItems();
    }

    private clearInvoiceItems(): void {
        while (this.itemsFormArray.length !== 0) {
            this.itemsFormArray.removeAt(0);
        }
        this.calculateTotals();
    }
    loadCustomerJobs(customerId: string): void {
        if (this.currentJobSubscription) {
            this.currentJobSubscription.unsubscribe();
        }

        this.currentJobSubscription = this._scheduledJobsService
            .getCustomerJobsByJobStatus('finished', customerId)
            .pipe(
                takeUntil(this.destroy$),
                switchMap(() => this._scheduledJobsService.scheduledJobs$),
                tap(jobs => {

                    // console.log(this.scheduledJobList);
                    this.scheduledJobList = jobs;
                    this.filteredJobs = jobs; // Initialize filteredJobs with all jobs
                    this.isJobLoaded = true;
                    this._changeDetectorRef.markForCheck();
                })
            ).subscribe();
    }


    displayFn(customer: Customer): string {
        // console.log(customer);
        return customer && customer.name ? customer.name : '';
    }

    get itemsFormArray(): FormArray {
        return this.invoiceForm.get('items') as FormArray;
    }

    formatDecimal(controlName: string) {
        const control = this.invoiceForm.get(controlName);
        if (control && control.value) {
            let value = control.value.toString().replace(/\D/g, ''); // Remove non-digit characters
            if (value) {
                // Convert to a number and format to 2 decimal places
                const formattedValue = (parseFloat(value) / 100).toFixed(2);
                control.setValue(formattedValue, { emitEvent: false });
            }
        }
    }

    createInvoiceItemForm(): FormGroup {
        const itemForm = this.fb.group({
            description: ['', [Validators.required, Validators.minLength(3)]],
            quantity: [1, [Validators.required, Validators.min(1)]],
            price: [0, [Validators.required, Validators.min(0)]],
            discount: [0, [Validators.min(0)]],
            total: [0, [Validators.min(0)]]
        });

        // Listen to changes to recalculate item total
        itemForm.get('quantity').valueChanges
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => this.calculateItemTotal(itemForm));

        itemForm.get('price').valueChanges
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => this.calculateItemTotal(itemForm));

        itemForm.get('discount').valueChanges
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => this.calculateItemTotal(itemForm));

        return itemForm;
    }

    addInvoiceItem(): void {
        this.itemsFormArray.push(this.createInvoiceItemForm());
    }

    removeInvoiceItem(index: number): void {
        this.itemsFormArray.removeAt(index);
        this.calculateTotals();
    }

    calculateItemTotal(itemForm: FormGroup): void {
        const quantity = itemForm.get('quantity').value || 0;
        const price = itemForm.get('price').value || 0;
        const discount = itemForm.get('discount').value || 0;

        const baseTotal = quantity * price;
        //const discountAmount = baseTotal * (discount / 100);
        const itemTotal = baseTotal - discount;

        itemForm.get('total').setValue(itemTotal, { emitEvent: false });
        this.calculateTotals();
    }

    calculateTotals(): void {
        let subtotal = 0;
        this.itemsFormArray.controls.forEach((item: FormGroup) => {
            const itemTotal = item.get('total').value || 0;
            subtotal += itemTotal;
        });

        const taxPercent = this.invoiceForm.get('taxPercent').value || 0;
        const tax = subtotal * (taxPercent / 100);
        const netTotal = subtotal + tax;

        this.invoiceForm.patchValue({
            total: Math.max(0, subtotal),
            tax: Math.max(0, tax),
            netTotal: Math.max(0, netTotal)
        }, { emitEvent: false });
    }

    private determineMode(): void {
        const url = this.router.url;
        this.isEditMode = url.includes('/edit');
        this.isViewMode = url.includes('/view');
        this.isCreateMode = url.includes('/create');

        if (this.isViewMode) {
            this.invoiceForm.disable();
        } else {
            this.invoiceForm.enable();
        }
    }


    private populateForm(invoice: Invoicing): void {
        // Populate main form
        this.invoiceForm.patchValue({
            ...invoice,
            invoiceDate: invoice.invoiceDate ? new Date(invoice.invoiceDate) : null,
            dueDate: invoice.dueDate ? new Date(invoice.dueDate) : null,
            paidDate: invoice.paidDate ? new Date(invoice.paidDate) : null
        });

        // Set customer
        if (invoice.customerId) {
            this._customerService.getCustomer(invoice.customerId).subscribe(customer => {
                this.selectedCustomer = customer;
                this.onCustomerSelected(customer);
            });
        }

        // Set scheduled job
        if (invoice.scheduledJobId) {
            this._scheduledJobsService.getScheduledJob(invoice.scheduledJobId).subscribe(job => {
                this.selectedScheduledJob = job;
                this.jobControl.setValue(job);
            });
        }

        // Populate items
        this.clearInvoiceItems();
        invoice.items?.forEach(item => {
            const parsedItem = typeof item === 'string' ? JSON.parse(item) : item;
            const itemForm = this.createInvoiceItemForm();
            itemForm.patchValue(parsedItem);
            this.itemsFormArray.push(itemForm);
        });

        this.calculateTotals();
    }

    onSubmit(): void {
        this.isSaving = true;
        if (this.invoiceForm.invalid) {
            this.markFormGroupTouched(this.invoiceForm);
            this._snackBar.open('Please fill all required fields correctly', 'Close', { duration: 3000 });
            return;
        }

        const formValue = this.invoiceForm.value;


        if (this.isCreateMode) {
            this.createInvoice(formValue);
        } else if (this.isEditMode && this.invoiceId) {
            this.updateInvoice(formValue);
        }
    }

    private markFormGroupTouched(formGroup: FormGroup | FormArray) {
        Object.values(formGroup.controls).forEach(control => {
            if (control instanceof FormGroup || control instanceof FormArray) {
                this.markFormGroupTouched(control);
            } else {
                control.markAsTouched();
            }
        });
    }

    private createInvoice(invoiceData: Invoicing): void {
        invoiceData.invoiceStatus = InvoiceStatus.Approved;
        this._invoicingService.createInvoicing(invoiceData)
            .pipe(
                takeUntil(this.destroy$),
                switchMap(createdInvoice => {
                    this._snackBar.open('Invoice created successfully', 'Close', { duration: 3000 });

                    // Update the scheduled job if it exists and is selected
                    if (this.selectedScheduledJob && this.selectedScheduledJob.$id === invoiceData.scheduledJobId) {
                        const updatedJob = {
                            ...this.selectedScheduledJob,
                            invoicingId: createdInvoice.$id,
                            invoiced: true
                        };
                        return this._scheduledJobsService.updateScheduledJob(
                            this.selectedScheduledJob.$id,
                            updatedJob
                        ).pipe(
                            map(() => createdInvoice)
                        );
                    }
                    return of(createdInvoice);
                })
            )
            .subscribe({

                next: (createdInvoice) => {
                    this.isSaving = false;
                    if (createdInvoice.invoiceStatus === InvoiceStatus.Approved) {
                        this.router.navigate(['/invoicing/view', createdInvoice.$id]);
                    } else {
                        this.router.navigate(['/invoicing/list']);
                    }

                },
                error: (err) => {
                    console.error('Error creating invoice or updating scheduled job:', err);
                    this._snackBar.open('Error creating invoice', 'Close', { duration: 3000 });
                }
            });
    }

    private updateInvoice(invoiceData: Invoicing): void {
        this._invoicingService.updateInvoicing(this.invoiceId, invoiceData)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: (updatedInvoice) => {
                    this.isSaving = false;
                    this._snackBar.open('Invoice updated successfully', 'Close', { duration: 3000 });
                    if (updatedInvoice.invoiceStatus === InvoiceStatus.Approved) {
                        this.router.navigate(['/invoicing/view', updatedInvoice.$id]);
                    } else {
                        this.router.navigate(['/invoicing/list']);
                    }

                },
                error: (err) => {
                    this._snackBar.open('Error updating invoice', 'Close', { duration: 3000 });
                }
            });
    }

    deleteInvoice(): void {
        const confirmation = this._fuseConfirmationService.open({
            title: `Delete invoice - `,
            message: 'Are you sure you want to delete this invoice? This action cannot be undone!',
            actions: {
                confirm: {
                    label: 'Delete',
                },
            },
        });

        confirmation.afterClosed().subscribe((result) => {
            if (result === 'confirmed') {
                if (this.invoiceId) {
                    this._invoicingService.deleteInvoicing(this.invoiceId)
                        .pipe(
                            takeUntil(this.destroy$),
                            switchMap(() => {
                                if (this.selectedScheduledJob) {
                                    const updatedJob = {
                                        ...this.selectedScheduledJob,
                                        invoicingId: null,
                                        invoiced: false
                                    };
                                    return this._scheduledJobsService.updateScheduledJob(
                                        this.selectedScheduledJob.$id,
                                        updatedJob
                                    );
                                }
                                return of(null);
                            })
                        )
                        .subscribe({
                            next: () => {
                                this._snackBar.open('Invoice deleted successfully', 'Close', { duration: 3000 });
                                this.router.navigate(['/invoicing/list']);
                            },
                            error: (err) => {
                                console.error('Error deleting invoice or updating scheduled job:', err);
                                this._snackBar.open('Error deleting invoice', 'Close', { duration: 3000 });
                            }
                        });
                }
            }
        });
    }

    cancelEdit(): void {
        // if (this.isCreateMode) {
        this.router.navigate(['/invoicing/list']);
        // } else if (this.invoiceId) {
        //     this.router.navigate(['/invoicing/view', this.invoiceId]);
        // }
    }

    changeInvoiceStatus(): void {
        const newStatus = this.invoiceForm.get('invoiceStatus').value === InvoiceStatus.Pending ? InvoiceStatus.Approved : InvoiceStatus.Pending;
        const confirmationMessage = `Are you sure you want to change the invoice status to ${newStatus}?`;

        const confirmation = this._fuseConfirmationService.open({
            title: `Change Invoice Status to ${newStatus}`,
            message: confirmationMessage,
            icon: {
                show: true,
                name: 'heroicons_outline:calculator',
                color: 'accent',
            },
            actions: {
                confirm: {
                    color: 'accent',
                    label: 'Yes, Change Status',
                },
            },
        });

        confirmation.afterClosed().subscribe((result) => {
            if (result === 'confirmed') {
                this.invoiceForm.patchValue({ invoiceStatus: newStatus });
                this.updateInvoice(this.invoiceForm.value);
            }
        });
    }

    openComposeDialog(mode: string): void { }

    selectAllText(event: FocusEvent) {
        const inputElement = event.target as HTMLInputElement;
        inputElement.select();
    }


}

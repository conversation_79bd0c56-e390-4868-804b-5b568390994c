// invoice-details.component.scss

:host {
    display: block;
    
    .mat-mdc-form-field {
        width: 100%;
    }

    .invoice-header {
        background-color: var(--mdc-filled-text-field-container-color);
        border-radius: var(--mdc-shape-small);
        margin-bottom: 1rem;
    }

    .items-table {
        border-collapse: collapse;
        width: 100%;

        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--mat-table-row-item-outline-color);
        }

        th {
            font-weight: 500;
            color: var(--mdc-text-button-label-text-color);
        }

        .amount-column {
            text-align: right;
            width: 150px;
        }

        .actions-column {
            width: 70px;
            text-align: center;
        }
    }

    .totals-section {
        padding: 1rem;
        border-radius: var(--mdc-shape-small);
        background-color: var(--mdc-filled-text-field-container-color);

        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;

            &.grand-total {
                font-weight: bold;
                border-top: 2px solid var(--mat-table-row-item-outline-color);
                margin-top: 0.5rem;
                padding-top: 1rem;
            }
        }
    }
}

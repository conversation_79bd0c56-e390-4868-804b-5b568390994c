import { <PERSON><PERSON><PERSON>cy<PERSON><PERSON><PERSON>, DatePipe, NgClass } from '@angular/common';
import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router, RouterLink } from '@angular/router';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { Customer } from 'app/core/databaseModels/customers/contacts.types';
import { CustomerService } from 'app/core/databaseModels/customers/customers.service';
import { ScheduledJobsService } from 'app/core/databaseModels/scheduledJobs/scheduledJob.service';
import { ScheduledJob } from 'app/core/databaseModels/scheduledJobs/scheduledJob.types';
import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';
import { TeamMembersService } from 'app/core/databaseModels/teammembers/teammembers.service';
import { ExportTableService } from 'app/services/export-table.service';

@Component({
    selector: 'app-invoicing-awaiting',
    imports: [NgClass,
        MatMenuModule,
        MatTooltipModule,
        MatTableModule,
        DatePipe,
        MatInputModule,
        MatIconModule,
        DatePipe,
        MatPaginatorModule,
        MatSortModule,
        MatDatepickerModule,
        MatNativeDateModule,
        ReactiveFormsModule,
        RouterLink,
        CurrencyPipe,
    ],
    providers: [DatePipe],
    templateUrl: './invoicing-awaiting.component.html',
    styleUrl: './invoicing-awaiting.component.scss'
})
export class InvoicingAwaitingComponent implements OnInit, AfterViewInit {
    @ViewChild(MatPaginator) paginator: MatPaginator;
    @ViewChild(MatSort) sort: MatSort;
    scheduledJobList: ScheduledJob[];
    dateFilter = new FormControl();
    currentSearchValue: string = '';
    loadingData: boolean;
    selectedTableFilter: string;
    teamMembers: TeamMember[];
    customers: Customer[];
    displayedColumns: string[] = ['avatar', 'jobTitle', 'dueDate', 'customerId', 'jobCost', 'invoicing',];
    dataSource: MatTableDataSource<ScheduledJob> = new MatTableDataSource<ScheduledJob>();

    // Pagination state
    currentPage: number = 0;
    pageSize: number = 200;
    totalItems: number = 0;
    
    // Search state
    currentSearchQuery: string = '';
    private searchSubject = new Subject<string>();

    dispatchStatusLookup = {
        '': { color: 'gray', icon: 'fa-solid fa-mobile-signal-out' },
        'null': { color: 'gray', icon: 'fa-solid fa-mobile-signal-out' },
        'dispatched': { color: 'blue', icon: 'fa-solid fa-check' },
        'received': { color: 'blue', icon: 'fa-solid fa-check-double' },
        'onMyWay': { color: 'yellow', icon: 'fa-solid fa-arrows-turn-right' },
        'finished': { color: 'green', icon: 'fa-solid fa-circle-check' },
        'started': { color: '#8FBC8F', icon: 'fa-solid fa-square-check' },
    };

    jobStatusLookup = {
        'pending': { color: 'gray', icon: 'fa-solid fa-clock' },
        'added': { color: 'gray', icon: 'fa-solid fa-circle-dot' },
        'approved': { color: 'blue' },
        'started': { color: 'green' },
        'onMyWay': { color: 'yellow' },
        'finished': { color: 'green' }
    };

    constructor(
        private _scheduledJobsService: ScheduledJobsService,
        private _customerService: CustomerService,
        private _teamMembersService: TeamMembersService,
        private exportTableService: ExportTableService,
        private router: Router,
        private datePipe: DatePipe
    ) {
        // Initialize date filter listener
        this.dateFilter.valueChanges.subscribe(date => {
            this.filterData(this.currentSearchValue, date);
        });

        // Set up debounced search
        this.searchSubject.pipe(
            debounceTime(300), // Wait 300ms after user stops typing
            distinctUntilChanged() // Only search if the value actually changed
        ).subscribe(searchQuery => {
            this.performSearch(searchQuery);
        });
    }

    ngOnInit(): void {
        this.loadingData = true;
        this.dataSource = new MatTableDataSource<ScheduledJob>();

        // Load customers and team members first, then pagination data
        this.loadCustomers();
        this.loadTeamMembers();

        // Load total count first, then load first page
        this.loadTotalCount();
    }

    loadTotalCount(): void {
        this._scheduledJobsService.getCustomerJobsByJobStatusCount('finished', undefined, this.currentSearchQuery)
            .subscribe(count => {
                this.totalItems = count;

                // Update paginator with total count if available
                if (this.paginator) {
                    this.paginator.length = this.totalItems;
                }

                this.loadPageData();
            });
    }

    loadPageData(): void {
        this.loadingData = true;
        const offset = this.currentPage * this.pageSize;

        this._scheduledJobsService.getCustomerJobsByJobStatus('finished', undefined, this.pageSize, offset, this.currentSearchQuery)
            .subscribe(scheduledJobs => {
                this.scheduledJobList = scheduledJobs;
                this.dataSource.data = this.scheduledJobList;

                // Update paginator after data loads
                if (this.paginator) {
                    this.paginator.length = this.totalItems;
                    this.paginator.pageSize = this.pageSize;
                    this.paginator.pageIndex = this.currentPage;
                }

                this.loadingData = false;
            });
    }

    ngAfterViewInit() {
        if (this.paginator) {
            // Initialize paginator with current state
            this.paginator.length = this.totalItems;
            this.paginator.pageSize = this.pageSize;
            this.paginator.pageIndex = this.currentPage;

            // Listen for page events
            this.paginator.page.subscribe(event => {
                this.currentPage = event.pageIndex;
                this.pageSize = event.pageSize;
                this.loadPageData();
            });
        }

        // Only connect sort to dataSource
        if (this.sort) {
            this.dataSource.sort = this.sort;
        }
    }

    // Explicit page change handler
    onPageChange(event: any): void {
        this.currentPage = event.pageIndex;
        this.pageSize = event.pageSize;
        
        // Check if we're in search mode
        if (this.currentSearchQuery) {
            this.loadDataWithUnifiedSearch();
        } else {
            this.loadPageData();
        }
    }

    // Apply filter method (unified search: job title, customer name, and phone number)
    applyFilter(event: Event) {
        const filterValue = (event.target as HTMLInputElement).value;
        const trimmedValue = filterValue.trim();
        
        // Use debounced search subject instead of immediate search
        this.searchSubject.next(trimmedValue);
    }

    // Perform the actual search (called after debounce)
    private performSearch(searchQuery: string) {
        this.currentSearchQuery = searchQuery;
        
        // Reset to first page when searching
        this.currentPage = 0;
        if (this.paginator) {
            this.paginator.pageIndex = 0;
        }
        
        if (this.currentSearchQuery) {
            // For any search, load larger dataset and filter client-side
            // This allows search across job title, customer name, and phone
            this.loadDataWithUnifiedSearch();
        } else {
            // No search query, use normal pagination
            this.loadTotalCount();
        }
    }

    // Load data with unified search across job title, customer name, and phone
    private loadDataWithUnifiedSearch(): void {
        this.loadingData = true;
        
        // Load a larger chunk to search across (adjust based on performance needs)
        const searchPageSize = Math.min(2000, this.totalItems || 4316);
        
        this._scheduledJobsService.getCustomerJobsByJobStatus('finished', undefined, searchPageSize, 0)
            .subscribe(scheduledJobs => {
                // Filter by job title, customer name, and phone number
                const filteredJobs = this.filterByAllSearchFields(scheduledJobs, this.currentSearchQuery);
                
                // Apply pagination to filtered results
                const startIndex = this.currentPage * this.pageSize;
                const endIndex = startIndex + this.pageSize;
                
                this.totalItems = filteredJobs.length;
                this.scheduledJobList = filteredJobs.slice(startIndex, endIndex);
                this.dataSource.data = this.scheduledJobList;
                
                // Update paginator
                if (this.paginator) {
                    this.paginator.length = this.totalItems;
                    this.paginator.pageSize = this.pageSize;
                    this.paginator.pageIndex = this.currentPage;
                }
                
                this.loadingData = false;
            });
    }

    // Filter jobs by job title, customer name, and phone number
    private filterByAllSearchFields(jobs: any[], searchQuery: string): any[] {
        if (!searchQuery) return jobs;
        
        const query = searchQuery.toLowerCase();
        
        return jobs.filter(job => {
            // 1. Check job title
            if (job.jobTitle && job.jobTitle.toLowerCase().includes(query)) {
                return true;
            }
            
            // 2. Check customer name and phone
            if (this.customers) {
                const customer = this.customers.find(c => c.$id === job.customerId);
                if (customer) {
                    // Check customer name
                    if (customer.name && customer.name.toLowerCase().includes(query)) {
                        return true;
                    }
                    
                    // Check phone numbers
                    if (customer.phoneNumbers && customer.phoneNumbers.length > 0) {
                        for (const phoneData of customer.phoneNumbers) {
                            let phoneNumber = '';
                            if (typeof phoneData === 'string') {
                                try {
                                    const parsed = JSON.parse(phoneData);
                                    phoneNumber = parsed.phoneNumber || '';
                                } catch {
                                    phoneNumber = phoneData;
                                }
                            } else {
                                phoneNumber = phoneData.phoneNumber || '';
                            }
                            
                            // Check phone number (remove spaces and special chars for better matching)
                            const cleanPhone = phoneNumber.replace(/[\s\-\(\)\+]/g, '');
                            const cleanQuery = query.replace(/[\s\-\(\)\+]/g, '');
                            
                            if (cleanPhone.includes(cleanQuery)) {
                                return true;
                            }
                        }
                    }
                }
            }
            
            return false;
        });
    }

    // Clear all filters
    clearFilters() {
        this.dateFilter.reset();
        this.currentSearchValue = '';
        this.currentSearchQuery = '';

        // Reset search input
        const searchInput = document.querySelector('input[matInput]') as HTMLInputElement;
        if (searchInput) {
            searchInput.value = '';
        }

        // Reset to first page
        this.currentPage = 0;
        if (this.paginator) {
            this.paginator.pageIndex = 0;
        }
        
        // Reload data without search
        this.loadTotalCount();
    }

    // Simplified filter data for date filter
    filterData(searchValue: string = '', dateValue: Date | null = null) {
        this.currentSearchValue = searchValue;
        // For now, just apply basic filtering - could be enhanced for server-side filtering later
        if (dateValue) {
            const selectedDate = this.datePipe.transform(dateValue, 'yyyy-MM-dd');
            this.dataSource.data = this.scheduledJobList.filter(job => {
                const jobDate = this.datePipe.transform(job.dueDate, 'yyyy-MM-dd');
                return jobDate === selectedDate;
            });
        } else {
            this.dataSource.data = this.scheduledJobList;
        }
    }
    ngOnChanges() {
        this.dataSource.data = this.scheduledJobList;
    }

    loadCustomers(): void {
        this._customerService.getCustomers().subscribe(customers => {
            this.customers = customers;
        });
    }

    loadTeamMembers(): void {
        this._teamMembersService.getTeamMembers().subscribe(teamMembers => {
            this.teamMembers = teamMembers;
            this.loadingData = false;
        });
    }

    getTeamMemberInfo(id: string): { name: string, avatar: string | null, fullName: string } {
        if (!this.teamMembers || !id) {
            return { name: '', avatar: null, fullName: '' };
        }

        const teamMember = this.teamMembers.find(item => item.$id === id);

        if (!teamMember) {
            return { name: '', avatar: null, fullName: '' }; // Return empty if no team member found
        }

        // Fetch avatar URL if avatarImageId is present
        const avatar = teamMember.avatarImageId
            ? this._teamMembersService.getFilePreview(teamMember.avatarImageId, 50, 50)
            : null;

        // Use avatar name or generate initials if avatarImageId/avatar is absent
        const name = teamMember.avatar || (avatar ? '' : this.generateInitials(teamMember.name));

        return {
            name: name,
            avatar: avatar,
            fullName: teamMember.name
        };
    }
    getCustomerName(id: string): { name: string, contact: string } {
        try {
            if (this.customers == null) {
                return { name: '', contact: '' };
            }

            const customer = this.customers.find(item => item.$id === id);
            if (customer) {
                let number;

                // Check if the phone number is a string and attempt to parse it
                if (typeof customer.phoneNumbers[0] === 'string') {
                    try {
                        number = JSON.parse(customer.phoneNumbers[0]);
                    } catch (error) {
                        console.error('Error parsing phone number', error);
                        // Handle the error or return a placeholder/fallback
                        return { name: customer.name, contact: 'Invalid Contact' };
                    }
                } else {
                    // If it's not a string, assume it's already an object
                    number = customer.phoneNumbers[0];
                }

                return { name: customer.name, contact: number.phoneNumber };
            } else {
                return { name: '', contact: '' };
            }
        } catch (error) {
            console.error('Error parsing customer ID', error);
            // Handle the error or return a placeholder/fallback
            return { name: '', contact: '' };
        }

    }

    /*************  ✨ Codeium Command ⭐  *************/
    /**
     * Generate initials from a full name.
     *
     * This function takes a full name string, splits it into individual parts
     * (first, last, middle, etc.), and then takes either the first and last
     * initial if there are multiple parts, or just the first initial if there
     * is only one part. The resulting initials are returned as an uppercase
     * string.
     *
     * @param fullName The full name string to generate initials from.
     * @returns The generated initials string.
     */
    /******  f8fc0bfb-31db-4f9c-bf54-8a88931b6455  *******/
    private generateInitials(fullName: string): string {
        const nameParts = fullName.split(' ');
        const initials = nameParts.length > 1
            ? `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}` // First and last initials
            : nameParts[0][0]; // Only first initial if single part
        return initials.toUpperCase();
    }

    getEventAttributes(item) {
        let eventColor = 'gray';
        let icon = 'fa-solid fa-clock';

        const dispatchAttributes = this.dispatchStatusLookup[item.dispatchStatus || 'null'];
        const jobAttributes = this.jobStatusLookup[item.jobStatus];

        if (dispatchAttributes) {
            eventColor = dispatchAttributes.color;
            icon = dispatchAttributes.icon;
        }

        if (jobAttributes) {
            eventColor = jobAttributes.color;
            if (jobAttributes.icon) {
                icon = jobAttributes.icon;
            }
        }

        return { eventColor, icon };
    }

    exportMaterialTable(format: 'pdf' | 'excel') {
        const _data = this.scheduledJobList
            .filter(item => this.selectedTableFilter === 'all' || item.jobStatus === this.selectedTableFilter)
            .map(job => {
                const _customer = this.getCustomerName(job.customerId);
                const _teamMember = this.getTeamMemberInfo(job.teamMemberID);
                return {
                    'Team Member': _teamMember.fullName,
                    'Job Title': job.jobTitle,
                    'Job Status': job.jobStatus,
                    'Job Address': job.jobAddress,
                    'Due Date': job.dueDate.toString().split('T')[0] + ' ' + job.startTime,
                    'Customer': _customer.name,
                    'Customer Contact': _customer.contact,
                };
            });

        this.exportTableService.exportMaterialTable(_data, format, 'ScheduledJobs-' + new Date().toISOString());
    }

    loadInvoicing(item) {

        if (item.invoicingId && item.invoiced) {
            this.router.navigate(['/invoicing/view', item.invoicingId]);
        } else {
            this.router.navigate(['/invoicing/create', item.$id]);
        }
    }

}

<div class="container mx-auto px-4 py-8 w-full">
  <!-- Back to Dashboard Link -->
  <div class="mb-4">
    <a
      [routerLink]="'/invoicing/'"
      class="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
      >
      <i class="fas fa-arrow-left mr-2"></i>
      Back to Dashboard
    </a>
  </div>

  <div class="dark:bg-gray-800 shadow-lg p-6">
    <h2
      class="text-3xl font-extrabold text-gray-900 dark:text-white mb-8 flex items-center justify-center"
      >
      <i class="fas fa-file-invoice text-blue-500 mr-4"></i>
      Awaiting Jobs for Invoicing
    </h2>

    <!-- Always show search and filter section -->
    <div class="flex-auto bg-gray-100 p-4">
      <div
        class="xl:col-span-2 flex flex-col flex-auto bg-card shadow rounded-2xl overflow-hidden"
        >
        <div
          class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b"
          >
          <!-- Title -->

          <!-- Actions -->

          <!-- Search and filter section - always visible -->
          <div
            class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4 gap-4"
            >
            <!-- Search -->
            <mat-form-field
              class="fuse-mat-dense fuse-mat-rounded min-w-64"
              [subscriptSizing]="'dynamic'"
              >
              <mat-icon
                class="icon-size-5"
                matPrefix
                [svgIcon]="'heroicons_solid:magnifying-glass'"
              ></mat-icon>
              <input
                matInput
                (keyup)="applyFilter($event)"
                [autocomplete]="'off'"
                [placeholder]="'Search by name, phone, job...'"
                #input
                />
            </mat-form-field>

            <!-- Date Filter -->
            <mat-form-field
              class="fuse-mat-dense fuse-mat-rounded"
              [subscriptSizing]="'dynamic'"
              >
              <input
                matInput
                [matDatepicker]="picker"
                [formControl]="dateFilter"
                placeholder="Filter by date"
                />
              <mat-datepicker-toggle
                matIconSuffix
                [for]="picker"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>

            <!-- Clear Filters -->
            @if (dateFilter.value || currentSearchValue) {
              <button
                mat-icon-button
                (click)="clearFilters()"
                matTooltip="Clear all filters"
                class="text-gray-600 hover:text-gray-900"
                >
                <i class="fas fa-times-circle"></i>
              </button>
            }

            <!-- Export Button -->
            <button mat-icon-button [matMenuTriggerFor]="moreMenu">
              <i
                class="ml-3 icon-size-5 fa-duotone fa-file-export"
              ></i>
            </button>
            <mat-menu #moreMenu="matMenu">
              <button
                mat-menu-item
                (click)="exportMaterialTable('pdf')"
                >
                <i class="fa-duotone fa-file-pdf"></i>
                <span class="ml-3">PDF</span>
              </button>
              <button
                mat-menu-item
                (click)="exportMaterialTable('excel')"
                >
                <i class="fa-duotone fa-file-excel"></i>
                <span class="ml-3">EXCEL</span>
              </button>
            </mat-menu>
          </div>
        </div>
      </div>

      <!-- Table section with loading state -->
      @if (loadingData) {
        <div class="overflow-x-auto mx-6 py-8">
          <div
            class="flex flex-auto flex-col items-center justify-center"
            >
            <i
              class="icon-size-12 fa-duotone fa-spinner fa-spin text-blue-500"
            ></i>
            <div
              class="mt-4 text-lg font-medium tracking-tight text-secondary"
              >
              Searching...
            </div>
          </div>
        </div>
      }
      @if (!loadingData && scheduledJobList?.length > 0) {
        <div
          class="overflow-x-auto mx-6"
          >
          <div class="w-full bg-transparent">
            <table mat-table [dataSource]="dataSource" matSort>
              <!-- Team Member Column -->
              <ng-container matColumnDef="avatar">
                <th mat-header-cell *matHeaderCellDef>
                  Team Member
                </th>
                <td mat-cell *matCellDef="let row">
                  <div
                    class="relative flex flex-0 items-center justify-center w-10 h-10 rounded-full"
                    >
                    <!-- Team Member Dispatch Status -->
                    @if (row.dispatchStatus) {
                      <div
                        class="flex absolute bottom-0 right-0 flex-0 w-4 h-4 -ml-0.5 rounded-full ring-2 ring-bg-card items-center justify-center text-on-primary"
                                            [ngClass]="{
                                                'bg-blue-200':
                                                    getEventAttributes(row)
                                                        .eventColor === 'blue',
                                                'bg-gray-500':
                                                    getEventAttributes(row)
                                                        .eventColor === 'gray',
                                                'bg-green-500':
                                                    getEventAttributes(row)
                                                        .eventColor === 'green'
                                            }"
                        >
                        <i
                          class="fa-xs"
                                                [ngClass]="
                                                    getEventAttributes(row).icon
                                                "
                        ></i>
                      </div>
                    }
                    @if (!row.teamMemberID) {
                      <span
                        class="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10"
                        >Pending</span
                        >
                      }
                      <!-- Team Member Avatar Status -->
                      <div
                        class="w-15 rounded-full overflow-hidden"
                        >
                        @if (
                          getTeamMemberInfo(row.teamMemberID)
                          .avatar
                          ) {
                          <img
                                                [matTooltip]="
                                                    getTeamMemberInfo(
                                                        row.teamMemberID
                                                    ).fullName
                                                "
                            class="object-cover w-full h-full"
                                                [src]="
                                                    getTeamMemberInfo(
                                                        row.teamMemberID
                                                    ).avatar
                                                "
                            alt="Team Member Avatar"
                            />
                        } @else {
                          <div
                                                [matTooltip]="
                                                    getTeamMemberInfo(
                                                        row.teamMemberID
                                                    ).fullName
                                                "
                            class="flex items-center justify-center w-full h-full rounded-full text-lg uppercase bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                            >
                            {{
                            getTeamMemberInfo(
                            row.teamMemberID
                            ).name
                            }}
                          </div>
                        }
                      </div>
                      <ng-template #noAvatar>
                        <div
                          class="flex items-center justify-center w-full h-full rounded-full text-lg uppercase bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                          >
                          {{
                          getTeamMemberInfo(
                          row.teamMemberID
                          ).name
                          }}
                        </div>
                      </ng-template>
                    </div>
                  </td>
                </ng-container>
                <!-- job Title Column -->
                <ng-container matColumnDef="jobTitle">
                  <th
                    mat-header-cell
                    *matHeaderCellDef
                    mat-sort-header
                    >
                    Job Title
                  </th>
                  <td mat-cell *matCellDef="let row">
                    <div class="flex flex-col mt-2 mb-2">
                      <div class="font-medium">
                        {{ row.jobTitle }}
                      </div>
                      <div
                        class="flex flex-col sm:flex-row sm:items-center -ml-0.5 mt-2 sm:mt-1 space-y-1 sm:space-y-0 sm:space-x-3"
                        >
                        @if (row.startTime) {
                          <div class="flex items-center">
                            <mat-icon
                              class="icon-size-5 text-hint"
                                                    [svgIcon]="
                                                        'heroicons_solid:clock'
                                                    "
                            ></mat-icon>
                            <div
                              class="ml-1.5 text-md text-secondary"
                              >
                              {{ row.startTime }}
                            </div>
                          </div>
                        }
                        @if (row.jobAddress) {
                          <div class="flex items-center">
                            <mat-icon
                              class="icon-size-5 text-hint"
                                                    [svgIcon]="
                                                        'heroicons_solid:map-pin'
                                                    "
                            ></mat-icon>
                            <div
                              class="ml-1.5 text-md text-secondary"
                              >
                              {{ row.jobAddress }}
                            </div>
                          </div>
                        }
                      </div>
                    </div>
                  </td>
                </ng-container>
                <!-- dueDate Column -->
                <ng-container matColumnDef="dueDate">
                  <th
                    mat-header-cell
                    *matHeaderCellDef
                    mat-sort-header
                    >
                    Due Date
                  </th>
                  <td mat-cell *matCellDef="let row">
                    {{ row.dueDate | date: "EEE, dd MMM yyyy" }}
                  </td>
                </ng-container>
                <!-- Customer Column -->
                <ng-container matColumnDef="customerId">
                  <th
                    mat-header-cell
                    *matHeaderCellDef
                    mat-sort-header
                    >
                    Customer
                  </th>
                  <td mat-cell *matCellDef="let row">
                    @if (
                      getCustomerName(row.customerId);
                      as customer
                      ) {
                      <div>
                        <div class="flex flex-col mt-2 mb-2">
                          <div class="font-medium">
                            {{ customer.name }}
                          </div>
                          <div
                            class="flex flex-col sm:flex-row sm:items-center -ml-0.5 mt-2 sm:mt-1 space-y-1 sm:space-y-0 sm:space-x-3"
                            >
                            @if (customer.contact) {
                              <div
                                class="flex items-center"
                                >
                                <i
                                  class="icon-size-3 text-hint fa-duotone fa-phone-volume"
                                ></i>
                                <div
                                  class="ml-1.5 text-md text-secondary"
                                  >
                                  {{
                                  customer.contact
                                  }}
                                </div>
                              </div>
                            }
                          </div>
                        </div>
                      </div>
                    }
                  </td>
                </ng-container>
                <!-- jobCost Column -->
                <ng-container matColumnDef="jobCost">
                  <th
                    mat-header-cell
                    *matHeaderCellDef
                    mat-sort-header
                    >
                    Job Expected Cost
                  </th>
                  <td mat-cell *matCellDef="let row">
                    <div class="flex items-center justify-center">
                      <span class="ml-2">
                        {{ row.jobCost | currency }}
                      </span>
                    </div>
                  </td>
                </ng-container>
                <!-- Invoicing Column -->
                <ng-container matColumnDef="invoicing">
                  <th mat-header-cell *matHeaderCellDef>Invoicing</th>
                  <td
                    mat-cell
                    *matCellDef="let row"
                    class="text-center items-center justify-center"
                    >
                    @if (row.jobStatus === "finished") {
                      <button
                        mat-icon-button
                        (click)="loadInvoicing(row)"
                        >
                        <mat-icon
                                            [svgIcon]="
                                                'heroicons_outline:currency-dollar'
                                            "
                                            [ngClass]="{
                                                'text-green-600':
                                                    row.invoiced === true
                                            }"
                        ></mat-icon>
                      </button>
                    }
                  </td>
                </ng-container>
                <tr
                  mat-header-row
                  *matHeaderRowDef="displayedColumns"
                ></tr>
                <tr
                  mat-row
                  *matRowDef="let row; columns: displayedColumns"
                ></tr>
                <!-- Row shown when there is no matching data. -->
                <tr class="mat-row" *matNoDataRow>
                  <td class="mat-cell" colspan="6">
                    No data matching the filter "{{ input.value }}"
                  </td>
                </tr>
              </table>
              <mat-paginator
                #paginator
                [pageSize]="pageSize"
                [pageIndex]="currentPage"
                [length]="totalItems"
                [pageSizeOptions]="[5, 10, 20, 50, 100]"
                [showFirstLastButtons]="true"
                (page)="onPageChange($event)"
                aria-label="Select page of requests"
              ></mat-paginator>
            </div>
          </div>
        }
        @if (
          !loadingData &&
          (!scheduledJobList || scheduledJobList.length === 0)
          ) {
          <div
            class="overflow-x-auto mx-6 py-8"
            >
            <div
              class="flex flex-auto flex-col items-center justify-center"
              >
              <i
                class="icon-size-12 fa-duotone fa-calendar-range text-gray-400"
              ></i>
              <div
                class="mt-4 text-lg font-medium tracking-tight text-secondary"
                >
                @if (currentSearchQuery) {
                  No results found for "{{ currentSearchQuery }}"
                } @else {
                  No jobs available for invoicing
                }
              </div>
            </div>
          </div>
        }
      </div>
    </div>
  </div>

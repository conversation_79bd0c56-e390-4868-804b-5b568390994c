<div class="flex flex-col flex-auto min-w-0">
    <!-- Header -->
    <div class="bg-card">
        <div class="flex flex-col w-full max-w-screen-xl mx-auto px-6 sm:px-8">
            <div
                class="flex flex-col sm:flex-row flex-auto sm:items-center min-w-0 my-8 sm:my-12"
            >
                <!-- Avatar and name -->
                <div class="flex flex-auto items-center min-w-0">
                    <div class="flex-0 w-16 h-16 rounded-full overflow-hidden">
                        @if (user.avatar) {
                            <img
                                class="w-full h-full object-cover"
                                [src]="user.avatar"
                            />
                        }
                        @if (!user.avatar) {
                            <mat-icon
                                [svgIcon]="'heroicons_outline:user-circle'"
                            ></mat-icon>
                        }
                    </div>
                    <div class="flex flex-col min-w-0 ml-4">
                        <ng-container *transloco="let t">
                            <div
                                class="text-2xl md:text-5xl font-semibold tracking-tight leading-7 md:leading-snug truncate"
                            >
                                {{ t("welcome-back") }},
                                {{ user.name.split(" ")[0] }}!
                            </div>
                        </ng-container>
                        <div class="flex items-center">
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_solid:bell'"
                            ></mat-icon>
                            <div
                                class="ml-1.5 leading-6 truncate text-secondary"
                            >
                                You have no new notifications
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Actions -->
                <div class="flex items-center mt-6 sm:mt-0 sm:ml-2 space-x-3">
                    <button
                        class="bg-accent"
                        mat-flat-button
                        [color]="'accent'"
                        (click)="navigateToScheduler()"
                    >
                        <i class="fa-duotone fa-diagram-subtask"></i>
                        <span class="ml-2">CREATE JOB</span>
                    </button>
                    <button
                        mat-flat-button
                        [color]="'primary'"
                        (click)="navigateToAdminTools()"
                    >
                        <mat-icon
                            class="icon-size-5"
                            [svgIcon]="'heroicons_solid:cog-8-tooth'"
                        ></mat-icon>
                        <span class="ml-2">Admin Tools</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main -->
    <div class="flex-auto border-t -mt-px pt-4 sm:pt-6">
        <div class="w-full max-w-screen-xl mx-auto">
            <!-- Tabs -->
            <mat-tab-group
                class="sm:px-2"
                mat-stretch-tabs="false"
                [animationDuration]="'0'"
            >
                <!-- Home -->
                <mat-tab label="Home">
                    <ng-template matTabContent>
                        <div
                            class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 w-full min-w-0"
                        >
                            <!-- Summary -->
                            <div
                                class="flex flex-col flex-auto p-6 bg-card shadow rounded-2xl overflow-hidden"
                            >
                                <div class="flex items-start justify-between">
                                    <div
                                        class="text-lg font-medium tracking-tight leading-6 truncate"
                                    >
                                        Summary
                                    </div>
                                    <div class="ml-2 -mt-2 -mr-3">
                                        <button
                                            mat-icon-button
                                            [matMenuTriggerFor]="summaryMenu"
                                        >
                                            <mat-icon
                                                class="icon-size-5"
                                                [svgIcon]="
                                                    'heroicons_mini:ellipsis-vertical'
                                                "
                                            ></mat-icon>
                                        </button>
                                        <mat-menu #summaryMenu="matMenu">
                                            <button
                                                mat-menu-item
                                                (click)="getSummary('today')"
                                            >
                                                Today
                                            </button>
                                            <button
                                                mat-menu-item
                                                (click)="
                                                    getSummary('yesterday')
                                                "
                                            >
                                                Yesterday
                                            </button>
                                            <button
                                                mat-menu-item
                                                (click)="getSummary('2daysAgo')"
                                            >
                                                2 days ago
                                            </button>
                                            <button
                                                mat-menu-item
                                                (click)="getSummary('3daysAgo')"
                                            >
                                                3 days ago
                                            </button>
                                        </mat-menu>
                                    </div>
                                </div>
                                <div class="flex flex-col items-center mt-2">
                                    <div
                                        class="text-7xl sm:text-8xl font-bold tracking-tight leading-none text-blue-500"
                                    >
                                        {{ getApprovedStatus("total") }}
                                    </div>
                                    <div
                                        class="text-lg font-medium text-blue-600 dark:text-blue-500"
                                    >
                                        Total Jobs
                                    </div>
                                </div>
                            </div>
                            <!-- Overdue -->
                            <div
                                class="flex flex-col flex-auto p-6 bg-card shadow rounded-2xl overflow-hidden"
                            >
                                <div class="flex items-start justify-between">
                                    <div
                                        class="text-lg font-medium tracking-tight leading-6 truncate"
                                    >
                                        Overdue
                                    </div>
                                </div>
                                <div class="flex flex-col items-center mt-2">
                                    <div
                                        class="text-7xl sm:text-8xl font-bold tracking-tight leading-none text-red-500"
                                    >
                                        {{ getApprovedStatus("overdue") }}
                                    </div>
                                    <div
                                        class="text-lg font-medium text-red-600 dark:text-red-500"
                                    >
                                        Jobs
                                    </div>
                                </div>
                            </div>
                            <!-- Active -->
                            <div
                                class="flex flex-col flex-auto p-6 bg-card shadow rounded-2xl overflow-hidden"
                            >
                                <div class="flex items-start justify-between">
                                    <div
                                        class="text-lg font-medium tracking-tight leading-6 truncate"
                                    >
                                        Active
                                    </div>
                                </div>
                                <div class="flex flex-col items-center mt-2">
                                    <div
                                        class="text-7xl sm:text-8xl font-bold tracking-tight leading-none text-indigo-800"
                                    >
                                        {{ getApprovedStatus("active") }}
                                    </div>
                                    <div
                                        class="text-lg font-medium text-indigo-800 dark:text-indigo-800"
                                    >
                                        Jobs
                                    </div>
                                </div>
                            </div>
                            <!-- Finished -->
                            <div
                                class="flex flex-col flex-auto p-6 bg-card shadow rounded-2xl overflow-hidden"
                            >
                                <div class="flex items-start justify-between">
                                    <div
                                        class="text-lg font-medium tracking-tight leading-6 truncate"
                                    >
                                        Finished
                                    </div>
                                </div>
                                <div class="flex flex-col items-center mt-2">
                                    <div
                                        class="text-7xl sm:text-8xl font-bold tracking-tight leading-none text-green-500"
                                    >
                                        {{ getApprovedStatus("finished") }}
                                    </div>
                                    <div
                                        class="text-lg font-medium text-green-600 dark:text-green-500"
                                    >
                                        Jobs
                                    </div>
                                </div>
                            </div>
                            <!-- Jobs Summary -->
                            <div
                                class="sm:col-span-2 md:col-span-4 flex flex-col flex-auto p-6 bg-card shadow rounded-2xl overflow-hidden"
                            >
                                <div
                                    class="flex flex-col sm:flex-row items-start justify-between"
                                >
                                    <div
                                        class="text-lg font-medium tracking-tight leading-6 truncate"
                                    >
                                        Jobs Summary
                                    </div>
                                    <div
                                        class="mt-3 sm:mt-0 sm:ml-2 flex flex-col"
                                    >
                                        <mat-button-toggle-group
                                            value="this-week"
                                            #jobsSummaryWeekSelector="matButtonToggleGroup"
                                            (change)="
                                                onDateRangeSelectionChange(
                                                    $event
                                                )
                                            "
                                        >
                                            <mat-button-toggle
                                                value="custom-range"
                                                >Custom Range</mat-button-toggle
                                            >
                                            <mat-button-toggle value="last-week"
                                                >Last Week</mat-button-toggle
                                            >
                                            <mat-button-toggle value="this-week"
                                                >This Week</mat-button-toggle
                                            >
                                        </mat-button-toggle-group>
                                        @if (
                                            jobsSummaryWeekSelector.value ===
                                            "custom-range"
                                        ) {
                                            <mat-form-field class="w-full mt-2">
                                                <mat-label
                                                    >Enter a date
                                                    range</mat-label
                                                >
                                                <mat-date-range-input
                                                    [formGroup]="range"
                                                    [rangePicker]="picker"
                                                >
                                                    <input
                                                        matStartDate
                                                        formControlName="start"
                                                        placeholder="Start date"
                                                    />
                                                    <input
                                                        matEndDate
                                                        formControlName="end"
                                                        placeholder="End date"
                                                    />
                                                </mat-date-range-input>
                                                <mat-hint
                                                    >DD/MM/YYYY –
                                                    DD/MM/YYYY</mat-hint
                                                >
                                                <mat-datepicker-toggle
                                                    matIconSuffix
                                                    [for]="picker"
                                                ></mat-datepicker-toggle>
                                                <mat-date-range-picker
                                                    #picker
                                                ></mat-date-range-picker>

                                                @if (
                                                    range.controls.start.hasError(
                                                        "matStartDateInvalid"
                                                    )
                                                ) {
                                                    <mat-error
                                                        >Invalid start
                                                        date</mat-error
                                                    >
                                                }
                                                @if (
                                                    range.controls.end.hasError(
                                                        "matEndDateInvalid"
                                                    )
                                                ) {
                                                    <mat-error
                                                        >Invalid end
                                                        date</mat-error
                                                    >
                                                }
                                            </mat-form-field>
                                        }
                                    </div>
                                </div>
                                <div
                                    class="grid grid-cols-1 lg:grid-cols-2 grid-flow-row gap-6 w-full mt-8 sm:mt-4"
                                >
                                    <!-- New vs. Closed -->
                                    <div class="flex flex-col flex-auto">
                                        <div class="font-medium text-secondary">
                                            Total Jobs.
                                        </div>
                                        <div class="flex flex-col flex-auto">
                                            <apx-chart
                                                class="flex-auto w-full h-80"
                                                [chart]="chartJobsSummary.chart"
                                                [colors]="
                                                    chartJobsSummary.colors
                                                "
                                                [dataLabels]="
                                                    chartJobsSummary.dataLabels
                                                "
                                                [grid]="chartJobsSummary.grid"
                                                [labels]="
                                                    chartJobsSummary.labels
                                                "
                                                [legend]="
                                                    chartJobsSummary.legend
                                                "
                                                [plotOptions]="
                                                    chartJobsSummary.plotOptions
                                                "
                                                [series]="
                                                    chartJobsSummary.series[
                                                        jobsSummaryWeekSelector
                                                            .value
                                                    ]
                                                "
                                                [states]="
                                                    chartJobsSummary.states
                                                "
                                                [stroke]="
                                                    chartJobsSummary.stroke
                                                "
                                                [tooltip]="
                                                    chartJobsSummary.tooltip
                                                "
                                                [xaxis]="chartJobsSummary.xaxis"
                                                [yaxis]="chartJobsSummary.yaxis"
                                            ></apx-chart>
                                        </div>
                                    </div>
                                    <!-- Overview -->
                                    <div class="flex flex-col">
                                        <div class="font-medium text-secondary">
                                            Overview
                                        </div>
                                        <div
                                            class="flex-auto grid grid-cols-4 gap-4 mt-6"
                                        >
                                            <!-- Total Jobs -->
                                            <div
                                                class="col-span-2 flex flex-col items-center justify-center py-8 px-1 rounded-2xl bg-indigo-50 text-indigo-800 dark:bg-white dark:bg-opacity-5 dark:text-indigo-400"
                                            >
                                                <div
                                                    class="text-5xl sm:text-7xl font-semibold leading-none tracking-tight"
                                                >
                                                    {{
                                                        jobSummary.overview[
                                                            jobsSummaryWeekSelector
                                                                .value
                                                        ]["total-jobs"]
                                                    }}
                                                </div>
                                                <div
                                                    class="mt-1 text-sm sm:text-lg font-medium"
                                                >
                                                    New Jobs
                                                </div>
                                            </div>
                                            <!-- Finished -->
                                            <div
                                                class="col-span-2 flex flex-col items-center justify-center py-8 px-1 rounded-2xl bg-green-50 text-green-800 dark:bg-white dark:bg-opacity-5 dark:text-green-500"
                                            >
                                                <div
                                                    class="text-5xl sm:text-7xl font-semibold leading-none tracking-tight"
                                                >
                                                    {{
                                                        jobSummary.overview[
                                                            jobsSummaryWeekSelector
                                                                .value
                                                        ]["finished-jobs"]
                                                    }}
                                                </div>
                                                <div
                                                    class="mt-1 text-sm sm:text-lg font-medium"
                                                >
                                                    Finished
                                                </div>
                                            </div>
                                            <!-- pending -->
                                            <div
                                                class="col-span-2 flex flex-col items-center justify-center py-8 px-1 rounded-2xl bg-gray-50 text-gray-800 dark:bg-white dark:bg-opacity-5 dark:text-gray-400"
                                            >
                                                <div
                                                    class="text-5xl sm:text-7xl font-semibold leading-none tracking-tight"
                                                >
                                                    {{
                                                        jobSummary.overview[
                                                            jobsSummaryWeekSelector
                                                                .value
                                                        ]["pending"]
                                                    }}
                                                </div>
                                                <div
                                                    class="mt-1 text-sm sm:text-lg font-medium"
                                                >
                                                    Pending Jobs
                                                </div>
                                            </div>
                                            <!-- Won't finished -->
                                            <div
                                                class="col-span-2 flex flex-col items-center justify-center py-8 px-1 rounded-2xl bg-orange-50 text-orange-800 dark:bg-white dark:bg-opacity-5 dark:text-orange-500"
                                            >
                                                <div
                                                    class="text-5xl sm:text-7xl font-semibold leading-none tracking-tight"
                                                >
                                                    {{
                                                        jobSummary.overview[
                                                            jobsSummaryWeekSelector
                                                                .value
                                                        ]["wont-finished"]
                                                    }}
                                                </div>
                                                <div
                                                    class="mt-1 text-sm sm:text-lg font-medium"
                                                >
                                                    Progressing
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Task distribution -->
                            <div
                                class="sm:col-span-2 md:col-span-4 lg:col-span-2 flex flex-col flex-auto p-6 bg-card shadow rounded-2xl overflow-hidden"
                            >
                                <div
                                    class="flex flex-col sm:flex-row items-start justify-between"
                                >
                                    <div
                                        class="text-lg font-medium tracking-tight leading-6 truncate"
                                    >
                                        Task Distribution
                                    </div>
                                    <div class="mt-3 sm:mt-0 sm:ml-2">
                                        <mat-button-toggle-group
                                            value="this-week"
                                            #taskDistributionWeekSelector="matButtonToggleGroup"
                                        >
                                            <mat-button-toggle value="last-week"
                                                >Last Week</mat-button-toggle
                                            >
                                            <mat-button-toggle value="this-week"
                                                >This Week</mat-button-toggle
                                            >
                                        </mat-button-toggle-group>
                                    </div>
                                </div>
                                <div class="flex flex-col flex-auto mt-6">
                                    <apx-chart
                                        class="flex-auto w-full h-80"
                                        [chart]="chartTaskDistribution.chart"
                                        [labels]="chartTaskDistribution.labels"
                                        [legend]="chartTaskDistribution.legend"
                                        [plotOptions]="
                                            chartTaskDistribution.plotOptions
                                        "
                                        [series]="
                                            chartTaskDistribution.series[
                                                taskDistributionWeekSelector
                                                    .value
                                            ]
                                        "
                                        [states]="chartTaskDistribution.states"
                                        [stroke]="chartTaskDistribution.stroke"
                                        [theme]="chartTaskDistribution.theme"
                                        [tooltip]="
                                            chartTaskDistribution.tooltip
                                        "
                                        [yaxis]="chartTaskDistribution.yaxis"
                                    ></apx-chart>
                                </div>
                                <div
                                    class="grid grid-cols-2 border-t divide-x -m-6 mt-4 bg-gray-50 dark:bg-transparent"
                                >
                                    <div
                                        class="flex flex-col items-center justify-center p-6 sm:p-8"
                                    >
                                        <div
                                            class="text-5xl font-semibold leading-none tracking-tighter"
                                        >
                                            {{
                                                jobSummary.taskDistribution
                                                    .overview[
                                                    taskDistributionWeekSelector
                                                        .value
                                                ]["total"]
                                            }}
                                        </div>
                                        <div
                                            class="mt-1 text-center text-secondary"
                                        >
                                            Total Scheduled Jobs
                                        </div>
                                    </div>
                                    <div
                                        class="flex flex-col items-center justify-center p-6 sm:p-8"
                                    >
                                        <div
                                            class="text-5xl font-semibold leading-none tracking-tighter"
                                        >
                                            {{
                                                jobSummary.taskDistribution
                                                    .overview[
                                                    taskDistributionWeekSelector
                                                        .value
                                                ]["finished"]
                                            }}
                                        </div>
                                        <div
                                            class="mt-1 text-center text-secondary"
                                        >
                                            Completed Jobs
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Schedule -->
                            <div
                                class="sm:col-span-2 md:col-span-4 lg:col-span-2 flex flex-col flex-auto p-6 bg-card shadow rounded-2xl overflow-hidden"
                            >
                                <div
                                    class="flex flex-col sm:flex-row items-start justify-between"
                                >
                                    <div
                                        class="text-lg font-medium tracking-tight leading-6 truncate"
                                    >
                                        Schedule
                                    </div>
                                    <div class="mt-3 sm:mt-0 sm:ml-2">
                                        <mat-button-toggle-group
                                            value="today"
                                            #scheduleDaySelector="matButtonToggleGroup"
                                        >
                                            <mat-button-toggle
                                                value="today"
                                                (click)="getSummary('today')"
                                                >Today</mat-button-toggle
                                            >
                                            <mat-button-toggle
                                                value="tomorrow"
                                                (click)="getSummary('tomorrow')"
                                                >Tomorrow</mat-button-toggle
                                            >
                                        </mat-button-toggle-group>
                                    </div>
                                </div>
                                <div class="flex flex-col mt-2 divide-y">
                                    @for (
                                        scheduleItem of scheduledJobList;
                                        track trackByFn($index, scheduleItem)
                                    ) {
                                        <div
                                            class="flex flex-row items-center justify-between py-4 px-0.5"
                                        >
                                            <div class="flex flex-col">
                                                <div class="font-medium">
                                                    {{ scheduleItem.jobTitle }}
                                                </div>
                                                <div
                                                    class="flex flex-col sm:flex-row sm:items-center -ml-0.5 mt-2 sm:mt-1 space-y-1 sm:space-y-0 sm:space-x-3"
                                                >
                                                    @if (
                                                        scheduleItem.startTime
                                                    ) {
                                                        <div
                                                            class="flex items-center"
                                                        >
                                                            <mat-icon
                                                                class="icon-size-5 text-hint"
                                                                [svgIcon]="
                                                                    'heroicons_solid:clock'
                                                                "
                                                            ></mat-icon>
                                                            <div
                                                                class="ml-1.5 text-md text-secondary"
                                                            >
                                                                {{
                                                                    scheduleItem.startTime
                                                                }}
                                                            </div>
                                                        </div>
                                                    }
                                                    @if (
                                                        scheduleItem.jobAddress
                                                    ) {
                                                        <div
                                                            class="flex items-center"
                                                        >
                                                            <mat-icon
                                                                class="icon-size-5 text-hint"
                                                                [svgIcon]="
                                                                    'heroicons_solid:map-pin'
                                                                "
                                                            ></mat-icon>
                                                            <div
                                                                class="ml-1.5 text-md text-secondary"
                                                            >
                                                                {{
                                                                    scheduleItem.jobAddress
                                                                }}
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                            </div>
                                            <button
                                                mat-icon-button
                                                (click)="navigateToScheduler()"
                                            >
                                                <mat-icon
                                                    [svgIcon]="
                                                        'heroicons_mini:chevron-right'
                                                    "
                                                ></mat-icon>
                                            </button>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </ng-template>
                </mat-tab>

                <!-- Team -->
                <mat-tab>
                    <ng-template mat-tab-label>
                        <span class="inline-flex items-center space-x-2">
                            <span class="">Team Members</span>
                            <span
                                class="px-2 py-1 text-sm rounded-full bg-primary-100 text-on-primary-100"
                                >{{ teamMembersCount }}</span
                            >
                        </span>
                    </ng-template>
                    <ng-template matTabContent>
                        <team-members-tab />
                    </ng-template>
                </mat-tab>

                <!--vehicles-->
                <mat-tab>
                    <ng-template mat-tab-label>
                        <span class="inline-flex items-center space-x-2">
                            <span class="">Vehicles</span>
                            <span
                                class="px-2 py-1 text-sm rounded-full bg-primary-100 text-on-primary-100"
                                >{{ vehiclesCount }}
                            </span>
                        </span>
                    </ng-template>
                    <ng-template matTabContent>
                        <vehicles-tab />
                    </ng-template>
                </mat-tab>
            </mat-tab-group>
        </div>
    </div>
</div>

@if (teamMembers && teamMembers.length > 0) {
    <!-- <div class="mt-3 sm:mt-0 sm:ml-2 flex flex-col">
        <mat-button-toggle-group
            value="today"
            #shiftSummaryDaySelector="matButtonToggleGroup"
            (change)="onDateDaySelectionChange($event)"
        >
            <mat-button-toggle value="custom-date"
                >Select Date</mat-button-toggle
            >
            <mat-button-toggle value="yesterday">Yesterday</mat-button-toggle>
            <mat-button-toggle value="today">Today</mat-button-toggle>
        </mat-button-toggle-group>
        @if (shiftSummaryDaySelector.value === "custom-date") {
            <mat-form-field class="w-full mt-2">
                <mat-label>select date</mat-label>
                <mat-date-input [formGroup]="range" [rangePicker]="picker">
                    <input
                        matStartDate
                        formControlName="start"
                        placeholder="Start date"
                    />
                    <input
                        matEndDate
                        formControlName="end"
                        placeholder="End date"
                    />
                </mat-date-input>
                <mat-hint>DD/MM/YYYY</mat-hint>
                <mat-datepicker-toggle
                    matIconSuffix
                    [for]="dayPicker"
                ></mat-datepicker-toggle>
                <mat-date-picker #dayPicker></mat-date-picker>

                @if (range.controls.start.hasError("matStartDateInvalid")) {
                    <mat-error>Invalid start date</mat-error>
                }
                @if (range.controls.end.hasError("matEndDateInvalid")) {
                    <mat-error>Invalid end date</mat-error>
                }
            </mat-form-field>
        }
    </div> -->
    <div
        class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-6 2xl:grid-cols-4 gap-6 w-full min-w-0"
    >
        @for (member of teamMembers; track member.$id) {
            <div
                class="flex flex-col bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden"
            >
                <!-- Header with Status -->
                <div class="relative px-6 pt-6">
                    <span
                        class="absolute top-4 right-4 px-3 py-1 text-sm font-medium rounded-full text-white"
                        [ngClass]="{
                            'bg-red-400': member.dutyStatus === 'offDuty',
                            'bg-blue-500': member.dutyStatus === 'onDuty',
                            'bg-green-500': member.dutyStatus === 'driving',
                            'bg-amber-500': member.dutyStatus === 'breaking'
                        }"
                    >
                        {{ formatDutyStatus(member.dutyStatus) }}
                    </span>
                </div>

                <!-- Avatar & Basic Info -->
                <div class="flex flex-col items-center px-6 pt-2 pb-6">
                    <div class="relative w-24 h-24">
                        @if (getTeamMemberInfo(member.$id).avatar) {
                            <img
                                class="w-24 h-24 rounded-full object-cover ring-4 ring-gray-400 dark:ring-gray-700"
                                [src]="getTeamMemberInfo(member.$id).avatar"
                                alt="Team Member Avatar"
                            />
                        } @else {
                            <div
                                class="flex items-center justify-center w-24 h-24 rounded-full text-3xl font-semibold bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200 ring-4 ring-gray-100 dark:ring-gray-700"
                            >
                                {{ getInitials(member.name) }}
                            </div>
                        }
                    </div>

                    <h3
                        class="mt-4 text-lg font-semibold text-gray-900 dark:text-white"
                    >
                        {{ member.name }}
                    </h3>

                    @if (member.assignedVehicle) {
                        <div
                            class="mt-1 flex items-center text-sm text-gray-800 dark:text-gray-400"
                        >
                            <i
                                class="icon-size-4 fa-duotone fa-truck-container mr-2"
                            ></i>
                            {{ getVehicleName(member.assignedVehicle) }}
                        </div>
                    } @else {
                        <div
                            class="mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400"
                        >
                            <i
                                class="icon-size-3 fa-duotone fa-truck-container mr-2"
                            ></i>
                            Not Assigned
                        </div>
                    }
                </div>

                <!-- Shift Information -->
                <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700/50">
                    @if (getTeamMembersShiftsInfo(member.$id, true).notes) {
                        <span
                            matTooltip="{{
                                getTeamMembersShiftsInfo(member.$id, true).notes
                            }}"
                            class="relative -top-7 justify-center px-3 py-1 text-sm font-medium rounded-full text-white bg-blue-500 dark:bg-blue-800"
                        >
                            Driver Note</span
                        >
                    }

                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span
                                class="text-sm text-gray-500 dark:text-gray-400"
                                >Shift Start</span
                            >
                            @if (
                                getTeamMembersShiftsInfo(member.$id, true)
                                    .startShift
                            ) {
                                <span
                                    class="text-sm font-medium text-gray-900 dark:text-white"
                                    >{{
                                        getTeamMembersShiftsInfo(
                                            member.$id,
                                            true
                                        ).startShift | date: "dd hh:mm a"
                                    }}</span
                                >
                            } @else {
                                <span
                                    class="text-sm font-medium text-gray-900 dark:text-white"
                                    >N/A</span
                                >
                            }
                        </div>
                        <div class="flex justify-between items-center">
                            <span
                                class="text-sm text-gray-500 dark:text-gray-400"
                                >Shift End</span
                            >
                            @if (
                                getTeamMembersShiftsInfo(member.$id, true)
                                    .endShift
                            ) {
                                <span
                                    class="text-sm font-medium text-gray-900 dark:text-white"
                                    >{{
                                        getTeamMembersShiftsInfo(
                                            member.$id,
                                            true
                                        ).endShift | date: "dd hh:mm a"
                                    }}</span
                                >
                            } @else {
                                <span
                                    class="text-sm font-medium text-gray-900 dark:text-white"
                                    >N/A</span
                                >
                            }
                        </div>
                        <div class="flex justify-between items-center">
                            <span
                                class="text-sm text-gray-500 dark:text-gray-400"
                                >Total Duration</span
                            >
                            <span
                                class="text-sm font-medium text-gray-900 dark:text-white"
                                >{{
                                    getTeamMembersShiftsInfo(member.$id, true)
                                        .totalDuration ?? 0 | minutesToTime
                                }}</span
                            >
                        </div>
                        <div class="flex justify-between items-center">
                            <span
                                class="text-sm text-gray-500 dark:text-gray-400"
                                >Total Break</span
                            >
                            <span
                                class="text-sm font-medium text-gray-900 dark:text-white"
                                >{{
                                    getTeamMembersShiftsInfo(member.$id, true)
                                        .totalBreaks ?? 0 | minutesToTime
                                }}</span
                            >
                        </div>
                    </div>
                </div>

                <!-- Contact Actions -->
                <div
                    class="grid grid-cols-2 divide-x divide-gray-200 dark:divide-gray-700 border-t border-gray-200 dark:border-gray-700"
                >
                    <a
                        class="flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                        [href]="'mailto:' + member.email"
                    >
                        <i
                            class="icon-size-5 fa-duotone fa-envelope text-gray-400 dark:text-gray-500"
                        ></i>
                        Email
                    </a>
                    <a
                        class="flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                        [href]="'tel:' + member.phone"
                    >
                        <i
                            class="icon-size-5 fa-duotone fa-phone text-gray-400 dark:text-gray-500"
                        ></i>
                        Call
                    </a>
                </div>
            </div>
        }
    </div>

    <!-- Jobs Summary -->
    <mat-divider class="my-8" />
    <div
        class="sm:col-span-2 md:col-span-4 flex flex-col flex-auto p-6 bg-card shadow rounded-2xl overflow-hidden"
    >
        <div class="flex flex-col sm:flex-row items-start justify-between">
            <div class="text-lg font-medium tracking-tight leading-6 truncate">
                Shift Summary
            </div>
            <div class="mt-3 sm:mt-0 sm:ml-2 flex flex-col">
                <mat-button-toggle-group
                    value="this-week"
                    #shiftSummaryWeekSelector="matButtonToggleGroup"
                    (change)="onDateRangeSelectionChange($event)"
                >
                    <mat-button-toggle value="custom-range"
                        >Custom Range</mat-button-toggle
                    >
                    <mat-button-toggle value="last-week"
                        >Last Week</mat-button-toggle
                    >
                    <mat-button-toggle value="this-week"
                        >This Week</mat-button-toggle
                    >
                </mat-button-toggle-group>
                @if (shiftSummaryWeekSelector.value === "custom-range") {
                    <mat-form-field class="w-full mt-2">
                        <mat-label>Enter a date range</mat-label>
                        <mat-date-range-input
                            [formGroup]="range"
                            [rangePicker]="picker"
                        >
                            <input
                                matStartDate
                                formControlName="start"
                                placeholder="Start date"
                            />
                            <input
                                matEndDate
                                formControlName="end"
                                placeholder="End date"
                            />
                        </mat-date-range-input>
                        <mat-hint>DD/MM/YYYY – DD/MM/YYYY</mat-hint>
                        <mat-datepicker-toggle
                            matIconSuffix
                            [for]="picker"
                        ></mat-datepicker-toggle>
                        <mat-date-range-picker #picker></mat-date-range-picker>

                        @if (
                            range.controls.start.hasError("matStartDateInvalid")
                        ) {
                            <mat-error>Invalid start date</mat-error>
                        }
                        @if (range.controls.end.hasError("matEndDateInvalid")) {
                            <mat-error>Invalid end date</mat-error>
                        }
                    </mat-form-field>
                }
            </div>
        </div>
        <div class="mt-6">
            @if (isLoadingShiftSummary) {
                <div class="flex items-center justify-center gap-3">
                    <mat-spinner [diameter]="24"></mat-spinner>
                    <span class="text-gray-600 dark:text-gray-300"
                        >Loading...</span
                    >
                </div>
            } @else {
                <!-- Data Table Section -->
                <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                    <table
                        class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400"
                    >
                        <thead
                            class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400"
                        >
                            <tr>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10"
                                ></th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Driver
                                </th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Total Shifts
                                </th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Total Duration
                                </th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Total Breaks
                                </th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                    Total Distance
                                </th>
                            </tr>
                        </thead>

                        <tbody>
                            @for (member of teamMembers; track member.$id) {
                                <!-- Main row -->
                                <tr
                                    class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                                    (click)="toggleAccordion(member.$id)"
                                >
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <i
                                            class="icon-size-5 fa-duotone"
                                            [ngClass]="{
                                                'fa-chevron-down': isExpanded(
                                                    member.$id
                                                ),
                                                'fa-chevron-right': !isExpanded(
                                                    member.$id
                                                )
                                            }"
                                        ></i>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            @if (
                                                getTeamMemberInfo(member.$id)
                                                    .avatar
                                            ) {
                                                <img
                                                    class="w-10 h-10 rounded-full object-cover ring-4 ring-gray-400 dark:ring-gray-700"
                                                    [src]="
                                                        getTeamMemberInfo(
                                                            member.$id
                                                        ).avatar
                                                    "
                                                    alt="Team Member Avatar"
                                                />
                                            } @else {
                                                <div
                                                    class="flex items-center justify-center w-10 h-10 rounded-full text-lg font-semibold bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200 ring-4 ring-gray-100 dark:ring-gray-700"
                                                >
                                                    {{
                                                        getInitials(member.name)
                                                    }}
                                                </div>
                                            }
                                            <div class="ml-4">
                                                <div
                                                    class="text-sm font-medium text-gray-900 dark:text-white"
                                                >
                                                    {{ member.name }}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {{
                                            getTeamMembersShiftsInfo(
                                                member.$id,
                                                false
                                            ).totalShifts
                                        }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {{
                                            getTeamMembersShiftsInfo(
                                                member.$id,
                                                false
                                            ).totalDuration ?? 0 | minutesToTime
                                        }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {{
                                            getTeamMembersShiftsInfo(
                                                member.$id,
                                                false
                                            ).totalBreaks ?? 0 | minutesToTime
                                        }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {{
                                            getTeamMembersShiftsInfo(
                                                member.$id,
                                                false
                                            ).totalDistance
                                        }}
                                        km
                                    </td>
                                </tr>

                                <!-- Accordion content - shifts detail -->
                                @if (isExpanded(member.$id)) {
                                    <tr
                                        class="bg-gray-50 dark:bg-gray-900/30 border-b dark:border-gray-700 border-gray-200"
                                    >
                                        <td class="px-6 py-4" colspan="6">
                                            <!-- Shifts Container -->
                                            <div class="space-y-6">
                                                <!-- Individual Shifts -->
                                                @for (
                                                    shift of getShiftsForTeamMember(
                                                        member.$id
                                                    );
                                                    track shift.$id
                                                ) {
                                                    <div
                                                        class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
                                                    >
                                                        <!-- Shift header -->
                                                        <div
                                                            class="bg-blue-50 dark:bg-blue-900/30 p-4 flex flex-wrap justify-between items-center"
                                                        >
                                                            <div
                                                                class="flex items-center space-x-4"
                                                            >
                                                                <div
                                                                    class="flex flex-col"
                                                                >
                                                                    <span
                                                                        class="text-sm font-medium text-gray-900 dark:text-white"
                                                                    >
                                                                        Shift
                                                                        {{
                                                                            shift.$id.substring(
                                                                                0,
                                                                                8
                                                                            )
                                                                        }}
                                                                    </span>
                                                                    <span
                                                                        class="text-xs text-gray-500 dark:text-gray-400"
                                                                    >
                                                                        {{
                                                                            shift.shiftDuration
                                                                                | minutesToTime
                                                                        }}
                                                                        Duration
                                                                    </span>
                                                                </div>
                                                            </div>
                                                            <div
                                                                class="flex flex-col items-end"
                                                            >
                                                                <span
                                                                    class="text-xs font-medium text-gray-900 dark:text-white"
                                                                >
                                                                    Started:
                                                                    {{
                                                                        shift.startShift
                                                                            | date
                                                                                : "MMM d, y, h:mm a"
                                                                    }}
                                                                </span>
                                                                <span
                                                                    class="text-xs font-medium text-gray-900 dark:text-white"
                                                                >
                                                                    Ended:
                                                                    {{
                                                                        shift.endShift
                                                                            | date
                                                                                : "MMM d, y, h:mm a"
                                                                    }}
                                                                </span>
                                                            </div>
                                                        </div>

                                                        <!-- Shift content -->
                                                        <div class="p-4">
                                                            <div
                                                                class="grid grid-cols-1 lg:grid-cols-4 gap-4"
                                                            >
                                                                <!-- Map - Takes 2/3 of space on large screens -->
                                                                <div
                                                                    class="lg:col-span-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4"
                                                                >
                                                                    <h4
                                                                        class="text-sm font-medium text-gray-900 dark:text-white mb-2"
                                                                    >
                                                                        Route
                                                                        Map
                                                                    </h4>
                                                                    <!-- Fixed height container to ensure map renders properly -->
                                                                    <div
                                                                        class="rounded border border-gray-200 dark:border-gray-700 overflow-hidden relative"
                                                                        style="
                                                                            height: 360px;
                                                                        "
                                                                    >
                                                                        @if (
                                                                            shift.locationGPSData &&
                                                                            shift
                                                                                .locationGPSData
                                                                                .length >
                                                                                0
                                                                        ) {
                                                                            <!-- Using fixed dimensions to ensure map renders correctly -->
                                                                            <google-map
                                                                                [id]="
                                                                                    'map-' +
                                                                                    shift.$id
                                                                                "
                                                                                height="360px"
                                                                                width="100%"
                                                                                [options]="
                                                                                    getMapOptions()
                                                                                "
                                                                                [center]="
                                                                                    getMapCenter(
                                                                                        shift
                                                                                    )
                                                                                "
                                                                                [zoom]="
                                                                                    12
                                                                                "
                                                                            >
                                                                                <map-polyline
                                                                                    [path]="
                                                                                        getRouteForShift(
                                                                                            shift
                                                                                        )
                                                                                    "
                                                                                    [options]="
                                                                                        getPolylineOptions()
                                                                                    "
                                                                                ></map-polyline>
                                                                                @if (
                                                                                    getRouteForShift(
                                                                                        shift
                                                                                    )
                                                                                        .length >
                                                                                    0
                                                                                ) {
                                                                                    <map-advanced-marker
                                                                                        [position]="
                                                                                            getRouteForShift(
                                                                                                shift
                                                                                            )[0]
                                                                                        "
                                                                                        [content]="
                                                                                            getCustomMarker(
                                                                                                'start'
                                                                                            )
                                                                                        "
                                                                                    />
                                                                                }
                                                                                @if (
                                                                                    getRouteForShift(
                                                                                        shift
                                                                                    )
                                                                                        .length >
                                                                                    1
                                                                                ) {
                                                                                    <map-advanced-marker
                                                                                        [position]="
                                                                                            getRouteForShift(
                                                                                                shift
                                                                                            )[
                                                                                                getRouteForShift(
                                                                                                    shift
                                                                                                )
                                                                                                    .length -
                                                                                                    1
                                                                                            ]
                                                                                        "
                                                                                        [content]="
                                                                                            getCustomMarker(
                                                                                                'end'
                                                                                            )
                                                                                        "
                                                                                    />
                                                                                }
                                                                                <!-- The markers will be added programmatically -->
                                                                            </google-map>
                                                                        } @else {
                                                                            <div
                                                                                class="w-full h-full flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-800"
                                                                            >
                                                                                <i
                                                                                    class="icon-size-12 fa-duotone fa-map-location-dot text-gray-300 dark:text-gray-600 mb-2"
                                                                                ></i>
                                                                                <span
                                                                                    class="text-gray-500 dark:text-gray-400 text-sm"
                                                                                >
                                                                                    No
                                                                                    route
                                                                                    data
                                                                                    available
                                                                                </span>
                                                                            </div>
                                                                        }
                                                                    </div>
                                                                    <div
                                                                        class="mt-2 flex justify-between items-center"
                                                                    >
                                                                        <span
                                                                            class="text-xs text-gray-700 dark:text-gray-300"
                                                                        >
                                                                            GPS
                                                                            Points:
                                                                            {{
                                                                                shift
                                                                                    .locationGPSData
                                                                                    ?.length ||
                                                                                    0
                                                                            }}
                                                                        </span>
                                                                        <span
                                                                            class="text-xs text-gray-700 dark:text-gray-300"
                                                                        >
                                                                            Distance:
                                                                            {{
                                                                                calculateShiftDistance(
                                                                                    shift
                                                                                )
                                                                            }}
                                                                            km
                                                                        </span>
                                                                    </div>
                                                                </div>

                                                                <!-- Video & Signature - Takes 1/3 of space on large screens -->
                                                                <div
                                                                    class="lg:col-span-1 flex flex-col"
                                                                >
                                                                    <!-- Video - Now takes up 2/3 of the vertical space -->
                                                                    <div
                                                                        class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 flex-grow mb-2"
                                                                    >
                                                                        <h4
                                                                            class="text-sm font-medium text-gray-900 dark:text-white mb-2"
                                                                        >
                                                                            Drive
                                                                            Video
                                                                        </h4>
                                                                        <!-- Fixed height container to match map height proportionally -->
                                                                        <div
                                                                            class="bg-gray-100 dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 overflow-hidden flex items-center justify-center"
                                                                            style="
                                                                                height: 240px;
                                                                            "
                                                                        >
                                                                            @if (
                                                                                shift.videoFileId
                                                                            ) {
                                                                                <video
                                                                                    [src]="
                                                                                        shift.videoFileUrl
                                                                                    "
                                                                                    controls
                                                                                    class="w-full h-full object-contain"
                                                                                ></video>
                                                                            } @else {
                                                                                <span
                                                                                    class="text-gray-400 dark:text-gray-500 text-sm"
                                                                                >
                                                                                    No
                                                                                    video
                                                                                    available
                                                                                </span>
                                                                            }
                                                                        </div>
                                                                    </div>

                                                                    <!-- Signature Image - Smaller card under video -->
                                                                    <div
                                                                        class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4"
                                                                    >
                                                                        <h4
                                                                            class="text-sm font-medium text-gray-900 dark:text-white mb-2"
                                                                        >
                                                                            Driver
                                                                            Signature
                                                                        </h4>
                                                                        <div
                                                                            class="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 overflow-hidden p-2 flex items-center justify-center"
                                                                            style="
                                                                                height: 100px;
                                                                            "
                                                                        >
                                                                            @if (
                                                                                shift.signatureImageId
                                                                            ) {
                                                                                <img
                                                                                    [src]="
                                                                                        shift.signatureFileImageUrl
                                                                                    "
                                                                                    alt="Driver Signature"
                                                                                    class="max-w-full max-h-full object-contain"
                                                                                />
                                                                            } @else {
                                                                                <span
                                                                                    class="text-gray-400 dark:text-gray-500 text-sm"
                                                                                >
                                                                                    No
                                                                                    signature
                                                                                    available
                                                                                </span>
                                                                            }
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <!-- Additional Info -->
                                                            <div
                                                                class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4"
                                                            >
                                                                <!-- Shift Details -->
                                                                <div
                                                                    class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 space-y-2"
                                                                >
                                                                    <h4
                                                                        class="text-sm font-medium text-gray-900 dark:text-white"
                                                                    >
                                                                        Shift
                                                                        Details
                                                                    </h4>
                                                                    <div
                                                                        class="flex justify-between"
                                                                    >
                                                                        <span
                                                                            class="text-xs text-gray-500 dark:text-gray-400"
                                                                            >Status:</span
                                                                        >
                                                                        <span
                                                                            class="text-xs font-medium text-gray-900 dark:text-white"
                                                                        >
                                                                            {{
                                                                                formatDutyStatus(
                                                                                    shift.status
                                                                                )
                                                                            }}
                                                                        </span>
                                                                    </div>
                                                                    <div
                                                                        class="flex justify-between"
                                                                    >
                                                                        <span
                                                                            class="text-xs text-gray-500 dark:text-gray-400"
                                                                            >Mileage:</span
                                                                        >
                                                                        <span
                                                                            class="text-xs font-medium text-gray-900 dark:text-white"
                                                                        >
                                                                            {{
                                                                                shift.mileage
                                                                            }}
                                                                            km
                                                                        </span>
                                                                    </div>
                                                                    <div
                                                                        class="flex justify-between"
                                                                    >
                                                                        <span
                                                                            class="text-xs text-gray-500 dark:text-gray-400"
                                                                            >Breaks:</span
                                                                        >
                                                                        <span
                                                                            class="text-xs font-medium text-gray-900 dark:text-white"
                                                                        >
                                                                            {{
                                                                                shift.totalBreakDuration
                                                                                    | minutesToTime
                                                                            }}
                                                                        </span>
                                                                    </div>
                                                                </div>

                                                                <!-- Vehicle Info -->
                                                                <div
                                                                    class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 space-y-2"
                                                                >
                                                                    <h4
                                                                        class="text-sm font-medium text-gray-900 dark:text-white"
                                                                    >
                                                                        Vehicle
                                                                    </h4>
                                                                    <div
                                                                        class="flex items-center"
                                                                    >
                                                                        <i
                                                                            class="icon-size-4 fa-duotone fa-truck-container mr-2 text-gray-400"
                                                                        ></i>
                                                                        <span
                                                                            class="text-xs font-medium text-gray-900 dark:text-white"
                                                                        >
                                                                            {{
                                                                                getVehicleName(
                                                                                    shift.selectedVehicleID
                                                                                ) ||
                                                                                    "Unknown Vehicle"
                                                                            }}
                                                                        </span>
                                                                    </div>
                                                                </div>

                                                                <!-- Notes -->
                                                                <div
                                                                    class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4"
                                                                >
                                                                    <h4
                                                                        class="text-sm font-medium text-gray-900 dark:text-white mb-2"
                                                                    >
                                                                        Notes
                                                                    </h4>
                                                                    <p
                                                                        class="text-xs text-gray-700 dark:text-gray-300"
                                                                    >
                                                                        {{
                                                                            shift.notes ||
                                                                                "No notes for this shift."
                                                                        }}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }

                                                @if (
                                                    getShiftsForTeamMember(
                                                        member.$id
                                                    ).length === 0
                                                ) {
                                                    <div
                                                        class="text-center py-6"
                                                    >
                                                        <i
                                                            class="icon-size-12 fa-duotone fa-calendar-xmark text-gray-300 dark:text-gray-600 mb-2"
                                                        ></i>
                                                        <p
                                                            class="text-gray-500 dark:text-gray-400"
                                                        >
                                                            No shift details
                                                            available for this
                                                            period
                                                        </p>
                                                    </div>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                </div>
            }
        </div>
    </div>
} @else {
    <div
        class="p-12 rounded-2xl bg-white dark:bg-gray-800 shadow-lg flex flex-col items-center justify-center"
    >
        <mat-icon
            class="icon-size-24 text-gray-400 dark:text-gray-500"
            [svgIcon]="'heroicons_outline:user-group'"
        ></mat-icon>
        <div class="mt-6 text-center">
            @if (isLoading) {
                <div class="flex items-center justify-center gap-3">
                    <mat-spinner [diameter]="24"></mat-spinner>
                    <span class="text-gray-600 dark:text-gray-300"
                        >Loading...</span
                    >
                </div>
            } @else {
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Add a Driver/Team Member to start planning!
                </h3>
                <div class="mt-4">
                    <button
                        mat-flat-button
                        [color]="'primary'"
                        (click)="navigateToAdminTools()"
                        class="inline-flex items-center"
                    >
                        <mat-icon
                            class="icon-size-5 mr-2"
                            [svgIcon]="'heroicons_outline:user-group'"
                        ></mat-icon>
                        Add Team Member
                    </button>
                </div>
            }
        </div>
    </div>
}

import { <PERSON><PERSON><PERSON><PERSON>, NgClass } from '@angular/common';
import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { GoogleMapsModule, MapAdvancedMarker } from '@angular/google-maps';
import { MatButtonToggleChange, MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltip } from '@angular/material/tooltip';
import { FuseLoadingService } from '@fuse/services/loading';
import { DriverShiftsService } from 'app/core/databaseModels/driverShifts/driverShifts.service';
import { DriverShift } from 'app/core/databaseModels/driverShifts/driverShifts.types';
import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';
import { TeamMembersService } from 'app/core/databaseModels/teammembers/teammembers.service';
import { VehicleService } from 'app/core/databaseModels/vehicle/vehicle.service';
import { Vehicle } from 'app/core/databaseModels/vehicle/vehicle.type';
import { PipesModule } from 'app/pipes/pipes.module';
import { DistanceCalculatorService } from 'app/utils/distanceCalculator';
import { catchError, Subject, takeUntil, throwError } from 'rxjs';

@Component({
    selector: 'team-members-tab',
    imports: [
        NgClass,
        MatButtonToggleModule,
        MatFormFieldModule,
        MatDatepickerModule,
        MatNativeDateModule,
        MatDividerModule,
        MatProgressSpinnerModule,
        ReactiveFormsModule,
        PipesModule,
        DatePipe,
        MatTooltip,
        GoogleMapsModule,
        MapAdvancedMarker,

    ],
    templateUrl: './team-members-tab.component.html',
    styleUrl: './team-members-tab.component.scss'
})
export class TeamMembersTabComponent implements OnInit, OnDestroy {
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    distanceCalculator = new DistanceCalculatorService();
    teamMembers: TeamMember[] = [];
    teamMemberShifts: DriverShift[] = [];
    teamMemberTodayShifts: DriverShift[] = [];
    vehicles: Vehicle[] = [];
    isLoadingShiftSummary: boolean = false;
    isLoading: boolean = true;

    expandedRows: Set<string> = new Set<string>();
    mapId = '9852b6410bcc430e'
    private customMarkers: { [key: string]: HTMLElement } = {};

    range = new FormGroup({
        start: new FormControl<Date | null>(null),
        end: new FormControl<Date | null>(null),
    });

    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseLoadingService: FuseLoadingService,
        private _teamMembersService: TeamMembersService,
        private _driverShiftsService: DriverShiftsService,
        private _vehicleService: VehicleService,) { }

    ngOnInit(): void {

        // Subscribe to date range changes
        this.range.valueChanges.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(() => {
            if (this.range.valid) {
                const start = this.range.get('start')?.value;
                const end = this.range.get('end')?.value;
                if (start && end) {
                    this.applyCustomDateRange(start, end);
                }
            }
        });

        /// Load Team Members
        //  this._teamMembersService.listenToRealTimeData();
        this.teamMembers = [];
        this._teamMembersService.teamMembers$.pipe(
            takeUntil(this._unsubscribeAll),
            catchError(error => {
                console.error('Error:', error);
                return throwError(error);
            })
        ).subscribe((teamMembers) => {
            if (!teamMembers) {
                return;
            }
            this.teamMembers = teamMembers;
            // Assign the data to the data source for the table to render

            // Mark for check
            this._changeDetectorRef.markForCheck();

        });


        /// Load Vehicles
        this._vehicleService.getVehicles().subscribe((vehicles) => {
            this._vehicleService.vehicles$
                .pipe(
                    takeUntil(this._unsubscribeAll),
                    catchError(error => {
                        console.error('Error:', error);
                        return throwError(error);
                    })
                )
                .subscribe((vehicles) => {
                    this.vehicles = vehicles;
                    // Mark for check
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();

                })
        });

        // Load Driver Shifts
        this.loadTodayDriverShifts();
        this.loadThisWeek();
    }

    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    getTeamMemberInfo(id: string): { name: string, avatar: string | null } {
        const teamMember = this.teamMembers.find(item => item.$id === id);

        if (!teamMember) {
            return { name: '', avatar: null }; // If no team member is found
        }

        // If avatarImageId exists, use the preview URL
        const avatar = teamMember.avatarImageId
            ? this._teamMembersService.getFilePreview(teamMember.avatarImageId, 100, 100)
            : null;

        // Generate initials if avatarImageId or avatar is null
        const name = teamMember.avatar || teamMember.avatarImageId
            ? teamMember.avatar
            : this.generateInitials(teamMember.name);

        return { name: name, avatar: avatar };
    }

    getTeamMembersShiftsInfo(teamMemberId: string, today: boolean = false): { totalShifts: number, totalDuration: number | null, totalBreak: number | null, totalDistance: number | null, startShift: Date | null, endShift: Date | null, notes: string | null } {
        let teamMemberShifts: { totalShifts: number, totalDuration: number | null, totalBreak: number | null, totalDistance: number | null, startShift: Date | null, endShift: Date | null, notes: string | null } = {
            totalShifts: 0,
            totalDuration: null,
            totalBreak: null,
            totalDistance: null,
            startShift: null,
            endShift: null,
            notes: null
        };

        if (today && this.teamMemberTodayShifts.length > 0) {
            const selectedTeamMemberShift = this.teamMemberTodayShifts.filter((teamMember) => teamMember.teamMemberId === teamMemberId);
            if (selectedTeamMemberShift.length === 0) {
                return teamMemberShifts;
            }

            teamMemberShifts.totalShifts = selectedTeamMemberShift.length;
            teamMemberShifts.totalDuration = selectedTeamMemberShift.reduce((total, shift) => total + shift.shiftDuration, 0);
            teamMemberShifts.totalBreak = selectedTeamMemberShift.reduce((total, shift) => total + shift.totalBreakDuration, 0);
            teamMemberShifts.totalDistance = selectedTeamMemberShift.reduce((total, shift) => total + this.totalDistance(shift.locationGPSData), 0);
            teamMemberShifts.startShift = selectedTeamMemberShift[0]?.startShift || null;
            teamMemberShifts.endShift = selectedTeamMemberShift[selectedTeamMemberShift.length - 1]?.endShift || null;
            teamMemberShifts.notes = selectedTeamMemberShift[0]?.notes || null;

            return teamMemberShifts;
        }

        const filteredShifts = this.teamMemberShifts.filter((shift) => shift.teamMemberId === teamMemberId);
        teamMemberShifts.totalShifts = filteredShifts.length;
        teamMemberShifts.totalDuration = filteredShifts.reduce((total, shift) => total + shift.shiftDuration, 0);
        teamMemberShifts.totalBreak = filteredShifts.reduce((total, shift) => total + shift.totalBreakDuration, 0);
        teamMemberShifts.totalDistance = filteredShifts.reduce((total, shift) => total + this.totalDistance(shift.locationGPSData), 0);
        teamMemberShifts.startShift = filteredShifts[0]?.startShift || null;
        teamMemberShifts.endShift = filteredShifts[filteredShifts.length - 1]?.endShift || null;
        teamMemberShifts.notes = filteredShifts[0]?.notes || null;

        return teamMemberShifts;
    }



    loadTodayDriverShifts() {
        this.teamMemberTodayShifts = [];
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        try {
            this._driverShiftsService.getDriverShiftsByDate(today).subscribe((driverShifts) => {
                this.teamMemberTodayShifts = driverShifts;
                // console.log(this.teamMemberTodayShifts);
                // Mark for check
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
            });
        } catch (error) {
            console.error('Error:', error);
        }

    }

    loadDriverShifts(startedShiftListDate: Date, endedShiftListDate: Date) {
        this.teamMemberShifts = [];

        this._fuseLoadingService.show();
        this.isLoadingShiftSummary = true;
        try {
            this._driverShiftsService.getDriverShiftsByDate(startedShiftListDate, endedShiftListDate).subscribe((driverShifts) => {
                //console.log(driverShifts);
                this.teamMemberShifts = driverShifts;
                this._fuseLoadingService.hide();
                // console.log(this.teamMemberShifts);
                if (this.teamMemberShifts.length > 0) {

                } else {


                }
                this.isLoadingShiftSummary = false;
                // Mark for check
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
            });
        } catch (error) {
            console.error('Error:', error);
        }

    }

    // Helper function to generate initials from a name
    private generateInitials(fullName: string): string {
        const nameParts = fullName.split(' ');
        const initials = nameParts.length > 1
            ? `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}` // First letter of first and last part
            : nameParts[0][0]; // First letter if only one part of name
        return initials.toUpperCase();
    }


    // Get vehicles name by ID
    getVehicleName(id: string): string {
        if (!id) {
            return '';
        }
        const vehicle = this.vehicles.find(vehicle => vehicle.$id === id);
        if (vehicle) {
            return vehicle.vehicleName;
        }

    }

    formatDutyStatus(status: string): string {
        switch (status) {
            case 'offDuty':
                return 'Off Duty';
            case 'onDuty':
                return 'On Duty';
            case 'driving':
                return 'Driving';
            case 'breaking':
                return 'Breaking';
            default:
                return status;
        }
    }


    /// Shifts and Duty Status ///

    loadThisWeek(): void {
        const { startDate: startDateThisWeek, endDate: endDateThisWeek } = this.getWeekStartAndEndDates(0);
        this.loadDriverShifts(startDateThisWeek, endDateThisWeek);
    }

    loadLastWeek(): void {

        const { startDate: startDateThisWeek, endDate: endDateThisWeek } = this.getWeekStartAndEndDates(1);

        this.loadDriverShifts(startDateThisWeek, endDateThisWeek);
    }

    loadCustomRange(start: Date, end: Date) {
        // this.loadShiftList(start, end, 'custom-range');
        this.loadDriverShifts(start, end);
    }

    onDateRangeSelectionChange(event: MatButtonToggleChange): void {
        if (event.value === 'custom-range') {
            // Reset the date range when switching to custom range
            this.range.reset();
            this._changeDetectorRef.markForCheck();
        } else {
            // Handle other selections (this-week, last-week)
            if (event.value === 'this-week') {
                this.loadThisWeek();
            } else if (event.value === 'last-week') {
                this.loadLastWeek();
            }
        }
    }

    getWeekStartAndEndDates(offsetWeeks = 0) {
        const today = new Date();
        const dayOfWeek = today.getDay(); // 0 (Sunday) to 6 (Saturday)
        const currentDayDiff = (dayOfWeek + 6) % 7; // Calculate difference from Monday (0) to Sunday (6)

        // Calculate start date (Monday) of the current or offset week
        const startDate = new Date(today);
        startDate.setDate(today.getDate() - currentDayDiff - (7 * offsetWeeks));
        startDate.setHours(0, 0, 0, 0); // Set start of the day

        // Calculate end date (Sunday) by adding 6 days to the start date
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6);
        endDate.setHours(23, 59, 59, 999); // Set end of the day

        return { startDate, endDate };
    }

    applyCustomDateRange(start: Date, end: Date): void {
        // Set time to start of day for start date and end of day for end date
        const startDate = new Date(start);
        startDate.setHours(0, 0, 0, 0);

        const endDate = new Date(end);
        endDate.setHours(23, 59, 59, 999);

        this.loadCustomRange(startDate, endDate);
        this._changeDetectorRef.markForCheck();
    }

    totalDistance(path: string[]): number {
        const pathList = this.parseLocationData(path);
        // return Round number
        return Math.round(this.distanceCalculator.calculateTotalDistance(pathList));


    }

    parseLocationData(locationGPSData: string[]): google.maps.LatLngLiteral[] {
        try {
            // Map over the array of stringified JSON objects
            const parsedArray = locationGPSData.map((item: string) => {
                try {
                    return JSON.parse(item);
                } catch (error) {
                    console.error("Error parsing item:", item, error);
                    return null; // Return null for items that couldn't be parsed
                }
            }).filter(item => item !== null); // Filter out null values

            // Map over the parsed array to extract lat and lng
            return parsedArray.map((loc: any) => ({ lat: loc.Lat, lng: loc.Lon }));
        } catch (error) {
            console.error("Error parsing JSON:", error);
            return [];
        }
    }

    onDateDaySelectionChange(event: MatButtonToggleChange): void {
        if (event.value === 'custom-day') {
            // Reset the date range when switching to custom range
            this.range.reset();
            this._changeDetectorRef.markForCheck();
        } else {
            // Handle other selections (this-week, last-week)
            if (event.value === 'today') {
                this.loadTodayDriverShifts();
            }
        }
    }
    getInitials(name: string): string {
        // Return empty string if name is empty
        if (!name) return '';

        // Split the name by spaces and filter out empty strings
        const nameParts = name.split(' ').filter(part => part.length > 0);

        // Map through parts, take first character and convert to uppercase
        const initials = nameParts.map(part => part.charAt(0).toUpperCase()).join('');

        return initials;
    }

    /**
     * Toggle the accordion state for a team member
     */
    toggleAccordion(teamMemberId: string): void {
        if (this.expandedRows.has(teamMemberId)) {
            this.expandedRows.delete(teamMemberId);
        } else {
            this.expandedRows.add(teamMemberId);
            // Initialize maps after expanding
            // setTimeout(() => this.initializeMaps(), 1000);
        }
        this._changeDetectorRef.markForCheck();
    }

    /**
     * Check if a team member's accordion is expanded
     */
    isExpanded(teamMemberId: string): boolean {
        return this.expandedRows.has(teamMemberId);
    }

    /**
     * Get all shifts for a specific team member
     */
    getShiftsForTeamMember(teamMemberId: string): DriverShift[] {
        return this.teamMemberShifts.filter(shift => shift.teamMemberId === teamMemberId);
    }

    /**
     * Calculate the total distance for a specific shift
     */
    calculateShiftDistance(shift: DriverShift): number {
        if (!shift.locationGPSData || shift.locationGPSData.length === 0) {
            return 0;
        }

        const pathList = this.parseLocationData(shift.locationGPSData);
        return Math.round(this.distanceCalculator.calculateTotalDistance(pathList));
    }

    /**
 * Get Google Maps options
 */
    getMapOptions(): google.maps.MapOptions {
        return {
            mapTypeId: 'roadmap',
            mapId: this.mapId,
            zoomControl: false,
            scrollwheel: true,
            disableDoubleClickZoom: false,
            maxZoom: 20,
            minZoom: 4,
            streetViewControl: false
        };
    }

    /**
     * Get polyline options for route display
     */
    getPolylineOptions(): google.maps.PolylineOptions {
        return {
            strokeColor: '#4285F4',
            strokeOpacity: 1.0,
            strokeWeight: 3
        };
    }

    /**
     * Get route polyline coordinates for a shift
     */
    getRouteForShift(shift: DriverShift): google.maps.LatLngLiteral[] {
        if (!shift.locationGPSData || shift.locationGPSData.length === 0) {
            return [];
        }

        return this.parseLocationData(shift.locationGPSData);
    }

    /**
     * Get map center coordinates for a shift
     */
    getMapCenter(shift: DriverShift): google.maps.LatLngLiteral {
        if (!shift.locationGPSData || shift.locationGPSData.length === 0) {
            // Default center if no coordinates available
            return { lat: -37.8136, lng: 144.9631 }; // Melbourne CBD as fallback
        }

        const coordinates = this.parseLocationData(shift.locationGPSData);

        // Use the middle point of the route as center
        const midPoint = Math.floor(coordinates.length / 2);
        return coordinates[midPoint] || coordinates[0];
    }

    /**
     * Fit map bounds to the route coordinates
     * Call this after the map is initialized
     */
    fitMapBounds(map: google.maps.Map, coordinates: google.maps.LatLngLiteral[]): void {
        if (!map || !coordinates || coordinates.length === 0) {
            return;
        }

        const bounds = new google.maps.LatLngBounds();
        coordinates.forEach(coord => {
            bounds.extend(new google.maps.LatLng(coord.lat, coord.lng));
        });

        map.fitBounds(bounds);

        // Add a small padding
        const listener = google.maps.event.addListenerOnce(map, 'bounds_changed', () => {
            map.setZoom(Math.min(16, map.getZoom() || 12));
        });
    }

    /**
     * Initialize maps when they become visible
     * Call this from ngAfterViewInit and when expanding an accordion
     */
    initializeMaps(): void {
        setTimeout(() => {
            this.expandedRows.forEach(memberId => {
                const shifts = this.getShiftsForTeamMember(memberId);
                shifts.forEach(shift => {
                    const mapId = 'map-' + shift.$id;
                    const mapElement = document.getElementById(mapId);
                    if (mapElement) {
                        const map = new google.maps.Map(mapElement, this.getMapOptions());
                        const coordinates = this.getRouteForShift(shift);

                        if (coordinates.length > 0) {
                            // Add the polyline
                            const polyline = new google.maps.Polyline({
                                path: coordinates,
                                ...this.getPolylineOptions()
                            });
                            polyline.setMap(map);

                            // Fit bounds to show the entire route
                            this.fitMapBounds(map, coordinates);
                        }
                    }
                });
            });
        }, 300); // Small delay to ensure DOM is ready
    }

    getCustomMarker(type: 'start' | 'end'): HTMLElement {
        if (!this.customMarkers[type]) {
            const markerElement = document.createElement('div');
            // Base Tailwind classes for layout, sizing, and interactivity
            markerElement.className = `flex items-center justify-center w-8 h-8 rounded-full shadow-lg border-2 text-white font-bold text-xs transition-transform duration-200 hover:scale-105 cursor-pointer`;

            if (type === 'start') {
                // Tailwind classes for a "start" marker: green gradient, darker green border
                markerElement.classList.add("bg-gradient-to-br", "from-green-500", "to-green-300", "border-green-700");
                markerElement.innerText = "Start";
            } else {
                // Tailwind classes for an "end" marker: red gradient, darker red border
                markerElement.classList.add("bg-gradient-to-br", "from-red-500", "to-red-300", "border-red-700");
                markerElement.innerText = "End";
            }

            this.customMarkers[type] = markerElement;
        }
        return this.customMarkers[type];
    }




}

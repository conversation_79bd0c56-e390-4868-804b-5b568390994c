import { DatePipe } from '@angular/common';
import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { VehicleService } from 'app/core/databaseModels/vehicle/vehicle.service';
import { Vehicle } from 'app/core/databaseModels/vehicle/vehicle.type';
import { PipesModule } from 'app/pipes/pipes.module';
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'vehicles-tab',
    imports: [
        PipesModule,
        DatePipe,
        MatProgressSpinnerModule,
    ],
    templateUrl: './vehicles-tab.component.html',
    styleUrl: './vehicles-tab.component.scss'
})
export class VehiclesTabComponent implements OnInit, OnDestroy {
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    isLoading: boolean = false;
    vehicles: Vehicle[] = [];

    constructor(
        private _vehicleService: VehicleService
    ) {

    }
    ngOnInit(): void {
        this.LoadVehicles();
    }
    ngOnDestroy(): void {
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }


    LoadVehicles() {
        this.isLoading = true;

        // this._vehicleService.getVehicles()
        //     .pipe(takeUntil(this._unsubscribeAll))
        //     .subscribe((vehicles) => {
        //         if (vehicles) {
        //             this.vehicles = vehicles;
        //             this.isLoading = false;
        //         }
        //         this.isLoading = false;
        //     });

        this._vehicleService.vehicles$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((vehicles) => {
                if (vehicles) {
                    this.vehicles = vehicles;
                    this.isLoading = false;
                }
                this.isLoading = false;
            })
    }

    getInitials(name: string): string {
        // Return empty string if name is empty
        if (!name) return '';

        // Split the name by spaces and filter out empty strings
        const nameParts = name.split(' ').filter(part => part.length > 0);

        // Map through parts, take first character and convert to uppercase
        const initials = nameParts.map(part => part.charAt(0).toUpperCase()).join('');

        return initials;
    }
    getVehicleInfo(id: string): { name: string, avatar: string | null } {

        const vehicle = this.vehicles.find(item => item.$id === id);

        if (!vehicle) {
            return { name: '', avatar: null }; // If no vehicle is found
        }

        // If avatarImageId exists, use the preview URL
        const avatar = vehicle.avatarImageId
            ? this._vehicleService.getFilePreview(vehicle.avatarImageId, 100, 100)
            : null;

        // Generate initials if avatarImageId or avatar is null
        const name = vehicle.avatar || vehicle.avatarImageId
            ? vehicle.avatar
            : this.getInitials(vehicle.vehicleName);

        return { name: name, avatar: avatar };
    }


}

<div class="relative overflow-x-auto shadow-md sm:rounded-lg">
    @if (isLoading) {
        <div class="flex flex-auto items-center justify-center m-20 gap-3">
            <mat-spinner [diameter]="24"></mat-spinner>
            <span class="mt-4 text-2xl font-semibold tracking-tight">
                Loading vehicles...
            </span>
        </div>
    } @else {
        @if (vehicles.length == 0) {
            <div
                class="flex flex-auto flex-col items-center justify-center bg-gray-100 dark:bg-transparent"
            >
                <mat-icon
                    class="icon-size-24"
                    [svgIcon]="'mat_outline:directions_car_filled'"
                ></mat-icon>
                <div
                    class="mt-4 text-2xl font-semibold tracking-tight text-secondary"
                >
                    Add a Vehicle to start planning!
                </div>
            </div>
        } @else {
            <table
                class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400"
            >
                <thead
                    class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400"
                >
                    <tr>
                        <th scope="col" class="px-6 py-3">Name</th>
                        <th scope="col" class="px-6 py-3">Odometer</th>
                        <th scope="col" class="px-6 py-3">Reading Date</th>
                        <th scope="col" class="px-6 py-3">Hours of Engine</th>
                        <th scope="col" class="px-6 py-3">Reading Date</th>
                        <th scope="col" class="px-6 py-3">Status</th>
                    </tr>
                </thead>
                <tbody>
                    @for (vehicle of vehicles; track vehicle.$id) {
                        <tr
                            class="bg-white border-b dark:bg-gray-800 dark:border-gray-700"
                        >
                            <th
                                scope="row"
                                class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white"
                            >
                                <div class="flex items-center">
                                    @if (getVehicleInfo(vehicle.$id).avatar) {
                                        <img
                                            class="w-10 h-10 rounded-full object-cover ring-4 ring-gray-400 dark:ring-gray-700"
                                            [src]="
                                                getVehicleInfo(vehicle.$id)
                                                    .avatar
                                            "
                                            alt="Vehicle Avatar"
                                        />
                                    } @else {
                                        <div
                                            class="flex items-center justify-center w-10 h-10 rounded-full text-lg font-semibold bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200 ring-4 ring-gray-100 dark:ring-gray-700"
                                        >
                                            {{
                                                getInitials(vehicle.vehicleName)
                                            }}
                                        </div>
                                    }
                                    <div class="ml-4">
                                        <div
                                            class="text-sm font-medium text-gray-900"
                                        >
                                            {{ vehicle.vehicleName }}
                                        </div>
                                    </div>
                                </div>
                            </th>
                            <td class="px-6 py-4">
                                {{ vehicle.odometer }}
                            </td>
                            <td class="px-6 py-4">
                                {{
                                    vehicle.odometerReading
                                        | date: "dd MMM, yyyy 'at' hh:mm a"
                                }}
                            </td>
                            <td class="px-6 py-4">
                                {{ vehicle.hoursOfEngine }}
                            </td>
                            <td class="px-6 py-4">
                                {{
                                    vehicle.hoursOfEngineReading
                                        | date: "dd MMM, yyyy 'at' hh:mm a"
                                }}
                            </td>
                            <td class="px-6 py-4">
                                <span
                                    class="px-2 py-1 text-sm rounded-full bg-primary-100 text-on-primary-100"
                                    [ngClass]="{
                                        'bg-primary-100 text-on-primary-100':
                                            vehicle.status === 'active',
                                        'bg-amber-100 text-on-amber-100':
                                            vehicle.status === 'inactive'
                                    }"
                                >
                                    {{ vehicle.status ? "active" : "inactive" }}
                                </span>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        }
    }
</div>

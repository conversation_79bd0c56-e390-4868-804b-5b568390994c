import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleChange, MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatNativeDateModule, MatRippleModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { Router } from '@angular/router';

import { DashboardService } from './dashboard.service';
import { ApexOptions, NgApexchartsModule } from 'ng-apexcharts';
import { Subject, catchError, finalize, of, switchMap, takeUntil, throwError } from 'rxjs';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { User } from 'app/core/databaseModels/user/user.types';

import { TeamMembersService } from 'app/core/databaseModels/teammembers/teammembers.service';
import { SchedulerService } from '../scheduler/scheduler.service';
import { ScheduledJobsService } from 'app/core/databaseModels/scheduledJobs/scheduledJob.service';
import { ScheduledJob } from 'app/core/databaseModels/scheduledJobs/scheduledJob.types';
import { FuseLoadingService } from '@fuse/services/loading';
import { NotificationService, Notification } from 'app/services/notification.service';
import { FuseConfirmationService } from '@fuse/services/confirmation';

import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { OtpDialogComponent } from 'app/modules/widgets/otp-dialog/otp-dialog.component';
import { SuccessDialogComponent } from 'app/modules/widgets/notification/success-dialog.component';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslocoModule } from '@jsverse/transloco';
import { TeamMembersTabComponent } from './team-members-tab/team-members-tab.component';
import { VehiclesTabComponent } from './vehicles-tab/vehicles-tab.component';
import { VehicleService } from 'app/core/databaseModels/vehicle/vehicle.service';



@Component({
    selector: 'app-dashboard',
    templateUrl: './dashboard.component.html',
    styleUrls: ['./dashboard.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        TeamMembersTabComponent,
        VehiclesTabComponent,
        TranslocoModule,
        MatIconModule,
        MatButtonModule,
        MatRippleModule,
        MatMenuModule,
        MatTabsModule,
        MatButtonToggleModule,
        NgApexchartsModule,
        MatDialogModule,
        MatTableModule,
        ReactiveFormsModule,
        MatDatepickerModule,
        MatNativeDateModule,
        MatFormFieldModule,
    ]
})
export class DashboardComponent implements OnInit, OnDestroy {
    chartJobsSummary: ApexOptions = {};
    chartTaskDistribution: ApexOptions = {};
    chartBudgetDistribution: ApexOptions = {};
    chartWeeklyExpenses: ApexOptions = {};
    chartMonthlyExpenses: ApexOptions = {};
    chartYearlyExpenses: ApexOptions = {};


    scheduledJobList: ScheduledJob[] = [];
    weeklyScheduledJobList: ScheduledJob[] = [];


    jobSummary: any;
    teamMembersCount: number = 0;
    vehiclesCount: number = 0;

    loadingData: boolean = false;

    private _unsubscribeAll: Subject<any> = new Subject<any>();
    user: User;

    range = new FormGroup({
        start: new FormControl<Date | null>(null),
        end: new FormControl<Date | null>(null),
    });

    /**
     * Constructor
     */
    constructor(
        private _schedulerService: SchedulerService,
        private _dashboardService: DashboardService,
        private _userService: UserService,
        private _router: Router,
        private _teamMembersService: TeamMembersService,
        private _vehicleService: VehicleService,

        private _fuseLoadingService: FuseLoadingService,
        private _changeDetectorRef: ChangeDetectorRef,

        private _scheduledJobsService: ScheduledJobsService,
        private notificationService: NotificationService,

        private dialog: MatDialog,



    ) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */

    ngOnInit(): void {

        // Subscribe to date range changes
        this.range.valueChanges.pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(() => {
            if (this.range.valid) {
                const start = this.range.get('start')?.value;
                const end = this.range.get('end')?.value;
                if (start && end) {
                    this.applyCustomDateRange(start, end);
                }
            }
        });

        // Listen to realtime Jobs

        // this._scheduledJobsService.listenToRealTimeData(new Date());

        // Get the user
        this._userService.user$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((user: User) => {

                this.user = user;
                this.checkUserVerification(user);
            });






        //Get team members count
        this._teamMembersService.teamMembers$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((teamMembers) => {
                if (teamMembers) {
                    this.teamMembersCount = teamMembers.length;
                }
            });


        //Get vehicles count
        this._vehicleService.getVehicles()
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((vehicles) => {
                if (vehicles) {
                    this.vehiclesCount = vehicles.length;
                }
            });





        // Attach SVG fill fixer to all ApexCharts
        window['Apex'] = {
            chart: {
                events: {
                    mounted: (chart: any, options?: any): void => {
                        this._fixSvgFill(chart.el);
                    },
                    updated: (chart: any, options?: any): void => {
                        this._fixSvgFill(chart.el);
                    },
                },
            },
        };

        this.getSummary('today');





        // Load loadJobList for this week
        // Usage for the current week
        this.loadThisWeek();
        this.loadLastWeek();

        this.jobSummary = {

            overview: {
                'this-week': {
                    'total-jobs': 0,
                    'finished-jobs': 0,
                    'pending': 0,
                    'wont-finished': 0,
                },
                'last-week': {
                    'total-jobs': 0,
                    'finished-jobs': 0,
                    'pending': 0,
                    'wont-finished': 0,
                },
                'custom-range': {
                    'total-jobs': 0,
                    'finished-jobs': 0,
                    'pending': 0,
                    'wont-finished': 0,
                }
            },
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            series: {
                'this-week': [
                    {
                        name: 'Total Jobs',
                        type: 'line',
                        data: [0, 0, 0, 0, 0, 0, 0],
                    },
                    {
                        name: 'Finished Jobs',
                        type: 'column',
                        data: [0, 0, 0, 0, 0, 0, 0],
                    },
                ],
                'last-week': [
                    {
                        name: 'Total Jobs',
                        type: 'line',
                        data: [0, 0, 0, 0, 0, 0, 0],
                    },
                    {
                        name: 'Finished Jobs',
                        type: 'column',
                        data: [0, 0, 0, 0, 0, 0, 0],
                    },
                ],
                'custom-range': [
                    {
                        name: 'Total Jobs',
                        type: 'line',
                        data: [0, 0, 0, 0, 0, 0, 0],
                    },
                    {
                        name: 'Finished Jobs',
                        type: 'column',
                        data: [0, 0, 0, 0, 0, 0, 0],

                    },
                ],

            },
            taskDistribution: {
                overview: {
                    'this-week': {
                        'total': 0,
                        'finished': 0,
                    },
                    'last-week': {
                        'total': 0,
                        'finished': 0,
                    },
                    'custom-range': {
                        'total': 0,
                        'finished': 0,
                    },
                },
                labels: ['Total', 'Pending', 'Added', 'Finished'],
                series: {
                    'this-week': [15, 20, 38, 27],
                    'last-week': [19, 16, 42, 23],
                },
            }
        }

        // Prepare the chart data
        this._prepareChartData();

        // Get the data

        // this._dashboardService.data$
        //     .pipe(takeUntil(this._unsubscribeAll))
        //     .subscribe((data) => {
        //         // Store the data
        //         this.data = data;
        //         // Prepare the chart data
        //         this._prepareChartData();

        //     });

    }


    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();

    }



    loadJobList(start: Date, end: Date, status: string) {
        this.loadingData = true;
        this._fuseLoadingService.show();

        // Adjusted to use the service method directly.
        this._scheduledJobsService.getScheduledJobs(start, end)
            .pipe(
                takeUntil(this._unsubscribeAll),
                finalize(() => {
                    this.finalizeDataLoad(); // Hide loading indicator and mark for check
                })
            )
            .subscribe(scheduledJobs => {
                if (status === 'last-week' || status === 'this-week') {
                    this.weeklyScheduledJobList = scheduledJobs;
                    this.loadJobSummary(status);

                } else if (status === 'custom-range') {
                    this.weeklyScheduledJobList = scheduledJobs;
                    this.loadJobSummary(status);

                    // this.scheduledJobList = scheduledJobs.filter(job => {
                    //     return job.jobStatus != 'pending' && job.jobStatus != 'added';
                    // });
                } else {
                    // // 'daily' or any other status leads to this default case
                    this.scheduledJobList = scheduledJobs.filter(job => {
                        return job.jobStatus != 'pending' && job.jobStatus != 'added';
                    });
                }


                this.finalizeDataLoad();// Ensure UI updates
            });
    }

    private finalizeDataLoad() {
        this._fuseLoadingService.hide();
        this.loadingData = false;
        this._changeDetectorRef.markForCheck();
        this._changeDetectorRef.detectChanges();
    }

    loadLastWeek() {
        const { startDate: startDateThisWeek, endDate: endDateThisWeek } = this.getWeekStartAndEndDates(1);
        // console.log(startDateThisWeek, endDateThisWeek);
        this.loadJobList(startDateThisWeek, endDateThisWeek, 'last-week');
    }

    loadThisWeek() {
        const { startDate: startDateThisWeek, endDate: endDateThisWeek } = this.getWeekStartAndEndDates(0);

        // console.log(startDateThisWeek, endDateThisWeek);
        this.loadJobList(startDateThisWeek, endDateThisWeek, 'this-week');

    }

    loadCustomRange(start: Date, end: Date) {
        this.loadJobList(start, end, 'custom-range');
    }




    loadJobSummary(weekStatus: string) {
        //   console.log(`${weekStatus}:`, this.weeklyScheduledJobList);
        //if (weekStatus === 'last-week' || weekStatus === 'this-week') {
        this.jobSummary.overview[weekStatus]['total-jobs'] = this.weeklyScheduledJobList.length;
        this.jobSummary.overview[weekStatus]['finished-jobs'] = this.weeklyScheduledJobList.filter(item => item.jobStatus === 'finished').length;
        this.jobSummary.overview[weekStatus]['pending'] = this.weeklyScheduledJobList.filter(item => item.jobStatus === 'pending' || item.jobStatus === 'added').length;
        this.jobSummary.overview[weekStatus]['wont-finished'] = this.weeklyScheduledJobList.filter(item => item.jobStatus === 'approved').length;
        this.jobSummary.series[weekStatus][0].data = this.getTotalJobsPerDay(this.weeklyScheduledJobList).totalJobs;
        this.jobSummary.series[weekStatus][1].data = this.getTotalJobsPerDay(this.weeklyScheduledJobList).finishedJobs;

        this.jobSummary.taskDistribution.overview[weekStatus]['total'] = this.weeklyScheduledJobList.length;
        this.jobSummary.taskDistribution.overview[weekStatus]['finished'] = this.weeklyScheduledJobList.filter(item => item.jobStatus === 'finished').length;
        this.jobSummary.taskDistribution.series[weekStatus] = this.getTotalJobsPerDay(this.weeklyScheduledJobList).summaryOfWeek;
        // Prepare the chart data
        this._prepareChartData();
        // } else if (weekStatus === 'custom-range') {


        //     this._prepareChartData();
        // }
    }

    getTotalJobsPerDay(jobs: ScheduledJob[]): {
        totalJobs: number[],
        finishedJobs: number[],
        summaryOfWeek: number[]
    } {
        const totalJobsPerDay = Array(7).fill(0); // Total jobs per day
        const finishedJobsPerDay = Array(7).fill(0); // Finished jobs per day
        const summaryOfWeek = Array(4).fill(0); // Summary of the week for different statuses

        jobs.forEach(job => {
            const dueDate = new Date(job.dueDate);
            const dayOfWeek = dueDate.getDay(); // JavaScript getDay() returns 0 for Sunday through 6 for Saturday
            const index = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Adjusting index to match 1 for Monday through 7 for Sunday

            // Increment total jobs for the day
            totalJobsPerDay[index]++;
            summaryOfWeek[0]++; // Increment the total jobs count

            // Update the summaryOfWeek based on jobStatus
            switch (job.jobStatus) {
                case 'pending':
                    summaryOfWeek[1]++; // Increment pending jobs count
                    break;
                case 'added':
                    summaryOfWeek[2]++; // Increment added jobs count
                    break;
                case 'finished':
                    summaryOfWeek[3]++; // Increment finished jobs count
                    finishedJobsPerDay[index]++; // Also increment the finished jobs per day
                    break;
                // Assuming 'total' isn't a job status but a summary of all jobs processed
            }
        });

        return {
            totalJobs: totalJobsPerDay,
            finishedJobs: finishedJobsPerDay,
            summaryOfWeek: summaryOfWeek
        };
    }


    getSummary(loadStatus: string) {
        const today = new Date();

        switch (loadStatus) {
            case 'today':
                this.loadJobList(today, today, 'today');
                break;
            case 'tomorrow':
                const tomorrow = new Date(today);
                tomorrow.setDate(tomorrow.getDate() + 1);
                this.loadJobList(tomorrow, tomorrow, 'tomorrow');
                break;
            case 'yesterday':
                const yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                this.loadJobList(yesterday, yesterday, 'yesterday');
                break;
            case '2daysAgo':
                const twoDaysAgo = new Date(today);
                twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);
                this.loadJobList(twoDaysAgo, twoDaysAgo, '2daysAgo');
                break;
            case '3daysAgo':
                const threeDaysAgo = new Date(today);
                threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
                this.loadJobList(threeDaysAgo, threeDaysAgo, '3daysAgo');
                break;
            default:
                return this.jobSummary.overview['daily'];
        }

    }

    navigateToScheduler() {

        this._router.navigate(['/scheduler']).then(() => {

        });
    }

    navigateToAdminTools() {
        this._router.navigate(['/tools']);
    }



    getApprovedStatus(status: string): number {
        switch (status) {
            case 'total':
                return this.scheduledJobList.length;
            case 'overdue':
                return this.scheduledJobList.filter(item => {
                    if (item.jobStatus == 'approved') {

                        const _overdue = this.checkOverdue(item.dueDate, item.startTime);
                        if (_overdue) {
                            return item
                        }
                    }
                }).length;
            case 'active': return this.scheduledJobList.filter(item => {
                if (item.jobStatus == 'approved') {

                    const _overdue = this.checkOverdue(item.dueDate, item.startTime);
                    if (!_overdue) {
                        return item
                    }
                }
            }).length;
            case 'finished':
                return this.scheduledJobList.filter(item => item.jobStatus === status).length;
            default:
                return 0;
        }

    }

    getWeeklyApprovedStatus(status: string): number {
        switch (status) {
            case 'total':
                return this.weeklyScheduledJobList.length;
            case 'pending':
                return this.weeklyScheduledJobList.filter(item => item.jobStatus === status).length;
            case 'finished':
                return this.weeklyScheduledJobList.filter(item => item.jobStatus === status).length;
            default:
                return 0;
        }

    }


    checkOverdue(dueDateInput: string | Date, startTime: string): boolean {
        // Ensure dueDate is a Date object, whether it's initially a string or already a Date
        const dueDate = new Date(dueDateInput);

        // Extract hours and minutes from startTime
        const timeParts = startTime.match(/(\d+):(\d+) (AM|PM)/);
        if (!timeParts) {
            throw new Error('Invalid startTime format');
        }
        let hours = parseInt(timeParts[1], 10);
        const minutes = parseInt(timeParts[2], 10);
        const AMPM = timeParts[3];

        // Adjust for AM/PM
        if (AMPM === 'PM' && hours < 12) {
            hours += 12;
        } else if (AMPM === 'AM' && hours === 12) {
            hours = 0; // Convert 12 AM to 00 hours
        }

        // Set hours and minutes to dueDate
        dueDate.setHours(hours, minutes, 0, 0); // Resets seconds and milliseconds to 0

        // Create _jobDue from the updated dueDate
        const _jobDue = dueDate;

        // Get the current date and time
        const now = new Date();

        // Compare _jobDue with now to check if it is overdue
        return _jobDue < now;
    }


    getWeekStartAndEndDates(offsetWeeks = 0) {
        const today = new Date();
        const dayOfWeek = today.getDay(); // 0 (Sunday) to 6 (Saturday)
        const currentDayDiff = (dayOfWeek + 6) % 7; // Calculate difference from Monday (0) to Sunday (6)

        // Calculate start date (Monday) of the current or offset week
        const startDate = new Date(today);
        startDate.setDate(today.getDate() - currentDayDiff - (7 * offsetWeeks));
        startDate.setHours(0, 0, 0, 0); // Set start of the day

        // Calculate end date (Sunday) by adding 6 days to the start date
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6);
        endDate.setHours(23, 59, 59, 999); // Set end of the day

        return { startDate, endDate };
    }



    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Track by function for ngFor loops
     *
     * @param index
     * @param item
     */
    trackByFn(index: number, item: any): any {
        return item.id || index;
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Private methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Fix the SVG fill references. This fix must be applied to all ApexCharts
     * charts in order to fix 'black color on gradient fills on certain browsers'
     * issue caused by the '<base>' tag.
     *
     * Fix based on https://gist.github.com/Kamshak/c84cdc175209d1a30f711abd6a81d472
     *
     * @param element
     * @private
     */
    private _fixSvgFill(element: Element): void {
        // Current URL
        const currentURL = this._router.url;

        // 1. Find all elements with 'fill' attribute within the element
        // 2. Filter out the ones that doesn't have cross reference so we only left with the ones that use the 'url(#id)' syntax
        // 3. Insert the 'currentURL' at the front of the 'fill' attribute value
        Array.from(element.querySelectorAll('*[fill]'))
            .filter(el => el.getAttribute('fill').indexOf('url(') !== -1)
            .forEach((el) => {
                const attrVal = el.getAttribute('fill');
                el.setAttribute('fill', `url(${currentURL}${attrVal.slice(attrVal.indexOf('#'))}`);
            });
    }

    /**
     * Prepare the chart data from the data
     *
     * @private
     */
    private _prepareChartData(): void {
        // Github issues
        this.chartJobsSummary = {
            chart: {
                fontFamily: 'inherit',
                foreColor: 'inherit',
                height: '100%',
                type: 'line',
                toolbar: {
                    show: false,
                },
                zoom: {
                    enabled: false,
                },
            },
            colors: ['#64748B', '#94A3B8'],
            dataLabels: {
                enabled: true,
                enabledOnSeries: [0],
                background: {
                    borderWidth: 0,
                },
            },
            grid: {
                borderColor: 'var(--fuse-border)',
            },
            labels: this.jobSummary.labels,
            legend: {
                show: false,
            },
            plotOptions: {
                bar: {
                    columnWidth: '50%',
                },
            },
            series: this.jobSummary.series,
            states: {
                hover: {
                    filter: {
                        type: 'darken',
                        //  value: 0.75,
                    },
                },
            },
            stroke: {
                width: [3, 0],
            },
            tooltip: {
                followCursor: true,
                theme: 'dark',
            },
            xaxis: {
                axisBorder: {
                    show: false,
                },
                axisTicks: {
                    color: 'var(--fuse-border)',
                },
                labels: {
                    style: {
                        colors: 'var(--fuse-text-secondary)',
                    },
                },
                tooltip: {
                    enabled: false,
                },
            },
            yaxis: {
                labels: {
                    offsetX: -16,
                    style: {
                        colors: 'var(--fuse-text-secondary)',
                    },
                },
            },
        };

        // Task distribution
        this.chartTaskDistribution = {
            chart: {
                fontFamily: 'inherit',
                foreColor: 'inherit',
                height: '100%',
                type: 'polarArea',
                toolbar: {
                    show: false,
                },
                zoom: {
                    enabled: false,
                },
            },
            labels: this.jobSummary.taskDistribution.labels,
            legend: {
                position: 'bottom',
            },
            plotOptions: {
                polarArea: {
                    spokes: {
                        connectorColors: 'var(--fuse-border)',
                    },
                    rings: {
                        strokeColor: 'var(--fuse-border)',
                    },
                },
            },
            series: this.jobSummary.taskDistribution.series,
            states: {
                hover: {
                    filter: {
                        type: 'darken',
                        // value: 0.75,
                    },
                },
            },
            stroke: {
                width: 2,
            },
            theme: {
                monochrome: {
                    enabled: true,
                    color: '#93C5FD',
                    shadeIntensity: 0.75,
                    shadeTo: 'dark',
                },
            },
            tooltip: {
                followCursor: true,
                theme: 'dark',
            },
            yaxis: {
                labels: {
                    style: {
                        colors: 'var(--fuse-text-secondary)',
                    },
                },
            },
        };



    }




    checkUserVerification(user: User) {
        if (user) {
            if (!user.verifiedEmail) {
                const urlParams = new URLSearchParams(window.location.search);
                const secret = urlParams.get('secret');
                const userId = urlParams.get('userId');
                if (secret && userId) {
                    this._userService.verifyEmail(userId, secret).subscribe((result) => {
                        if (result) {
                            this._userService.updateEmailVerified(user.$id).subscribe({
                                // Handle success
                                next: () => {

                                    this.openSuccessDialog();
                                }
                            });
                        }
                    })

                } else {
                    const emailNotification: Notification = {
                        message: 'Email Verification!',
                        details: 'Your email has not been verified! Please check your inbox.',
                        duration: 9000,
                        icon: 'fa-duotone fa-solid fa-exclamation-triangle',
                        iconColor: 'text-yellow-500 bg-yellow-100 dark:text-yellow-300 dark:bg-yellow-900',
                        messageColor: 'text-yellow-700 dark:text-yellow-400',
                        action: {
                            label: 'Request verification email',
                            callback: (dismiss) => {
                                console.log('Request verification email action clicked');
                                // Perform any other actions
                                this._userService.requestEmailVerification().subscribe({

                                    next: () => {
                                        dismiss();
                                        // create a notification for the user say email was sent please check your inbox

                                        const emailNotification: Notification = {
                                            message: 'Email Verification!',
                                            details: 'Your email has not been verified! Please check your inbox.',
                                            duration: 9000,
                                            icon: 'fa-duotone fa-solid fa-exclamation-triangle',
                                            iconColor: 'text-yellow-500 bg-yellow-100 dark:text-yellow-300 dark:bg-yellow-900',
                                            messageColor: 'text-yellow-700 dark:text-yellow-400',

                                        }
                                        this.notificationService.notify(emailNotification);
                                        // console.log('Email verification requested');
                                        //
                                    }
                                });
                                dismiss(); // Dismiss the notification
                            },
                        },
                    };
                    this.notificationService.notify(emailNotification);
                }

            }

            if (!user.verifiedPhone) {
                const phoneNotification: Notification = {
                    message: 'Phone Verification!',
                    details: 'Your phone has not been verified! Please check your inbox.',
                    duration: 9000,
                    icon: 'fa-duotone fa-solid fa-exclamation-triangle',
                    iconColor: 'text-yellow-500 bg-yellow-100 dark:text-yellow-300 dark:bg-yellow-900',
                    messageColor: 'text-yellow-700 dark:text-yellow-400',
                    action: {
                        label: 'Request verification SMS',
                        callback: (dismiss) => {
                            //    console.log('Request verification phone action clicked');
                            // Open the dialog
                            this._userService.requestPhoneVerification();
                            const dialogRef = this.dialog.open(OtpDialogComponent, {});

                            dialogRef.componentInstance.otpSubmit.subscribe((otpCode: string) => {
                                //  console.log('OTP entered:', otpCode);

                                // Verify the OTP code
                                if (otpCode.length === 6) {
                                    // Perform any necessary actions with the OTP code
                                    this._userService.verifyOTPcode(otpCode, user.authId).subscribe((result) => {
                                        if (result) {
                                            // Close the dialog after processing
                                            //  update user with verified phone in db
                                            this._userService.updatePhoneVerified(user.$id).subscribe(() => {
                                                // console.log('Phone verified successfully');
                                                dialogRef.close();
                                                const phoneNotification: Notification = {
                                                    message: 'Phone Verification!',
                                                    details: 'Your phone has been successfully verified!',
                                                    duration: 5000,
                                                    icon: 'fa-duotone fa-solid fa-check',
                                                    iconColor: 'text-green-500 bg-green-100 dark:text-green-300 dark:bg-green-900',
                                                    messageColor: 'text-green-700 dark:text-green-400',
                                                };
                                                this.notificationService.notify(phoneNotification);
                                            });


                                        }
                                    });
                                }



                                dismiss();  // Dismiss the notification
                            });
                        },

                    },
                };
                this.notificationService.notify(phoneNotification);
            }


        }
    }

    checkTrialMode() {

    }

    openSuccessDialog() {
        const dialogRef = this.dialog.open(SuccessDialogComponent, {
            data: {
                message: 'Your email has been successfully verified!',
            }
        });

        dialogRef.afterClosed().subscribe(() => {
            // Optional: Handle any actions after the dialog is closed
        });
    }

    // Handle button toggle changes
    onDateRangeSelectionChange(event: MatButtonToggleChange): void {
        if (event.value === 'custom-range') {
            // Reset the date range when switching to custom range
            this.range.reset();
            this._changeDetectorRef.markForCheck();
        } else {
            // Handle other selections (this-week, last-week)
            if (event.value === 'this-week') {
                this.loadThisWeek();
            } else if (event.value === 'last-week') {
                this.loadLastWeek();
            }
        }
    }

    // Apply custom date range
    applyCustomDateRange(start: Date, end: Date): void {
        // Set time to start of day for start date and end of day for end date
        const startDate = new Date(start);
        startDate.setHours(0, 0, 0, 0);

        const endDate = new Date(end);
        endDate.setHours(23, 59, 59, 999);

        this.loadCustomRange(startDate, endDate);
        this._changeDetectorRef.markForCheck();
    }

    getInitials(name: string): string {
        // Return empty string if name is empty
        if (!name) return '';

        // Split the name by spaces and filter out empty strings
        const nameParts = name.split(' ').filter(part => part.length > 0);

        // Map through parts, take first character and convert to uppercase
        const initials = nameParts.map(part => part.charAt(0).toUpperCase()).join('');

        return initials;
    }


}

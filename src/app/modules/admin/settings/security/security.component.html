<div class="w-full max-w-3xl">
    <!-- Form -->
    <form [formGroup]="securityForm">
        <!-- Section -->
        <div class="w-full">
            <div class="text-xl">Change your password</div>
            <div class="text-secondary">
                You can only change your password twice within 24 hours!
            </div>
        </div>
        <div class="mt-8 grid w-full gap-6 sm:grid-cols-4">
            <!-- Alert -->
            @if (showAlert) {
                <fuse-alert
                    class="mt-8"
                    [appearance]="'outline'"
                    [showIcon]="false"
                    [type]="alert.type"
                    [@shake]="alert.type === 'error'"
                >
                    {{ alert.message }}
                </fuse-alert>
            }
            <!-- Current password -->
            <div class="sm:col-span-4">
                <mat-form-field class="w-full" [subscriptSizing]="'dynamic'">
                    <mat-label>Current password</mat-label>
                    <mat-icon
                        class="icon-size-5"
                        [svgIcon]="'heroicons_solid:key'"
                        matPrefix
                    ></mat-icon>
                    <input
                        [formControlName]="'currentPassword'"
                        type="password"
                        matInput
                    />
                </mat-form-field>
                @if (securityForm.get("currentPassword").hasError("required")) {
                    <mat-error>Password is required.</mat-error>
                }
            </div>
            <!-- New password -->
            <div class="sm:col-span-4">
                <mat-form-field class="w-full" [subscriptSizing]="'dynamic'">
                    <mat-label>New password</mat-label>
                    <mat-icon
                        class="icon-size-5"
                        [svgIcon]="'heroicons_solid:key'"
                        matPrefix
                    ></mat-icon>
                    <input
                        [formControlName]="'newPassword'"
                        type="password"
                        matInput
                    />
                </mat-form-field>
                <div class="text-hint mt-1 text-md">
                    Minimum 8 characters. Must include numbers, letters and
                    special characters.
                </div>
                @if (securityForm.get("newPassword").hasError("required")) {
                    <mat-error>Password is required.</mat-error>
                }
                @if (securityForm.get("newPassword").hasError("pattern")) {
                    <mat-error>
                        Password must include:
                        @if (!hasMinLength()) {
                            At least 8 characters.
                        }
                        @if (!hasUppercase()) {
                            At least one uppercase letter.
                        }
                        @if (!hasLowercase()) {
                            At least one lowercase letter.
                        }
                        @if (!hasNumber()) {
                            At least one number.
                        }
                        @if (!hasSpecialChar()) {
                            At least one special character.
                        }
                    </mat-error>
                }
            </div>
        </div>

        <!-- Divider -->
        <div class="my-10 border-t"></div>

        <!-- Section -->
        <div class="w-full">
            <div class="text-xl">Security preferences</div>
            <div class="text-secondary">
                Keep your account more secure with following preferences.
            </div>
        </div>
        <div class="mt-8 grid w-full gap-6 sm:grid-cols-4">
            <!-- 2-step auth -->
            <div class="flex items-center justify-between sm:col-span-4">
                <div
                    class="flex-auto cursor-pointer"
                    (click)="twoStepToggle.toggle()"
                >
                    <div class="font-medium leading-6">
                        Enable 2-step authentication
                    </div>
                    <div class="text-secondary text-md">
                        Protects you against password theft by requesting an
                        authentication code via SMS on every login.
                    </div>
                </div>
                <mat-slide-toggle
                    class="ml-4"
                    [color]="'primary'"
                    [formControlName]="'twoStep'"
                    #twoStepToggle
                >
                </mat-slide-toggle>
            </div>
            <!-- Ask to change password -->
            <div class="flex items-center justify-between sm:col-span-4">
                <div
                    class="flex-auto cursor-pointer"
                    (click)="askPasswordChangeToggle.toggle()"
                >
                    <div class="font-medium leading-6">
                        Ask to change password on every 6 months
                    </div>
                    <div class="text-secondary text-md">
                        A simple but an effective way to be protected against
                        data leaks and password theft.
                    </div>
                </div>
                <mat-slide-toggle
                    class="ml-4"
                    [color]="'primary'"
                    [formControlName]="'askPasswordChange'"
                    #askPasswordChangeToggle
                >
                </mat-slide-toggle>
            </div>
        </div>

        <!-- Divider -->
        <div class="my-10 border-t"></div>

        <!-- Actions -->
        <div class="flex items-center justify-end">
            <button mat-stroked-button type="button">Cancel</button>
            <button
                class="ml-4"
                mat-flat-button
                type="button"
                [color]="'primary'"
                (click)="updatePassword()"
            >
                @if (isSaving) {
                    <div class="flex justify-center p-10">
                        <mat-spinner class="spinnr" [diameter]="24">
                        </mat-spinner>
                        <div class="ml-5 text-center">Updating...</div>
                    </div>
                } @else {
                    Save
                }
            </button>
        </div>
    </form>
</div>

import {
    ChangeDetectionStrategy,
    Component,
    OnInit,
    ViewEncapsulation,
} from '@angular/core';
import {
    FormsModule,
    ReactiveFormsModule,
    UntypedFormBuilder,
    UntypedFormGroup,
    Validators,

} from '@angular/forms';
import { FuseAlertComponent, FuseAlertType } from '@fuse/components/alert';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@Component({
    selector: 'settings-security',
    templateUrl: './security.component.html',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        MatSlideToggleModule,
        MatButtonModule,
        FuseAlertComponent,
        MatProgressSpinnerModule,
    ]
})
export class SettingsSecurityComponent implements OnInit {
    securityForm: UntypedFormGroup;
    isSaving: boolean = false;
    alert: { type: FuseAlertType; message: string } = {
        type: 'success',
        message: '',
    };
    showAlert: boolean = false;

    /**
     * Constructor
     */
    constructor(
        private _formBuilder: UntypedFormBuilder,
        private _userService: UserService,
    ) { }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        // Create the form
        this.securityForm = this._formBuilder.group({
            currentPassword: ['', [Validators.required,]],
            newPassword: ['', [
                Validators.required,
                Validators.minLength(8),
                Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/), // At least one lowercase, one uppercase, one number, and one special character

            ]],
            twoStep: [false],
            askPasswordChange: [false],
        });
    }
    hasUppercase(): boolean {
        const value = this.securityForm.get('password')?.value;
        return /[A-Z]/.test(value);
    }

    hasLowercase(): boolean {
        const value = this.securityForm.get('password')?.value;
        return /[a-z]/.test(value);
    }

    hasNumber(): boolean {
        const value = this.securityForm.get('password')?.value;
        return /\d/.test(value);
    }

    hasSpecialChar(): boolean {
        const value = this.securityForm.get('password')?.value;
        return /[\W_]/.test(value);
    }

    hasMinLength(): boolean {
        const value = this.securityForm.get('password')?.value;
        return value && value.length >= 8;
    }

    updatePassword(): void {
        this.isSaving = true;
        const newPassword = this.securityForm.get('newPassword')?.value;
        const oldPassword = this.securityForm.get('currentPassword')?.value;

        this._userService.updatePassword(oldPassword, newPassword).subscribe((response) => {
            this.isSaving = false;
            if (response) {
                this.alert = {
                    type: 'success',
                    message: 'Password updated successfully!',
                };
            } else {
                this.alert = {
                    type: 'error',
                    message: 'Failed to update password. Please try again.',
                };
            }
            this.showAlert = true;
        },
            (error) => {
                this.isSaving = false;
                this.alert = {
                    type: 'error',
                    message: 'An error occurred while updating the password. Please try again.',
                };
                this.showAlert = true;
            });
    }


}

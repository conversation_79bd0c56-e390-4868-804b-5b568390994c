<div class="w-full max-w-3xl" [ngClass]="{ 'blur-sm': isLoading }">
    <!-- Form -->
    <form [formGroup]="organisationsForm">
        <!-- Section -->
        <div class="flex w-full">
            <div class="w-full">
                <div class="text-xl">Company Profile</div>
                <div class="text-secondary">
                    Following information is publicly show on the platform!
                </div>
            </div>
            <!-- Avatar -->
            <div class="flex flex-auto items-end -mt-16 ml-20">
                <div
                    class="relative flex items-center justify-center w-32 h-32 rounded-full overflow-hidden ring-4 ring-bg-card"
                >
                    <!-- Upload / Remove avatar -->
                    <div
                        class="absolute inset-0 bg-black bg-opacity-50 z-10"
                    ></div>
                    <div
                        class="absolute inset-0 flex items-center justify-center z-20"
                    >
                        <div>
                            <input
                                id="avatar-file-input"
                                class="absolute h-0 w-0 opacity-0 invisible pointer-events-none"
                                type="file"
                                [multiple]="false"
                                [accept]="'image/jpeg, image/png'"
                                (change)="uploadAvatar(avatarFileInput.files)"
                                #avatarFileInput
                            />
                            <label
                                class="flex items-center justify-center w-10 h-10 rounded-full cursor-pointer hover:bg-hover"
                                for="avatar-file-input"
                                matRipple
                            >
                                <mat-icon
                                    class="text-white"
                                    [svgIcon]="'heroicons_outline:camera'"
                                ></mat-icon>
                            </label>
                        </div>
                        <div>
                            <button mat-icon-button (click)="removeAvatar()">
                                <mat-icon
                                    class="text-white"
                                    [svgIcon]="'heroicons_outline:trash'"
                                ></mat-icon>
                            </button>
                        </div>
                    </div>
                    <!-- Image/Letter -->

                    @if (selectedOrganisations) {
                        @if (selectedOrganisations.avatar) {
                            <img
                                class="object-cover w-full h-full"
                                [src]="selectedOrganisations.avatar"
                            />
                        }
                        @if (
                            !selectedOrganisations.avatar &&
                            selectedOrganisations.organisationName
                        ) {
                            <div
                                class="flex items-center justify-center w-full h-full rounded overflow-hidden uppercase text-8xl font-bold leading-none bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                            >
                                {{
                                    selectedOrganisations.organisationName.charAt(
                                        0
                                    )
                                }}
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
        <div class="grid sm:grid-cols-4 gap-6 w-full mt-8">
            <!-- Name -->
            <div class="sm:col-span-4">
                <mat-form-field class="w-full" [subscriptSizing]="'dynamic'">
                    <mat-label>Company Name</mat-label>
                    <mat-icon
                        class="icon-size-5"
                        [svgIcon]="'heroicons_solid:building-office-2'"
                        matPrefix
                    ></mat-icon>
                    <input [formControlName]="'organisationName'" matInput />
                </mat-form-field>
            </div>
            <!-- ABN -->
            <div class="sm:col-span-4">
                <mat-form-field class="w-full" [subscriptSizing]="'dynamic'">
                    <mat-label>ABN Number:</mat-label>
                    <mat-icon
                        class="icon-size-5"
                        [svgIcon]="'heroicons_solid:check-badge'"
                        matPrefix
                    ></mat-icon>
                    <input [formControlName]="'abn'" matInput />
                </mat-form-field>
            </div>
            <!-- Payment Email -->
            <div class="sm:col-span-4">
                <mat-form-field
                    class="fuse-mat-emphasized-affix w-full"
                    [subscriptSizing]="'dynamic'"
                >
                    <mat-label
                        >Payment Email:<span class="text-hint">
                            This will be used for payment process.</span
                        ></mat-label
                    >
                    <div class="text-secondary" matPrefix>
                        <i class="fa-solid fa-envelope-open-dollar"></i>
                    </div>
                    <input [formControlName]="'paymentEmail'" matInput />
                    <div class="text-secondary" matSuffix>
                        <i
                            class="icon-size-5 fa-brands fa-stripe text-primary-500"
                        ></i>
                    </div>
                </mat-form-field>
            </div>
            <!-- Address -->
            <div class="sm:col-span-4">
                <mat-form-field
                    class="fuse-mat-emphasized-affix w-full"
                    [subscriptSizing]="'dynamic'"
                >
                    <mat-label>Address:</mat-label>
                    <div class="text-secondary" matPrefix>
                        <i class="fa-solid fa-location-dot"></i>
                    </div>
                    <input [formControlName]="'address'" matInput />
                </mat-form-field>
            </div>
            <!-- Website -->
            <div class="sm:col-span-4">
                <mat-form-field
                    class="fuse-mat-emphasized-affix w-full"
                    [subscriptSizing]="'dynamic'"
                >
                    <mat-label>Website:</mat-label>
                    <div class="text-secondary" matPrefix>
                        <i class="fa-solid fa-globe"></i>
                    </div>
                    <input [formControlName]="'website'" matInput />
                </mat-form-field>
            </div>

            <!-- Description -->
            <div class="sm:col-span-4">
                <mat-form-field class="w-full" [subscriptSizing]="'dynamic'">
                    <mat-label>About</mat-label>
                    <textarea
                        matInput
                        [formControlName]="'description'"
                        cdkTextareaAutosize
                        [cdkAutosizeMinRows]="5"
                    ></textarea>
                </mat-form-field>
                <div class="mt-1 text-md text-hint">
                    Brief description for your Company.
                </div>
            </div>
        </div>

        <!-- Divider -->
        <div class="my-10 border-t"></div>

        <!-- Section -->
        <div class="w-full">
            <div class="text-xl">Contact Information</div>
            <div class="text-secondary">
                Communication details in case we want to connect with you. These
                will be kept private.
            </div>
        </div>
        <div class="grid sm:grid-cols-4 gap-6 w-full mt-8">
            <!-- Emails -->
            <div class="sm:col-span-2">
                <div class="space-y-4">
                    <div class="space-y-4">
                        @for (
                            email of organisationsForm.get("emails")[
                                "controls"
                            ];
                            track trackByFn(i, email);
                            let i = $index;
                            let first = $first;
                            let last = $last
                        ) {
                            <div class="flex">
                                <mat-form-field
                                    class="flex-auto"
                                    [subscriptSizing]="'dynamic'"
                                >
                                    @if (first) {
                                        <mat-label>Email</mat-label>
                                    }
                                    <mat-icon
                                        matPrefix
                                        class="hidden icon-size-5 sm:flex"
                                        [svgIcon]="'heroicons_solid:envelope'"
                                    ></mat-icon>
                                    <input
                                        matInput
                                        [formControl]="email.get('email')"
                                        [placeholder]="'Email address'"
                                        [spellcheck]="false"
                                    />
                                </mat-form-field>
                                <mat-form-field
                                    class="ml-2 w-full max-w-24 flex-auto sm:ml-4 sm:max-w-40"
                                    [subscriptSizing]="'dynamic'"
                                >
                                    @if (first) {
                                        <mat-label>Label</mat-label>
                                    }
                                    <mat-icon
                                        matPrefix
                                        class="hidden icon-size-5 sm:flex"
                                        [svgIcon]="'heroicons_solid:tag'"
                                    ></mat-icon>
                                    <input
                                        matInput
                                        [formControl]="email.get('label')"
                                        [placeholder]="'Label'"
                                    />
                                </mat-form-field>
                                <!-- Remove email -->
                                @if (!(first && last)) {
                                    <div
                                        class="flex w-10 items-center pl-2"
                                        [ngClass]="{ 'mt-6': first }"
                                    >
                                        <button
                                            class="h-8 min-h-8 w-8"
                                            mat-icon-button
                                            (click)="removeEmailField(i)"
                                            matTooltip="Remove"
                                        >
                                            <mat-icon
                                                class="icon-size-5"
                                                [svgIcon]="
                                                    'heroicons_solid:trash'
                                                "
                                            ></mat-icon>
                                        </button>
                                    </div>
                                }
                            </div>
                        }
                    </div>
                    <div
                        class="group -ml-4 mt-2 inline-flex cursor-pointer items-center rounded px-4 py-2"
                        (click)="addEmailField()"
                    >
                        <mat-icon
                            class="icon-size-5"
                            [svgIcon]="'heroicons_solid:plus-circle'"
                        ></mat-icon>
                        <span
                            class="text-secondary ml-2 font-medium group-hover:underline"
                            >Add an email address</span
                        >
                    </div>
                </div>
            </div>
            <!-- Phone numbers -->
            <div class="sm:col-span-2">
                <div class="space-y-4">
                    @for (
                        phoneNumber of organisationsForm.get("phoneNumbers")[
                            "controls"
                        ];
                        track trackByFn(i, phoneNumber);
                        let i = $index;
                        let first = $first;
                        let last = $last
                    ) {
                        <div class="flex">
                            <mat-form-field
                                class="flex-auto"
                                [subscriptSizing]="'dynamic'"
                            >
                                @if (first) {
                                    <mat-label>Phone</mat-label>
                                }
                                <mat-icon
                                    matPrefix
                                    class="hidden icon-size-5 sm:flex"
                                    [svgIcon]="'heroicons_solid:phone'"
                                ></mat-icon>
                                <input
                                    matInput
                                    [formControl]="
                                        phoneNumber.get('phoneNumber')
                                    "
                                    [placeholder]="'Phone'"
                                />
                            </mat-form-field>
                            <mat-form-field
                                class="ml-2 w-full max-w-24 flex-auto sm:ml-4 sm:max-w-40"
                                [subscriptSizing]="'dynamic'"
                            >
                                @if (first) {
                                    <mat-label>Label</mat-label>
                                }
                                <mat-icon
                                    matPrefix
                                    class="hidden icon-size-5 sm:flex"
                                    [svgIcon]="'heroicons_solid:tag'"
                                ></mat-icon>
                                <input
                                    matInput
                                    [formControl]="phoneNumber.get('label')"
                                    [placeholder]="'Label'"
                                />
                            </mat-form-field>
                            <!-- Remove phone number -->
                            @if (!(first && last)) {
                                <div
                                    class="flex w-10 items-center pl-2"
                                    [ngClass]="{ 'mt-6': first }"
                                >
                                    <button
                                        class="h-8 min-h-8 w-8"
                                        mat-icon-button
                                        (click)="removePhoneNumberField(i)"
                                        matTooltip="Remove"
                                    >
                                        <mat-icon
                                            class="icon-size-5"
                                            [svgIcon]="'heroicons_solid:trash'"
                                        ></mat-icon>
                                    </button>
                                </div>
                            }
                        </div>
                    }
                </div>
                <div
                    class="group -ml-4 mt-2 inline-flex cursor-pointer items-center rounded px-4 py-2"
                    (click)="addPhoneNumberField()"
                >
                    <mat-icon
                        class="icon-size-5"
                        [svgIcon]="'heroicons_solid:plus-circle'"
                    ></mat-icon>
                    <span
                        class="text-secondary ml-2 font-medium group-hover:underline"
                        >Add a phone number</span
                    >
                </div>
            </div>

            <!-- Country -->
            <div class="sm:col-span-2">
                <mat-form-field class="w-full" [subscriptSizing]="'dynamic'">
                    <mat-label>Country</mat-label>
                    <mat-icon
                        class="icon-size-5"
                        [svgIcon]="'heroicons_solid:map-pin'"
                        matPrefix
                    ></mat-icon>
                    <mat-select [formControlName]="'country'">
                        <mat-option [value]="'australia'">Australia</mat-option>
                        <mat-option [value]="'usa'">United States</mat-option>
                        <mat-option [value]="'canada'">Canada</mat-option>
                        <mat-option [value]="'mexico'">Mexico</mat-option>
                        <mat-option [value]="'france'">France</mat-option>
                        <mat-option [value]="'germany'">Germany</mat-option>
                        <mat-option [value]="'italy'">Italy</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <!-- Timezone -->
            <div class="sm:col-span-2">
                <mat-form-field class="w-full" [subscriptSizing]="'dynamic'">
                    <mat-label>Timezone</mat-label>
                    <mat-icon
                        class="icon-size-5"
                        [svgIcon]="'heroicons_solid:globe-alt'"
                        matPrefix
                    ></mat-icon>
                    <mat-select
                        [formControlName]="'timezone'"
                        (selectionChange)="onTimeZoneChange($event)"
                    >
                        @for (timezone of timeZones; track timezone) {
                            <mat-option [value]="timezone"
                                >{{ timezone.name }} /
                                {{ timezone.offset }}</mat-option
                            >
                        }
                    </mat-select>
                </mat-form-field>
            </div>
        </div>

        <!-- Divider -->
        <div class="mt-11 mb-10 border-t"></div>

        <!-- Actions -->
        <div class="flex items-center justify-end">
            <button
                mat-stroked-button
                type="button"
                (click)="cancelUpdateDetails()"
            >
                Cancel
            </button>
            <button
                class="ml-4"
                mat-flat-button
                type="button"
                [color]="'primary'"
                (click)="updateOrganisations()"
            >
                @if (isSaving) {
                    <div class="flex justify-center p-10">
                        <mat-spinner class="spinnr" [diameter]="24">
                        </mat-spinner>
                        <div class="ml-5 mt-1 text-center">Saving...</div>
                    </div>
                } @else {
                    Save
                }
            </button>
        </div>
    </form>
</div>

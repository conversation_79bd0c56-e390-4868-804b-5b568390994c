import { TextFieldModule } from '@angular/cdk/text-field';
import { NgClass } from '@angular/common';
import { ChangeDetectorRef, Component, ElementRef, OnDestroy, OnInit, viewChild, ViewEncapsulation, } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormArray, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { FuseLoadingService } from '@fuse/services/loading';
import { OrganisationsService } from 'app/core/databaseModels/organisations/organisations.service';
import { Organisation } from 'app/core/databaseModels/organisations/organisations.types'
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'settings-company',
    templateUrl: './company.component.html',
    styleUrls: ['./company.component.scss'],
    encapsulation: ViewEncapsulation.None,
    imports: [
        NgClass,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        TextFieldModule,
        MatSelectModule,
        MatOptionModule,
        MatButtonModule,
        MatProgressSpinnerModule,
    ]
})
export class SettingsCompanyComponent implements OnInit, OnDestroy {
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    private _avatarFileInput = viewChild<ElementRef<HTMLInputElement>>('avatarFileInput');
    isLoading: boolean = false; // Initially, data is loading
    isSaving: boolean = false;

    organisationsForm: UntypedFormGroup;
    selectedOrganisations = {} as Organisation;
    selectedImage: any;

    timeZones = [
        { name: 'UTC', offset: '+00:00' },
        { name: 'America/New_York', offset: '-05:00' },
        { name: 'America/Los_Angeles', offset: '-08:00' },
        { name: 'Europe/London', offset: '+00:00' },
        { name: 'Europe/Paris', offset: '+01:00' },
        { name: 'Asia/Tokyo', offset: '+09:00' },
        { name: 'Australia/Sydney', offset: '+10:00' },
        { name: 'Australia/Melbourne', offset: '+10:00' },
        // Add more time zones as needed
    ];


    /**
     * Constructor
     */
    constructor(
        private _formBuilder: UntypedFormBuilder,
        private _organisationsService: OrganisationsService,
        private _fuseLoadingService: FuseLoadingService,
        private _changeDetectorRef: ChangeDetectorRef,
    ) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        // Create the form
        this.organisationsForm = this._formBuilder.group({
            organisationName: ['', [Validators.required]],
            abn: [''],
            paymentEmail: ['', [Validators.required, Validators.email]],
            emails: this._formBuilder.array([]), // FormArray for emails
            phoneNumbers: this._formBuilder.array([]), // FormArray for phone numbers
            description: [''],
            address: [''],
            avatar: [''],
            avatarImageId: [''],
            timezone: [''],
            country: [''],
            website: [''],
        });
        //Load Company Information

        this.loadOrganisation();
    }
    /**
    * On destroy
    */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    loadOrganisation() {
        this.isLoading = true;
        this._fuseLoadingService.show();
        this._organisationsService.get()
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe(organisations => {
                this.selectedOrganisations = organisations;

                // Find the timezone object that matches the stored timezone and offset
                const selectedTimezone = this.timeZones.find(tz =>
                    tz.name === organisations.timezone && tz.offset === organisations.timezoneOffset);

                console.log(selectedTimezone)

                // Patch the form with the correct timezone object
                this.organisationsForm.patchValue({
                    ...organisations,
                });

                // Manually set the timezone control value
                //TODO: Must update to Autocomplete
                this.organisationsForm.get('timezone').setValue(selectedTimezone);

                (this.organisationsForm.get('emails') as UntypedFormArray).clear();
                (
                    this.organisationsForm.get('phoneNumbers') as UntypedFormArray
                ).clear();
                // Clear the emails and phoneNumbers form arrays
                this.organisationsForm.patchValue(this.selectedOrganisations);
                // Setup the emails form array
                const emailFormGroups = [];

                if (this.selectedOrganisations.emails.length > 0) {
                    // Iterate through them
                    this.selectedOrganisations.emails.forEach((email) => {
                        // Create an email form group
                        if (typeof email === 'string') {
                            email = JSON.parse(email);
                        }
                        emailFormGroups.push(
                            this._formBuilder.group({
                                email: [email.email],
                                label: [email.label],
                            })
                        );
                    });
                } else {
                    // Create an email form group
                    emailFormGroups.push(
                        this._formBuilder.group({
                            email: [''],
                            label: [''],
                        })
                    );
                }

                // Add the email form groups to the emails form array
                emailFormGroups.forEach((emailFormGroup) => {
                    (this.organisationsForm.get('emails') as UntypedFormArray).push(
                        emailFormGroup
                    );
                });

                // Setup the phone numbers form array
                const phoneNumbersFormGroups = [];
                if (this.selectedOrganisations.phoneNumbers.length > 0) {
                    // Iterate through them
                    this.selectedOrganisations.phoneNumbers.forEach((phoneNumber) => {
                        // Create an phone number form group
                        if (typeof phoneNumber === 'string') {
                            phoneNumber = JSON.parse(phoneNumber);
                        }
                        phoneNumbersFormGroups.push(
                            this._formBuilder.group({
                                phoneNumber: [phoneNumber.phoneNumber],
                                label: [phoneNumber.label],
                            })
                        );
                    });
                } else {
                    // Create an phone number form group
                    phoneNumbersFormGroups.push(
                        this._formBuilder.group({
                            phoneNumber: [''],
                            label: [''],
                        })
                    );
                }

                // Add the phone number form groups to the phoneNumbers form array
                phoneNumbersFormGroups.forEach((phoneNumberFormGroup) => {
                    (this.organisationsForm.get('phoneNumbers') as UntypedFormArray).push(
                        phoneNumberFormGroup
                    );
                });


                //TODO: change email button add stripe function
                this.organisationsForm.get('paymentEmail')?.disable();
                this.isLoading = false;
                // console.log(this._organisations);
                // Create the form

                this._fuseLoadingService.hide();

                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
            });
    }

    trackByFn(index: number, item: any): any {
        return item.id || index;
    }

    uploadAvatar(fileList: FileList): void {
        // Return if canceled
        if (!fileList.length) {
            return;
        }

        const allowedTypes = ['image/jpeg', 'image/png'];
        const file = fileList[0];

        // Return if the file is not allowed
        if (!allowedTypes.includes(file.type)) {
            return;
        }

        // update the avatar url from file
        this.selectedOrganisations.avatar = URL.createObjectURL(file);

        this.selectedImage = file;
    }
    removeAvatar(): void {
        // Get the form control for 'avatar'
        const avatarFormControl = this.organisationsForm.get('avatar');

        // Set the avatar as null
        avatarFormControl.setValue(null);

        // Set the file input value as null
        this._avatarFileInput().nativeElement.value = null;

        // Update the contact
        this.selectedOrganisations.avatar = null;
        this.selectedImage = null;


    }
    cancelUpdateDetails() {
        // load data with old value
        this.removeAvatar();
        this.organisationsForm.patchValue(this.selectedOrganisations);
    }
    addPhoneNumberField(): void {
        // Create an empty phone number form group
        const phoneNumberFormGroup = this._formBuilder.group({

            phoneNumber: [''],
            label: [''],
        });

        // Add the phone number form group to the phoneNumbers form array
        (this.organisationsForm.get('phoneNumbers') as UntypedFormArray).push(phoneNumberFormGroup);

        // Mark for check
        this._changeDetectorRef.markForCheck();
    }
    removePhoneNumberField(index: number): void {
        // Get form array for phone numbers
        const phoneNumbersFormArray = this.organisationsForm.get('phoneNumbers') as UntypedFormArray;


        // Remove the phone number field
        phoneNumbersFormArray.removeAt(index);

        // Mark for check
        this._changeDetectorRef.markForCheck();
    }
    addEmailField(): void {
        // Create an empty email form group
        const emailFormGroup = this._formBuilder.group({
            email: [''],
            label: [''],
        });

        // Add the email form group to the emails form array
        (this.organisationsForm.get('emails') as UntypedFormArray).push(
            emailFormGroup
        );

        // Mark for check
        this._changeDetectorRef.markForCheck();
    }

    removeEmailField(index: number): void {
        // Get form array for emails
        const emailsFormArray = this.organisationsForm.get(
            'emails'
        ) as UntypedFormArray;

        // Remove the email field
        emailsFormArray.removeAt(index);

        // Mark for check
        this._changeDetectorRef.markForCheck();
    }

    onTimeZoneChange(event: any): void {
        //console.log(event.value);
        this.organisationsForm.get('timezone').patchValue(event.value);
    }

    updateOrganisations(): void {
        // Get the organisation object
        const
            organisation = this.organisationsForm.getRawValue();

        // Go through the organisation object and clear empty values

        organisation.emails = organisation.emails.filter(email => email.email);

        organisation.phoneNumbers =
            organisation.phoneNumbers.filter(phoneNumber => phoneNumber.phoneNumber);


        organisation.phoneNumbers =
            organisation.phoneNumbers?.map(str =>
                typeof str === 'string' ? JSON.parse(str) : str
            );

        organisation.emails =
            organisation.emails?.map(str =>
                typeof str === 'string' ? JSON.parse(str) : str
            );



        this.selectedOrganisations.organisationName = organisation.organisationName;
        this.selectedOrganisations.abn = organisation.abn;
        this.selectedOrganisations.address = organisation.address;
        this.selectedOrganisations.description = organisation.description;
        this.selectedOrganisations.emails = organisation.emails;
        this.selectedOrganisations.phoneNumbers = organisation.phoneNumbers;
        this.selectedOrganisations.timezone = organisation.timezone.name || '';
        this.selectedOrganisations.timezoneOffset = organisation.timezone.offset || '';
        this.selectedOrganisations.country = organisation.country;
        this.selectedOrganisations.paymentEmail = organisation.paymentEmail;
        this.selectedOrganisations.website = organisation.website;
        // this.selectedOrganisations.avatar = organisation.avatar;


        this.isSaving = true;
        if (this.selectedOrganisations.$id !== null && this.organisationsForm.valid) {


            this.saveOrganisations(this.selectedOrganisations);
        } else {
            return;
        }

    }

    saveOrganisations(newOrganisation: Organisation) {

        newOrganisation.emails = newOrganisation.emails.filter(email => email.email);
        newOrganisation.phoneNumbers = newOrganisation.phoneNumbers.filter(phoneNumber => phoneNumber.phoneNumber);
        console.log(newOrganisation);

        try {
            this._fuseLoadingService.show();

            if (this.selectedImage) {
                // Upload new image
                let _oldImageID = newOrganisation.avatarImageId;
                this._organisationsService.uploadFile(this.selectedImage).subscribe(
                    result => {
                        newOrganisation.avatar = result.fileUrl;
                        newOrganisation.avatarImageId = result.fileId;
                        this._organisationsService.updateOrganisation(newOrganisation.$id, newOrganisation).subscribe(result => {
                            this._fuseLoadingService.hide();
                            // Mark for check
                            this._changeDetectorRef.markForCheck();
                            this._changeDetectorRef.detectChanges();
                            if (_oldImageID) {
                                this._organisationsService.deleteFile(_oldImageID).subscribe(result => {
                                    // console.log(result);
                                    this.isSaving = false;
                                    this._changeDetectorRef.markForCheck();
                                    this._changeDetectorRef.detectChanges();

                                });
                            } else {
                                this.isSaving = false;
                                this._changeDetectorRef.markForCheck();
                                this._changeDetectorRef.detectChanges();

                            }
                        })
                    }
                );
            } else if (newOrganisation.avatar === null && newOrganisation.avatarImageId) {
                // Delete existing image
                this._organisationsService.deleteFile(newOrganisation.avatarImageId).subscribe(result => {
                    newOrganisation.avatarImageId = null; // Clear the ID after deletion
                    this._organisationsService.updateOrganisation(newOrganisation.$id, newOrganisation).subscribe(result => {
                        this._fuseLoadingService.hide();
                        this.isSaving = false;
                        this._changeDetectorRef.markForCheck();
                        this._changeDetectorRef.detectChanges();

                    });
                });
            } else {
                // No image changes, just update the organisation
                this._organisationsService.updateOrganisation(newOrganisation.$id, newOrganisation).subscribe(result => {
                    this._fuseLoadingService.hide();
                    this.isSaving = false;
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();

                });
            }
        } catch (error) {
            console.error('Error saving user:', error.message);
            this.isSaving = false;
        }
    }




}

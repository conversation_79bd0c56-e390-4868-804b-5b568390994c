import { Title<PERSON>asePipe } from '@angular/common';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnInit,
    ViewEncapsulation,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { UserService } from 'app/core/databaseModels/user/user.service';

@Component({
    selector: 'settings-user-permission',
    templateUrl: './user-permission.component.html',
    styleUrl: './user-permission.component.scss',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        MatButtonModule,
        MatSelectModule,
        MatOptionModule,
        TitleCasePipe,
        MatProgressSpinnerModule,
    ]
})
export class SettingsUserPermissionComponent {
    members: any[] = [];
    roles: any[];
    IsLoading = false;

    /**
     * Constructor
     */
    constructor(
        private _userService: UserService,
        private _changeDetectorRef: ChangeDetectorRef,
    ) { }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        // Setup the team members
        this.IsLoading = true;
        this._userService.getUsers().subscribe((users) => {
            // load members same as this example
            // {
            //     avatar: 'images/avatars/male-01.jpg',
            //     name: 'Dejesus Michael',
            //     email: '<EMAIL>',
            //     role: 'admin',
            // }
            users.forEach((user) => {
                this.members.push({
                    avatar: user.avatar,
                    name: user.name,
                    email: user.email,
                    role: 'admin' //user.role,
                })
            });
            this.IsLoading = false;

            this._changeDetectorRef.markForCheck();
            this._changeDetectorRef.detectChanges();
        })






        // Setup the roles
        this.roles = [
            {
                label: 'Read',
                value: 'read',
                description:
                    'Can read and clone the jobs. Can also open and comment on issues and chat requests.',
            },
            {
                label: 'Write',
                value: 'write',
                description:
                    'Can read, clone, and push to the jobs. Can also manage jobs and approve jobs.',
            },
            {
                label: 'Admin',
                value: 'admin',
                description:
                    'Can read, clone, and push to the jobs. Can also manage jobs, user requests, and application settings, including adding user and change user account permissions.',
            },
        ];
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Track by function for ngFor loops
     *
     * @param index
     * @param item
     */
    trackByFn(index: number, item: any): any {
        return item.id || index;
    }
}

import { TextFieldModule } from '@angular/cdk/text-field';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, OnDestroy, OnInit, viewChild, ViewEncapsulation } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { FuseLoadingService } from '@fuse/services/loading';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { User } from 'app/core/databaseModels/user/user.types';
import { Subject } from 'rxjs';

@Component({
    selector: 'settings-account',
    templateUrl: './account.component.html',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        TextFieldModule,
        MatSelectModule,
        MatOptionModule,
        MatButtonModule,
        MatProgressSpinnerModule]
})
export class SettingsAccountComponent implements OnInit, OnDestroy {
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    private _avatarFileInput = viewChild<ElementRef<HTMLInputElement>>('avatarFileInput');
    accountForm: UntypedFormGroup;
    isLoading: boolean = false;
    isSaving: boolean = false;
    emailChanged: boolean = false;
    phoneChanged: boolean = false;
    selectedUser = {} as User;
    selectedImage: any;


    /**
     * Constructor
     */
    constructor(
        private _formBuilder: UntypedFormBuilder,
        private _changeDetectorRef: ChangeDetectorRef,
        private _userService: UserService,
        private _fuseLoadingService: FuseLoadingService,

    ) {
    }


    ngOnInit(): void {
        // Create the form
        this.isLoading = true;
        this._userService.user$.subscribe((user) => {
            this.selectedUser = user;
            this.accountForm = this._formBuilder.group({
                name: [user.name, [Validators.required, Validators.minLength(2)]],
                username: [user.email, [Validators.required, Validators.email]],
                email: [user.email, [Validators.required, Validators.email]],
                phone: [user.phone, [Validators.required, Validators.pattern(/^\+[0-9]{10,15}$/)]],
                country: [user.country],
                language: [user.language],
                avatar: [user.avatar],
                avatarImageId: [user.avatarImageId],
            });
            // Add listeners for email and phone changes
            this.accountForm.get('email').valueChanges.subscribe(value => {
                if (this.selectedUser && this.selectedUser.email !== value) {
                    this.checkExistEmail(value);
                } else {
                    this.accountForm.get('email').setErrors(null);
                }
            });

            this.accountForm.get('phone').valueChanges.subscribe(value => {
                if (this.selectedUser && this.selectedUser.phone !== value) {
                    this.checkExistPhone(value);
                } else {
                    this.accountForm.get('phone').setErrors(null);
                }
            });
            this.isLoading = false;
            this._changeDetectorRef.markForCheck();
        });


    }


    ngOnDestroy(): void {
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    checkExistEmail(email: string) {
        if (!email) return;

        this._userService.checkExistingEmail(email.trim()).subscribe(result => {
            const control = this.accountForm.get('email');

            // Preserve existing errors
            let errors = control.errors || {};

            if (result) {
                errors['emailExists'] = true;
            } else {
                delete errors['emailExists'];
            }

            // Set or update errors
            control.setErrors(Object.keys(errors).length ? errors : null);
        });
    }

    checkExistPhone(phone: string) {
        if (!phone) return;

        this._userService.checkExistingPhone(`+${phone.replace(/\D/g, '')}`).subscribe(result => {
            const control = this.accountForm.get('phone');

            // Preserve existing errors
            let errors = control.errors || {};

            if (result) {
                errors['phoneExists'] = true;
            } else {
                delete errors['phoneExists'];
            }

            // Set or update errors
            control.setErrors(Object.keys(errors).length ? errors : null);
        });
    }



    uploadAvatar(fileList: FileList): void {
        // Return if canceled
        if (!fileList.length) {
            return;
        }

        const allowedTypes = ['image/jpeg', 'image/png'];
        const file = fileList[0];

        // Return if the file is not allowed
        if (!allowedTypes.includes(file.type)) {
            return;
        }

        // update the avatar url from file
        this.selectedUser.avatar = URL.createObjectURL(file);

        this.selectedImage = file;
    }
    removeAvatar(): void {
        // Get the form control for 'avatar'
        const avatarFormControl = this.accountForm.get('avatar');

        // Set the avatar as null
        avatarFormControl.setValue(null);

        // Set the file input value as null
        this._avatarFileInput().nativeElement.value = null;

        // Update the contact
        this.selectedUser.avatar = null;
        this.selectedImage = null;


    }
    cancelUpdateDetails() {
        // load data with old value
        this.removeAvatar();
        this.accountForm.patchValue(this.selectedUser);
    }

    updateDetails() {
        const oldUserData = this.selectedUser;
        const newUserData = this.selectedUser;
        // set all new values from form
        newUserData.name = this.accountForm.get('name').value;
        newUserData.email = this.accountForm.get('email').value;
        newUserData.phone = this.accountForm.get('phone').value;
        newUserData.country = this.accountForm.get('country').value;
        newUserData.language = this.accountForm.get('language').value;

        if (oldUserData.email != newUserData.email) {
            this.selectedUser.verifiedEmail = false;
            newUserData.verifiedEmail = false;
            this.emailChanged = true;
        }
        if (oldUserData.phone != newUserData.phone) {
            this.selectedUser.verifiedPhone = false;
            newUserData.verifiedPhone = false;
            this.phoneChanged = true;

        }
        this.isSaving = true;
        this.saveEdited(newUserData);

    }
    saveEdited(newUserData: User) {



        try {
            this._fuseLoadingService.show();

            if (this.selectedImage) {
                let _oldImageID = newUserData.avatarImageId;
                this._userService.uploadFile(this.selectedImage).subscribe(
                    result => {
                        newUserData.avatar = result.fileUrl;
                        newUserData.avatarImageId = result.fileId;
                        this._userService.update(newUserData, this.emailChanged, this.phoneChanged).subscribe(result => {
                            this._fuseLoadingService.hide();
                            // Mark for check
                            this._changeDetectorRef.markForCheck();
                            this._changeDetectorRef.detectChanges();
                            this._userService.deleteFile(_oldImageID).subscribe(result => {
                                // console.log(result);
                                this.isSaving = false;
                                this._changeDetectorRef.markForCheck();
                                this._changeDetectorRef.detectChanges();
                            });
                        })
                    })
            } else {
                this._userService.update(newUserData, this.emailChanged, this.phoneChanged).subscribe(result => {
                    this._fuseLoadingService.hide();
                    // console.log(result);
                    // Mark for check
                    this.isSaving = false;
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();


                })
            }
        } catch (error) {
            console.error('Error saving user:', error.message);
            this.isSaving = false;

        }

    }
}

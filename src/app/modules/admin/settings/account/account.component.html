<div class="w-full max-w-3xl">
    <!-- Form -->
    <form [formGroup]="accountForm">
        <!-- Section -->
        <div class="flex w-full">
            <div class="w-full">
                <div class="text-xl">Profile</div>
                <div class="text-secondary">
                    Following information is publicly displayed, be careful!
                </div>
            </div>

            <!-- Avatar -->
            <div class="flex flex-auto items-end -mt-16 ml-20">
                <div
                    class="relative flex items-center justify-center w-32 h-32 rounded-full overflow-hidden ring-4 ring-bg-card"
                >
                    <!-- Upload / Remove avatar -->
                    <div
                        class="absolute inset-0 bg-black bg-opacity-50 z-10"
                    ></div>
                    <div
                        class="absolute inset-0 flex items-center justify-center z-20"
                    >
                        <div>
                            <input
                                id="avatar-file-input"
                                class="absolute h-0 w-0 opacity-0 invisible pointer-events-none"
                                type="file"
                                [multiple]="false"
                                [accept]="'image/jpeg, image/png'"
                                (change)="uploadAvatar(avatarFileInput.files)"
                                #avatarFileInput
                            />
                            <label
                                class="flex items-center justify-center w-10 h-10 rounded-full cursor-pointer hover:bg-hover"
                                for="avatar-file-input"
                                matRipple
                            >
                                <mat-icon
                                    class="text-white"
                                    [svgIcon]="'heroicons_outline:camera'"
                                ></mat-icon>
                            </label>
                        </div>
                        <div>
                            <button mat-icon-button (click)="removeAvatar()">
                                <mat-icon
                                    class="text-white"
                                    [svgIcon]="'heroicons_outline:trash'"
                                ></mat-icon>
                            </button>
                        </div>
                    </div>
                    <!-- Image/Letter -->

                    @if (selectedUser) {
                        @if (selectedUser.avatar) {
                            <img
                                class="object-cover w-full h-full"
                                [src]="selectedUser.avatar"
                            />
                        }
                        @if (!selectedUser.avatar && selectedUser.name) {
                            <div
                                class="flex items-center justify-center w-full h-full rounded overflow-hidden uppercase text-8xl font-bold leading-none bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                            >
                                {{ selectedUser.name.charAt(0) }}
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
        <div class="grid sm:grid-cols-4 gap-6 w-full mt-8">
            <!-- Name -->
            <div class="sm:col-span-4">
                <mat-form-field class="w-full" [subscriptSizing]="'dynamic'">
                    <mat-label>Name</mat-label>
                    <mat-icon
                        class="icon-size-5"
                        [svgIcon]="'heroicons_solid:user'"
                        matPrefix
                    ></mat-icon>
                    <input [formControlName]="'name'" matInput />
                </mat-form-field>
                @if (accountForm.get("name").hasError("required")) {
                    <mat-error> Name is required. </mat-error>
                } @else if (accountForm.get("name").hasError("minlength")) {
                    <mat-error>
                        Name must be at least 2 characters long.
                    </mat-error>
                }
            </div>
            <!-- Username -->
            <div class="sm:col-span-4">
                <mat-form-field class="w-full" [subscriptSizing]="'dynamic'">
                    <mat-label>Username</mat-label>
                    <mat-icon
                        class="icon-size-5"
                        [svgIcon]="'heroicons_solid:envelope'"
                        matPrefix
                    ></mat-icon>
                    <input [formControlName]="'username'" readonly matInput />

                    <div class="text-secondary" matSuffix>
                        {{
                            selectedUser.verifiedEmail
                                ? "Verified"
                                : "Unverified"
                        }}
                    </div>
                </mat-form-field>
            </div>
        </div>

        <!-- Divider -->
        <div class="my-10 border-t"></div>

        <!-- Section -->
        <div class="w-full">
            <div class="text-xl">Personal Information</div>
            <div class="text-secondary">
                Communication details in case we want to connect with you. These
                will be kept private.
            </div>
        </div>
        <div class="grid sm:grid-cols-4 gap-6 w-full mt-8">
            <!-- Email -->
            <div class="sm:col-span-2">
                <mat-form-field class="w-full" [subscriptSizing]="'dynamic'">
                    <mat-label>Email</mat-label>
                    <mat-icon
                        class="icon-size-5"
                        [svgIcon]="'heroicons_solid:envelope'"
                        matPrefix
                    ></mat-icon>
                    <input [formControlName]="'email'" matInput />
                </mat-form-field>
                @if (accountForm.get("email").hasError("required")) {
                    <mat-error> Email is required.</mat-error>
                } @else if (accountForm.get("email").hasError("email")) {
                    <mat-error> Please enter a valid email address. </mat-error>
                }
                @if (accountForm.get("email").hasError("emailExists")) {
                    <mat-error> Email already exists. </mat-error>
                }
            </div>
            <!-- Phone -->
            <div class="sm:col-span-2">
                <mat-form-field class="w-full" [subscriptSizing]="'dynamic'">
                    <mat-label
                        >Phone
                        <span class="text-hint text-sm ml-2"
                            >Phone [Must start with country code ex: +61]</span
                        ></mat-label
                    >
                    <mat-icon
                        class="icon-size-5"
                        [svgIcon]="'heroicons_solid:phone'"
                        matPrefix
                    ></mat-icon>
                    <input [formControlName]="'phone'" matInput />
                </mat-form-field>
                @if (accountForm.get("phone").hasError("required")) {
                    <mat-error> Phone number is required. </mat-error>
                } @else if (accountForm.get("phone").hasError("pattern")) {
                    <mat-error> Please enter a valid phone number. </mat-error>
                }
                @if (accountForm.get("phone").hasError("phoneExists")) {
                    <mat-error> Phone number already exists. </mat-error>
                }
            </div>
            <!-- Country -->
            <div class="sm:col-span-2">
                <mat-form-field class="w-full" [subscriptSizing]="'dynamic'">
                    <mat-label>Country</mat-label>
                    <mat-icon
                        class="icon-size-5"
                        [svgIcon]="'heroicons_solid:map-pin'"
                        matPrefix
                    ></mat-icon>
                    <mat-select [formControlName]="'country'">
                        <mat-option [value]="'australia'">Australia</mat-option>
                        <mat-option [value]="'usa'">United States</mat-option>
                        <mat-option [value]="'canada'">Canada</mat-option>
                        <mat-option [value]="'united-kingdom'"
                            >United Kingdom</mat-option
                        >
                        <mat-option [value]="'mexico'">Mexico</mat-option>
                        <mat-option [value]="'france'">France</mat-option>
                        <mat-option [value]="'germany'">Germany</mat-option>
                        <mat-option [value]="'italy'">Italy</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <!-- Language -->
            <div class="sm:col-span-2">
                <mat-form-field class="w-full" [subscriptSizing]="'dynamic'">
                    <mat-label>Language</mat-label>
                    <mat-icon
                        class="icon-size-5"
                        [svgIcon]="'heroicons_solid:globe-alt'"
                        matPrefix
                    ></mat-icon>
                    <mat-select [formControlName]="'language'">
                        <mat-option [value]="'english'">English</mat-option>
                        <mat-option [value]="'french'">French</mat-option>
                        <mat-option [value]="'spanish'">Spanish</mat-option>
                        <mat-option [value]="'german'">German</mat-option>
                        <mat-option [value]="'italian'">Italian</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
        </div>

        <!-- Divider -->
        <div class="mt-11 mb-10 border-t"></div>

        <!-- Actions -->
        <div class="flex items-center justify-end">
            <button
                mat-stroked-button
                type="button"
                (click)="cancelUpdateDetails()"
            >
                Cancel
            </button>
            <button
                class="ml-4"
                mat-flat-button
                type="button"
                [color]="'primary'"
                (click)="updateDetails()"
                [disabled]="!accountForm.valid"
            >
                @if (isSaving) {
                    <div class="flex justify-center p-10">
                        <mat-spinner class="spinnr" [diameter]="24">
                        </mat-spinner>
                        <div class="ml-5 mt-1 text-center">Saving...</div>
                    </div>
                } @else {
                    Save
                }
            </button>
        </div>
    </form>
</div>

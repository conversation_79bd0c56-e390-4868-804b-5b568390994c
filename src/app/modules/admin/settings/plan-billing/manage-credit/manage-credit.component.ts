import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit, ViewEncapsulation } from '@angular/core';
import {
    FormsModule,
    ReactiveFormsModule,
    UntypedFormBuilder,
    UntypedFormGroup,
    Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { StripeService } from 'app/core/databaseModels/stripe.service';
import { FuseAlertComponent } from '@fuse/components/alert';

@Component({
    selector: 'app-manage-credit',
    imports: [
        CommonModule,
        MatButtonModule,
        MatIconModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatProgressSpinnerModule,
        MatCheckboxModule,
        FuseAlertComponent,
    ],
    templateUrl: './manage-credit.component.html',
    styleUrl: './manage-credit.component.scss'
})
export class ManageCreditComponent implements OnInit {
    manageCreditForm: UntypedFormGroup;
    currentCredit: number;
    minCredit: number;
    organisationID: string;
    stripeCustomerId: string;
    stripePriceId: string;
    selectedPlanName: string;
    price: number;
    inCurrency: string;
    selectedTotalPrice: number;
    isLoading: boolean = false;
    customerEmail: string;
    customerName: string;
    isAgreed: boolean = false;
    trialMode: boolean = false;
    teamMemberCredits: number = 0;


    constructor(
        public matDialogRef: MatDialogRef<ManageCreditComponent>,
        @Inject(MAT_DIALOG_DATA) public data: any,
        private stripeService: StripeService

    ) {
        this.currentCredit = data.teamMemberCredits;
        this.minCredit = data.currentCredit;
        this.organisationID = data.organisationID;
        this.stripeCustomerId = data.stripeCustomerId;
        this.stripePriceId = data.stripePriceId;
        this.customerEmail = data.customerEmail;
        this.customerName = data.customerName;
        this.trialMode = data.trialMode;
        this.teamMemberCredits = data.teamMemberCredits;

        this.selectedPlanName = data.selectedPlanName;
        this.price = data.price;
        this.inCurrency = data.currency;

    }

    ngOnInit(): void {
        // Create the form

        this.selectedTotalPrice = this.currentCredit * this.price;

    }

    increaseQuantity(): void {
        this.currentCredit++;
        this.selectedTotalPrice = this.currentCredit * this.price;
    }

    decreaseQuantity(): void {
        if (this.currentCredit > this.minCredit) {
            this.currentCredit--;
            this.selectedTotalPrice = this.currentCredit * (this.price);
        }
    }

    discard(): void {
        this.matDialogRef.close();
    }
    manageCredit(): void {
        console
        if (this.currentCredit < 1) {
            return
        }

        // if (this.currentCredit == this.teamMemberCredits) {
        //     return
        // }
        this.isLoading = true;



        // Existing Update Stripe Subscription
        // if (this.stripeCustomerId && !this.trialMode) {

        //     const data = {
        //         stripeCustomerId: this.stripeCustomerId,
        //         quantity: this.currentCredit,
        //     }
        //     console.log('data:', data);
        //     this.stripeService.updateSubscription(data).subscribe((result) => {
        //         this.isLoading = false;
        //         this.matDialogRef.close();
        //     }, (error) => {
        //         console.log('Error:', error);
        //         this.isLoading = false;
        //         this.matDialogRef.close();
        //     });
        // } 

        const data = {
            organisationID: this.organisationID,
            stripeCustomerId: this.stripeCustomerId,
            stripePriceId: this.stripePriceId,
            customerEmail: this.customerEmail,
            customerName: this.customerName,
            quantity: this.currentCredit,
            failureUrl: 'settings?panel=plan-billing',
            successUrl: 'settings?panel=plan-billing',
            ...((this.trialMode && this.teamMemberCredits < 1) ? { trialDays: 30 } : {})
        };

        console.log('data:', data);
        this.stripeService.createSubscription(data).subscribe((url) => {
            // Check if the URL contains 'stripe.com'
            if (url && url.includes('stripe.com')) {
                console.log('Navigating to Stripe URL:', url);
                window.location.href = url;  // Navigate to the Stripe URL
            } else {
                console.log('URL does not contain stripe.com or is invalid:', url);
                this.isLoading = false;
                this.matDialogRef.close();
            }
        });

    }

}

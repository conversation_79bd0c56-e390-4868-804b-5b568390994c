<div class="-m-6 flex max-h-screen max-w-240 flex-col md:min-w-160">
    <!-- Header -->
    <div
        class="flex h-16 flex-0 items-center justify-between bg-primary pl-6 pr-3 text-on-primary sm:pl-8 sm:pr-5"
    >
        <div class="text-lg font-medium">
            @if (trialMode && teamMemberCredits < 1) {
                <i
                    class="icon-size-5 fa-duotone fa-solid fa-gift-card mr-5"
                ></i>
                Start Free Trial
            } @else {
                Manage Subscription
            }
        </div>
        <button mat-icon-button (click)="discard()" [tabIndex]="-1">
            <mat-icon
                class="text-current"
                [svgIcon]="'heroicons_outline:x-mark'"
            ></mat-icon>
        </button>
    </div>

    <!-- Compose form -->
    <div class="flex flex-auto flex-col overflow-y-auto p-6 sm:p-8">
        <!-- currentCredit -->
        <div class="flex items-center justify-center">
            <span class="text-lg font-bold">You Selected:&nbsp;</span>
            <span class="text-lg font-bold text-primary">
                {{ selectedPlanName }}
                {{ price | currency: inCurrency : "symbol" : "1.0" }}
            </span>
        </div>
        <div class="flex flex-auto flex-col items-center text-hint">
            <span>
                The cost is calculated on a per team member, per month basis.
            </span>
            @if (trialMode && teamMemberCredits < 1) {
                <fuse-alert
                    class="mt-5"
                    [appearance]="'outline'"
                    [type]="'warn'"
                >
                    Please note that once you've selected the quantity of
                    licenses, you cannot change the number of team member
                    credits during the trial period. Carefully consider your
                    needs before making your selection.
                </fuse-alert>
            }
        </div>

        <div class="flex items-center justify-center mt-4">
            <!-- Decrease quantity -->
            <button mat-icon-button (click)="decreaseQuantity()">
                <mat-icon
                    class="icon-size-5 text-red-500"
                    [svgIcon]="'heroicons_solid:minus-circle'"
                ></mat-icon>
            </button>

            <!-- Current Credit -->
            <span class="mx-4 text-2xl font-bold text-primary">{{
                currentCredit
            }}</span>

            <!-- Increase quantity -->
            <button mat-icon-button (click)="increaseQuantity()">
                <mat-icon
                    class="icon-size-5 text-green-500"
                    [svgIcon]="'heroicons_solid:plus-circle'"
                ></mat-icon>
            </button>

            <span class="ml-4 text-2xl font-bold text-primary">
                =
                {{
                    selectedTotalPrice | currency: inCurrency : "symbol" : "1.0"
                }}</span
            >
        </div>
        <!-- Divider -->
        <div class="mt-10 border-t"></div>

        <!-- Actions -->
        <div
            class="mt-4 flex flex-col justify-between sm:mt-6 sm:flex-row sm:items-center"
        >
            <div class="-ml-2">
                <mat-checkbox [color]="'primary'" [(ngModel)]="isAgreed">
                    <span class="underline font-bold">
                        @if (trialMode && teamMemberCredits < 1) {
                            By pressing the process button, you agree to modify
                            your team member's credit for a 30-day trial period.
                            After the trial period ends, the credit will be
                            charged to your team's billing account. You can
                            cancel before the period ends if you're not
                            satisfied. Please note that this action is final,
                            and no refunds will be issued once the process is
                            complete. I agree.
                        } @else {
                            By pressing the process button, you agree to modify
                            your team member's credit. Please note that this
                            action is final, and no refunds will be issued once
                            the process is complete. I agree.
                        }
                    </span>
                </mat-checkbox>
            </div>

            <div class="mt-4 flex items-center sm:mt-0">
                <button
                    class="ml-auto sm:ml-0"
                    mat-stroked-button
                    (click)="discard()"
                >
                    Discard
                </button>
                <button
                    class="order-first sm:order-last ml-5 sm:mr-5"
                    mat-flat-button
                    [color]="'primary'"
                    (click)="manageCredit()"
                    [disabled]="!isAgreed || currentCredit == 0"
                >
                    @if (isLoading) {
                        <div class="flex justify-center">
                            <mat-spinner class="spinnr" [diameter]="24">
                            </mat-spinner>
                            <div class="ml-5 mt-1 text-center">
                                Processing...
                            </div>
                        </div>
                    } @else {
                        <div class="flex justify-center">
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_solid:credit-card'"
                            ></mat-icon>
                            <span class="ml-2 mt-1"> Process To Payment</span>
                        </div>
                    }
                </button>
            </div>
        </div>
    </div>
</div>

import { CommonModule, CurrencyPipe } from '@angular/common';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnDestroy,
    OnInit,
    ViewChild,
    ViewEncapsulation,
} from '@angular/core';
import {
    FormsModule,
    ReactiveFormsModule,
    UntypedFormBuilder,
    UntypedFormGroup,
} from '@angular/forms'
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { FuseAlertComponent } from '@fuse/components/alert';
import { FuseLoadingService } from '@fuse/services/loading';
import { OrganisationsService } from 'app/core/databaseModels/organisations/organisations.service';
import { Organisation } from 'app/core/databaseModels/organisations/organisations.types';
import { SubscriptionPlansService } from 'app/core/databaseModels/subscriptionPlans/subscriptionPlans.service';
import { SubscriptionPlans } from 'app/core/databaseModels/subscriptionPlans/subscriptionPlans.types';
import { TeamMembersService } from 'app/core/databaseModels/teammembers/teammembers.service';
import { DateTime } from 'luxon';
import { Subject, takeUntil } from 'rxjs';
import { ManageCreditComponent } from './manage-credit/manage-credit.component';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { StripeService } from 'app/core/databaseModels/stripe.service';


@Component({
    selector: 'settings-plan-billing',
    templateUrl: './plan-billing.component.html',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        FormsModule,
        ReactiveFormsModule,
        FuseAlertComponent,
        MatRadioModule,
        MatIconModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatOptionModule,
        MatButtonModule,
        CommonModule,
        CurrencyPipe,
        MatTableModule,
        MatPaginatorModule,
        MatProgressSpinnerModule,
    ]
})
export class SettingsPlanBillingComponent implements OnInit, OnDestroy {
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    @ViewChild(MatPaginator) paginator: MatPaginator;

    planBillingForm: UntypedFormGroup;
    isLoading = false;
    moveToDashboard = false;
    selectedOrganisations = {} as Organisation;
    subscriptionPlans: SubscriptionPlans[] = [];
    teamMembersUsedCount = 0;

    paymentHistory: any[] = [];
    filterValue: string = '';

    displayedColumns: string[] = ['invoiceNumber', 'hostedInvoiceUrl', 'invoicePdf', 'effectiveAt'];
    dataSource = new MatTableDataSource([]);

    planStatement
    /**
     * Constructor
     */
    constructor(
        private _formBuilder: UntypedFormBuilder,
        private _organisationsService: OrganisationsService,
        private _subscriptionPlansService: SubscriptionPlansService,
        private _fuseLoadingService: FuseLoadingService,
        private _changeDetectorRef: ChangeDetectorRef,
        private _teamMembersService: TeamMembersService,
        private _matDialog: MatDialog,
        private stripeService: StripeService


    ) { }


    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        // Create the form
        this.planBillingForm = this._formBuilder.group({
            subscriptionTypeId: [''],

        });

        const now = DateTime.now();
        this.planStatement = {
            status: 'unpaid',
            date: now.startOf('day').minus({ days: 15 }).toFormat('DDD'),
            subscriptionStartDate: now.startOf('day').minus({ days: 15 }).toFormat('DDD'),
            subscriptionEndDate: now.startOf('day').plus({ month: 1 }).toFormat('DDD'),
            limit: 0,
            spent: 0,
            remaining: 0,
        }


        this.loadSubscriptionPlans();

    }

    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }
    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Track by function for ngFor loops
     *
     * @param index
     * @param item
     */
    trackByFn(index: number, item: any): any {
        return item.$id || index;
    }


    loadSubscriptionPlans() {
        this.isLoading = true;
        this._fuseLoadingService.show();
        this._subscriptionPlansService.getSubscriptionPlans().pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((plans) => {
            this.subscriptionPlans = plans;
            this.isLoading = false;
            this._fuseLoadingService.hide();
            this._changeDetectorRef.markForCheck();
            this.loadOrganisation();
        })
    }
    loadOrganisation() {
        this.isLoading = true;
        this._fuseLoadingService.show();
        this._organisationsService.get()
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe(organisations => {
                this.selectedOrganisations = organisations;
                // set value for planBillingForm subscriptionTypeId from organisations planId
                this.dataSource.data = this.selectedOrganisations.paymentHistory
                    .map(item => JSON.parse(item))
                    .sort((a, b) => new Date(b.effectiveAt).getTime() - new Date(a.effectiveAt).getTime());

                this.planBillingForm.get('subscriptionTypeId').setValue(this.selectedOrganisations.planId);
                this.isLoading = false;
                this._fuseLoadingService.hide();

                this._changeDetectorRef.detectChanges();
                this._changeDetectorRef.markForCheck();
                //   console.log(organisations);

                this.loadTeamMembers();

            })
    }


    loadTeamMembers() {
        this.isLoading = true;
        this._fuseLoadingService.show();
        this._teamMembersService.getTeamMembers().pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((members) => {
            this.isLoading = false;
            this.teamMembersUsedCount = members.length;
            //update plan statement
            this.planStatement.limit = this.selectedOrganisations.teamMemberCredits;
            this.planStatement.spent = this.teamMembersUsedCount;
            this.planStatement.remaining = this.planStatement.limit - this.teamMembersUsedCount;
            this.planStatement.status = this.selectedOrganisations.paymentStatus;
            this.planStatement.subscriptionStartDate = this.selectedOrganisations.subscriptionStartDate;
            this.planStatement.subscriptionEndDate = this.selectedOrganisations.subscriptionEndDate;
            this._fuseLoadingService.hide();
            this._changeDetectorRef.markForCheck();

            ``
        })
    }

    openManageCreditDialog(): void {
        const selectedPlan = this.subscriptionPlans.find(item => item.$id === this.planBillingForm.get('subscriptionTypeId').value);
        const dialogRef = this._matDialog.open(ManageCreditComponent, {
            data: {
                organisationID: this.selectedOrganisations.$id,
                currentCredit: this.planStatement.spent,
                stripeCustomerId: this.selectedOrganisations.stripeCustomerId,
                stripePriceId: selectedPlan.stripePriceId,
                customerEmail: this.selectedOrganisations.paymentEmail,
                customerName: this.selectedOrganisations.organisationName,
                selectedPlanName: selectedPlan.subscriptionName,
                price: selectedPlan.price,
                currency: selectedPlan.currency,
                trialMode: this.selectedOrganisations.trialMode,
                teamMemberCredits: this.selectedOrganisations.teamMemberCredits
            }
        });

        dialogRef.afterClosed().subscribe(result => {
            // console.log('Compose dialog was closed!');
            this.loadSubscriptionPlans();
        });
    }
    openManageSubscription(): void {
        this.moveToDashboard = true;
        if (this.selectedOrganisations.stripeCustomerId) {
            this.stripeService.navigateToDashboard(this.selectedOrganisations.stripeCustomerId).subscribe((url) => {
                if (url && url.includes('stripe.com')) {
                    // console.log('Navigating to Stripe URL:', url);
                    this.moveToDashboard = false;
                    window.location.href = url;  // Navigate to the Stripe URL
                } else {
                    //   console.log('URL does not contain stripe.com or is invalid:', url);
                    this.moveToDashboard = false;

                }
            });
        }

    }
    ///// Invoices /////



    applyFilter(event: Event) {
        this.filterValue = (event.target as HTMLInputElement).value;
        this.dataSource.filter = this.filterValue.trim().toLowerCase();

        if (this.dataSource.paginator) {
            this.dataSource.paginator.firstPage();
        }
    }



}

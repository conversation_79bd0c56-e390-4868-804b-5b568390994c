<div class="w-full max-w-3xl">
    <!-- Form -->
    <form [formGroup]="planBillingForm">
        <!-- Section -->
        <div class="w-full">
            <div class="text-xl">
                Change your plan
                @if (planStatement.status == "cancelled") {
                    <span class="text-red-500 ml-5">(cancelled)</span>
                }
            </div>
            <div class="text-secondary">
                Upgrade or downgrade your current plan.
            </div>
            <blockquote>
                <p class="text-secondary font-bold">
                    Please note that this process may take some time. You can
                    check here for status updates.
                </p>
            </blockquote>
        </div>
        <!-- Previous statement -->
        @if (isLoading) {
            <div class="flex justify-center p-10">
                <mat-spinner class="spinnr" [diameter]="24"> </mat-spinner>
                <div class="ml-5 text-center">Loading...</div>
            </div>
        } @else {
            <div
                class="mt-8 bg-card relative flex flex-auto flex-col overflow-hidden rounded-2xl p-6 pb-3 pr-3 shadow"
            >
                <div class="absolute bottom-0 right-0 -m-6 h-24 w-24">
                    @if (planStatement.status == "paid") {
                        <mat-icon
                            class="text-green-500 opacity-25 icon-size-24 dark:text-green-400"
                            [svgIcon]="'heroicons_outline:check-circle'"
                        ></mat-icon>
                    } @else {
                        <mat-icon
                            class="text-red-500 opacity-25 icon-size-24 dark:text-red-400"
                            [svgIcon]="'heroicons_outline:exclamation-circle'"
                        ></mat-icon>
                    }
                </div>
                <div class="flex items-center">
                    <div class="flex flex-col">
                        <div
                            class="truncate text-lg font-medium leading-6 tracking-tight"
                        >
                            @if (!selectedOrganisations.trialMode) {
                                Plan Statement
                            }
                            @if (selectedOrganisations.trialMode) {
                                Trial Statement
                            }
                        </div>
                        <div class="text-sm font-medium text-green-600">
                            @if (selectedOrganisations.trialMode) {
                                <span>
                                    Trial Start Date:

                                    {{
                                        planStatement.subscriptionStartDate
                                            | date: "MMM dd, y"
                                    }}
                                </span>
                            } @else {
                                <span>
                                    {{
                                        planStatement.status == "paid"
                                            ? "Paid on"
                                            : "Last paid"
                                    }}
                                    {{
                                        planStatement.subscriptionStartDate
                                            | date: "MMM dd, y"
                                    }}
                                </span>
                            }
                        </div>
                    </div>
                    <div class="flex flex-col ml-10">
                        <div
                            class="truncate text-lg font-medium leading-6 tracking-tight"
                        >
                            @if (!selectedOrganisations.trialMode) {
                                Plan Validation
                            } @else {
                                Trial Validation
                            }
                        </div>
                        <div class="text-sm font-medium text-red-600">
                            @if (selectedOrganisations.trialMode) {
                                <span>
                                    Trial End Date:

                                    {{
                                        selectedOrganisations.trialEndDate
                                            | date: "MMM dd, y"
                                    }}
                                </span>
                            } @else {
                                <span>
                                    {{
                                        planStatement.status == "cancelled"
                                            ? "Cancelled on"
                                            : "Expires on"
                                    }}
                                    {{
                                        planStatement.subscriptionEndDate
                                            | date: "MMM dd, y"
                                    }}</span
                                >
                            }
                        </div>
                    </div>
                </div>
                <div class="-mx-6 mt-4 flex flex-row flex-wrap">
                    <div class="mx-6 my-3 flex flex-col">
                        <div
                            class="text-secondary text-sm font-medium leading-none"
                        >
                            Team Members Limit
                        </div>
                        <div class="mt-2 text-3xl font-medium leading-none">
                            {{ planStatement.limit }}
                        </div>
                    </div>
                    <div class="mx-6 my-3 flex flex-col">
                        <div
                            class="text-secondary text-sm font-medium leading-none"
                        >
                            Credit Spent
                        </div>
                        <div
                            class="mt-2 text-3xl font-medium leading-none text-red-500"
                        >
                            {{ planStatement.spent }}
                        </div>
                    </div>
                    <div class="mx-6 my-3 flex flex-col">
                        <div
                            class="text-secondary text-sm font-medium leading-none"
                        >
                            Remaining Credit
                        </div>
                        <div
                            class="mt-2 text-3xl font-medium leading-none text-green-500"
                        >
                            {{ planStatement.remaining }}
                        </div>
                    </div>
                </div>
                <div class="mt-auto flex items-center justify-center">
                    @if (
                        selectedOrganisations.stripeCustomerId &&
                        selectedOrganisations.teamMemberCredits > 0
                    ) {
                        <button
                            class="ml-5 mt-2"
                            mat-flat-button
                            (click)="openManageSubscription()"
                            [color]="'accent'"
                        >
                            @if (moveToDashboard) {
                                <div class="flex justify-center">
                                    <mat-spinner class="spinnr" [diameter]="24">
                                    </mat-spinner>
                                    <div class="ml-5 mt-1 text-center">
                                        Processing...
                                    </div>
                                </div>
                            } @else {
                                <div class="flex justify-center">
                                    <mat-icon
                                        class="icon-size-5"
                                        [svgIcon]="
                                            'heroicons_solid:adjustments-vertical'
                                        "
                                    ></mat-icon>
                                    <span class="ml-2 mt-1"
                                        >Subscription Dashboard
                                    </span>
                                </div>
                            }
                        </button>
                    } @else {
                        <button
                            class="mt-2"
                            mat-flat-button
                            (click)="openManageCreditDialog()"
                            [color]="'primary'"
                            [disabled]="
                                selectedOrganisations.paymentEmail == null
                            "
                        >
                            <mat-icon
                                class="icon-size-5"
                                svgIcon="heroicons_outline:user-group"
                            ></mat-icon>
                            <span class="ml-2">
                                @if (
                                    selectedOrganisations.stripeCustomerId &&
                                    selectedOrganisations.teamMemberCredits > 0
                                ) {
                                    Manage Credit
                                } @else {
                                    Start free trial
                                }
                            </span>
                        </button>
                    }
                </div>
            </div>
        }
        <div class="mt-8 grid w-full gap-6 sm:grid-cols-3">
            <!-- Plan -->
            <div class="sm:col-span-3">
                <fuse-alert [appearance]="'outline'" [type]="'info'">
                    Changing the plan will take effect immediately. You will be
                    charged for the rest of the current month. If you manage
                    your team members and add new members during the trial
                    period, your subscription will start from that date.
                </fuse-alert>
            </div>

            <mat-radio-group
                class="pointer-events-none invisible absolute h-0 w-0"
                [formControlName]="'subscriptionTypeId'"
                #planRadioGroup="matRadioGroup"
            >
                @for (
                    plan of subscriptionPlans;
                    track trackByFn($index, plan)
                ) {
                    <mat-radio-button
                        [value]="plan.$id"
                        [disabled]="plan.disabled"
                    ></mat-radio-button>
                }
            </mat-radio-group>
            @for (plan of subscriptionPlans; track trackByFn($index, plan)) {
                <div
                    class="bg-card relative flex flex-col items-start justify-start rounded-md p-6 shadow"
                    [ngClass]="{
                        'cursor-pointer': !plan.disabled,
                        'ring ring-inset ring-primary':
                            planRadioGroup.value === plan.$id
                    }"
                    (click)="
                        !plan.disabled && (planRadioGroup.value = plan.$id)
                    "
                >
                    @if (planRadioGroup.value === plan.$id) {
                        <mat-icon
                            class="absolute right-0 top-0 mr-3 mt-3 text-primary icon-size-7"
                            [svgIcon]="'heroicons_solid:check-circle'"
                        ></mat-icon>
                    }
                    <div class="font-semibold">{{ plan.subscriptionName }}</div>
                    <div class="text-secondary mt-1 whitespace-normal">
                        {{ plan.details }}
                    </div>
                    <div class="flex-auto"></div>
                    <div class="mt-8 text-lg">
                        <span
                            >{{
                                plan.price
                                    | currency
                                        : plan.currency
                                        : "symbol"
                                        : "1.0"
                            }}&nbsp;</span
                        >
                        <span class="text-secondary">
                            / per team member per month
                        </span>
                    </div>
                </div>
            } @empty {
                @if (isLoading) {
                    <div class="flex justify-center p-10">
                        <mat-spinner class="spinnr" [diameter]="24">
                        </mat-spinner>
                        <div class="ml-5 text-center">Loading...</div>
                    </div>
                } @else {
                    <div class="flex justify-center p-10">
                        <span class="text-center">there are no plans</span>
                    </div>
                }
            }
        </div>

        <!-- Divider -->
        <div class="mb-10 mt-12 border-t"></div>

        <!-- Section -->
        <div class="w-full">
            <div class="text-xl">Payment History</div>
            <div class="text-secondary">
                Update your billing information. Make sure to set your location
                correctly as it could affect your tax rates.
            </div>

            <div class="w-full h-full mt-8">
                <div
                    class="flex flex-col flex-auto bg-white shadow rounded-2xl overflow-hidden"
                >
                    <div
                        class="flex flex-col sm:flex-row items-center justify-between py-8 px-6 border-b"
                    >
                        <div class="text-lg font-extrabold tracking-tight">
                            Invoices History
                            <div class="text-secondary font-medium">
                                Total Invoices:
                                <span
                                    class="text-primary font-bold ml-2 text-base font-medium"
                                >
                                    {{ dataSource.data.length }}</span
                                >
                            </div>
                        </div>
                        <div class="flex items-center mt-6 sm:mt-0 sm:ml-4">
                            <mat-form-field class="min-w-64">
                                <mat-icon matPrefix>search</mat-icon>
                                <input
                                    matInput
                                    (keyup)="applyFilter($event)"
                                    placeholder="Search ..."
                                />
                            </mat-form-field>
                        </div>
                    </div>

                    <div class="overflow-x-auto mx-6">
                        <table
                            mat-table
                            [dataSource]="dataSource"
                            matSort
                            class="w-full"
                        >
                            <!-- Invoice Number Column -->
                            <ng-container matColumnDef="invoiceNumber">
                                <th
                                    mat-header-cell
                                    *matHeaderCellDef
                                    mat-sort-header
                                >
                                    Invoice Number
                                </th>
                                <td mat-cell *matCellDef="let row">
                                    {{ row.invoiceNumber }}
                                </td>
                            </ng-container>

                            <!-- Hosted Invoice URL Column -->
                            <ng-container matColumnDef="hostedInvoiceUrl">
                                <th
                                    mat-header-cell
                                    *matHeaderCellDef
                                    mat-sort-header
                                >
                                    View Invoice URL
                                </th>
                                <td mat-cell *matCellDef="let row">
                                    <a
                                        [href]="row.hostedInvoiceUrl"
                                        target="_blank"
                                        class="text-blue-500"
                                    >
                                        <i
                                            class="fa-duotone fa-solid fa-arrow-up-right-from-square"
                                        ></i>
                                        <span class="ml-2">View Invoice</span>
                                    </a>
                                </td>
                            </ng-container>

                            <!-- Invoice PDF Column -->
                            <ng-container matColumnDef="invoicePdf">
                                <th
                                    mat-header-cell
                                    *matHeaderCellDef
                                    mat-sort-header
                                >
                                    Download Invoice
                                </th>
                                <td mat-cell *matCellDef="let row">
                                    <a
                                        [href]="row.invoicePdf"
                                        target="_blank"
                                        class="text-blue-500"
                                    >
                                        <i
                                            class="fa-duotone fa-solid fa-download"
                                        ></i>
                                        <span class="ml-2"> Download PDF</span>
                                    </a>
                                </td>
                            </ng-container>

                            <!-- Effective Date Column -->
                            <ng-container matColumnDef="effectiveAt">
                                <th
                                    mat-header-cell
                                    *matHeaderCellDef
                                    mat-sort-header
                                >
                                    Effective Date
                                </th>
                                <td mat-cell *matCellDef="let row">
                                    {{ row.effectiveAt | date: "short" }}
                                </td>
                            </ng-container>

                            <tr
                                mat-header-row
                                *matHeaderRowDef="displayedColumns"
                            ></tr>
                            <tr
                                mat-row
                                *matRowDef="let row; columns: displayedColumns"
                            ></tr>
                            <tr class="mat-row" *matNoDataRow>
                                <td class="mat-cell" colspan="4">
                                    @if (isLoading) {
                                        <div class="flex justify-center p-10">
                                            <mat-spinner
                                                class="spinnr"
                                                [diameter]="24"
                                            >
                                            </mat-spinner>
                                            <div class="ml-5 text-center">
                                                Loading...
                                            </div>
                                        </div>
                                    } @else {
                                        No data matching the filter "{{
                                            filterValue
                                        }}"
                                    }
                                </td>
                            </tr>
                        </table>
                        <mat-paginator
                            [pageSize]="20"
                            [pageSizeOptions]="[5, 10, 20, 50, 100]"
                            aria-label="Select page of invoice"
                        ></mat-paginator>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot, Routes } from '@angular/router';
import { ChatService } from 'app/core/databaseModels/chat/chat.service';
import { chats } from 'app/mock-api/apps/chat/data';
import { ChatComponent } from 'app/modules/admin/chat/chat.component';
// import { ChatService } from 'app/modules/admin/chat/chat.service';
import { ChatsComponent } from 'app/modules/admin/chat/chats/chats.component';
import { ConversationComponent } from 'app/modules/admin/chat/conversation/conversation.component';
import { EmptyConversationComponent } from 'app/modules/admin/chat/empty-conversation/empty-conversation.component';
import { catchError, throwError } from 'rxjs';

/**
 * Conversation resolver
 *
 * @param route
 * @param state
 */


export default [
    {
        path: '',
        component: ChatComponent,
        children: [
            {
                path: '',
                component: ChatsComponent,
                children: [
                    {
                        path: '',
                        pathMatch: 'full',
                        component: EmptyConversationComponent,
                    },
                    {
                        path: ':id',
                        component: ConversationComponent,

                    },
                ],
            },
        ],
    },
] as Routes;

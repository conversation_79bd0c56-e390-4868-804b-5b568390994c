import { ChangeDetectionStrategy, Component, Input, OnChanges, OnInit, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDrawer } from '@angular/material/sidenav';
import { Chat, ChatMessages } from 'app/core/databaseModels/chat/chat.types';

@Component({
    selector: 'chat-contact-info',
    templateUrl: './contact-info.component.html',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [MatButtonModule, MatIconModule]
})
export class ContactInfoComponent implements OnInit, OnChanges {

    @Input() chat: Chat;
    @Input() drawer: MatDrawer;
    @Input() messages: ChatMessages[]

    /**
     * Constructor
     */
    constructor() {

    }
    ngOnChanges(changes: SimpleChanges): void {
        if (changes.messages) {
            // Perform operations needed when messages update
            //  console.log('Messages have been updated', this.messages);
        }
    }

    ngOnInit(): void {

    }


    fileDetails(file: string): void {

        return JSON.parse(file);
    }
}

@if (chat.contact) {
    <div class="flex flex-col flex-auto h-full bg-card dark:bg-default">
        <!-- Header -->
        <div
            class="flex flex-0 items-center h-18 px-4 border-b bg-gray-50 dark:bg-transparent"
        >
            <button mat-icon-button (click)="drawer.close()">
                <mat-icon [svgIcon]="'heroicons_outline:x-mark'"></mat-icon>
            </button>
            <div class="ml-2 text-lg font-medium">Contact info</div>
        </div>

        <div class="overflow-y-auto">
            <!-- Contact avatar & info -->
            <div class="flex flex-col items-center mt-8">
                <div class="w-40 h-40 rounded-full">
                    @if (chat.contact.avatar) {
                        <img
                            class="w-full h-full rounded-full object-cover"
                            [src]="chat.contact.avatar"
                            [alt]="'Contact avatar'"
                        />
                    } @else {
                        <div
                            class="flex items-center justify-center w-full h-full rounded-full text-8xl font-semibold uppercase bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                        >
                            {{ chat.contact.name.charAt(0) }}
                        </div>
                    }
                </div>
                <div class="mt-4 text-lg font-medium">
                    {{ chat.contact.name }}
                </div>
                <div class="mt-0.5 text-md text-secondary">
                    {{ chat.contact.notes }}
                </div>
            </div>
            @if (messages) {
                <div class="py-10 px-7">
                    <!-- Media -->
                    <div class="text-lg font-medium">Media</div>
                    <div class="grid grid-cols-4 gap-1 mt-4">
                        @for (file of messages; track file) {
                            @switch (
                                fileDetails(file).fileName.split(".").pop()
                            ) {
                                @case ("pdf") {
                                    <a
                                        [href]="fileDetails(file).fileUrl"
                                        target="_blank"
                                    >
                                        <div
                                            class="flex h-20 w-20 items-center justify-center bg-gray-300 dark:bg-gray-700 text-sm font-semibold text-primary rounded object-cover"
                                        >
                                            PDF
                                        </div></a
                                    >
                                }
                                @case ("doc") {
                                    <a
                                        [href]="fileDetails(file).fileUrl"
                                        target="_blank"
                                    >
                                        <div
                                            class="flex h-20 w-20 items-center justify-center bg-gray-300 dark:bg-gray-700 text-sm font-semibold text-primary rounded object-cover"
                                        >
                                            DOC
                                        </div></a
                                    >
                                }
                                @default {
                                    <a
                                        [href]="fileDetails(file).fileUrl"
                                        target="_blank"
                                    >
                                        <ng-container>
                                            <img
                                                class="h-20 rounded object-cover"
                                                [src]="
                                                    fileDetails(file).fileUrl
                                                "
                                        /></ng-container>
                                    </a>
                                }
                            }
                        }
                    </div>
                    <!-- Details -->
                    <div class="mt-10 space-y-4">
                        <div class="text-lg font-medium mb-3">Details</div>

                        @if (chat.contact.emails.length) {
                            <div>
                                <div class="font-medium text-secondary">
                                    Email
                                </div>
                                <div class="">
                                    {{ chat.contact.emails[0].email }}
                                </div>
                            </div>
                        }

                        @if (chat.contact.phoneNumbers.length) {
                            <div>
                                <div class="font-medium text-secondary">
                                    Phone number
                                </div>
                                <div class="">
                                    {{
                                        chat.contact.phoneNumbers[0].phoneNumber
                                    }}
                                </div>
                            </div>
                        }
                        @if (chat.contact.company) {
                            <div>
                                <div class="font-medium text-secondary">
                                    Company
                                </div>
                                <div class="">{{ chat.contact.company }}</div>
                            </div>
                        }

                        @if (chat.contact.birthday) {
                            <div>
                                <div class="font-medium text-secondary">
                                    Birthday
                                </div>
                                <div class="">{{ chat.contact.birthday }}</div>
                            </div>
                        }
                        @if (chat.contact.addresses) {
                            <div>
                                <div class="font-medium text-secondary">
                                    Address
                                </div>
                                <div class="">
                                    {{ chat.contact.addresses[0] }}
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
    </div>
}

import { TextFieldModule } from '@angular/cdk/text-field';
import { Date<PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, HostListener, NgZone, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSidenavModule } from '@angular/material/sidenav';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FuseMediaWatcherService } from '@fuse/services/media-watcher';
import { ChatService } from 'app/core/databaseModels/chat/chat.service';
import { Cha<PERSON>, ChatMessages } from 'app/core/databaseModels/chat/chat.types';
import { LottieComponent, AnimationOptions } from 'ngx-lottie';
import {
    MatBottomSheet,
    MatBottomSheetModule,
    MatBottomSheetRef,
} from '@angular/material/bottom-sheet';

import { FileInputDropzoneModule } from 'app/modules/widgets/file-input-dropzone/file-input-dropzone.module';
import { FileInputDropzoneComponent } from 'app/modules/widgets/file-input-dropzone/file-input-dropzone.component';

import { ContactInfoComponent } from 'app/modules/admin/chat/contact-info/contact-info.component';
import { catchError, combineLatest, distinctUntilChanged, EMPTY, finalize, forkJoin, map, Observable, of, Subject, switchMap, takeUntil } from 'rxjs';
import _, { forEach } from 'lodash';
import { FormsModule } from '@angular/forms';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { FuseLoadingService } from '@fuse/services/loading';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { MatDialog } from '@angular/material/dialog';
import { OrderHistoryComponent } from '../../../widgets/order-history/order-history.component';
import { FcmNotificationService } from 'app/core/databaseModels/fcmNotification/fcmNotification.service';
import { FcmNotification } from 'app/core/databaseModels/fcmNotification/fcmNotification.types';

@Component({
    selector: 'chat-conversation',
    templateUrl: './conversation.component.html',
    styleUrls: ['./conversation.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [MatProgressSpinnerModule, FormsModule, FileInputDropzoneModule, MatSidenavModule, MatBottomSheetModule, LottieComponent, ContactInfoComponent, MatButtonModule, RouterLink, MatIconModule, MatMenuModule, NgClass, NgTemplateOutlet, MatFormFieldModule, MatInputModule, TextFieldModule, DatePipe]
})
export class ConversationComponent implements OnInit, OnDestroy {
    @ViewChild('messageInput') messageInput: ElementRef;
    messages: ChatMessages[] = [];
    messagesMedia: any = [];
    fcmNotification: FcmNotification;

    chat: Chat = null;
    drawerMode: 'over' | 'side' = 'side';
    drawerOpened: boolean = false;
    isLoading: boolean = false;
    firstID: string = '';
    sending: boolean = false;
    startDeletingChat: boolean = false;

    deletingItems = new Set<number>();
    private _unsubscribeAll: Subject<any> = new Subject<any>();

    options: AnimationOptions = {
        path: '/lottie/wired-complaint.json',
        autoplay: true,
        loop: true,

    };
    showEmojis: boolean = false;
    showDeleteMessage: boolean = false;
    inputValue: string = '';
    emojis = [
        {
            emoji: '😂', // Face with Tears of Joy
        },
        {
            emoji: '😭', // Loudly Crying Face
        },
        {
            emoji: '❤️', // Red Heart
        },
        {
            emoji: '🤣', // Rolling on the Floor Laughing
        },
        {
            emoji: '🔥', // Fire
        },
        {
            emoji: '😍', // Smiling Face with Heart-Shaped Eyes
        },
        {
            emoji: '🥺', // Pleading Face
        },
        {
            emoji: '🥰', // Smiling Face with Hearts
        },
        {
            emoji: '🙏', // Person with Folded Hands
        },
        {
            emoji: '✨', // Sparkles
        },
    ];


    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _chatService: ChatService,
        private _fuseMediaWatcherService: FuseMediaWatcherService,
        private _ngZone: NgZone,
        private route: ActivatedRoute,
        private _router: Router,
        private _bottomSheet: MatBottomSheet,
        private _fuseConfirmationService: FuseConfirmationService,
        private _fuseLoadingService: FuseLoadingService,
        private sanitizer: DomSanitizer,
        private _orderHistoryDialog: MatDialog,
        private _fcmNotificationService: FcmNotificationService,


    ) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Decorated methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Resize on 'input' and 'ngModelChange' events
     *
     * @private
     */
    @HostListener('input')
    @HostListener('ngModelChange')
    private _resizeMessageInput(): void {
        // This doesn't need to trigger Angular's change detection by itself
        this._ngZone.runOutsideAngular(() => {
            setTimeout(() => {
                // Set the height to 'auto' so we can correctly read the scrollHeight
                this.messageInput.nativeElement.style.height = 'auto';

                // Detect the changes so the height is applied
                this._changeDetectorRef.detectChanges();

                // Get the scrollHeight and subtract the vertical padding
                this.messageInput.nativeElement.style.height = `${this.messageInput.nativeElement.scrollHeight}px`;

                // Detect the changes one more time to apply the final height
                this._changeDetectorRef.detectChanges();
            });
        });
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        // Combine route param and chat service observables
        // Combine route param and chat service observables
        combineLatest([
            this.route.paramMap.pipe(
                map(params => params.get('id')),
                distinctUntilChanged()
            ),
            this._chatService.chats$
        ]).pipe(
            takeUntil(this._unsubscribeAll),
            switchMap(([id, chats]) => {
                if (!id) {
                    console.log('No ID found');
                    return EMPTY; // Using RxJS EMPTY to avoid further processing
                }

                // Check if the firstID is different to handle first-time loading scenarios
                if (this.firstID !== id) {
                    console.log('First loading');
                    this.firstID = id;
                    this.isLoading = true;
                    this._changeDetectorRef.markForCheck();
                }

                const chat = chats.find(chat => chat.$id === id);
                if (!chat) {
                    console.log('Chat not found for the given ID');
                    this.isLoading = false;
                    this._changeDetectorRef.markForCheck();
                    return EMPTY;
                }

                this.chat = chat;
                this._chatService.chat = chat;
                this._changeDetectorRef.markForCheck();

                // Get messages and start listening to real-time updates
                this._chatService.listenToRealTimeClientMessages(id);
                return this._chatService.getMessages(id).pipe(
                    switchMap(() => this._chatService.messages$)
                );
            }),
            catchError(error => {
                console.error('Error processing chat data', error);
                this.isLoading = false;
                this._changeDetectorRef.markForCheck();
                return of([]); // Return an empty array or appropriate default on error
            })
        ).subscribe(messages => {
            this.messages = messages;
            // console.log(messages);
            this.messagesMedia = messages.flatMap(message => message.media || []);
            this.isLoading = false;
            this._changeDetectorRef.markForCheck();
        });

        // Subscribe to media changes
        this._fuseMediaWatcherService.onMediaChange$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe(({ matchingAliases }) => {
                // Set the drawerMode if the given breakpoint is active
                if (matchingAliases.includes('lg')) {
                    this.drawerMode = 'side';
                }
                else {
                    this.drawerMode = 'over';
                }

                // Mark for check
                this._changeDetectorRef.markForCheck();
            });


    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Open the contact info
     */
    openContactInfo(): void {
        // Open the drawer
        this.drawerOpened = true;
        // Mark for check
        this._changeDetectorRef.markForCheck();
    }

    /**
     * Reset the chat
     */
    resetChat(): void {
        this._chatService.resetChat();

        // Close the contact info in case it's opened
        this.drawerOpened = false;

        // Mark for check
        this._changeDetectorRef.markForCheck();
    }

    /**
     * Track by function for ngFor loops
     *
     * @param index
     * @param item
     */
    trackByFn(index: number, item: any): any {
        return item.id || index;
    }

    toggleEmojiPicker() {
        this.showEmojis = !this.showEmojis;
    }

    addEmojiToInput(emoji: string) {
        this.inputValue += ` ${emoji} `;
        this.toggleEmojiPicker();
    }
    openBottomSheet(): void {
        const bottomSheetRef = this._bottomSheet.open(FileInputDropzoneComponent);

        bottomSheetRef.afterDismissed().subscribe(result => {
            if (result) {
                console.log('Files selected:', result);

                if (result.length > 0) {
                    this.sending = true;
                    this._changeDetectorRef.markForCheck();
                    const _attachedFiles = [];
                    forEach(result, (file) => {

                        this._chatService.uploadFile(file).subscribe((savedFile) => {
                            let fileObject = { fileName: file.name, fileUrl: savedFile.fileUrl, fileId: savedFile.fileId };
                            let fileString = JSON.stringify(fileObject);
                            _attachedFiles.push(fileString);
                            if (_attachedFiles.length == result.length && this.chat) {

                                const message: ChatMessages = {
                                    $id: null,
                                    organisationID: this.chat.organisationID,
                                    chatId: this.chat.$id,
                                    sender: 'platform',
                                    value: this.inputValue,
                                    createdAt: new Date(),
                                    messageType: 'media',
                                    messageStatus: 'sent',
                                    media: _attachedFiles,
                                }
                                this._chatService.createMessage(message).subscribe(() => {
                                    this._changeDetectorRef.markForCheck();
                                    this.inputValue = '';
                                    this.sending = false;

                                })
                            }
                        })


                    })
                    // Here you can handle the selected files, such as updating the UI or processing the files
                }
            }
        });
    }
    dismissPopUps(): void {
        this.showEmojis = false;
        this.showDeleteMessage = false;
    }

    fileDetails(file: string): any {

        return JSON.parse(file);
    }

    previewFileUrl(file: string): string {
        const fileDetail = this.fileDetails(file);
        if (fileDetail && fileDetail.fileUrl) {
            return fileDetail.fileUrl.replace('/view?', '/preview?width=250&');
        }
        return '';
    }

    sanitizeContent(content: string): SafeHtml {
        const replacedContent = content.replace(/\n/g, '<br/>'); // Convert line breaks to <br>
        return this.sanitizer.bypassSecurityTrustHtml(replacedContent);
    }
    sendMessage(): void {
        const savingMessage = this.inputValue.trim();
        this.inputValue = '';
        if (!savingMessage || !this.chat.contact) return; // Ensure there's input and a contact

        this.sending = true;
        this._changeDetectorRef.markForCheck();

        // Create the message object
        const message: ChatMessages = {
            $id: null,
            organisationID: this.chat.organisationID,
            chatId: this.chat.$id,
            sender: 'platform',
            value: savingMessage.trim(),
            createdAt: new Date(),
            messageType: 'text',
            messageStatus: 'sent',
            media: [],
        };
        if (this.chat.contact.deviceTokenId) {
            this.sendNotification(savingMessage.trim(), '💭 Streamliner admin message ', this.chat.contact.deviceTokenId);
        }

        this._chatService.createMessage(message).pipe(
            finalize(() => {
                this.sending = false;
                this.inputValue = ''; // Reset input value regardless of operation result
                this._changeDetectorRef.markForCheck(); // Trigger change detection for UI update
            })
        ).subscribe({
            next: () => {
                // Update last message details on chat
                this.chat.lastMessage = message.value;
                this.chat.lastMessageAt = message.createdAt;
                this.chat.unreadCount = 0;
                this._chatService.updateChat(this.chat.$id, this.chat).subscribe();
            },
            error: (error) => console.error('Error sending message', error), // Handle any errors
        });
    }

    /**
    * Toggle mute notifications
    */
    toggleMuteNotifications(): void {
        // Toggle the muted
        this.chat.muted = !this.chat.muted;

        // Update the chat on the server
        this._chatService.updateChat(this.chat.$id, this.chat).subscribe();
    }

    makeRead(): void {
        this.chat.unreadCount = 0;
        this._chatService.updateChat(this.chat.$id, this.chat).subscribe();
    }

    toggleDeleteMessage(): void {
        this.showDeleteMessage = !this.showDeleteMessage;
    }

    //delete message and chat
    deleteMessage(message: ChatMessages, index: number): void {
        this.deletingItems.add(index); // Mark the item as being deleted
        // console.log(message);

        // Define an observable for deleting files if the message contains media
        const deleteFiles$ = message.messageType === 'media' && message.media.length > 0 ?
            forkJoin(message.media.map(file =>
                this._chatService.deleteFile(JSON.parse(file).fileId)
            )) : of(null); // Use of(null) to represent no operation needed for non-media messages

        // Chain the file deletion observable with the message deletion
        deleteFiles$.pipe(
            switchMap(() => this._chatService.deleteMessage(message.$id)), // Proceed to delete the message after all files (if any) are deleted
            finalize(() => {
                this.deletingItems.delete(index); // Clean up regardless of success or failure
                this._changeDetectorRef.markForCheck(); // Trigger UI update
            })
        ).subscribe({
            error: error => console.error('Error deleting message', error) // Improved error handling
        });
    }

    deleteMessagesAndChat(): void {

        const confirmation = this._fuseConfirmationService.open({
            title: 'Delete chat!',
            message: `Are you sure you want to delete all chats for <i class="text-semibold text-red-600">${this.chat.contact.name}</i>? This action cannot be undone!`,
            actions: {
                confirm: {
                    label: 'Delete',
                },
            },
            icon: {
                show: true,
                name: 'heroicons_outline:trash',
            },
        });


        confirmation.afterClosed().subscribe((result) => {
            if (result === 'confirmed') {
                this._fuseLoadingService.show();
                this.startDeletingChat = true;
                this.isLoading = true;
                this._changeDetectorRef.markForCheck();
                this.deleteAllRelatedFiles()
                    .pipe(
                        switchMap(() => this.deleteAllMessages()),
                        switchMap(() => this._chatService.deleteChat(this.chat.$id)), // Delete the chat here
                        finalize(() => {
                            this._fuseLoadingService.hide();
                            this.chat = null;
                            this.isLoading = false;
                            this.startDeletingChat = false;

                        })
                    )
                    .subscribe({
                        next: () => {
                            this._chatService.chat = null;
                            this._changeDetectorRef.markForCheck();
                            this.startDeletingChat = false;
                            this._router.navigate(['/chat']);

                            console.log('Chat and all messages deleted successfully');

                        },
                        error: (error) => console.error('Error in deletion process', error)
                    });
            } else {
                this._fuseLoadingService.hide();
            }
        });


    }

    deleteAllRelatedFiles(): Observable<any> {
        const fileIds = this.messages.flatMap(message => message.media.map(file => JSON.parse(file).fileId));
        if (fileIds.length === 0) {
            return of([]); // Immediately return an observable that emits once if there are no files.
        }
        return forkJoin(fileIds.map(fileId => this._chatService.deleteFile(fileId)));
    }


    deleteAllMessages(): Observable<any> {
        if (this.messages.length === 0) {
            return of([]); // Immediately return an observable that emits once if there are no messages.
        }
        return forkJoin(this.messages.map(message => this._chatService.deleteMessage(message.$id)));
    }

    openOrderHistoryDialog(): void {


        let dialogRef = this._orderHistoryDialog.open(OrderHistoryComponent, {
            data: { customerId: this.chat.contact.$id },

        });
        dialogRef.afterClosed().subscribe(result => {
            if (result) {
                // console.log('The dialog was closed', result);
                // move to the scheduler page

                this._router.navigate(['/scheduler'], { queryParams: { scheduleID: result.$id } });

            }


        });
    }

    sendNotification(body: string, title: string, to: string, imageUrl?: string) {
        this.fcmNotification = {
            body: body,
            title: title,
            to: to,
            imageUrl: imageUrl,
            type: 'chat'
        }
        this._fcmNotificationService.sendNotification(this.fcmNotification);
    }

}

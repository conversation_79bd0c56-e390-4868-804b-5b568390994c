<div
    class="flex flex-col flex-auto overflow-y-auto lg:overflow-hidden bg-card dark:bg-default"
>
    @if (isLoading) {
        <div class="flex flex-auto items-center justify-center mt-20">
            <ng-lottie class="flex-col" width="150px" [options]="options" />
            <span class="mt-4 text-2xl font-semibold tracking-tight">
                @if (startDeletingChat) {
                    Deleting chat...
                } @else {
                    Loading chat...
                }
            </span>
        </div>
    } @else {
        @if (chat) {
            <mat-drawer-container
                class="flex-auto h-full"
                [hasBackdrop]="false"
            >
                <!-- Drawer -->
                <mat-drawer
                    class="w-full sm:w-100 lg:border-l lg:shadow-none dark:bg-gray-900"
                    [autoFocus]="false"
                    [mode]="drawerMode"
                    [position]="'end'"
                    [(opened)]="drawerOpened"
                    #drawer
                >
                    <!-- Contact info -->
                    <chat-contact-info
                        [drawer]="drawer"
                        [chat]="chat"
                        [messages]="messagesMedia"
                    ></chat-contact-info>
                </mat-drawer>
                <!-- Drawer content -->
                <mat-drawer-content
                    class="flex flex-col overflow-hidden"
                    [ngClass]="{
                        'opacity-20': startDeletingChat
                    }"
                >
                    <!-- Header -->
                    <div
                        class="flex flex-0 items-center h-18 px-4 md:px-6 border-b bg-gray-50 dark:bg-transparent"
                    >
                        <!-- Back button -->
                        <a
                            class="lg:hidden md:-ml-2"
                            mat-icon-button
                            [routerLink]="['./']"
                            (click)="resetChat()"
                        >
                            <mat-icon
                                [svgIcon]="'heroicons_outline:arrow-long-left'"
                            ></mat-icon>
                        </a>
                        <!-- Contact info -->
                        @if (chat.contact) {
                            <div
                                class="flex items-center ml-2 lg:ml-0 mr-2 cursor-pointer"
                                (click)="openContactInfo()"
                            >
                                <div
                                    class="relative flex flex-0 items-center justify-center w-10 h-10"
                                >
                                    @if (chat.contact.avatar) {
                                        <img
                                            class="w-full h-full rounded-full object-cover"
                                            [src]="chat.contact.avatar"
                                            alt="Contact avatar"
                                        />
                                    }
                                    @if (!chat.contact.avatar) {
                                        <div
                                            class="flex items-center justify-center w-full h-full rounded-full text-lg uppercase bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                        >
                                            {{ chat.contact.name.charAt(0) }}
                                        </div>
                                    }
                                </div>
                                <div
                                    class="ml-4 text-lg font-medium leading-5 truncate"
                                >
                                    {{ chat.contact.name }}
                                </div>
                            </div>
                        }
                        <button
                            class="ml-auto flex justify-end"
                            mat-flat-button
                            color="primary"
                            (click)="openOrderHistoryDialog()"
                        >
                            Scheduled Jobs
                        </button>
                        <button
                            class="ml-auto flex justify-end"
                            mat-icon-button
                            [matMenuTriggerFor]="conversationHeaderMenu"
                        >
                            <mat-icon
                                [svgIcon]="
                                    'heroicons_outline:ellipsis-vertical'
                                "
                            ></mat-icon>
                            <mat-menu #conversationHeaderMenu>
                                <button
                                    mat-menu-item
                                    (click)="openContactInfo()"
                                >
                                    <mat-icon
                                        [svgIcon]="
                                            'heroicons_outline:user-circle'
                                        "
                                    ></mat-icon>
                                    Contact info
                                </button>
                                <button mat-menu-item (click)="makeRead()">
                                    <mat-icon
                                        [svgIcon]="
                                            'heroicons_outline:check-circle'
                                        "
                                    ></mat-icon>
                                    Make messages read
                                </button>
                                <button
                                    mat-menu-item
                                    (click)="toggleMuteNotifications()"
                                >
                                    @if (!chat.muted) {
                                        <mat-icon
                                            [svgIcon]="
                                                'heroicons_outline:speaker-x-mark'
                                            "
                                        ></mat-icon>
                                        Mute notifications
                                    }
                                    @if (chat.muted) {
                                        <mat-icon
                                            [svgIcon]="
                                                'heroicons_outline:volume-up'
                                            "
                                        ></mat-icon>
                                        Unmute notifications
                                    }
                                </button>
                                <button
                                    mat-menu-item
                                    (click)="toggleDeleteMessage()"
                                >
                                    <mat-icon
                                        [svgIcon]="
                                            'heroicons_outline:backspace'
                                        "
                                    ></mat-icon>
                                    Delete messages
                                </button>
                                <button
                                    mat-menu-item
                                    (click)="deleteMessagesAndChat()"
                                >
                                    <mat-icon
                                        [svgIcon]="'heroicons_outline:trash'"
                                    ></mat-icon>
                                    Delete chat
                                </button>
                            </mat-menu>
                        </button>
                    </div>
                    <!-- Conversation -->
                    <div class="flex overflow-y-auto flex-col-reverse">
                        <div
                            class="flex flex-col flex-auto shrink p-6 bg-card dark:bg-transparent"
                        >
                            @for (
                                message of messages;
                                track trackByFn(i, message);
                                let i = $index;
                                let first = $first;
                                let last = $last
                            ) {
                                <!-- Start of the day -->
                                @if (
                                    first ||
                                    (messages[i - 1].createdAt | date: "d") !==
                                        (message.createdAt | date: "d")
                                ) {
                                    <div
                                        class="flex items-center justify-center my-3 -mx-6"
                                    >
                                        <div class="flex-auto border-b"></div>
                                        <div
                                            class="flex-0 mx-4 text-sm font-medium leading-5 text-secondary"
                                        >
                                            {{
                                                message.createdAt
                                                    | date: "longDate"
                                            }}
                                        </div>
                                        <div class="flex-auto border-b"></div>
                                    </div>
                                }
                                <div
                                    class="flex flex-col"
                                    [ngClass]="{
                                        'items-end':
                                            message.sender === 'platform',
                                        'items-start':
                                            message.sender !== 'platform',
                                        'mt-0.5':
                                            i > 0 &&
                                            messages[i - 1].sender ===
                                                message.sender,
                                        'mt-3':
                                            i > 0 &&
                                            messages[i - 1].sender !==
                                                message.sender
                                    }"
                                >
                                    <!-- Bubble -->
                                    <div class="flex relative max-w-3/4">
                                        <div
                                            class="flex-col"
                                            [ngClass]="{
                                                'order-1 sm:order-2 ':
                                                    message.sender !==
                                                    'platform'
                                            }"
                                        >
                                            @if (showDeleteMessage) {
                                                <button
                                                    mat-icon-button
                                                    (click)="
                                                        deleteMessage(
                                                            message,
                                                            i
                                                        )
                                                    "
                                                >
                                                    <mat-icon
                                                        class="text-hint"
                                                        [svgIcon]="
                                                            'heroicons_outline:trash'
                                                        "
                                                    ></mat-icon>
                                                </button>
                                            }
                                        </div>
                                        <div
                                            class="flex-col px-3 py-2 rounded-lg"
                                            [ngClass]="{
                                                'bg-blue-500 text-blue-50':
                                                    message.sender ===
                                                    'platform',
                                                'bg-gray-500 text-gray-50':
                                                    message.sender !==
                                                    'platform'
                                            }"
                                        >
                                            <!-- Speech bubble tail -->
                                            @if (
                                                last ||
                                                messages[i + 1].sender !==
                                                    message.sender
                                            ) {
                                                <div
                                                    class="absolute bottom-0 w-3"
                                                    [ngClass]="{
                                                        'text-blue-500 -right-1 -mr-px mb-px':
                                                            message.sender ===
                                                            'platform',
                                                        'text-gray-500 -left-1 -ml-px mb-px -scale-x-1':
                                                            message.sender !==
                                                            'platform'
                                                    }"
                                                >
                                                    <ng-container
                                                        *ngTemplateOutlet="
                                                            speechBubbleExtension
                                                        "
                                                    ></ng-container>
                                                </div>
                                            }
                                            <!-- Message -->
                                            @if (!deletingItems.has(i)) {
                                                @if (
                                                    message.messageType ===
                                                    "media"
                                                ) {
                                                    <div class="flex flex-wrap">
                                                        <div
                                                            class="flex flex-col items-center m-3 ng-star-inserted"
                                                        >
                                                            @for (
                                                                file of message.media;
                                                                track file
                                                            ) {
                                                                @switch (
                                                                    fileDetails(
                                                                        file
                                                                    )
                                                                        .fileName.split(
                                                                            "."
                                                                        )
                                                                        .pop()
                                                                ) {
                                                                    @case (
                                                                        "pdf"
                                                                    ) {
                                                                        <div
                                                                            class="leading-1.5 flex w-full max-w-[320px] flex-col"
                                                                        >
                                                                            <div
                                                                                class="flex items-start bg-gray-50 dark:bg-gray-700 rounded-xl p-2"
                                                                            >
                                                                                <div
                                                                                    class="me-2"
                                                                                >
                                                                                    <span
                                                                                        class="flex items-center gap-2 text-sm font-medium text-gray-900 dark:text-white pb-2"
                                                                                    >
                                                                                        <i
                                                                                            class="fa-duotone fa-file-pdf text-red-500 icon-size-5 flex-shrink-0"
                                                                                        ></i>
                                                                                        {{
                                                                                            fileDetails(
                                                                                                file
                                                                                            )
                                                                                                .fileName
                                                                                        }}
                                                                                    </span>
                                                                                </div>
                                                                                <div
                                                                                    class="inline-flex self-center items-center"
                                                                                >
                                                                                    <a
                                                                                        [href]="
                                                                                            fileDetails(
                                                                                                file
                                                                                            )
                                                                                                .fileUrl
                                                                                        "
                                                                                        target="_blank"
                                                                                        class="inline-flex self-center items-center p-2 text-sm font-medium text-center text-gray-900 bg-gray-50 rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none dark:text-white focus:ring-gray-50 dark:bg-gray-700 dark:hover:bg-gray-600 dark:focus:ring-gray-600"
                                                                                        type="button"
                                                                                    >
                                                                                        <i
                                                                                            class="fa-duotone fa-download icon-size-4 text-gray-900 dark:text-white"
                                                                                        ></i>
                                                                                    </a>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    }
                                                                    @case (
                                                                        "doc"
                                                                    ) {
                                                                        <div
                                                                            class="leading-1.5 flex w-full max-w-[320px] flex-col"
                                                                        >
                                                                            <div
                                                                                class="flex items-start bg-gray-50 dark:bg-gray-700 rounded-xl p-2"
                                                                            >
                                                                                <div
                                                                                    class="me-2"
                                                                                >
                                                                                    <span
                                                                                        class="flex items-center gap-2 text-sm font-medium text-gray-900 dark:text-white pb-2"
                                                                                    >
                                                                                        <i
                                                                                            class="fa-duotone fa-file-doc text-blue-500 icon-size-5 flex-shrink-0"
                                                                                        ></i>
                                                                                        {{
                                                                                            fileDetails(
                                                                                                file
                                                                                            )
                                                                                                .fileName
                                                                                        }}
                                                                                    </span>
                                                                                </div>
                                                                                <div
                                                                                    class="inline-flex self-center items-center"
                                                                                >
                                                                                    <a
                                                                                        [href]="
                                                                                            fileDetails(
                                                                                                file
                                                                                            )
                                                                                                .fileUrl
                                                                                        "
                                                                                        target="_blank"
                                                                                        class="inline-flex self-center items-center p-2 text-sm font-medium text-center text-gray-900 bg-gray-50 rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none dark:text-white focus:ring-gray-50 dark:bg-gray-700 dark:hover:bg-gray-600 dark:focus:ring-gray-600"
                                                                                        type="button"
                                                                                    >
                                                                                        <i
                                                                                            class="fa-duotone fa-download icon-size-4 text-gray-900 dark:text-white"
                                                                                        ></i>
                                                                                    </a>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    }
                                                                    @default {
                                                                        <div
                                                                            class="group relative mt-2"
                                                                        >
                                                                            <div
                                                                                class="absolute w-full h-full bg-gray-900/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg flex items-center justify-center"
                                                                            >
                                                                                <a
                                                                                    [href]="
                                                                                        fileDetails(
                                                                                            file
                                                                                        )
                                                                                            .fileUrl
                                                                                    "
                                                                                    target="_blank"
                                                                                    class="inline-flex items-center justify-center rounded-full h-10 w-10 bg-white/30 hover:bg-white/50 focus:ring-4 focus:outline-none dark:text-white focus:ring-gray-50"
                                                                                >
                                                                                    <i
                                                                                        class="fa-duotone fa-download icon-size-5 text-white"
                                                                                    ></i>
                                                                                </a>
                                                                            </div>
                                                                            <img
                                                                                class="w-60 h-60 rounded-lg overflow-hidden ng-star-inserted"
                                                                                [src]="
                                                                                    previewFileUrl(
                                                                                        file
                                                                                    )
                                                                                "
                                                                            />
                                                                        </div>
                                                                    }
                                                                }
                                                            }
                                                        </div>
                                                    </div>
                                                } @else {
                                                    <div
                                                        class="min-w-4 leading-5"
                                                        [innerHTML]="
                                                            sanitizeContent(
                                                                message.value
                                                            )
                                                        "
                                                    ></div>
                                                }
                                            } @else {
                                                <div role="status">
                                                    <svg
                                                        aria-hidden="true"
                                                        class="w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                                                        viewBox="0 0 100 101"
                                                        fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <path
                                                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                                            fill="currentColor"
                                                        />
                                                        <path
                                                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                                            fill="currentFill"
                                                        />
                                                    </svg>
                                                    <span class="sr-only"
                                                        >Loading...</span
                                                    >
                                                </div>
                                            }
                                        </div>
                                    </div>
                                    <!-- Time -->
                                    @if (
                                        first ||
                                        last ||
                                        messages[i + 1].sender !==
                                            message.sender ||
                                        messages[i + 1].createdAt !==
                                            message.createdAt
                                    ) {
                                        <div
                                            class="my-0.5 text-sm font-medium text-secondary"
                                            [ngClass]="{
                                                'mr-3':
                                                    message.sender ===
                                                    'platform',
                                                'ml-3':
                                                    message.sender !==
                                                    'platform'
                                            }"
                                        >
                                            {{
                                                message.createdAt
                                                    | date: "HH:mm"
                                            }}
                                        </div>
                                    }
                                </div>
                            }
                            @if (sending) {
                                <div class="flex flex-col items-end">
                                    <div
                                        class="relative max-w-3/4 px-3 py-2 rounded-lg bg-blue-500 text-blue-50"
                                    >
                                        <div
                                            class="absolute bottom-0 w-3 text-blue-500 -right-1 -mr-px mb-px"
                                        >
                                            <ng-container
                                                *ngTemplateOutlet="
                                                    speechBubbleExtension
                                                "
                                            ></ng-container>
                                        </div>
                                        <div role="status">
                                            <svg
                                                aria-hidden="true"
                                                class="inline w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-white"
                                                viewBox="0 0 100 101"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                            >
                                                <path
                                                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                                    fill="currentColor"
                                                />
                                                <path
                                                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                                    fill="currentFill"
                                                />
                                            </svg>
                                            <span class="sr-only"
                                                >Loading...</span
                                            >
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                    <!-- Message field -->
                    <div
                        class="flex items-end p-4 border-t bg-gray-50 dark:bg-transparent"
                    >
                        <div class="relative flex items-center h-11 my-px">
                            @if (showEmojis) {
                                <div class="absolute bottom-5 z-10">
                                    <div
                                        class="flex space-x-2 p-2 border rounded-lg bg-white dark:bg-slate-500"
                                    >
                                        @for (emoji of emojis; track emoji) {
                                            <span
                                                class="flex gap-1 p-1 cursor-pointer rounded-lg hover:bg-gray-100 dark:hover:bg-slate-400"
                                                (click)="
                                                    addEmojiToInput(emoji.emoji)
                                                "
                                            >
                                                {{ emoji.emoji }}
                                            </span>
                                        }
                                    </div>
                                </div>
                            }
                            <button
                                mat-icon-button
                                (click)="toggleEmojiPicker()"
                            >
                                <mat-icon
                                    [svgIcon]="'heroicons_outline:face-smile'"
                                ></mat-icon>
                            </button>
                            <button
                                class="ml-0.5"
                                mat-icon-button
                                (click)="openBottomSheet()"
                            >
                                <mat-icon
                                    [svgIcon]="'heroicons_outline:paper-clip'"
                                ></mat-icon>
                            </button>
                        </div>
                        <mat-form-field
                            class="fuse-mat-dense fuse-mat-rounded fuse-mat-bold w-full ml-4"
                            subscriptSizing="dynamic"
                        >
                            <textarea
                                matInput
                                [(ngModel)]="inputValue"
                                cdkTextareaAutosize
                                #messageInput
                                (keydown.enter)="
                                    !$event.shiftKey && sendMessage($event)
                                "
                                (keydown.shift.enter)="(true)"
                            >
                            </textarea>
                        </mat-form-field>
                        <div class="flex items-center h-11 my-px ml-4">
                            <button mat-icon-button (click)="sendMessage()">
                                <mat-icon
                                    [svgIcon]="
                                        'heroicons_outline:paper-airplane'
                                    "
                                ></mat-icon>
                            </button>
                        </div>
                    </div>
                </mat-drawer-content>
                <div
                    role="status"
                    class="absolute -translate-x-1/2 -translate-y-1/2 top-2/4 left-1/2"
                >
                    <svg
                        aria-hidden="true"
                        class="w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="currentColor"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentFill"
                        />
                    </svg>
                    <span class="sr-only">Loading...</span>
                </div>
            </mat-drawer-container>
        } @else {
            <div
                class="flex flex-col flex-auto items-center justify-center bg-gray-100 dark:bg-transparent"
            >
                <mat-icon
                    class="icon-size-24"
                    [svgIcon]="
                        'heroicons_outline:chat-bubble-oval-left-ellipsis'
                    "
                ></mat-icon>
                <div
                    class="mt-4 text-2xl font-semibold tracking-tight text-secondary"
                >
                    <div
                        class="flex flex-auto items-center justify-center mt-20"
                    >
                        <ng-lottie width="150px" [options]="options" />
                    </div>
                    Select a conversation or start a new chat
                </div>
            </div>
        }

        <!-- Select chat or start new template -->

        <!-- Speech bubble tail SVG -->
        <!-- @formatter:off -->
        <ng-template #speechBubbleExtension>
            <svg
                width="100%"
                height="100%"
                viewBox="0 0 66 66"
                xmlns="http://www.w3.org/2000/svg"
            >
                <g
                    id="Page-1"
                    stroke="none"
                    stroke-width="1"
                    fill="none"
                    fill-rule="evenodd"
                >
                    <path
                        d="M1.01522827,0.516204834 C-8.83532715,54.3062744 61.7609863,70.5215302 64.8009949,64.3061218 C68.8074951,54.8859711 30.1663208,52.9997559 37.5036011,0.516204834 L1.01522827,0.516204834 Z"
                        fill="currentColor"
                        fill-rule="nonzero"
                    ></path>
                </g>
            </svg>
        </ng-template>
        <!-- @formatter:on -->
    }
</div>

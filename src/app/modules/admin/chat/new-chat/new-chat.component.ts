
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDrawer } from '@angular/material/sidenav';
import { Router } from '@angular/router';
import { FuseLoadingService } from '@fuse/services/loading';
import { ChatService } from 'app/core/databaseModels/chat/chat.service';
import { Chat } from 'app/core/databaseModels/chat/chat.types';
import { Customer, CustomerTags } from 'app/core/databaseModels/customers/contacts.types';
import { CustomerService } from 'app/core/databaseModels/customers/customers.service';


import { catchError, EMPTY, map, of, Subject, switchMap, take, takeUntil } from 'rxjs';

@Component({
    selector: 'chat-new-chat',
    templateUrl: './new-chat.component.html',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [MatButtonModule, MatIconModule]
})
export class NewChatComponent implements OnInit, OnDestroy {
    @Input() drawer: MatDrawer;
    contacts: Customer[] = [];
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    isSaving: boolean = false;

    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _customerService: CustomerService,
        private _fuseLoadingService: FuseLoadingService,
        private _chatService: ChatService,
        private _router: Router,) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        // Contacts
        this._fuseLoadingService.show();
        this.isSaving = true;
        this._customerService.getCustomers().subscribe(customers => {
            this._fuseLoadingService.hide();
            this.isSaving = false;
            this.contacts = customers;
            // console.log(this.contacts);
            // Mark for check
            this._changeDetectorRef.markForCheck();
            this._changeDetectorRef.detectChanges();

        });
    }
    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Track by function for ngFor loops
     *
     * @param index
     * @param item
     */
    trackByFn(index: number, item: any): any {
        return item.$id || index;
    }
    startChat(contact: Customer): void {
        // Only take the first emission to prevent multiple subscriptions
        this._chatService.chats$.pipe(
            take(1),
            switchMap(chats => {
                // Find an existing chat with the contact
                const existingChat = chats.find(chat => chat.contact.$id === contact.$id);
                if (existingChat) {
                    // Navigate to the existing chat
                    return of(this._router.createUrlTree(['/chat', existingChat.$id]));
                } else {
                    // Create a new chat if it doesn't exist
                    const newChat: Chat = {
                        $id: null, // Assuming $id is generated on the backend
                        organisationID: contact.organisationID,
                        contactId: contact.$id,
                        unreadCount: 0,
                        muted: false,
                        lastMessage: '',
                        lastMessageAt: new Date(),
                        contact: contact,
                    };
                    return this._chatService.createChat(newChat).pipe(
                        map(createdChat => this._router.createUrlTree(['/chat', createdChat.$id]))
                    );
                }
            }),
            catchError(error => {
                console.error('Error starting chat', error);
                return EMPTY; // Handle error appropriately
            })
        ).subscribe(urlTree => {
            this._router.navigateByUrl(urlTree);
            this.drawer.close(); // Ensure drawer is closed after navigation or error
        });
    }

}

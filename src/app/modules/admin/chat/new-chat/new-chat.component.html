<div
  class="flex flex-col flex-auto h-full overflow-hidden bg-card dark:bg-default"
  >
  <!-- Header -->
  <div
    class="flex flex-0 items-center h-18 -mb-px px-6 bg-gray-50 dark:bg-transparent"
    >
    <button mat-icon-button (click)="drawer.close()">
      <mat-icon
        [svgIcon]="'heroicons_outline:arrow-long-left'"
      ></mat-icon>
    </button>
    <div class="ml-2 text-2xl font-semibold">Start new chat</div>
  </div>

  <div class="relative overflow-y-auto">
    @if (contacts.length) {
      <!-- Contacts -->
      @for (
        contact of contacts; track trackByFn(i,
        contact); let i = $index) {
        <!-- Group -->
        @if (
          i === 0 ||
          contact.name.charAt(0) !==
          contacts[i - 1].name.charAt(0)
          ) {
          <div
            class="z-10 sticky top-0 -mt-px px-6 py-1 md:px-8 border-t border-b font-medium uppercase text-secondary bg-gray-100 dark:bg-gray-900"
            >
            {{ contact.name.charAt(0) }}
          </div>
        }
        <!-- Contact -->
        <div
          class="z-20 flex items-center px-6 py-4 md:px-8 cursor-pointer border-b hover:bg-gray-100 dark:hover:bg-hover"
          (click)="startChat(contact)"
          >
          <div
            class="flex flex-0 items-center justify-center w-10 h-10 rounded-full overflow-hidden"
            >
            @if (contact.avatar) {
              <img
                class="object-cover w-full h-full"
                [src]="contact.avatar"
                alt="Contact avatar"
                />
            }
            @if (!contact.avatar) {
              <div
                class="flex items-center justify-center w-full h-full rounded-full text-lg uppercase bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                >
                {{ contact.name.charAt(0) }}
              </div>
            }
          </div>
          <div class="min-w-0 ml-4">
            <div class="font-medium leading-5 truncate">
              {{ contact.name }}
            </div>
            <div class="leading-5 truncate text-secondary">
              {{ contact.about }}
            </div>
          </div>
        </div>
      }
      <!--End Loop-->
    } @else {
      <div
        class="p-8 sm:p-16 border-t text-4xl font-semibold tracking-tight text-center"
        >
        @if(isSaving){ Loading... }@else{ There are no contacts! }
        </div>
      }
    </div>

    <!-- No contacts -->
  </div>

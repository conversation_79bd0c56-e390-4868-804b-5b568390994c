import { <PERSON><PERSON><PERSON><PERSON>, Ng<PERSON><PERSON> } from '@angular/common';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    DestroyRef,
    OnDestroy,
    OnInit,
    ViewEncapsulation
} from '@angular/core';
import { MatFormField } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSidenavModule } from '@angular/material/sidenav';
import { RouterLink, RouterOutlet } from '@angular/router';
import { FuseLoadingService } from '@fuse/services/loading';
import { ChatService } from 'app/core/databaseModels/chat/chat.service';
import { Chat } from 'app/core/databaseModels/chat/chat.types';
import { OrganisationsService } from 'app/core/databaseModels/organisations/organisations.service';
import { Organisation } from 'app/core/databaseModels/organisations/organisations.types';
import { NewChatComponent } from 'app/modules/admin/chat/new-chat/new-chat.component';
import { ProfileComponent } from 'app/modules/admin/chat/profile/profile.component';
import { PipesModule } from 'app/pipes/pipes.module';
import { catchError, combineLatest, filter, switchMap, take, throwError } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
    selector: 'chat-chats',
    templateUrl: './chats.component.html',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        NgClass,
        PipesModule,
        DatePipe,
        NewChatComponent,
        ProfileComponent,
        MatIcon,
        MatFormField,
        MatInput,
        RouterLink,
        RouterOutlet,
        MatSidenavModule,
        MatMenuModule,
    ]
})
export class ChatsComponent implements OnInit, OnDestroy {
    isLoading = false;
    chats: Chat[] = [];
    drawerComponent: 'profile' | 'new-chat';
    drawerOpened = false;
    filteredChats: Chat[] = [];
    profile: Organisation;
    selectedChat: Chat;

    constructor(
        private _chatService: ChatService,
        private _changeDetectorRef: ChangeDetectorRef,
        private _organisationsService: OrganisationsService,
        private _fuseLoadingService: FuseLoadingService,
        private destroyRef: DestroyRef
    ) { }

    ngOnInit(): void {
        //TODO:Load Profile In refresh
        this.loadProfile();

    }

    loadProfile() {
        this._chatService
            .getProfile()
            .pipe(
                switchMap(() => this._chatService.profile$),
                takeUntilDestroyed(this.destroyRef),
                catchError((error) => {
                    console.error('Error:', error);
                    return throwError(() => error);
                })
            )
            .subscribe((profile: Organisation) => {
                this.profile = profile;
                this._changeDetectorRef.markForCheck();
                this.loadChats();
                this._changeDetectorRef.markForCheck();
            });
    }

    loadChats() {
        this.isLoading = true;

        // Just subscribe to the existing streams since listenToRealTimeChats is already running
        combineLatest([
            this._chatService.chats$.pipe(
                // Only take values that aren't empty (means initial load completed)
                filter(chats => chats.length > 0)
            ),
            this._chatService.chat$
        ]).pipe(
            // Take the first emission that has chats
            take(1),
            // Then switch to ongoing subscription
            switchMap(([initialChats, initialChat]) => {
                // Set initial state
                this.chats = initialChats;
                this.filteredChats = initialChats;
                if (initialChat) {
                    this.selectedChat = initialChat;
                }

                // Now subscribe to future updates
                return combineLatest([
                    this._chatService.chats$,
                    this._chatService.chat$
                ]);
            }),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe({
            next: ([chats, selectedChat]) => {
                this.chats = chats;
                this.filteredChats = chats;
                if (selectedChat) {
                    this.selectedChat = selectedChat;
                }
                this.isLoading = false;
                this._changeDetectorRef.markForCheck();
            },
            error: (error) => {
                console.error('Error:', error);
                this.isLoading = false;
                this._changeDetectorRef.markForCheck();
            }
        });
    }
    ngOnDestroy(): void {
        this._chatService.resetChat();

        // Unsubscribe from all subscriptions

    }
    setChat(chat: Chat) {
        this.selectedChat = chat;
    }
    filterChats(query: string): void {
        if (!query) {
            this.filteredChats = this.chats;
            return;
        }

        this.filteredChats = this.chats.filter((chat) =>
            chat.contact.name.toLowerCase().includes(query.toLowerCase())
        );
    }

    openNewChat(): void {
        this.drawerComponent = 'new-chat';
        this.drawerOpened = true;
        this._changeDetectorRef.markForCheck();
    }

    openProfile(): void {
        this.drawerComponent = 'profile';
        this.drawerOpened = true;
        this._changeDetectorRef.markForCheck();
    }

    trackByFn(index: number, item: Chat): any {
        return item.$id || index;
    }
}

<div class="relative flex flex-auto w-full bg-card dark:bg-transparent">
    <mat-drawer-container class="flex-auto h-full" [hasBackdrop]="false">
        <!-- Drawer -->
        <mat-drawer
            class="w-full sm:w-100 lg:border-r lg:shadow-none dark:bg-gray-900"
            [autoFocus]="false"
            [(opened)]="drawerOpened"
            #drawer
        >
            <!-- New chat -->
            @if (drawerComponent === "new-chat") {
                <chat-new-chat [drawer]="drawer"></chat-new-chat>
            }
            <!-- Profile -->
            @if (drawerComponent === "profile") {
                <chat-profile [drawer]="drawer"></chat-profile>
            }
        </mat-drawer>

        <!-- Drawer content -->
        <mat-drawer-content class="flex overflow-hidden">
            <!-- Chats list -->
            <ng-container>
                <div
                    class="relative flex flex-auto flex-col w-full min-w-0 lg:min-w-100 lg:max-w-100 bg-card dark:bg-transparent"
                >
                    <!-- Header -->
                    <div
                        class="flex flex-col flex-0 py-4 px-8 border-b bg-gray-50 dark:bg-transparent"
                    >
                        <div class="flex items-center">
                            @if (profile) {
                                <div
                                    class="flex items-center mr-1 cursor-pointer"
                                    (click)="openProfile()"
                                >
                                    <div class="w-10 h-10">
                                        @if (profile.avatar) {
                                            <img
                                                class="object-cover w-full h-full rounded-full"
                                                [src]="profile.avatar"
                                                alt="Profile avatar"
                                            />
                                        } @else {
                                            <div
                                                class="flex items-center justify-center w-full h-full rounded-full text-lg uppercase bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                            >
                                                {{
                                                    profile.organisationName.charAt(
                                                        0
                                                    )
                                                }}
                                            </div>
                                        }
                                    </div>
                                    <div class="ml-4 font-medium truncate">
                                        {{ profile.organisationName }}
                                    </div>
                                </div>
                            }
                            <button
                                class="ml-auto"
                                mat-icon-button
                                (click)="openNewChat()"
                            >
                                <mat-icon
                                    [svgIcon]="'heroicons_outline:plus-circle'"
                                ></mat-icon>
                            </button>
                        </div>
                        <!-- Search -->
                        <div class="mt-4">
                            <mat-form-field
                                class="fuse-mat-rounded fuse-mat-dense w-full"
                                [subscriptSizing]="'dynamic'"
                            >
                                <mat-icon
                                    matPrefix
                                    class="icon-size-5"
                                    [svgIcon]="
                                        'heroicons_solid:magnifying-glass'
                                    "
                                ></mat-icon>
                                <input
                                    matInput
                                    [autocomplete]="'off'"
                                    [placeholder]="'Search or start new chat'"
                                    (input)="filterChats(searchField.value)"
                                    #searchField
                                />
                            </mat-form-field>
                        </div>
                    </div>

                    <!-- Chats -->
                    <div class="flex-auto overflow-y-auto">
                        @if (filteredChats && filteredChats.length > 0) {
                            @for (
                                chat of filteredChats;
                                track trackByFn($index, chat)
                            ) {
                                <a
                                    class="z-20 flex flex-col py-5 px-8 cursor-pointer border-b"
                                    [ngClass]="{
                                        'hover:bg-gray-100 dark:hover:bg-hover':
                                            !selectedChat ||
                                            selectedChat.$id !== chat.$id,
                                        'bg-primary-50 dark:bg-hover':
                                            selectedChat &&
                                            selectedChat.$id === chat.$id
                                    }"
                                    (click)="setChat(chat)"
                                    [routerLink]="[chat.$id]"
                                >
                                    <div
                                        class="flex items-center justify-between"
                                    >
                                        <!-- Avatar and Contact Name -->
                                        @if (chat.contact) {
                                            <div class="flex items-center">
                                                <div
                                                    class="relative flex items-center justify-center w-10 h-10 mr-4"
                                                >
                                                    @if (chat.unreadCount > 0) {
                                                        <div
                                                            class="absolute bottom-0 right-0 flex-0 w-2 h-2 -ml-0.5 rounded-full ring-2 ring-bg-card dark:ring-gray-900 bg-primary dark:bg-primary-500 text-on-primary"
                                                            [class.ring-primary-50]="
                                                                selectedChat &&
                                                                selectedChat.$id ===
                                                                    chat.$id
                                                            "
                                                        ></div>
                                                    }

                                                    @if (
                                                        chat.contact &&
                                                        chat.contact.avatar
                                                    ) {
                                                        <img
                                                            class="w-full h-full rounded-full object-cover"
                                                            [src]="
                                                                chat.contact
                                                                    .avatar
                                                            "
                                                            alt="Contact avatar"
                                                        />
                                                    }

                                                    @if (
                                                        chat.contact &&
                                                        !chat.contact.avatar
                                                    ) {
                                                        <div
                                                            class="flex items-center justify-center w-full h-full rounded-full text-lg uppercase bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                                        >
                                                            {{
                                                                chat.contact.name.charAt(
                                                                    0
                                                                )
                                                            }}
                                                        </div>
                                                    }
                                                </div>
                                                <div
                                                    class="font-medium leading-5 truncate"
                                                    style="max-width: 70%"
                                                >
                                                    {{ chat.contact.name }}
                                                </div>
                                            </div>
                                        }

                                        <!-- Timestamps -->
                                        <div
                                            class="flex flex-col items-end ml-2"
                                            style="max-width: 30%"
                                        >
                                            <div
                                                class="text-sm leading-5 text-secondary truncate"
                                            >
                                                {{
                                                    chat.lastMessageAt
                                                        | date
                                                            : "MMM d, y, h:mm:ss a"
                                                }}
                                            </div>
                                            <div
                                                class="text-sm leading-5 text-secondary truncate"
                                            >
                                                {{
                                                    chat.lastMessageAt
                                                        | relativeTime
                                                }}
                                                <!-- Assuming you have a pipe or method to format this -->
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Second Row: Last Message and Possible Icon -->
                                    <div class="flex items-center mt-1">
                                        <div
                                            class="flex-1 leading-5 truncate text-secondary"
                                            [class.text-primary]="
                                                chat.unreadCount > 0
                                            "
                                            [class.dark:text-primary-500]="
                                                chat.unreadCount > 0
                                            "
                                        >
                                            {{ chat.lastMessage }}
                                        </div>
                                        @if (chat.muted) {
                                            <mat-icon
                                                class="icon-size-5 text-hint ml-2"
                                                [svgIcon]="
                                                    'heroicons_solid:speaker-x-mark'
                                                "
                                            ></mat-icon>
                                        }
                                    </div>
                                </a>
                            }
                        } @else {
                            <div
                                class="flex flex-auto flex-col items-center justify-center h-full"
                            >
                                <mat-icon
                                    class="icon-size-24"
                                    [svgIcon]="
                                        'heroicons_outline:chat-bubble-oval-left-ellipsis'
                                    "
                                ></mat-icon>
                                <div
                                    class="mt-4 text-2xl font-semibold tracking-tight text-secondary"
                                >
                                    @if (isLoading) {
                                        Loading...
                                    } @else {
                                        No chats!
                                    }
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </ng-container>

            <!-- Conversation -->
            @if (chats && chats.length > 0) {
                <div
                    class="flex-auto border-l"
                    [ngClass]="{
                        'z-20 absolute inset-0 lg:static lg:inset-auto flex':
                            selectedChat && selectedChat.id,
                        'hidden lg:flex': !selectedChat || !selectedChat.id
                    }"
                >
                    <router-outlet></router-outlet>
                </div>
            }
        </mat-drawer-content>
    </mat-drawer-container>
</div>

<div class="absolute inset-0 flex flex-col min-w-0 overflow-hidden">
    <mat-drawer-container class="flex-auto h-full">
        <!-- Drawer -->
        <mat-drawer
            class="w-72 dark:bg-gray-900"
            [autoFocus]="false"
            [mode]="drawerMode"
            [opened]="drawerOpened"
            #matDrawer
        >
            <!-- example sidebar options -->
            <div class="">
                <div class="mt-10 mx-6 text-3xl font-bold tracking-tighter">
                    example
                </div>
            </div>
            <div>
                <mat-calendar
                    (selectedChange)="onSelectedCalendarChange($event)"
                    [(selected)]="selectedDate"
                ></mat-calendar>
                <div class="text-xl text-center">
                    {{ selectedDate | date : "EEE, dd MMM yyyy" }}
                </div>
            </div>
        </mat-drawer>

        <!-- Drawer content -->
        <mat-drawer-content class="flex flex-col">
            <!-- Main -->
            <div class="flex-auto p-1 sm:p-5">
                <!-- Drawer toggle -->
                <button
                    class="mb-2"
                    mat-icon-button
                    (click)="matDrawer.toggle()"
                >
                    <mat-icon [svgIcon]="'heroicons_outline:bars-3'"></mat-icon>
                </button>

                <!-- CONTENT GOES HERE -->
                <div
                    class="flex flex-col flex-auto min-w-0 items-center justify-center w-full h-full border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-bray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600"
                >
                    @if (hasLoaded) {
                    <div class="flex flex-col flex-auto">
                        <google-map
                            height="400px"
                            width="750px"
                            [center]="center"
                            [zoom]="zoom"
                            (authFailure)="authFailure()"
                            (mapClick)="handleClick($event)"
                            (mapMousemove)="handleMove($event)"
                            (mapRightclick)="handleRightclick()"
                            [mapTypeId]="mapTypeId"
                            [mapId]="mapId"
                        >
                            <map-marker-clusterer
                                [imagePath]="markerClustererImagePath"
                            >
                                <map-marker
                                    #firstMarker="mapMarker"
                                    [position]="center"
                                    (mapClick)="clickMarker(firstMarker)"
                                ></map-marker>
                                @for (markerPosition of markerPositions; track
                                markerPosition) {
                                <map-marker
                                    #marker="mapMarker"
                                    [position]="markerPosition"
                                    [options]="markerOptions"
                                    (mapClick)="clickMarker(marker)"
                                ></map-marker>
                                }
                            </map-marker-clusterer>
                            @if (hasAdvancedMarker) {
                            <map-advanced-marker
                                #secondMarker="mapAdvancedMarker"
                                (mapClick)="clickAdvancedMarker(secondMarker)"
                                title="Advanced Marker"
                                [gmpDraggable]="false"
                                [content]="advancedMarkerContent"
                                [position]="mapAdvancedMarkerPosition"
                            >
                                <svg
                                    #advancedMarkerContent
                                    fill="oklch(69.02% .277 452.77)"
                                    viewBox="0 0 960 960"
                                    width="50px"
                                    height="50px"
                                    xml:space="preserve"
                                >
                                    <g>
                                        <polygon
                                            points="562.6,109.8 804.1,629.5 829.2,233.1 	"
                                        />
                                        <polygon
                                            points="624.9,655.9 334.3,655.9 297.2,745.8 479.6,849.8 662,745.8 	"
                                        />
                                        <polygon
                                            points="384.1,539.3 575.2,539.3 479.6,307 	"
                                        />
                                        <polygon
                                            points="396.6,109.8 130,233.1 155.1,629.5 	"
                                        />
                                    </g>
                                </svg>
                            </map-advanced-marker>
                            }
                            <map-info-window>Testing 1 2 3</map-info-window>
                            @if (isPolylineDisplayed) {
                            <map-polyline
                                [options]="polylineOptions"
                            ></map-polyline>
                            } @if (isPolygonDisplayed) {
                            <map-polygon
                                [options]="polygonOptions"
                            ></map-polygon>
                            } @if (isRectangleDisplayed) {
                            <map-rectangle
                                [options]="rectangleOptions"
                            ></map-rectangle>
                            } @if (isCircleDisplayed) {
                            <map-circle [options]="circleOptions"></map-circle>
                            } @if (isGroundOverlayDisplayed) {
                            <map-ground-overlay
                                [url]="groundOverlayUrl"
                                [bounds]="groundOverlayBounds"
                            ></map-ground-overlay>
                            } @if (isKmlLayerDisplayed) {
                            <map-kml-layer [url]="demoKml"></map-kml-layer>
                            } @if (isTrafficLayerDisplayed) {
                            <map-traffic-layer></map-traffic-layer>
                            } @if (isTransitLayerDisplayed) {
                            <map-transit-layer></map-transit-layer>
                            } @if (isBicyclingLayerDisplayed) {
                            <map-bicycling-layer></map-bicycling-layer>
                            } @if (directionsResult) {
                            <map-directions-renderer
                                [directions]="directionsResult"
                            ></map-directions-renderer>
                            } @if (isHeatmapDisplayed) {
                            <map-heatmap-layer
                                [data]="heatmapData"
                                [options]="heatmapOptions"
                            ></map-heatmap-layer>
                            }
                        </google-map>

                        <p><label>Latitude:</label> {{ display?.lat }}</p>
                        <p><label>Longitude:</label> {{ display?.lng }}</p>

                        <div>
                            <label for="map-type">
                                Select map type

                                <select
                                    id="map-type"
                                    (change)="mapTypeChanged($event)"
                                    #mapType
                                >
                                    @for (type of mapTypeIds; track type) {
                                    <option [value]="type">{{ type }}</option>
                                    }
                                </select>
                            </label>
                        </div>

                        <div>
                            <label for="polyline-checkbox">
                                Toggle Polyline
                                <input
                                    type="checkbox"
                                    (click)="togglePolylineDisplay()"
                                />
                            </label>
                        </div>
                        <div>
                            <label for="editable-polyline-checkbox">
                                Toggle Editable Polyline
                                <input
                                    type="checkbox"
                                    [disabled]="!isPolylineDisplayed"
                                    (click)="toggleEditablePolyline()"
                                />
                            </label>
                        </div>

                        <div>
                            <label for="polygon-checkbox">
                                Toggle Polygon
                                <input
                                    type="checkbox"
                                    (click)="togglePolygonDisplay()"
                                />
                            </label>
                        </div>
                        <div>
                            <label for="editable-polygon-checkbox">
                                Toggle Editable Polygon
                                <input
                                    type="checkbox"
                                    [disabled]="!isPolygonDisplayed"
                                    (click)="toggleEditablePolygon()"
                                />
                            </label>
                        </div>

                        <div>
                            <label for="rectangle-checkbox">
                                Toggle Rectangle
                                <input
                                    type="checkbox"
                                    (click)="toggleRectangleDisplay()"
                                />
                            </label>
                        </div>
                        <div>
                            <label for="editable-rectangle-checkbox">
                                Toggle Editable Rectangle
                                <input
                                    type="checkbox"
                                    [disabled]="!isRectangleDisplayed"
                                    (click)="toggleEditableRectangle()"
                                />
                            </label>
                        </div>

                        <div>
                            <label for="circle-checkbox">
                                Toggle Circle
                                <input
                                    type="checkbox"
                                    (click)="toggleCircleDisplay()"
                                />
                            </label>
                        </div>
                        <div>
                            <label for="editable-circle-checkbox">
                                Toggle Editable Circle
                                <input
                                    type="checkbox"
                                    [disabled]="!isCircleDisplayed"
                                    (click)="toggleEditableCircle()"
                                />
                            </label>
                        </div>

                        <div>
                            <label for="ground-overlay-checkbox">
                                Toggle Ground Overlay
                                <input
                                    type="checkbox"
                                    (click)="toggleGroundOverlayDisplay()"
                                />
                            </label>
                        </div>

                        <div>
                            <label for="ground-overlay-image">
                                Ground Overlay image

                                <select
                                    id="ground-overlay-image"
                                    (change)="groundOverlayUrlChanged($event)"
                                >
                                    @for (image of groundOverlayImages; track
                                    image) {
                                    <option [value]="image.url">
                                        {{ image.title }}
                                    </option>
                                    }
                                </select>
                            </label>
                        </div>

                        <div>
                            <label for="kml-layer-checkbox">
                                Toggle KML Layer
                                <input
                                    type="checkbox"
                                    (click)="toggleKmlLayerDisplay()"
                                />
                            </label>
                        </div>

                        <div>
                            <label for="traffic-layer-checkbox">
                                Toggle Traffic Layer
                                <input
                                    type="checkbox"
                                    (click)="toggleTrafficLayerDisplay()"
                                />
                            </label>
                        </div>

                        <div>
                            <label for="transit-layer-checkbox">
                                Toggle Transit Layer
                                <input
                                    type="checkbox"
                                    (click)="toggleTransitLayerDisplay()"
                                />
                            </label>
                        </div>

                        <div>
                            <label for="bicycling-layer-checkbox">
                                Toggle Bicycling Layer
                                <input
                                    type="checkbox"
                                    (click)="toggleBicyclingLayerDisplay()"
                                />
                            </label>
                        </div>

                        <div>
                            <label for="heatmap-layer-checkbox">
                                Toggle Heatmap Layer
                                <input
                                    type="checkbox"
                                    (click)="toggleHeatmapLayerDisplay()"
                                />
                            </label>
                        </div>

                        <div>
                            <label>
                                Toggle Advanced Marker
                                <input
                                    type="checkbox"
                                    (click)="toggleAdvancedMarker()"
                                />
                            </label>
                        </div>

                        <div>
                            <button mat-button (click)="calculateDirections()">
                                Calculate directions between first two markers
                            </button>
                        </div>
                    </div>
                    } @else {
                    <div>Loading Google Maps API...</div>
                    }
                </div>
            </div>
        </mat-drawer-content>
    </mat-drawer-container>
</div>

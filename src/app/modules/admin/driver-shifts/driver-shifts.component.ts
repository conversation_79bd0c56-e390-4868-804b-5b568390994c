import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FuseMediaWatcherService } from '@fuse/services/media-watcher';
import { GoogleMap, MapAdvancedMarker, MapInfoWindow, MapPolyline } from '@angular/google-maps';
import { Subject, catchError, takeUntil, throwError } from 'rxjs';
import { FuseCardComponent } from '@fuse/components/card';
import { DriverShift } from 'app/core/databaseModels/driverShifts/driverShifts.types';
import { TeamMembersService } from 'app/core/databaseModels/teammembers/teammembers.service';
import { DriverShiftsService } from 'app/core/databaseModels/driverShifts/driverShifts.service';
import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';
import { FuseLoadingService } from '@fuse/services/loading';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { VehicleService } from 'app/core/databaseModels/vehicle/vehicle.service';
import { Vehicle } from 'app/core/databaseModels/vehicle/vehicle.type';
import { PipesModule } from 'app/pipes/pipes.module';

// Add Marker interface


@Component({
    selector: 'app-driver-shifts',
    templateUrl: './driver-shifts.component.html',
    styleUrl: './driver-shifts.component.scss',
    encapsulation: ViewEncapsulation.None,
    //changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        FuseCardComponent,
        CommonModule,
        MatFormFieldModule,
        MatDatepickerModule,
        MatNativeDateModule,
        MatInputModule,
        MatSidenavModule,
        MatButtonModule,
        MatIconModule,
        GoogleMap,
        MapAdvancedMarker,
        MapPolyline,
        MapInfoWindow,
        MatProgressSpinnerModule,
        PipesModule
    ]
})
export class DriverShiftsComponent implements OnInit, OnDestroy, AfterViewInit {
    @ViewChild(MapInfoWindow) infoWindow: MapInfoWindow;
    @ViewChild(GoogleMap, { static: false }) set imap(m: GoogleMap) {
        if (m) {
            // setTimeout(() => { this.initDrawingManager(m); }, 0);

            Promise.resolve().then(() => {
                this.map = m; // Store reference to map
            });
        }
    }

    drawerMode: 'over' | 'side' = 'side';
    drawerOpened: boolean = true;
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    selectedShiftListDate: Date = new Date();
    driverShifts: DriverShift[] = [];
    teamMemberShifts: DriverShift[] = [];
    selectedDriverShift: DriverShift | null = null;
    teamMembers: TeamMember[] = [];
    teamMembersFiltered: TeamMember[] = [];
    vehicles: Vehicle[] = [];
    selectedVehicle: Vehicle | null = null;
    isLoading: boolean = true;
    showDriverNoteModal: boolean = false;

    mapId = '9852b6410bcc430e'
    path: google.maps.LatLngLiteral[] = [];

    map: GoogleMap | null = null; // Reference to the map
    center: google.maps.LatLngLiteral = {
        lat: -24,
        lng: 134
    };
    zoom = 4;
    options: google.maps.MapOptions = {
        controlSize: 24,
        fullscreenControl: false,

        mapTypeControlOptions: {
            position: google.maps.ControlPosition.RIGHT_TOP
        },
        zoomControlOptions: {
            position: google.maps.ControlPosition.RIGHT_BOTTOM
        }

    };
    markers = [];
    polylineOptions = {
        path: [],
        strokeColor: '#32a1d0',
        strokeOpacity: 1.0,
        strokeWeight: 2,
    };

    constructor(private _fuseMediaWatcherService: FuseMediaWatcherService,
        private _teamMembersService: TeamMembersService,
        private _driverShiftsService: DriverShiftsService,
        private _changeDetectorRef: ChangeDetectorRef,
        private _vehicleService: VehicleService,
        private _fuseLoadingService: FuseLoadingService,
    ) {

    }



    ngOnInit(): void {
        // Subscribe to media changes
        this._fuseMediaWatcherService.onMediaChange$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe(({ matchingAliases }) => {
                // Set the drawerMode and drawerOpened
                if (matchingAliases.includes('lg')) {
                    this.drawerMode = 'side';
                    this.drawerOpened = true;
                }
                else {
                    this.drawerMode = 'over';
                    this.drawerOpened = false;
                }
            });
        /// Load Team Members
        // this._teamMembersService.listenToRealTimeData();

        this._teamMembersService.getTeamMembers().subscribe((teamMembers) => {

            this.isLoading = true;
            this._fuseLoadingService.show();
            this._fuseLoadingService.hide();
            this.teamMembers = teamMembers;
            // this.teamMembersFiltered = teamMembers;
            // Assign the data to the data source for the table to render
            //
            this.isLoading = false;
            // Mark for check
            this._changeDetectorRef.markForCheck();
            this._changeDetectorRef.detectChanges();
            this.loadDriverShifts(new Date());

        });

        this._vehicleService.getVehicles().subscribe((vehicles) => {
            if (vehicles.length > 0) {
                this.vehicles = vehicles;
            }
        })



    }
    ngAfterViewInit(): void {
        //this.loadDriverShifts(this.selectedShiftListDate);
    }
    loadDriverShifts(selectedShiftListDate: Date) {
        this.teamMemberShifts = [];
        this.teamMembersFiltered = [];
        this._fuseLoadingService.show();
        this.isLoading = true;
        try {
            this._driverShiftsService.getDriverShiftsByDate(selectedShiftListDate).subscribe((driverShifts) => {
                this.driverShifts = driverShifts;
                this._fuseLoadingService.hide();
                console.log(this.driverShifts);
                if (this.driverShifts.length > 0) {
                    //this.selectedShiftListDate = this.driverShifts[0].startShift;
                    this.teamMembersFiltered = this.teamMembers.filter((teamMember) =>
                        driverShifts.some((shift) => shift.teamMemberId === teamMember.$id)
                    );
                } else {

                    this.teamMembersFiltered = [];
                }
                this.isLoading = false;
                // Mark for check
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
            });
        } catch (error) {
            console.error('Error:', error);
        }

    }

    onSelectedCalendarChange(selectedDate: Date) {

        this.loadDriverShifts(selectedDate);
        // console.log('onSelectedJobLisCalendarChange:', selectedDate);

    }

    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    formatDutyStatus(status: string): string {
        switch (status) {
            case 'offDuty':
                return 'Off Duty';
            case 'onDuty':
                return 'On Duty';
            case 'driving':
                return 'Driving';
            case 'breaking':
                return 'Breaking';
            default:
                return status;
        }
    }

    loadSelectedTeamMemberShifts(selectedTeamMemberId: string) {
        this.selectedDriverShift = null;
        this.teamMemberShifts = this.driverShifts.filter((shift) => shift.teamMemberId === selectedTeamMemberId);
        console.log(this.teamMemberShifts);

    }

    loadSelectedShift(selectedShift: DriverShift) {
        this.selectedDriverShift = null;
        // Clear existing markers and polylines
        this.clearMapData();

        this.selectedVehicle = this.vehicles.find((vehicle) => vehicle.$id === selectedShift.selectedVehicleID);

        setTimeout(() => {
            this.selectedDriverShift = selectedShift;
            // console.log(this.selectedDriverShift.locationGPSData)

            if (this.selectedDriverShift.locationGPSData) {


                // console.log(customIconUrl);
                this.path = [];
                this.path = this.parseLocationData(this.selectedDriverShift.locationGPSData);
                this.polylineOptions = { ...this.polylineOptions, path: this.path };
                if (this.path.length > 0) {
                    this.markers = [];
                    this.markers.push({
                        id: 0,
                        position: this.path[0],
                        title: 'Start',
                        content: this.buildContent('fa-map-marker-alt', 'red-400', 'Start'),
                    },
                        {
                            id: 1,
                            position: this.path[this.path.length - 1],
                            title: 'End',
                            content: this.buildContent('fa-flag', 'blue-800', 'End'),
                        });
                    this.fitBoundsToPolyline();

                }
            }
        }, 10); // Delay of 1000 milliseconds (1 second)

    }

    parseLocationData(locationGPSData: string[]): google.maps.LatLngLiteral[] {
        try {
            // Map over the array of stringified JSON objects
            const parsedArray = locationGPSData.map((item: string) => {
                try {
                    return JSON.parse(item);
                } catch (error) {
                    console.error("Error parsing item:", item, error);
                    return null; // Return null for items that couldn't be parsed
                }
            }).filter(item => item !== null); // Filter out null values

            // Map over the parsed array to extract lat and lng
            return parsedArray.map((loc: any) => ({ lat: loc.Lat, lng: loc.Lon }));
        } catch (error) {
            console.error("Error parsing JSON:", error);
            return [];
        }
    }

    fitBoundsToPolyline() {
        const bounds = new google.maps.LatLngBounds();
        this.path.forEach((latLng) => {
            bounds.extend(latLng);
        });

        // Assuming 'this.map' is the GoogleMap instance
        this.map.fitBounds(bounds);
        // this.markers.push(path[0], (path[path.length - 1]));
        this.map


    }

    clearMapData(): void {
        // Clear the polyline path
        this.polylineOptions = { ...this.polylineOptions, path: [] };

        // Clear the markers array
        this.markers = [];

        this._changeDetectorRef.markForCheck();
    }




    buildContent(iconClass: string, color: string, title: string): HTMLElement {
        // Create the SVG element as the marker's content
        const content = document.createElement('div');
        content.className = 'text-center', 'p-2';
        content.innerHTML = `
          <div class="text-${color} text-xl">
            <span class="fa-stack" style="vertical-align: top;">
              <i class="fa-duotone fa-solid fa-circle fa-stack-2x"></i>
              <i class="fa-duotone fa-solid ${iconClass} fa-stack-1x fa-inverse"></i>
            </span>
          </div>
        `;
        return content;
    }


    clickAdvancedMarker(advancedMarker: any) {

        this.infoWindow.openAdvancedMarkerElement(
            advancedMarker.advancedMarker,
            advancedMarker.advancedMarker.title,

        );
        //advancedMarker.content.classList.toggle('bg-gray-200', 'p-4', 'rounded-lg', 'shadow-lg');

    }



}

<div class="absolute inset-0 flex flex-col min-w-0 overflow-hidden">
  <mat-drawer-container class="flex-auto h-full">
    <!-- Drawer -->
    <mat-drawer
      class="w-72 dark:bg-gray-900"
      [autoFocus]="false"
      [mode]="drawerMode"
      [opened]="drawerOpened"
      #matDrawer
      >
      <!-- Driver Shifts sidebar options -->
      <div class="">
        <div class="mt-10 mx-6 text-3xl font-bold tracking-tighter">
          Team Members Shifts
        </div>
      </div>
      <div>
        <mat-calendar
          (selectedChange)="onSelectedCalendarChange($event)"
          [(selected)]="selectedShiftListDate"
        ></mat-calendar>
        <div class="text-xl text-center">
          {{ selectedShiftListDate | date: "EEE, dd MMM yyyy" }}
        </div>
      </div>
    </mat-drawer>

    <!-- Drawer content -->
    <mat-drawer-content class="flex flex-col">
      <!-- Main -->
      <div class="flex-auto p-1 sm:p-5">
        <!-- Drawer toggle -->
        <button
          class="mb-2"
          mat-icon-button
          (click)="matDrawer.toggle()"
          >
          <mat-icon [svgIcon]="'heroicons_outline:bars-3'"></mat-icon>
        </button>

        <!-- CONTENT GOES HERE -->
        <div class="min-h-screen bg-gray-100 dark:bg-gray-900 p-8">
          <!-- Container for the whole page -->
          <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
            <!-- Left Column: Tracking Delivery List -->
            <div
              class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg lg:col-span-1"
              >
              <h2
                class="text-xl font-semibold mb-4 text-gray-900 dark:text-white"
                >
                Team Members
              </h2>
              <!-- Search and filters would go here -->
              <!-- ... -->

              <!-- TemMembers  -->

              @if (isLoading) {
                <div
                  role="status"
                  class="p-4 space-y-4 max-w-md divide-y divide-gray-200 shadow animate-pulse dark:divide-gray-700 md:p-6"
                  >
                  <div class="flex justify-center p-10">
                    <mat-spinner
                      class="spinnr"
                      [diameter]="24"
                      >
                    </mat-spinner>
                    <div class="ml-5 text-center">
                      Loading...
                    </div>
                  </div>
                  <div
                    class="flex justify-between items-center"
                    >
                    <div>
                      <div
                        class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"
                      ></div>
                      <div
                        class="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"
                      ></div>
                    </div>
                    <div
                      class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"
                    ></div>
                  </div>
                  <div
                    class="flex justify-between items-center pt-4"
                    >
                    <div>
                      <div
                        class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"
                      ></div>
                      <div
                        class="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"
                      ></div>
                    </div>
                    <div
                      class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"
                    ></div>
                  </div>
                  <div
                    class="flex justify-between items-center pt-4"
                    >
                    <div>
                      <div
                        class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"
                      ></div>
                      <div
                        class="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"
                      ></div>
                    </div>
                    <div
                      class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"
                    ></div>
                  </div>
                  <div
                    class="flex justify-between items-center pt-4"
                    >
                    <div>
                      <div
                        class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"
                      ></div>
                      <div
                        class="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"
                      ></div>
                    </div>
                    <div
                      class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"
                    ></div>
                  </div>
                  <div
                    class="flex justify-between items-center pt-4"
                    >
                    <div>
                      <div
                        class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"
                      ></div>
                      <div
                        class="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"
                      ></div>
                    </div>
                    <div
                      class="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"
                    ></div>
                  </div>
                  <span class="sr-only">Loading...</span>
                </div>
              } @else {
                @for (
                  teamMember of teamMembersFiltered;
                  track teamMember.$id
                  ) {
                  <div
                    class="cursor-pointer mb-4 p-4 bg-gray-100 dark:bg-gray-900 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-800 active:bg-gray-300 dark:active:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-300 focus:ring-opacity-50"
                                        (click)="
                                            loadSelectedTeamMemberShifts(
                                                teamMember.$id
                                            )
                                        "
                    >
                    <div
                      class="flex justify-between items-center"
                      >
                      <!-- <div
                      class="h-8 w-8 rounded-full bg-white dark:bg-neutral-700"
                    ></div> -->
                    <h3
                      class="text-lg font-medium text-gray-900 dark:text-white"
                      >
                      {{ teamMember.name }}
                    </h3>
                    <span
                      class="px-3 py-1 text-white text-sm rounded-full"
                                                [ngClass]="{
                                                    'bg-red-500':
                                                        teamMember.dutyStatus ===
                                                        'offDuty',
                                                    'bg-blue-500':
                                                        teamMember.dutyStatus ===
                                                        'onDuty',
                                                    'bg-green-500':
                                                        teamMember.dutyStatus ===
                                                        'driving',
                                                    'bg-amber-500':
                                                        teamMember.dutyStatus ===
                                                        'breaking'
                                                }"
                      >{{
                      formatDutyStatus(
                      teamMember.dutyStatus
                      )
                      }}</span
                      >
                    </div>
                    <!-- Timeline would go here -->
                    <!-- ... -->
                  </div>
                  } @empty {
                  <div class="justify-center">
                    <p class="text-2xl">
                      No Shifts for Yet.
                    </p>
                    <p>
                      Once Team Member Start shift,
                      they'll show up here.
                    </p>
                  </div>
                }
              }
            </div>

            @if (teamMemberShifts.length > 0) {
              <!-- Right Column: Order Details and Map -->
              <div class="lg:col-span-3">
                <!-- Main Info -->
                <div
                  class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg"
                  >
                  <h2
                    class="text-xl font-semibold mb-4 text-gray-900 dark:text-white"
                    >
                    Shift info
                  </h2>
                  <!-- Video and signature  content would go here -->
                  <div class="mt-4 flex flex-col md:flex-row">
                    <div
                      class="grow bg-gray-300 rounded-xl dark:bg-gray-600 mb-2 md:mb-0 md:mr-2 p-5"
                      >
                      @for (
                        teamMemberShift of teamMemberShifts;
                        track teamMemberShift.$id;
                        let i = $index
                        ) {
                        <div
                          class="cursor-pointer p-4 mb-4 bg-gray-100 dark:bg-gray-900 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-800 active:bg-gray-300 dark:active:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-300 focus:ring-opacity-50"
                                                    (click)="
                                                        loadSelectedShift(
                                                            teamMemberShift
                                                        )
                                                    "
                          >
                          <div
                            class="flex justify-between items-center"
                            >
                            <div
                              class="h-8 w-8 rounded-full text-xl text-center bg-white dark:bg-neutral-700 text-gray-900 dark:text-white"
                              >
                              {{ i + 1 }}
                            </div>
                            <div
                              class="flex flex-col"
                              >
                              <h3
                                class="text-xs font-medium text-gray-900 dark:text-white"
                                >
                                Start:
                                {{
                                teamMemberShift.startShift
                                | date
                                : "dd hh:mm a"
                                }}
                              </h3>
                              <h3
                                class="text-xs font-medium text-gray-900 dark:text-white"
                                >
                                End:
                                {{
                                teamMemberShift.endShift
                                | date
                                : "dd hh:mm a"
                                }}
                              </h3>
                            </div>

                            <span
                              class="px-3 py-1 text-white text-sm rounded-full"
                                                            [ngClass]="{
                                                                'bg-red-500':
                                                                    teamMemberShift.status ===
                                                                    'offDuty',
                                                                'bg-blue-500':
                                                                    teamMemberShift.status ===
                                                                    'onDuty',
                                                                'bg-green-500':
                                                                    teamMemberShift.status ===
                                                                    'driving',
                                                                'bg-amber-500':
                                                                    teamMemberShift.status ===
                                                                    'breaking'
                                                            }"
                              >
                              {{
                              formatDutyStatus(
                              teamMemberShift.status
                              )
                              }}
                            </span>
                          </div>
                        </div>
                      }
                    </div>
                    @if (selectedDriverShift) {
                      <fuse-card
                        class="flex flex-col md:flex-row max-w-80 md:max-w-160 w-full filter-shopping"
                        >
                        <div
                          class="flex-0 w-80 md:w-60"
                          >
                          <video
                            class="w-full h-full object-cover"
                            controls
                            >
                            <source
                                                            [src]="
                                                                selectedDriverShift.videoFileUrl
                                                            "
                              type="video/mp4"
                              />
                            Your browser does not
                            support the video tag.
                          </video>
                        </div>
                        <div
                          class="flex flex-col flex-auto m-8 mb-4 dark:bg-gray-800"
                          >
                          <div class="flex-col">
                            <!-- Vehicle Info -->
                            <div
                              class="flex items-center mt-4 mb-4"
                              >
                              <div
                                class="p-2 bg-blue-100 dark:bg-blue-900 rounded-full mr-3"
                              ></div>
                              <div>
                                <div
                                  class="text-l font-bold text-gray-800 dark:text-gray-200"
                                  >
                                  Selected
                                  Vehicle
                                </div>
                                <div
                                  class="text-l text-gray-600 dark:text-gray-400"
                                  >
                                  <i
                                    class="fas fa-car mr-2"
                                  ></i>
                                  {{
                                  selectedVehicle.vehicleName
                                  }}
                                </div>
                              </div>
                            </div>
                            <div
                              class="flex flex-row"
                              >
                              <!-- Timeline -->
                              <div
                                class="relative pl-8"
                                >
                                <!-- Vertical line -->
                                <div
                                  class="absolute left-7 top-2 bottom-10 border-l-2 border-dotted border-gray-200 dark:border-gray-600"
                                ></div>

                                <!-- Dot 1 -->
                                <div
                                  class="mb-4"
                                  >
                                  <div
                                    class="absolute left-8 -ml-2.5 mt-0.5 bg-green-500 rounded-full h-4 w-4 border-2 border-white z-10"
                                  ></div>
                                  <div
                                    class="text-sm font-semibold text-gray-800 dark:text-gray-200 ml-5"
                                    >
                                    {{
                                    selectedDriverShift.startShift
                                    | date
                                    : "HH:mm"
                                    }}
                                  </div>
                                  <div
                                    class="text-sm text-gray-800 dark:text-gray-200 ml-2"
                                    >
                                    Start
                                  </div>
                                </div>

                                <!-- Dot 2 -->
                                <div>
                                  <div
                                    class="absolute left-8 -ml-2.5 bg-blue-400 rounded-full h-4 w-4 border-2 border-white z-10"
                                  ></div>
                                  <div
                                    class="text-sm font-semibold text-gray-800 dark:text-gray-200 ml-5"
                                    >
                                    @if (
                                      selectedDriverShift.endShift
                                      ) {
                                      {{
                                      selectedDriverShift.endShift
                                      | date
                                      : "HH:mm"
                                      }}
                                    } @else {
                                      N/A
                                    }
                                  </div>
                                  <div
                                    class="text-sm text-gray-800 dark:text-gray-200 ml-2"
                                    >
                                    Finished
                                  </div>
                                </div>
                              </div>
                              <div
                                class="flex-col ml-2 p-5"
                                >
                                <img
                                                                    [src]="
                                                                        selectedDriverShift.signatureFileImageUrl
                                                                    "
                                  alt="signature image"
                                  />
                              </div>
                            </div>
                          </div>

                          <hr
                            class="w-full border-b mt-6 mb-2 dark:border-gray-700"
                            />
                          <div
                            class="flex justify-between items-center md:mt-auto -mx-3"
                            >
                            <div
                              class="grid grid-cols-3 divide-x dark:divide-gray-700"
                              >
                              @if (
                                selectedDriverShift.notes
                                ) {
                                <div
                                  class="pr-2"
                                  >
                                  <button
                                    class="bg-amber-600 text-white px-6 py-2 rounded-full text-sm focus:outline-none hover:bg-yellow-500 dark:hover:bg-yellow-600"
                                    data-bs-toggle="modal"
                                    data-bs-target="#exampleModal"
                                                                        (click)="
                                                                            showDriverNoteModal = true
                                                                        "
                                    >
                                    Driver
                                    Note
                                  </button>
                                </div>
                                <!-- Modal -->
                                @if (
                                  showDriverNoteModal
                                  ) {
                                  <div
                                    class="fixed top-0 left-0 flex items-center justify-center w-full h-full"
                                    >
                                    <div
                                      class="modal-dialog relative w-auto pointer-events-none"
                                      >
                                      <div
                                        class="modal-content border-none shadow-lg relative flex flex-col w-full pointer-events-auto bg-white bg-clip-padding rounded-md outline-none text-current"
                                        >
                                        <!-- Modal header -->
                                        <div
                                          class="modal-header flex flex-shrink-0 items-center justify-between p-4 border-b border-gray-200 rounded-t-md"
                                          >
                                          <h5
                                            class="text-xl font-medium leading-normal text-gray-800"
                                            id="exampleModalLabel"
                                            >
                                            Driver
                                            Note
                                          </h5>
                                          <button
                                            type="button"
                                            class="text-black"
                                                                                    (click)="
                                                                                        showDriverNoteModal = false
                                                                                    "
                                            aria-label="Close"
                                            >
                                            ✕
                                          </button>
                                        </div>
                                        <!-- Modal body -->
                                        <div
                                          class="modal-body relative p-4"
                                          >
                                          {{
                                          selectedDriverShift.notes
                                          }}
                                        </div>
                                        <!-- Modal footer -->
                                        <div
                                          class="modal-footer flex flex-shrink-0 flex-wrap items-center justify-end p-4 border-t border-gray-200 rounded-b-md"
                                          >
                                          <button
                                            type="button"
                                            class="bg-purple-600 text-white ..."
                                            class="px-6 py-2.5 bg-purple-600 text-white font-medium text-xs leading-tight uppercase rounded shadow-md hover:bg-purple-700 hover:shadow-lg focus:bg-purple-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-purple-800 active:shadow-lg transition duration-150 ease-in-out"
                                                                                    (click)="
                                                                                        showDriverNoteModal = false
                                                                                    "
                                            >
                                            Close
                                          </button>
                                          <!-- Save button here -->
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                }
                              }
                              <div class="pl-2">
                                <div
                                  class="text-sm font-semibold text-gray-800 dark:text-gray-200"
                                  >
                                  Total Break
                                </div>
                                <div
                                  class="text-sm text-gray-800 dark:text-gray-200"
                                  >
                                  {{
                                  selectedDriverShift.totalBreakDuration
                                  | minutesToTime
                                  }}
                                </div>
                              </div>
                              <div class="pl-2">
                                <div
                                  class="text-sm font-semibold text-gray-800 dark:text-gray-200"
                                  >
                                  Shift
                                  Duration
                                </div>
                                <div
                                  class="text-sm text-gray-800 dark:text-gray-200"
                                  >
                                  {{
                                  selectedDriverShift.shiftDuration
                                  | minutesToTime
                                  }}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </fuse-card>
                    } @else {
                      <div
                        class="flex flex-col md:flex-row max-w-80 md:max-w-160 w-full filter-shopping"
                      ></div>
                    }
                  </div>
                </div>
              </div>
              <!-- Shift Details -->
              <div
                class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg col-span-4 min-h-200"
                >
                <div
                  class="flex justify-between items-center mb-4"
                  >
                  <h2
                    class="uppercase text-xl font-semibold text-gray-900 dark:text-white"
                    >
                    MAP Overview
                  </h2>
                  @if (path.length > 0) {
                    <button
                      class="uppercase bg-slate-900 dark:bg-slate-700 text-white px-4 py-2 rounded-lg"
                      mat-button
                      (click)="fitBoundsToPolyline()"
                      matTooltip="Fit map to polyline"
                      >
                      fit to map
                    </button>
                  }
                </div>
                <!-- Map would go here -->
                <div
                  class="h-4/5 flex flex-grow overflow-hidden bg-gray-300 dark:bg-gray-700 rounded-lg mb-4"
                  >
                  <google-map
                    class="flex flex-grow rounded-lg"
                    width="100%"
                    height="100%"
                    [center]="center"
                    [zoom]="zoom"
                    [options]="options"
                    style="border-radius: inherit"
                    [mapId]="mapId"
                    >
                    @for (
                      marker of markers;
                      track marker.id
                      ) {
                      <!-- <map-marker
                      [position]="marker.position"
                      [label]="marker.label"
                      [title]="marker.title"
                      [options]="marker.options"
                      >
                    </map-marker> -->
                    <map-advanced-marker
                      #secondMarker="mapAdvancedMarker"
                                                (mapClick)="
                                                    clickAdvancedMarker(
                                                        secondMarker
                                                    )
                                                "
                      [title]="marker.title"
                      [gmpDraggable]="false"
                      [content]="marker.content"
                      [position]="marker.position"
                      >
                    </map-advanced-marker>
                  }
                  <map-info-window></map-info-window>
                  <map-polyline
                    [options]="polylineOptions"
                  ></map-polyline>
                </google-map>
              </div>

              <!-- movement Stats -->
              <div class="grid grid-cols-2 gap-4">
                <!-- ... -->
              </div>
            </div>
          } @else {
            <div class="lg:col-span-3">
              <div
                class="flex flex-auto flex-col items-center justify-center bg-gray-100 dark:bg-transparent"
                >
                <i
                  class="icon-size-24 fa-duotone fa-ballot-check"
                ></i>
                <div
                  class="mt-4 text-2xl font-semibold tracking-tight text-secondary"
                  >
                  None of the shifts have been chosen.
                </div>
              </div>
            </div>
          }
        </div>
      </div>
    </div>
  </mat-drawer-content>
</mat-drawer-container>
</div>


@import "@bryntum/scheduler/scheduler.material.css";

/* width */
::-webkit-scrollbar {
    width: 5px;
}

/* Track */
::-webkit-scrollbar-track {

    border-radius: 5px;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #000;
    border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #555;
}

::ng-deep .mat-option-selected.mat-selected:not(.mat-active) .mat-option-pseudo-checkbox {
    display: none;
  }

google-map {
    width: 100% !important;
    height: 100% !important;
  }

.mat-mdc-mini-fab {
    border-radius: 25px !important;
}





mat-tab-group .mdc-tab {
    min-width: 60px;
    padding-right: 15px;
    padding-left: 15px;
  }
// bryntum
bryntum-scheduler {
    flex : 1;

    .b-sch-event {
        background : #fff;
        border-radius : 3px;
    }

    &.b-highlighting {
        .b-sch-event {
            background : #fff;
            opacity : 0.2;

            &.b-match {
                opacity    : 1;
                box-shadow : 0 0 10px red;
            }
        }
    }
    .event-content {
        padding: 5px;
        
        .event-title-section {
            display: flex;
            align-items: flex-start;
            
            .meeting-icon {
                margin-right: 5px;
                margin-top: 2px;
            }
            
            .title-container {
                flex: 1;
                
                .job-title {
                    display: block;
                    font-weight: bold;
                    margin-bottom: 2px;
                }
                
                .customer-name {
                    font-size: 0.85em;
                    color: #666;
                }
            }
        }
    }

}

body:not(.b-theme-classic-dark) .b-grid-subgrid-normal {
    background: #f3f4f5;
  }
  body:not(.b-theme-classic-dark) .b-column-line {
    border-color: #d8d9da;
  }
  body:not(.b-theme-classic-dark) .b-grid-headers-locked .b-grid-header {
    background: #fff;
    color: #000;
  }
  body:not(.b-theme-classic-dark) .b-sch-event {
    background-color: #fff;
    box-shadow: 1px 2px 4px #ddd;
  }
  body:not(.b-theme-classic-dark) .b-sch-event-content {
    color: #333 !important;
  }
  body:not(.b-theme-classic-dark) .b-resource-avatar {
    border: 1px solid #fff;
  }
  body:not(.b-theme-classic-dark) .b-type-timeoff {
    background-image: linear-gradient(-45deg, rgba(0, 0, 0, 0) 46%, #ccc 49%, #ccc 52%, rgba(0, 0, 0, 0) 49%);
  }

  .b-theme-classic-dark .b-resource-avatar {
    border: 1px solid #333;
  }
  .b-theme-classic-dark .b-sch-event {
    box-shadow: 1px 1px 3px #222;
    background-color: #fff;
  }
  .b-theme-classic-dark .b-type-timeoff {
    background-image: linear-gradient(-45deg, rgba(0, 0, 0, 0) 46%, #666 49%, #666 52%, rgba(0, 0, 0, 0) 49%);
  }
  .b-theme-classic-dark .b-eventeditor .b-buttongroup .b-button:not(.b-pressed) {
    background: transparent;
  }

  .b-sch-event {
    border-radius: 0.5em;
  }

  .b-type-timeoff {
    background-size: 7px 7px !important;
  }



  .b-sch-event-content {
    flex-direction: column;
    width: 100%;
    margin: 0 0.75em !important;
    height: 100%;
    justify-content: space-evenly;
  }
  .b-sch-event-content .header,
  .b-sch-event-content .footer {
    display: flex;

  }
  .b-sch-event-content .header {
    align-items: flex-start;
  }
  .b-sch-event-content .footer {
    align-items: center;
  }
  .b-sch-event-content .footer span {
    margin-inline-end: auto;
  }
  .b-sch-event-content .b-fa-ellipsis {
    position: relative;
    top: -0.5em;
    margin-inline-start: auto;
    margin-inline-end: 0 !important;
  }
  .b-sch-event-content .b-resource-avatar {
    margin-inline-start: -0.7em;
  }
  .b-sch-event-content .b-resource-avatar:hover {
    z-index: 10;
  }

  .progress-outer {
    display: flex;
    width: 0.5em;
    height: 0.5em;
    background: #eee;
    border-radius: 0.5em;
    background: rgba(0, 118, 248, 0.1333333333);
  }
  .progress-outer .progress-fill {
    background: #0076f8;
    border-radius: inherit;
  }
  .b-scheduler:not(.b-grid-refreshing) .progress-outer .progress-fill {
    transition: width 0.3s;
  }


/* Left group for progress and files icon */
.progress-outer,
.fa-comment-image {
    margin-right: 4px;
    flex-shrink: 0;
}
.fa-comment-image {
    color: #4f46e5;
}

/* Center group for time display */
.time-display {
    flex-grow: 1;
    text-align: center;
}

/* Right group for status icon */
.b-sch-event .header > .fa-solid,
.b-sch-event .header > .fa-regular {
    flex-shrink: 0;
    margin-left: 4px;
}

.b-time{
    font-size   : 1.2em;
    font-weight : 500;
}
.b-date{
    color     : #999;
    font-size : .8em;

    &:not(.b-duration ) {
        margin-top : .2em;
    }
}

.b-sch-event-wrap:not(.b-milestone-wrap) {
    border-radius : .15em;
  
    .b-sch-event-content {
        width           : 100%;
        height          : 100%;
        flex-direction  : column;
        justify-content : space-around;
    }

   
    .b-sch-event {
        box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.25);
        border-left: 8px solid ;
        border-radius: .5em;
        opacity: .9;
        .header,
        .footer {
            display     : flex;
            align-items : center;
        }

      

        .outbound,
        .inbound {
            font-size   : 1.2em;
            font-weight : 600;
        }

        .aircraft {
            border-radius : 1em;
            padding       : 0.2em 0.6em;
            font-size     : .8em;
        }

        .box {
            height            : 1em;
            width             : 1em;
            border            : 1px solid var(--event-border-color);
            border-radius     : 4px;
            margin-inline-end : 0.1em;
           
            &.filled {
                background : var(--event-border-color);
            }

            &:not(.filled):hover {
                background : var(--event-border-color);
                opacity    : .3;
            }
        }

        .value {
            margin-inline-start : auto;
            opacity             : .5;
        }

        .b-fa-star {
            transition : opacity .2s;

            &:not(.starred) {
                opacity : .2;
            }
        }
    }
}

.b-sch-event:not(.b-milestone) .b-fa-plane-departure {
    margin-inline : 0.2em 0.5em;
    margin-block  : 0;
    font-size     : .9em;
}


.b-sch-event-tooltip .b-tooltip-content {


    align-items    : center;
    background-color: #fff;
    padding: 0;
    margin: 0;
    flex-wrap      : nowrap;
    height         : 25em;
    width:  18em;

    .b-icons-container {

        flex-direction : column;
        align-items    : center;
        width          : 5em;
        margin-top     : -1.1em;
    }

    .b-circle {
        height        : .5em;
        width         : .5em;
        border        : 1px solid #373d45;
        border-radius : 50%;
        margin-bottom : .5em;
    }

    .b-vertical-line {
        display     : flex;
        height      : 9.7em;
        border-left : 1px dotted #999;
    }



}
.b-sch-timeaxis-cell {
    background-color: #fff; /* Use your desired color */
}
 .b-sch-timeaxis-cell:hover {
    background-color: #edeef0; /* Use your desired color */
}

.b-dragging-event {
    cursor: copy;
   /* Change to url('path_to_cursor_image'), auto; for custom images */
}
// .b-sch-timeaxis-cell:hover{
//     background-color: #0076f8;
// }

.drop-target{
    background-color: #bcc4f5;
}
// .b-schedule-selected-tick{
//     background-color: #0076f8;
//     border: 1px solid #0076f8;
//     border-radius: 50%;
//     width: 1em
// }

.employee {
    flex: 1;
    display: grid;
    column-gap: 1em;
    row-gap: 0.5em;
    align-items: start;
    margin-top: 1em;
    grid-template-columns: auto 1fr auto;
    max-width: 165px;
  }
  .employee img {
    border-radius: 50%;
    width: 3em;
    height: 3em;
    grid-column: 1;
    grid-row: 1/4;
    border: 3px solid currentColor;
  }
  .employee .name {
    grid-column: 2;
    font-size: 1.1em;
    font-weight: bold;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  .employee .dutyStatus {
    grid-column: 2;
    font-size: 0.9em;
  }
  .employee .dutyStatus:hover {
    text-decoration: underline;
    cursor: pointer;
    font-weight: bold;

}
  .employee .team {
    grid-column: 2;
    font-size: 0.9em;
  }
  .employee .tasks,
  .employee .duration {
    grid-column: 3;
    color: #999;
    font-size: 0.9em;
  }
  .employee .tasks::before,
  .employee .duration::before {
    margin-inline-end: 0.25em;
  }
  .employee .tasks {
    grid-row: 1;
  }
  .employee .duration {
    grid-row: 2;
  }

  .employee .employee-initial {
    display: flex;
    align-items: center;
    justify-content: center;
    grid-column: 1;
    grid-row: 1/4;
    width: 42px; /* Match the image size */
    height: 42px; /* Match the image size */
    border-radius: 50%; /* Creates the round shape */
    background-color: #fefefe; /* Example background color, adjust as needed */
    color: #7c7c7c; /* Adjust text color as needed */

    font-size: 1.2em; /* Adjust font size as needed */
    border: 3px solid currentColor; /* Matches the image border styling */
}
.employee .border-foreground-red {
    border: 3px solid #f00;
}
.employee .border-foreground-green {
    border: 3px solid #0f0;
}
.employee .border-foreground-blue {
    border: 3px solid #00f;
}
.employee .border-foreground-deep-orange {
    border: 3px solid #ff8f00;
}


.mat-calendar-body-in-preview {
    color: #ff4081 !important;
}


.custom-scrollbar::-webkit-scrollbar {
    width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.3);
    border-radius: 2px;
}

.mat-mdc-form-field-subscript-wrapper {
    display: none !important;
}

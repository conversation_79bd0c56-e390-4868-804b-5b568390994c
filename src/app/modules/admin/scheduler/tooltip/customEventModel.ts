// CustomEventModel.ts
import { EventModel, EventModelConfig } from '@bryntum/scheduler';

export interface EventModelCustomFields {
    type: string;
    progress?: number;
    address?: string;
    customerName?: string;
    customerPhone?: string;
    jobId?: string;
    icon?: string;
    dueDate: Date
    jobStatus?: string;
    driverAttachedFiles?: boolean;
}
export class CustomEventModel extends EventModel implements EventModelCustomFields {
    static get $name() {
        return 'CustomEventModel';
    }
    static override get fields() {
        return [
            { name: 'type', type: 'string' },
            { name: 'resourceIds', persist: true },
            { name: 'progress', type: 'number' },
            { name: 'address', type: 'string' },
            { name: 'customerName', type: 'string' },
            { name: 'customerPhone', type: 'string' },
            { name: 'jobId', type: 'string' },
            { name: 'icon', type: 'string' },
            { name: 'dueDate', type: 'Date' },
            { name: 'jobStatus', type: 'string' },
            { name: 'driverAttachedFiles', type: 'boolean' },
            ...EventModel.fields
        ];
    }

    public type: string;
    public progress?: number;
    public address?: string;
    public customerName?: string;
    public customerPhone?: string;
    public jobId?: string;
    public icon?: string;
    public dueDate: Date;
    public jobStatus?: string;
    public driverAttachedFiles?: boolean;

    constructor(config?: Partial<EventModelConfig & EventModelCustomFields>) {
        super(config);
        this.type = config?.type || '';
        this.progress = config?.progress || 0;
        this.address = config?.address || '';
        this.customerName = config?.customerName || '';
        this.customerPhone = config?.customerPhone || '';
        this.jobId = config?.jobId || '';
        this.icon = config?.icon || '';
        this.dueDate = config?.dueDate || new Date();
        this.jobStatus = config?.jobStatus || '';
        this.driverAttachedFiles = config?.driverAttachedFiles || false;
    }

}

import { ResourceModel, ResourceModelConfig } from '@bryntum/scheduler';

export interface ResourceModelCustomFields {
    address?: string;
    customerName?: string;
    customerPhone?: string;
    jobId?: string;
    icon?: string;
}

export class CustomResourceModel extends ResourceModel implements ResourceModelCustomFields {

    static get $name() {
        return 'CustomResourceModel';
    }
    static override get fields() {
        return [

            { name: 'address', type: 'string' },
            { name: 'customerName', type: 'string' },
            { name: 'customerPhone', type: 'string' },
            { name: 'jobId', type: 'string' },
            { name: 'icon', type: 'string' }
        ];
    }


    public address?: string;
    public customerName?: string;
    public customerPhone?: string;
    public jobId?: string;
    public icon?: string;

    constructor(config?: Partial<ResourceModelConfig & ResourceModelCustomFields>) {
        super(config);

        this.address = config?.address || '';
        this.customerName = config?.customerName || '';
        this.customerPhone = config?.customerPhone || '';
        this.jobId = config?.jobId || '';
        this.icon = config?.icon || '';

    }

}

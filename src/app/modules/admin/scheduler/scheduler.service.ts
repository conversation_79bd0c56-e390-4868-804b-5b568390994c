import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, ReplaySubject, tap } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class SchedulerService {
    private _data: BehaviorSubject<any> = new BehaviorSubject(null);
    private addJobTrigger = new ReplaySubject<void>(1);

    /**
     * Constructor
     */
    constructor(private _httpClient: HttpClient) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Accessors
    // -----------------------------------------------------------------------------------------------------

    /**
     * Getter for data
     */
    get data$(): Observable<any> {
        return this._data.asObservable();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Get data
     */

    triggerAddNewJob() {

        this.addJobTrigger.next();
    }

    getAddJobTrigger() {
        return this.addJobTrigger.asObservable();
    }
    public cleanUp(): void {
        this.addJobTrigger.complete(); // Complete the ReplaySubject to prevent future emissions and allow for garbage collection
        // Complete other subjects or perform additional cleanup if necessary
    }
}

<div class="absolute inset-0 top-20 w-full p-1">
    @if (!loadingData && scheduledJobList && scheduledJobList.length > 0) {
        <div class="flex-auto bg-gray-100 p-4">
            <div
                class="xl:col-span-2 flex flex-col flex-auto bg-card shadow rounded-2xl overflow-hidden"
            >
                <div
                    class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b"
                >
                    <!-- Title -->
                    <div class="text-lg font-extrabold tracking-tight">
                        Scheduler List
                        <div class="text-secondary font-medium">
                            <button
                                class="mr-5"
                                (click)="
                                    applyFilter(null, null);
                                    selectedTableFilter = 'all'
                                "
                            >
                                <div
                                    class="inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-primary-800 border-2 border-gray rounded-full dark:border-gray-900"
                                >
                                    {{ getApprovedStatus("all") }}
                                </div>
                                All
                            </button>
                            <button
                                class="mr-5"
                                (click)="
                                    applyFilter(null, 'pending');
                                    selectedTableFilter = 'pending'
                                "
                            >
                                <div
                                    class="inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-gray-500 border-2 border-gray rounded-full dark:border-gray-900"
                                >
                                    {{ getApprovedStatus("pending") }}
                                </div>
                                Pending
                            </button>
                            <button
                                class="mr-5"
                                (click)="
                                    applyFilter(null, 'approved');
                                    selectedTableFilter = 'approved'
                                "
                            >
                                <div
                                    class="inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-blue-500 border-2 border-gray rounded-full dark:border-gray-900"
                                >
                                    {{ getApprovedStatus("approved") }}
                                </div>
                                Approved
                            </button>
                            <button
                                class="mr-5"
                                (click)="
                                    applyFilter(null, 'finished');
                                    selectedTableFilter = 'finished'
                                "
                            >
                                <div
                                    class="inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-green-500 border-2 border-gray rounded-full dark:border-gray-900"
                                >
                                    {{ getApprovedStatus("finished") }}
                                </div>
                                Finished
                            </button>
                        </div>
                    </div>
                    <!-- Actions -->
                    <div
                        class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4"
                    >
                        <!-- Search -->
                        <mat-form-field
                            class="fuse-mat-dense fuse-mat-rounded min-w-64"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-icon
                                class="icon-size-5"
                                matPrefix
                                [svgIcon]="'heroicons_solid:magnifying-glass'"
                            ></mat-icon>
                            <input
                                matInput
                                (keyup)="applyFilter($event)"
                                [autocomplete]="'off'"
                                [placeholder]="'Search ...'"
                                #input
                            />
                        </mat-form-field>
                        <button mat-icon-button [matMenuTriggerFor]="moreMenu">
                            <i
                                class="ml-3 icon-size-5 fa-duotone fa-file-export"
                            ></i>
                        </button>
                        <mat-menu #moreMenu="matMenu">
                            <button
                                mat-menu-item
                                (click)="exportMaterialTable('pdf')"
                            >
                                <i class="fa-duotone fa-file-pdf"></i>
                                <span class="ml-3">PDF</span>
                            </button>
                            <button
                                mat-menu-item
                                (click)="exportMaterialTable('excel')"
                            >
                                <i class="fa-duotone fa-file-excel"></i>
                                <span class="ml-3">EXCEL</span>
                            </button>
                        </mat-menu>
                    </div>
                </div>
                <div class="overflow-x-auto mx-6">
                    <div class="w-full bg-transparent">
                        <table mat-table [dataSource]="dataSource" matSort>
                            <!-- Avatar Column -->
                            <ng-container matColumnDef="avatar">
                                <th mat-header-cell *matHeaderCellDef>
                                    Team Member
                                </th>
                                <td mat-cell *matCellDef="let row">
                                    <div
                                        class="relative flex flex-0 items-center justify-center w-10 h-10 rounded-full"
                                    >
                                        <!-- Team Member Dispatch Status -->
                                        @if (row.dispatchStatus) {
                                            <div
                                                class="flex absolute bottom-0 right-0 flex-0 w-4 h-4 -ml-0.5 rounded-full ring-2 ring-bg-card items-center justify-center text-on-primary"
                                                [ngClass]="{
                                                    'bg-blue-200':
                                                        getEventAttributes(row)
                                                            .eventColor ===
                                                        'blue',
                                                    'bg-gray-500':
                                                        getEventAttributes(row)
                                                            .eventColor ===
                                                        'gray',
                                                    'bg-green-500':
                                                        getEventAttributes(row)
                                                            .eventColor ===
                                                        'green'
                                                }"
                                            >
                                                <i
                                                    class="fa-xs"
                                                    [ngClass]="
                                                        getEventAttributes(row)
                                                            .icon
                                                    "
                                                ></i>
                                            </div>
                                        }
                                        @if (!row.teamMemberID) {
                                            <span
                                                class="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10"
                                                >Pending</span
                                            >
                                        }
                                        <!-- Team Member Avatar Status -->
                                        <div
                                            class="w-15 rounded-full overflow-hidden"
                                        >
                                            @if (
                                                getTeamMemberInfo(
                                                    row.teamMemberID
                                                ).avatar
                                            ) {
                                                <img
                                                    [matTooltip]="
                                                        getTeamMemberInfo(
                                                            row.teamMemberID
                                                        ).fullName
                                                    "
                                                    class="object-cover w-full h-full"
                                                    [src]="
                                                        getTeamMemberInfo(
                                                            row.teamMemberID
                                                        ).avatar
                                                    "
                                                    alt="Team Member Avatar"
                                                />
                                            } @else {
                                                <div
                                                    [matTooltip]="
                                                        getTeamMemberInfo(
                                                            row.teamMemberID
                                                        ).fullName
                                                    "
                                                    class="flex items-center justify-center w-full h-full rounded-full text-lg uppercase bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                                >
                                                    {{
                                                        getTeamMemberInfo(
                                                            row.teamMemberID
                                                        ).name
                                                    }}
                                                </div>
                                            }
                                        </div>
                                        <ng-template #noAvatar>
                                            <div
                                                class="flex items-center justify-center w-full h-full rounded-full text-lg uppercase bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                            >
                                                {{
                                                    getTeamMemberInfo(
                                                        row.teamMemberID
                                                    ).name
                                                }}
                                            </div>
                                        </ng-template>
                                    </div>
                                </td>
                            </ng-container>
                            <!-- Progress Column -->
                            <ng-container matColumnDef="jobTitle">
                                <th
                                    mat-header-cell
                                    *matHeaderCellDef
                                    mat-sort-header
                                >
                                    Job Title
                                </th>
                                <td mat-cell *matCellDef="let row">
                                    <div class="flex flex-col mt-2 mb-2">
                                        <div class="font-medium">
                                            {{ row.jobTitle }}
                                        </div>
                                        <div
                                            class="flex flex-col sm:flex-row sm:items-center -ml-0.5 mt-2 sm:mt-1 space-y-1 sm:space-y-0 sm:space-x-3"
                                        >
                                            @if (row.startTime) {
                                                <div class="flex items-center">
                                                    <mat-icon
                                                        class="icon-size-5 text-hint"
                                                        [svgIcon]="
                                                            'heroicons_solid:clock'
                                                        "
                                                    ></mat-icon>
                                                    <div
                                                        class="ml-1.5 text-md text-secondary"
                                                    >
                                                        {{ row.startTime }}
                                                    </div>
                                                </div>
                                            }
                                            @if (row.jobAddress) {
                                                <div class="flex items-center">
                                                    <mat-icon
                                                        class="icon-size-5 text-hint"
                                                        [svgIcon]="
                                                            'heroicons_solid:map-pin'
                                                        "
                                                    ></mat-icon>
                                                    <div
                                                        class="ml-1.5 text-md text-secondary"
                                                    >
                                                        {{ row.jobAddress }}
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </td>
                            </ng-container>
                            <!-- dueDate Column -->
                            <ng-container matColumnDef="dueDate">
                                <th
                                    mat-header-cell
                                    *matHeaderCellDef
                                    mat-sort-header
                                >
                                    Due Date
                                </th>
                                <td mat-cell *matCellDef="let row">
                                    {{ row.dueDate | date: "EEE, dd MMM yyyy" }}
                                </td>
                            </ng-container>
                            <!-- Customer Column -->
                            <ng-container matColumnDef="customerId">
                                <th
                                    mat-header-cell
                                    *matHeaderCellDef
                                    mat-sort-header
                                >
                                    Customer
                                </th>
                                <td mat-cell *matCellDef="let row">
                                    @if (
                                        getCustomerName(row.customerId);
                                        as customer
                                    ) {
                                        <div>
                                            <div
                                                class="flex flex-col mt-2 mb-2"
                                            >
                                                <div class="font-medium">
                                                    {{ customer.name }}
                                                </div>
                                                <div
                                                    class="flex flex-col sm:flex-row sm:items-center -ml-0.5 mt-2 sm:mt-1 space-y-1 sm:space-y-0 sm:space-x-3"
                                                >
                                                    @if (customer.contact) {
                                                        <div
                                                            class="flex items-center"
                                                        >
                                                            <i
                                                                class="icon-size-3 text-hint fa-duotone fa-phone-volume"
                                                            ></i>
                                                            <div
                                                                class="ml-1.5 text-md text-secondary"
                                                            >
                                                                {{
                                                                    customer.contact
                                                                }}
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </td>
                            </ng-container>
                            <!-- Status Column -->
                            <ng-container matColumnDef="jobStatus">
                                <th
                                    mat-header-cell
                                    *matHeaderCellDef
                                    mat-sort-header
                                >
                                    Status
                                </th>
                                <td mat-cell *matCellDef="let row">
                                    <div
                                        class="rounded-full p-2 font-semibold text-center text-sm"
                                        [ngClass]="{
                                            'bg-blue-200':
                                                getEventAttributes(row)
                                                    .eventColor === 'blue',
                                            'text-blue-600':
                                                getEventAttributes(row)
                                                    .eventColor === 'blue',
                                            'bg-gray-500':
                                                getEventAttributes(row)
                                                    .eventColor === 'gray',
                                            'text-gray-200':
                                                getEventAttributes(row)
                                                    .eventColor === 'gray',
                                            'bg-green-200':
                                                getEventAttributes(row)
                                                    .eventColor === 'green',
                                            'text-yellow-600':
                                                getEventAttributes(row)
                                                    .eventColor === 'yellow',
                                            'text-green-800':
                                                getEventAttributes(row)
                                                    .eventColor === 'green'
                                        }"
                                    >
                                        {{ row.jobStatus }}
                                    </div>
                                </td>
                            </ng-container>
                            <!-- Actions Column -->
                            <ng-container matColumnDef="actions">
                                <th mat-header-cell *matHeaderCellDef>
                                    Actions
                                </th>
                                <td mat-cell *matCellDef="let row">
                                    <button
                                        mat-icon-button
                                        (click)="loadSelected(row, row.$id)"
                                    >
                                        <mat-icon
                                            [svgIcon]="
                                                'heroicons_outline:pencil'
                                            "
                                        ></mat-icon>
                                    </button>
                                    <button
                                        mat-icon-button
                                        [matMenuTriggerFor]="moreMenu"
                                    >
                                        <mat-icon
                                            [svgIcon]="
                                                'heroicons_outline:ellipsis-vertical'
                                            "
                                        ></mat-icon>
                                    </button>
                                    <mat-menu #moreMenu="matMenu">
                                        @if (
                                            row.teamMemberID &&
                                            row.jobStatus != "finished"
                                        ) {
                                            <button
                                                mat-menu-item
                                                (click)="dispatchItem(row)"
                                            >
                                                <span class="flex items-center">
                                                    <i
                                                        class="icon-size-5 mr-5 fa-duotone fa-mobile-signal-out"
                                                    ></i>
                                                    <span>Dispatch</span>
                                                </span>
                                            </button>
                                        }
                                        <mat-divider class="my-2"></mat-divider>
                                        @if (row.invoiced) {
                                            <button
                                                mat-menu-item
                                                (click)="editInvoice(row)"
                                            >
                                                <span class="flex items-center">
                                                    <i
                                                        class="icon-size-5 mr-5 fa-duotone fa-file-invoice-dollar"
                                                    ></i>
                                                    <span>Edit Invoice</span>
                                                </span>
                                            </button>
                                        }
                                        <mat-divider class="my-2"></mat-divider>
                                        <button
                                            mat-menu-item
                                            (click)="deleteItem(row)"
                                        >
                                            <span class="flex items-center">
                                                <i
                                                    class="icon-size-5 mr-5 fa-duotone fa-solid fa-trash-can-clock"
                                                ></i>
                                                <span>Delete</span></span
                                            >
                                        </button>
                                    </mat-menu>
                                </td>
                            </ng-container>
                            <!-- Invoicing Column -->

                            <ng-container matColumnDef="invoicing">
                                <th mat-header-cell *matHeaderCellDef>
                                    Invoicing
                                </th>
                                <td
                                    mat-cell
                                    *matCellDef="let row"
                                    class="text-center items-center justify-center"
                                >
                                    @if (row.jobStatus === "finished") {
                                        <button
                                            mat-icon-button
                                            (click)="loadInvoicing(row)"
                                        >
                                            <mat-icon
                                                [svgIcon]="
                                                    'heroicons_outline:currency-dollar'
                                                "
                                                [ngClass]="{
                                                    'text-green-600':
                                                        row.invoiced === true
                                                }"
                                            ></mat-icon>
                                        </button>
                                    }
                                </td>
                            </ng-container>
                            <tr
                                mat-header-row
                                *matHeaderRowDef="displayedColumns"
                            ></tr>
                            <tr
                                mat-row
                                *matRowDef="let row; columns: displayedColumns"
                            ></tr>
                            <!-- Row shown when there is no matching data. -->
                            <tr class="mat-row" *matNoDataRow>
                                <td class="mat-cell" colspan="4">
                                    No data matching the filter "{{
                                        input.value
                                    }}"
                                </td>
                            </tr>
                        </table>
                        <mat-paginator
                            [pageSize]="20"
                            [pageSizeOptions]="[5, 10, 20, 50, 100]"
                            aria-label="Select page of users"
                        ></mat-paginator>
                    </div>
                </div>
            </div>
        </div>
    } @else {
        @if (loadingData) {
            <div
                class="flex flex-auto flex-col items-center justify-center bg-gray-100 dark:bg-transparent"
            >
                <i class="icon-size-24 fa-duotone fa-calendar-range"></i>
                <div
                    class="mt-4 text-2xl font-semibold tracking-tight text-secondary"
                >
                    Loading existing scheduled Jobs !
                </div>
            </div>
        } @else {
            <div
                class="flex flex-auto flex-col items-center justify-center bg-gray-100 dark:bg-transparent"
            >
                <i class="icon-size-24 fa-duotone fa-calendar-range"></i>
                <div
                    class="mt-4 text-2xl font-semibold tracking-tight text-secondary"
                >
                    Add a Job to start planning!
                </div>
            </div>
        }
    }
</div>

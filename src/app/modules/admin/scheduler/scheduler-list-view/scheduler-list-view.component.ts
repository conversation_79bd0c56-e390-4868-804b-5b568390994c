import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ScheduledJob } from 'app/core/databaseModels/scheduledJobs/scheduledJob.types';
import { CustomerService } from 'app/core/databaseModels/customers/customers.service';
import { TeamMembersService } from 'app/core/databaseModels/teammembers/teammembers.service';
import { ExportTableService } from 'app/services/export-table.service';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { DatePipe, NgClass } from '@angular/common';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';
import { Customer } from 'app/core/databaseModels/customers/contacts.types';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { Router } from '@angular/router';

@Component({
    imports: [
        NgClass,
        MatMenuModule,
        MatTooltipModule,
        MatTableModule,
        DatePipe,
        MatInputModule,
        MatIconModule,
    ],
    selector: 'scheduler-list-view',
    templateUrl: './scheduler-list-view.component.html',
    styleUrls: ['./scheduler-list-view.component.scss']
})
export class SchedulerListViewComponent implements OnInit {
    @Input() scheduledJobList: ScheduledJob[];
    @Input() loadingData: boolean;
    @Input() selectedTableFilter: string;
    @Input() teamMembers: TeamMember[];
    @Input() customers: Customer[];
    @Output() loadSelectedEvent = new EventEmitter<{ eventRecord: any, rowID: string }>();
    @Output() dispatchItemEvent = new EventEmitter<any>();
    @Output() deleteItemEvent = new EventEmitter<any>();

    displayedColumns: string[] = ['avatar', 'jobTitle', 'dueDate', 'customerId', 'jobStatus', 'actions', 'invoicing'];
    dataSource: MatTableDataSource<ScheduledJob> = new MatTableDataSource<ScheduledJob>();

    dispatchStatusLookup = {
        '': { color: 'gray', icon: 'fa-solid fa-mobile-signal-out' },
        'null': { color: 'gray', icon: 'fa-solid fa-mobile-signal-out' },
        'dispatched': { color: 'blue', icon: 'fa-solid fa-check' },
        'received': { color: 'blue', icon: 'fa-solid fa-check-double' },
        'onMyWay': { color: 'yellow', icon: 'fa-solid fa-arrows-turn-right' },
        'finished': { color: 'green', icon: 'fa-solid fa-circle-check' },
        'started': { color: '#8FBC8F', icon: 'fa-solid fa-square-check' },
    };

    jobStatusLookup = {
        'pending': { color: 'gray', icon: 'fa-solid fa-clock' },
        'added': { color: 'gray', icon: 'fa-solid fa-circle-dot' },
        'approved': { color: 'blue' },
        'started': { color: 'green' },
        'onMyWay': { color: 'yellow' },
        'finished': { color: 'green' }
    };
    constructor(
        private _customerService: CustomerService,
        private _teamMembersService: TeamMembersService,
        private exportTableService: ExportTableService,
        private router: Router
    ) { }
    ngOnInit(): void {
        //The custom filterPredicate Assuming you have already populated your dataSource somewhere
        this.dataSource.filterPredicate = (data: ScheduledJob, filter: string) => {
            const customerInfo = this.getCustomerName(data.customerId);
            // Combine all fields you want to search by into one string. Adjust as necessary.
            const accumulator = (currentTerm, key) => {
                return key === 'customerId' ? currentTerm + customerInfo.name + customerInfo.contact : currentTerm + data[key];
            };
            const dataStr = Object.keys(data).reduce(accumulator, '').toLowerCase();
            // Check if the data string contains the filter string
            return dataStr.indexOf(filter) !== -1;
        };
    }

    ngOnChanges() {
        this.dataSource.data = this.scheduledJobList;
    }

    applyFilter(event: Event, filter?: string) {
        this.dataSource.filter = '';
        const filterValue = filter ? filter : (event.target as HTMLInputElement).value;
        this.dataSource.filter = filterValue.trim().toLowerCase();

        if (this.dataSource.paginator) {
            this.dataSource.paginator.firstPage();
        }
    }

    getApprovedStatus(approved: string): number {
        if (approved === 'all') {
            return this.scheduledJobList.length;
        }
        return this.scheduledJobList.filter(item => item.jobStatus === approved).length;
    }

    exportMaterialTable(format: 'pdf' | 'excel') {
        const _data = this.scheduledJobList
            .filter(item => this.selectedTableFilter === 'all' || item.jobStatus === this.selectedTableFilter)
            .map(job => {
                const _customer = this.getCustomerName(job.customerId);
                const _teamMember = this.getTeamMemberInfo(job.teamMemberID);
                return {
                    'Team Member': _teamMember.fullName,
                    'Job Title': job.jobTitle,
                    'Job Status': job.jobStatus,
                    'Job Address': job.jobAddress,
                    'Due Date': job.dueDate.toString().split('T')[0] + ' ' + job.startTime,
                    'Customer': _customer.name,
                    'Customer Contact': _customer.contact,
                };
            });

        this.exportTableService.exportMaterialTable(_data, format, 'ScheduledJobs-' + new Date().toISOString());
    }

    // Add other helper methods like getCustomerName, getTeamMemberInfo, etc.
    getTeamMemberInfo(id: string): { name: string, avatar: string | null, fullName: string } {
        const teamMember = this.teamMembers.find(item => item.$id === id);

        if (!teamMember) {
            return { name: '', avatar: null, fullName: '' }; // Return empty if no team member found
        }

        // Fetch avatar URL if avatarImageId is present
        const avatar = teamMember.avatarImageId
            ? this._teamMembersService.getFilePreview(teamMember.avatarImageId, 50, 50)
            : null;

        // Use avatar name or generate initials if avatarImageId/avatar is absent
        const name = teamMember.avatar || (avatar ? '' : this.generateInitials(teamMember.name));

        return {
            name: name,
            avatar: avatar,
            fullName: teamMember.name
        };
    }
    // Helper function to generate initials from a full name
    private generateInitials(fullName: string): string {
        const nameParts = fullName.split(' ');
        const initials = nameParts.length > 1
            ? `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}` // First and last initials
            : nameParts[0][0]; // Only first initial if single part
        return initials.toUpperCase();
    }

    getEventAttributes(item) {
        let eventColor = 'gray';
        let icon = 'fa-solid fa-clock';

        const dispatchAttributes = this.dispatchStatusLookup[item.dispatchStatus || 'null'];
        const jobAttributes = this.jobStatusLookup[item.jobStatus];

        if (dispatchAttributes) {
            eventColor = dispatchAttributes.color;
            icon = dispatchAttributes.icon;
        }

        if (jobAttributes) {
            eventColor = jobAttributes.color;
            if (jobAttributes.icon) {
                icon = jobAttributes.icon;
            }
        }

        return { eventColor, icon };
    }

    getCustomerName(id: string): { name: string, contact: string } {
        try {
            if (this.customers == null) {
                return { name: '', contact: '' };
            }

            const customer = this.customers.find(item => item.$id === id);
            if (customer) {
                let number;

                // Check if the phone number is a string and attempt to parse it
                if (typeof customer.phoneNumbers[0] === 'string') {
                    try {
                        number = JSON.parse(customer.phoneNumbers[0]);
                    } catch (error) {
                        console.error('Error parsing phone number', error);
                        // Handle the error or return a placeholder/fallback
                        return { name: customer.name, contact: 'Invalid Contact' };
                    }
                } else {
                    // If it's not a string, assume it's already an object
                    number = customer.phoneNumbers[0];
                }

                return { name: customer.name, contact: number.phoneNumber };
            } else {
                return { name: '', contact: '' };
            }
        } catch (error) {
            console.error('Error parsing customer ID', error);
            // Handle the error or return a placeholder/fallback
            return { name: '', contact: '' };
        }

    }
    deleteItem(item) {
        this.deleteItemEvent.emit(item);
    }
    dispatchItem(item) {
        this.dispatchItemEvent.emit(item);
    }
    loadSelected(eventRecord: any, rowID: string) {
        this.loadSelectedEvent.emit({ eventRecord, rowID });
    }

    loadInvoicing(item) {
        console.log(item);
        if (item.invoicingId && item.invoiced) {
            this.router.navigate(['/invoicing/view', item.invoicingId]);
        } else {
            this.router.navigate(['/invoicing/create', item.$id]);
        }
    }
    editInvoice(item) {
        if (item.invoicingId && item.invoiced) {
            this.router.navigate(['/invoicing/edit', item.invoicingId]);
        }
    }

}

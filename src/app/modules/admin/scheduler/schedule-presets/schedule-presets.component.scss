// Custom styling for the Schedule Presets component

// Style for the dialog container
:host {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    height: 100%;
  }
  
  // Style for the table
  .mat-mdc-table {
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
  }
  
  // Style for table headers
  .mat-mdc-header-cell {
    font-weight: 600;
    color: rgba(0, 0, 0, 0.7);
    background-color: #f3f4f6;
  }
  
  // Style for table rows
  .mat-mdc-row:hover {
    background-color: #f9fafb;
  }
  
  // Style for the "no items" state
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
    text-align: center;
  }

/* Styling for the drag preview */
.cdk-drag-preview {
    box-sizing: border-box;
    border-radius: 4px;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
                0 8px 10px 1px rgba(0, 0, 0, 0.14),
                0 3px 14px 2px rgba(0, 0, 0, 0.12);
    opacity: 0.8;
    background-color: white;
  }
  
  /* Styling for the item being dragged */
  .cdk-drag-placeholder {
    opacity: 0.3;
  }
  
  /* Animation for the drop */
  .cdk-drop-list-dragging .mat-row:not(.cdk-drag-placeholder) {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }
  
  /* Animation for items being sorted */
  .cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }
  
  /* Style for the drag handle */
  .fa-grip-vertical {
    cursor: move;
  }
  
  /* Highlight rows on hover to indicate draggable */
  .mat-row:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }

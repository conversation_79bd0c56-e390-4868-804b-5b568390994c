import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { BehaviorSubject, Observable, Subject, debounceTime, distinctUntilChanged, map, startWith, takeUntil } from 'rxjs';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';

import { FuseLoadingService } from '@fuse/services/loading';
import { FuseConfirmationService } from '@fuse/services/confirmation';

import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';
import { MatTooltipModule } from '@angular/material/tooltip';
import { SchedulePreset } from 'app/core/databaseModels/schedulePresets/schedulePresets.types';
import { SchedulePresetsService } from 'app/core/databaseModels/schedulePresets/schedulePresets.service';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { CdkDragDrop, DragDropModule, moveItemInArray } from '@angular/cdk/drag-drop';

@Component({
    selector: 'app-schedule-presets',
    imports: [
        CommonModule,
        MatButtonModule,
        MatIconModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatTableModule,
        MatDialogModule,
        ReactiveFormsModule,
        MatTooltipModule,
        MatAutocompleteModule,
        MatProgressSpinnerModule,
        DragDropModule
    ],
    templateUrl: './schedule-presets.component.html',
    styleUrl: './schedule-presets.component.scss'
})
export class SchedulePresetsComponent implements OnInit, OnDestroy {
    presetForm: FormGroup;
    selectedPreset: SchedulePreset;
    presets: SchedulePreset[] = [];
    teamMembers: TeamMember[] = [];
    jobTemplates: any[] = [];
    editMode = false;
    isLoading = false;
    isSaving = false;

    displayedColumns: string[] = ['dragHandle', 'jobTemplate', 'teamMember', 'address', 'actions'];
    templateItems: any[] = [];

    // Autocomplete controls
    jobTemplateControl = new FormControl('');
    teamMemberControl = new FormControl('');

    // Filtered autocomplete options
    filteredJobTemplates$: Observable<any[]>;
    filteredTeamMembers$: Observable<TeamMember[]>;

    selectedJobTemplate: any = null;
    selectedTeamMember: TeamMember = null;

    private _unsubscribeAll: Subject<any> = new Subject<any>();
    private _organisationID: string;

    constructor(
        @Inject(MAT_DIALOG_DATA) public data: any,
        private _dialogRef: MatDialogRef<SchedulePresetsComponent>,
        private _formBuilder: FormBuilder,
        private _schedulePresetsService: SchedulePresetsService,
        private _fuseLoadingService: FuseLoadingService,
        private _fuseConfirmationService: FuseConfirmationService
    ) {
        this.teamMembers = data.teamMembers || [];
        this.jobTemplates = data.jobTemplates || [];
        this._organisationID = data.organisationID;
        this.selectedPreset = data.preset;
    }

    ngOnInit(): void {
        // Initialize the form
        this.presetForm = this._formBuilder.group({
            presetName: ['', Validators.required],
            description: ['']
        });

        // Set up autocomplete for job templates
        this.filteredJobTemplates$ = this.jobTemplateControl.valueChanges.pipe(
            startWith(''),
            debounceTime(300),
            distinctUntilChanged(),
            map(value => {
                const filterValue = typeof value === 'string' ? value.toLowerCase() : '';
                return this.jobTemplates.filter(template =>
                    template.name.toLowerCase().includes(filterValue) || template.customerName.toLowerCase().includes(filterValue) || template.customerPhone.toLowerCase().includes(filterValue)
                );
            })
        );

        // Set up autocomplete for team members
        this.filteredTeamMembers$ = this.teamMemberControl.valueChanges.pipe(
            startWith(''),
            debounceTime(300),
            distinctUntilChanged(),
            map(value => {
                const filterValue = typeof value === 'string' ? value.toLowerCase() : '';
                return this.teamMembers.filter(member =>
                    member.name.toLowerCase().includes(filterValue) || member.phone.toLowerCase().includes(filterValue)
                );
            })
        );

        // Load presets
        this._schedulePresetsService.getSchedulePresets()
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((presets) => {
                this.presets = presets;
            });

        // If we have a selected preset, we're in edit mode
        if (this.selectedPreset) {
            this.editMode = true;
            this.loadPresetData();
        }
    }

    ngOnDestroy(): void {
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    loadPresetData(): void {
        this.isLoading = true;
        this._fuseLoadingService.show();

        this._schedulePresetsService.getSchedulePreset()
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe({
                next: (preset) => {
                    this.selectedPreset = preset;
                    this.presetForm.patchValue({
                        presetName: preset.presetName,
                        description: preset.description
                    });

                    // Clear any existing items
                    this.templateItems = [];

                    // Parse template items from JSON strings
                    if (preset.templateItems && preset.templateItems.length > 0) {
                        const parsedItems = preset.templateItems.map(item => {
                            const parsedItem = typeof item === 'string' ? JSON.parse(item) : item;

                            // Find the job template and team member for display
                            const jobTemplate = this.jobTemplates.find(t => t.id === parsedItem.jobTemplateId);
                            const teamMember = this.teamMembers.find(t => t.$id === parsedItem.teamMemberId);

                            return {
                                ...parsedItem,
                                jobTemplateName: jobTemplate ? jobTemplate.name : 'Unknown Template',
                                teamMemberName: teamMember ? teamMember.name : 'Unknown Team Member',
                                customerName: jobTemplate ? jobTemplate.customerName : '',
                                customerPhone: jobTemplate ? jobTemplate.customerPhone : '',
                                address: jobTemplate ? jobTemplate.address : '',
                                position: parsedItem.position || 0 // Default to 0 if position is not defined
                            };
                        });

                        // Sort by position
                        this.templateItems = parsedItems.sort((a, b) => a.position - b.position);
                    }

                    this.isLoading = false;
                    this._fuseLoadingService.hide();
                },
                error: (error) => {
                    console.error('Error loading preset:', error);
                    this.isLoading = false;
                    this._fuseLoadingService.hide();
                }
            });
    }

    displayJobTemplateFn(template: any): string {
        return template && template.name ? template.name : '';
    }

    displayTeamMemberFn(member: TeamMember): string {
        return member && member.name ? member.name : '';
    }

    onJobTemplateSelected(event: MatAutocompleteSelectedEvent): void {
        this.selectedJobTemplate = event.option.value;
    }

    onTeamMemberSelected(event: MatAutocompleteSelectedEvent): void {
        this.selectedTeamMember = event.option.value;
    }

    dropTemplateItem(event: CdkDragDrop<any[]>): void {
        moveItemInArray(this.templateItems, event.previousIndex, event.currentIndex);

        // Update position property for each item
        this.templateItems = this.templateItems.map((item, index) => ({
            ...item,
            position: index
        }));
    }


    addTemplateItem(): void {
        const jobTemplate = this.selectedJobTemplate;
        const teamMember = this.selectedTeamMember;

        if (!jobTemplate || !teamMember) {
            return;
        }

        // Create template item
        const newItem = {
            jobTemplateId: jobTemplate.id,
            jobTemplateName: jobTemplate.name,
            teamMemberId: teamMember.$id,
            teamMemberName: teamMember.name,
            customerName: jobTemplate.customerName,
            customerPhone: jobTemplate.customerPhone,
            address: jobTemplate.address,
            position: this.templateItems.length  // Add position at the end
        };

        // Add to template items array
        this.templateItems = [...this.templateItems, newItem];

        // Reset selection
        this.selectedJobTemplate = null;
        this.selectedTeamMember = null;
        this.jobTemplateControl.setValue('');
        this.teamMemberControl.setValue('');
    }

    removeTemplateItem(index: number): void {
        const updatedItems = [...this.templateItems];
        updatedItems.splice(index, 1);
        this.templateItems = updatedItems;
    }

    savePreset(): void {
        if (this.presetForm.invalid || this.templateItems.length === 0) {
            return;
        }

        this.isLoading = true;
        this.isSaving = true;
        this._fuseLoadingService.show();

        // Map template items to the format expected by the service
        const templateItems = this.templateItems.map((item, index) => ({
            jobTemplateId: item.jobTemplateId,
            teamMemberId: item.teamMemberId,
            position: item.position || index // Use existing position or fallback to index
        }));

        const presetData: SchedulePreset = {
            $id: this.editMode ? this.selectedPreset.$id : null,
            organisationID: this._organisationID,
            presetName: this.presetForm.get('presetName').value,
            description: this.presetForm.get('description').value,
            templateItems: templateItems.map(item => JSON.stringify(item))
        };

        if (this.editMode) {
            // Update existing preset
            this._schedulePresetsService.updateSchedulePreset(presetData)
                .pipe(takeUntil(this._unsubscribeAll))
                .subscribe({
                    next: (updatedPreset) => {
                        this.isLoading = false;
                        this.isSaving = false;
                        this._fuseLoadingService.hide();
                        this._dialogRef.close(updatedPreset);
                    },
                    error: (error) => {
                        console.error('Error updating preset:', error);
                        this.isLoading = false;
                        this.isSaving = false;
                        this._fuseLoadingService.hide();
                    }
                });
        } else {
            // Create new preset
            this._schedulePresetsService.createSchedulePreset(presetData)
                .pipe(takeUntil(this._unsubscribeAll))
                .subscribe({
                    next: (newPreset) => {
                        this.isLoading = false;
                        this.isSaving = false;
                        this._fuseLoadingService.hide();
                        this._dialogRef.close(newPreset);
                    },
                    error: (error) => {
                        console.error('Error creating preset:', error);
                        this.isLoading = false;
                        this.isSaving = false;
                        this._fuseLoadingService.hide();
                    }
                });
        }
    }

    deletePreset(): void {
        if (!this.editMode || !this.selectedPreset) {
            return;
        }

        // Show confirmation dialog
        const confirmation = this._fuseConfirmationService.open({
            title: 'Delete Schedule Preset',
            message: `Are you sure you want to delete '${this.selectedPreset.presetName}'? This action cannot be undone.`,
            actions: {
                confirm: {
                    label: 'Delete'
                }
            }
        });

        // Subscribe to the confirmation dialog
        confirmation.afterClosed().subscribe((result) => {
            if (result === 'confirmed') {
                this.isLoading = true;
                this._fuseLoadingService.show();

                this._schedulePresetsService.deleteSchedulePreset(this.selectedPreset)
                    .pipe(takeUntil(this._unsubscribeAll))
                    .subscribe({
                        next: () => {
                            this.isLoading = false;
                            this._fuseLoadingService.hide();
                            this._dialogRef.close({ deleted: true });
                        },
                        error: (error) => {
                            console.error('Error deleting preset:', error);
                            this.isLoading = false;
                            this._fuseLoadingService.hide();
                        }
                    });
            }
        });
    }

    cancel(): void {
        this._dialogRef.close();
    }
}

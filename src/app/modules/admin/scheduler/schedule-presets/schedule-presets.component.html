<div class="flex flex-col max-w-3xl w-full">
    <!-- Header -->
    <div
        class="flex flex-col sm:flex-row items-start sm:items-center justify-between p-6 pb-4 sm:py-6 border-b"
    >
        <div>
            <div class="text-2xl font-extrabold tracking-tight">
                {{ editMode ? "Edit" : "Create" }} Schedule Preset
            </div>
            <div class="mt-1 text-sm text-secondary">
                Manage schedule templates and team assignments
            </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center mt-4 sm:mt-0 space-x-2">
            @if (editMode) {
                <button
                    mat-flat-button
                    [color]="'warn'"
                    [disabled]="isLoading || isSaving"
                    (click)="deletePreset()"
                >
                    @if (isLoading) {
                        <mat-spinner diameter="20"></mat-spinner>
                    } @else {
                        <i class="fa fa-trash icon-size-3 mr-2"></i>
                        Delete
                    }
                </button>
            }

            <button
                mat-flat-button
                [color]="'primary'"
                (click)="savePreset()"
                [disabled]="
                    presetForm.invalid ||
                    templateItems.length === 0 ||
                    isLoading ||
                    isSaving
                "
            >
                @if (isSaving) {
                    <mat-spinner diameter="20"></mat-spinner>
                } @else {
                    <i class="fa fa-save icon-size-3 mr-2"></i>
                    Save
                }
            </button>

            <button mat-flat-button [color]="'basic'" (click)="cancel()">
                Cancel
            </button>
        </div>
    </div>

    <!-- Content -->
    <div class="flex flex-col p-6">
        <div class="flex flex-col gap-6">
            <form [formGroup]="presetForm">
                <!-- Basic info section -->
                <div class="flex flex-col gap-6 mb-8">
                    <h2 class="text-xl font-semibold">Basic Information</h2>

                    <mat-form-field class="w-full">
                        <mat-label>Preset Name</mat-label>
                        <input matInput formControlName="presetName" required />
                        @if (
                            presetForm
                                .get("presetName")
                                ?.hasError("required") &&
                            presetForm.get("presetName")?.touched
                        ) {
                            <mat-error> Preset name is required </mat-error>
                        }
                    </mat-form-field>

                    <mat-form-field class="w-full">
                        <mat-label>Description</mat-label>
                        <textarea
                            matInput
                            formControlName="description"
                            rows="3"
                        ></textarea>
                    </mat-form-field>
                </div>

                <!-- Add template items section -->
                <div class="flex flex-col gap-6 mb-8">
                    <h2 class="text-xl font-semibold">Add Template Items</h2>

                    <div class="flex flex-col md:flex-row gap-4 items-end">
                        <!-- Job Template Autocomplete -->
                        <mat-form-field class="flex-1">
                            <mat-label>Job Template</mat-label>
                            <input
                                type="text"
                                matInput
                                [formControl]="jobTemplateControl"
                                [matAutocomplete]="jobAutoComplete"
                                placeholder="Search templates..."
                            />
                            <mat-autocomplete
                                #jobAutoComplete="matAutocomplete"
                                [displayWith]="displayJobTemplateFn"
                                (optionSelected)="onJobTemplateSelected($event)"
                            >
                                @for (
                                    template of filteredJobTemplates$ | async;
                                    track template.id
                                ) {
                                    <mat-option [value]="template">
                                        <div class="flex flex-col">
                                            <div class="font-medium">
                                                {{ template.name }}
                                            </div>
                                            <div class="text-xs text-gray-600">
                                                {{ template.customerName }} -
                                                {{ template.customerPhone }}
                                            </div>
                                        </div>
                                    </mat-option>
                                }
                            </mat-autocomplete>
                        </mat-form-field>

                        <!-- Team Member Autocomplete -->
                        <mat-form-field class="flex-1">
                            <mat-label>Team Member</mat-label>
                            <input
                                type="text"
                                matInput
                                [formControl]="teamMemberControl"
                                [matAutocomplete]="teamAutoComplete"
                                placeholder="Search team members..."
                            />
                            <mat-autocomplete
                                #teamAutoComplete="matAutocomplete"
                                [displayWith]="displayTeamMemberFn"
                                (optionSelected)="onTeamMemberSelected($event)"
                            >
                                @for (
                                    member of filteredTeamMembers$ | async;
                                    track member.$id
                                ) {
                                    <mat-option [value]="member">
                                        {{ member.name }}
                                    </mat-option>
                                }
                            </mat-autocomplete>
                        </mat-form-field>

                        <button
                            mat-flat-button
                            color="accent"
                            (click)="addTemplateItem()"
                            [disabled]="
                                !selectedJobTemplate || !selectedTeamMember
                            "
                            matTooltip="Add to template items"
                        >
                            <i class="fa fa-plus"></i>
                        </button>
                    </div>
                </div>
            </form>

            <!-- Template items table -->
            <div class="flex flex-col">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold">Template Items</h2>
                    <div class="text-sm text-secondary">
                        {{ templateItems.length }} items
                    </div>
                </div>

                @if (templateItems.length === 0) {
                    <div
                        class="flex flex-col items-center justify-center py-8 bg-gray-100 rounded-lg"
                    >
                        <i class="fa fa-file-text text-gray-400 text-4xl"></i>
                        <div class="mt-4 text-secondary text-lg font-medium">
                            No template items added yet
                        </div>
                        <div class="mt-2 text-secondary text-sm">
                            Add job templates and assign team members above
                        </div>
                    </div>
                } @else {
                    <div
                        class="w-full"
                        cdkDropList
                        (cdkDropListDropped)="dropTemplateItem($event)"
                    >
                        <table
                            mat-table
                            [dataSource]="templateItems"
                            class="mat-elevation-z1 w-full"
                        >
                            <!-- Drag Handle Column -->
                            <ng-container matColumnDef="dragHandle">
                                <th mat-header-cell *matHeaderCellDef></th>
                                <td
                                    mat-cell
                                    *matCellDef="let item"
                                    class="w-10"
                                >
                                    <div
                                        class="cursor-move flex justify-center"
                                        cdkDragHandle
                                    >
                                        <i
                                            class="fa fa-grip-vertical text-gray-400"
                                        ></i>
                                    </div>
                                </td>
                            </ng-container>
                            <!-- Job Template Column -->
                            <ng-container matColumnDef="jobTemplate">
                                <th mat-header-cell *matHeaderCellDef>
                                    Job Template
                                </th>
                                <td mat-cell *matCellDef="let item">
                                    <div class="flex flex-col py-2">
                                        <div class="font-medium">
                                            {{ item.jobTemplateName }}
                                        </div>
                                        <div class="text-xs text-gray-600">
                                            {{ item.customerName }}
                                            {{
                                                item.customerPhone
                                                    ? "- " + item.customerPhone
                                                    : ""
                                            }}
                                        </div>
                                    </div>
                                </td>
                            </ng-container>

                            <!-- Team Member Column -->
                            <ng-container matColumnDef="teamMember">
                                <th mat-header-cell *matHeaderCellDef>
                                    Team Member
                                </th>
                                <td mat-cell *matCellDef="let item">
                                    {{ item.teamMemberName }}
                                </td>
                            </ng-container>
                            <!-- Job Template address -->
                            <ng-container matColumnDef="address">
                                <th mat-header-cell *matHeaderCellDef>
                                    Address
                                </th>
                                <td mat-cell *matCellDef="let item">
                                    {{ item.address }}
                                </td>
                            </ng-container>

                            <!-- Actions Column -->
                            <ng-container matColumnDef="actions">
                                <th mat-header-cell *matHeaderCellDef>
                                    Actions
                                </th>
                                <td
                                    mat-cell
                                    *matCellDef="let item; let i = index"
                                >
                                    <button
                                        mat-icon-button
                                        color="warn"
                                        (click)="removeTemplateItem(i)"
                                        matTooltip="Remove item"
                                    >
                                        <i class="fa fa-trash icon-size-3"></i>
                                    </button>
                                </td>
                            </ng-container>

                            <tr
                                mat-header-row
                                *matHeaderRowDef="displayedColumns"
                            ></tr>
                            <tr
                                mat-row
                                *matRowDef="let row; columns: displayedColumns"
                                cdkDrag
                                [cdkDragData]="row"
                            ></tr>
                        </table>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

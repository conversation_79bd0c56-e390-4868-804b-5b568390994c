import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    OnDestroy,
    OnInit,
    Renderer2,
    TemplateRef,
    ViewChild,
    ViewContainerRef,
    ViewEncapsulation
} from '@angular/core';
import { CommonModule, DatePipe, NgClass } from '@angular/common';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatTabsModule } from '@angular/material/tabs';
import { MatIconModule } from '@angular/material/icon';
import { MatDrawer, MatSidenavModule } from '@angular/material/sidenav';
import { Observable, Subject, catchError, filter, map, of, startWith, switchMap, take, takeUntil, tap, throwError } from 'rxjs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FormControl, FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Valida<PERSON> } from '@angular/forms';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { FuseLoadingService } from '@fuse/services/loading';
import { MatMapsAutocompleteModule } from 'app/modules/widgets/mat-maps-autocomplete/mat-maps-autocomplete.module';
import { GoogleMap, MapMarker } from '@angular/google-maps';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { MatNativeDateModule, MatOptionModule, MatRippleModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import {
    MatDatepicker,
    MatDatepickerInputEvent,
    MatDatepickerModule,
    DateRange,
    DefaultMatCalendarRangeStrategy,
    MatCalendar,
    MAT_DATE_RANGE_SELECTION_STRATEGY
} from '@angular/material/datepicker';
import { FuseFindByKeyPipe } from '@fuse/pipes/find-by-key/find-by-key.pipe';
import { TextFieldModule } from '@angular/cdk/text-field';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';


import { MatMenuModule } from '@angular/material/menu';
import { MatSlideToggleChange, MatSlideToggleModule } from '@angular/material/slide-toggle';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { FileInputDropzoneModule } from 'app/modules/widgets/file-input-dropzone/file-input-dropzone.module';
import { FileInputDropzoneComponent } from 'app/modules/widgets/file-input-dropzone/file-input-dropzone.component';


import { DateTime } from 'luxon';
import { BryntumSchedulerComponent, BryntumSchedulerModule } from '@bryntum/scheduler-angular';
import { JobTags, ScheduledJob } from 'app/core/databaseModels/scheduledJobs/scheduledJob.types';
import { ScheduledJobsService } from 'app/core/databaseModels/scheduledJobs/scheduledJob.service';

import {
    DateHelper,
    DomHelper,
    DragHelper,
    EventModel,
    ResourceModelConfig,
    ScrollManager,
    EventStore,
    ResourceStore
} from '@bryntum/scheduler';
//DB Models
import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';
import { TeamMembersService } from 'app/core/databaseModels/teammembers/teammembers.service';
import { SchedulerBaseConfig } from './scheduler.config';

import { CustomEventModel } from './tooltip/customEventModel';
import { CustomResourceModel } from './tooltip/customResourceModel';

import { Customer } from 'app/core/databaseModels/customers/contacts.types';
import { CustomerService } from 'app/core/databaseModels/customers/customers.service';
import { MatDialog } from '@angular/material/dialog';
import { SchedulerContactsComponent } from './schedulerContacts/schedulerContacts.component';
import { JobMapComponent } from './jobMap/jobMap.component';

import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { FcmNotification } from 'app/core/databaseModels/fcmNotification/fcmNotification.types';
import { FcmNotificationService } from 'app/core/databaseModels/fcmNotification/fcmNotification.service';
import { GroupService } from 'app/core/databaseModels/groups/groups.service';
import { TreeNode } from 'app/modules/widgets/custom-matselect/select-check-all-tree/select-check-all-tree.component';
import { CustomMatSelectTreeModule } from 'app/modules/widgets/custom-matselect/custom-matselect-tree.module';
import { OrderHistoryComponent } from 'app/modules/widgets/order-history/order-history.component';
import { MatPaginator } from '@angular/material/paginator';
import { ActivatedRoute, Router } from '@angular/router';
import { JobNumberService } from 'app/services/job-number.service';
import { SchedulerListViewComponent } from './scheduler-list-view/scheduler-list-view.component';
import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { SchedulePresetsService } from 'app/core/databaseModels/schedulePresets/schedulePresets.service';
import { SchedulePresetsComponent } from './schedule-presets/schedule-presets.component';
import { SchedulePreset } from 'app/core/databaseModels/schedulePresets/schedulePresets.types';
import { ApplyPresetDialogComponent } from './widgets/apply-preset-dialog.component';


@Component({
    selector: 'app-scheduler',
    templateUrl: './scheduler.component.html',
    styleUrls: ['./scheduler.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [NgClass, BryntumSchedulerModule,
        MatMenuModule,
        CommonModule,
        MatTooltipModule,
        MatSidenavModule,
        MatTabsModule,
        GoogleMap,
        MapMarker,
        MatMapsAutocompleteModule,
        MatButtonModule,
        MatIconModule,
        MatNativeDateModule,
        MatAutocompleteModule,
        FormsModule,
        ReactiveFormsModule,
        MatRippleModule,
        MatFormFieldModule,
        MatInputModule,
        DragDropModule,
        MatCheckboxModule,
        MatSelectModule,
        MatOptionModule,
        MatDatepickerModule,
        MatCalendar,
        MatSlideToggleModule,
        TextFieldModule,
        DatePipe,
        FileInputDropzoneModule,
        MatProgressSpinnerModule,
        CustomMatSelectTreeModule,
        JobMapComponent,
        NgxMaskDirective,
        SchedulerListViewComponent,
        FuseFindByKeyPipe,
        MatSnackBarModule
    ],
    providers: [provideNgxMask(),
    {
        provide: MAT_DATE_RANGE_SELECTION_STRATEGY,
        useClass: DefaultMatCalendarRangeStrategy,
    }
    ]
})

export class SchedulerComponent implements OnInit, OnDestroy, AfterViewInit {
    @ViewChild('jobDrawer', { static: true }) jobDrawer: MatDrawer;
    @ViewChild(SchedulerListViewComponent) schedulerListView: SchedulerListViewComponent;

    @ViewChild(BryntumSchedulerComponent, { static: false }) scheduler!: BryntumSchedulerComponent;
    @ViewChild('fileInputDropzone') fileInputDropzone: FileInputDropzoneComponent;
    @ViewChild('pendingContainer', { static: true }) pendingContainer: ElementRef;
    @ViewChild('dueDatePicker') dueDatePicker: MatDatepicker<Date>;
    @ViewChild('tabCalendar') calendar: MatCalendar<Date> | undefined;
    @ViewChild(MatPaginator) paginator: MatPaginator;
    @ViewChild('tagsPanel') private _tagsPanel: TemplateRef<any>;
    @ViewChild('tagsPanelOrigin') private _tagsPanelOrigin: ElementRef;


    private _tagsPanelOverlayRef: OverlayRef;
    tagsEditMode: boolean = false;
    filteredTags: JobTags[];
    tags: JobTags[] = [];


    drawerMode: 'over' | 'side' = 'side';
    drawerOpened: boolean = true;
    private _unsubscribeAll: Subject<any> = new Subject<any>();

    _organisationID;
    newJob: ScheduledJob = this.initializeToNull<ScheduledJob>();
    scheduledJobList: ScheduledJob[] = [];
    displayedColumns: string[] = ['avatar', 'jobTitle', 'dueDate', 'customerId', 'jobStatus', 'actions'];

    private lastHoveredCell: HTMLElement | null = null;
    selectedTableFilter = 'all';



    isLoading: boolean = false;
    formReady: boolean = false;

    jobForm: UntypedFormGroup;
    editMode: boolean = false;
    isSaving: boolean = false;
    jobTemplateListVisible: boolean = false;
    pendingLoading: boolean = false;
    loadingData: boolean = false;
    loadingPendingData: boolean = false;
    loadingTemplates: boolean = false;
    editTemplate: boolean = false;

    teamMembers: TeamMember[] = [];
    customers: Customer[];
    selectedCustomer: Customer;

    fcmNotification: FcmNotification;
    pendingCount: number = 0;

    selectedAddresses: any[] = [];
    selectedAddress: any;
    makePendingCopy: boolean = false;
    pendingTitle: string = '';


    customersFilteredOptions: Observable<Customer[]>;
    customerInputCtrl = new FormControl();

    teamMembersFilteredOptions: Observable<TeamMember[]>;
    teamMemberInputCtrl = new FormControl();

    selectedTab = 0;
    selectedJobListDate: Date = new Date();
    calenderSelectedDate: DateRange<Date> | undefined;


    treeData = [];
    selectedGroupIds = [];


    presetSearchControl = new FormControl('');
    filteredPresets$: Observable<SchedulePreset[]>;
    recentPresets: SchedulePreset[] = [];


    // Status Lookup objects
    dispatchStatusLookup = {
        '': { color: 'gray', icon: 'fa-regular fa-mobile-signal-out' },
        'null': { color: 'gray', icon: 'fa-regular fa-mobile-signal-out' },
        'dispatched': { color: 'blue', icon: 'fa-solid fa-check' },
        'received': { color: 'blue', icon: 'fa-solid fa-check-double' },
        'onMyWay': { color: 'yellow', icon: 'fa-solid fa-arrows-turn-right' },
        'finished': { color: 'green', icon: 'fa-solid fa-circle-check' },
        'started': { color: '#8FBC8F', icon: 'fa-solid fa-square-check' },
    };

    jobStatusLookup = {
        'pending': { color: 'gray', icon: 'fa-solid fa-clock' },
        'added': { color: 'gray', icon: 'fa-solid fa-circle-dot' },
        'approved': { color: '#06b6d4' },
        'started': { color: 'green' },
        'onMyWay': { color: '#fde047' },
        'finished': { color: '#6ee7b7' }
    };

    assignTaskId = '';


    ////
    center: google.maps.LatLngLiteral = {
        lat: -24,
        lng: 134
    };
    zoom = 4;
    mapOptions: google.maps.MapOptions = {
        disableDefaultUI: true,
        fullscreenControl: false,
    }
    markerOptions: google.maps.MarkerOptions = { draggable: false };
    markerPositions: google.maps.LatLngLiteral[] = [];
    ///


    // private scheduler!: Scheduler;
    resources: Partial<ResourceModelConfig>[];
    resourcesFilter;
    schedulerConfig = { ...SchedulerBaseConfig };
    private drag: DragHelper;

    // Bryntum stores for direct data management
    eventStore: EventStore;
    resourceStore: ResourceStore;

    selectedNewEvent;
    loadScheduleID: string = '';
    schedulerSelectedDate: Date = new Date();
    draggedTask: any;
    events: CustomEventModel[] = [];

    pendingEvents: Partial<CustomEventModel>[] = [];
    filteredPendingEvents: Partial<CustomEventModel>[] = [];
    filterPendingText: string = '';

    jobsTemplates: Partial<CustomEventModel>[] = [];
    filteredJobsTemplates: Partial<CustomEventModel>[] = [];
    filterJobsTemplatesText = '';

    selectedFiles: Array<{ filename: string, file: File, url: string }> = [];
    preloadedFiles: Array<{ filename: string, fileUrl: string, fileId: string }> = [];
    private hoursList = [];
    hoursInputCtrl = new FormControl('');
    hoursFilteredOptions: Observable<string[]>;




    /**
    * Constructor
    */
    constructor(
        //  private _schedulerService: SchedulerService,
        private _changeDetectorRef: ChangeDetectorRef,
        private _formBuilder: UntypedFormBuilder,
        private _fuseConfirmationService: FuseConfirmationService,
        private _fuseLoadingService: FuseLoadingService,
        private _teamMembersService: TeamMembersService,
        private _customerService: CustomerService,
        private _matDialog: MatDialog,
        private _scheduledJobsService: ScheduledJobsService,
        private _fcmNotificationService: FcmNotificationService,

        private _schedulePresetsService: SchedulePresetsService,
        // private readonly tabCalendarStrategy: DefaultMatCalendarRangeStrategy<Date>,

        private _groupService: GroupService,
        private _orderHistoryDialog: MatDialog,
        private _route: ActivatedRoute,
        private _router: Router,
        private jobNumberService: JobNumberService,
        private _overlay: Overlay,
        private _renderer2: Renderer2,
        private _viewContainerRef: ViewContainerRef,
        private _snackBar: MatSnackBar,


    ) {
    }

    /**
   * On init
   */
    ngOnInit(): void {
        // Initialize EventStore and ResourceStore
        this.initializeStores();

        // Load customers with caching and load all in background
        this._customerService.customers$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe(customers => {
                if (customers) {
                    this.customers = customers;
                    // Set up the filtered options observable
                    this.customersFilteredOptions = this.setupFilteredOptions();
                    this._changeDetectorRef.markForCheck();

                    // Log loading progress
                    if (this._customerService.isLoadingAllCustomers()) {
                        console.log(`Loading customers: ${customers.length}/${this._customerService.totalCustomers}`);
                    } else if (this._customerService.isAllCustomersLoaded()) {
                        console.log(`All customers loaded: ${customers.length} total`);

                        // All customers loaded successfully
                    }
                }
            });

        // Trigger initial customer loading (will use cache if available)
        // This will automatically start loading all customers in the background
        this._customerService.getCustomers(1, 1000).subscribe();

        // No need for periodic checks as Appwrite real-time events handle deletions

        // Get the tags

        this.filteredTags = this.tags;
        // this.schedulerComponent.header
        //Config Schedule

        // Call the function to generate the hoursList
        this.generateHoursList();
        // get organisationID
        this._scheduledJobsService.getOrganisationID().subscribe((organisationID) => {
            this._organisationID = organisationID;
            //  console.log(this._organisationID);
        })

        // Load Job Tags
        this._scheduledJobsService.getJobTags()
            .pipe(
                switchMap((response) => {
                    // Return tags$ observable after initial load
                    return this._scheduledJobsService.tags$;
                }),
                takeUntil(this._unsubscribeAll)
            )
            .subscribe({
                next: (tags) => {
                    this.tags = tags || [];
                    this.filteredTags = this.tags;
                    this._changeDetectorRef.markForCheck();
                },
                error: (error) => {
                    console.error('Error loading tags:', error);
                }
            });



        /// Load Team Members

        this.teamMembers = [];
        this._teamMembersService.teamMembers$.pipe(
            takeUntil(this._unsubscribeAll),
            catchError(error => {
                console.error('Error:', error);
                return throwError(error);
            })
        ).subscribe((teamMembers) => {
            if (!teamMembers) {
                return;
            }
            this.teamMembers = teamMembers;
            //  console.log(this.teamMembers);
            this.teamMembersFilteredOptions = this.teamMemberInputCtrl.valueChanges
                .pipe(
                    startWith(''),
                    map(value => value ? (typeof value === 'string' ? value : value.name) : ''),
                    map(name => name ? this._filterTeamMembers(name) : this.teamMembers.slice())
                );

            if (this.teamMembers.length > 0) {
                // Create resource objects for the scheduler
                this.resources = this.teamMembers.map(item => ({
                    id: item.$id,
                    name: item.name,
                    imageUrl: item.avatarImageId
                        ? this._teamMembersService.getFilePreview(item.avatarImageId, 50, 50)
                        : null,
                    dutyStatus: item.dutyStatus,
                    groupIds: item.groupMemberIDs
                }));

                this.resourcesFilter = this.resources;

                // Update the ResourceStore with the team members data
                if (this.resourceStore) {
                    this.resourceStore.data = this.resources;
                    this.resourceStore.commit(); // Commit changes to update the UI
                }

                // Set up the filtered options observable

                this._route.queryParams.subscribe(params => {
                    if (params.scheduleID) {
                        this.loadScheduleID = params.scheduleID;


                        this.loadSelectedEvent(
                            null,
                            this.loadScheduleID
                        );
                        // Remove 'scheduleID' from the URL after loading data
                        this._router.navigate([], {
                            relativeTo: this._route,
                            queryParams: { scheduleID: null },
                            queryParamsHandling: 'merge', // Retain other query params if needed
                            replaceUrl: true
                        });
                    }
                });
                this._changeDetectorRef.markForCheck();

            } else {
                // There are no team members, so Show message
                const addTeamMember = this._fuseConfirmationService.open({
                    title: `There are no team members available...`,
                    message: 'No team members found, please add team members first.',
                    icon: { show: true, name: "heroicons_outline:user-group", color: "success" },
                    actions: { confirm: { show: true, label: 'Add Team Members', color: 'primary' }, cancel: { show: false } },

                    dismissible: false,
                });
                addTeamMember.afterClosed().subscribe((result) => {

                    if (result === 'confirmed') {
                        this._router.navigate(['/tools']);
                    }
                })

            }

        })

        // Mark for check
        this._changeDetectorRef.markForCheck();


        this.jobForm = this._formBuilder.group({
            $id: [''],
            organisationID: [''],
            jobNumber: [''],
            orderNumber: [''],
            customerId: ['', [Validators.required]],
            jobAddress: ['', [Validators.required]],
            latLon: [''],
            jobTitle: ['', [Validators.required]],
            jobNotes: [''],
            dueDate: [null],
            priority: [0],
            startTime: [''],
            durationHours: [1],
            durationMinutes: [0],
            finishTime: [''],
            teamMemberID: [''],
            adminNote: [''],
            jobStatus: [''],
            attachedFiles: [[]],
            tags: [[]],
            status: [true],
            pendingCopyToggle: [false],
            dispatchStatus: [''],
            pendingTitle: [''],
            jobCost: [0],
            jobTags: [[]],

        });










        this.hoursFilteredOptions = this.hoursInputCtrl.valueChanges.pipe(
            startWith(''),
            map(value => this._hoursFilter(value || '')),
        );


        /// DO NOT DELETE THIS CODE ///
        // Subscribe to media changes Side bar Mode ///
        // this._fuseMediaWatcherService.onMediaChange$
        //     .pipe(takeUntil(this._unsubscribeAll))
        //     .subscribe(({ matchingAliases }) => {
        //         // Set the drawerMode and drawerOpened
        //         if (matchingAliases.includes('lg')) {
        //             this.drawerMode = 'side';
        //             this.drawerOpened = true;
        //         }
        //         else {
        //             this.drawerMode = 'side';
        //             this.drawerOpened = false;
        //         }
        //     });
        /// Set duration Hours and minutes relationship
        /// DO NOT DELETE THIS CODE ///
        this.jobForm.get('durationHours').valueChanges.subscribe(value => {
            if (value === null || value === '') {
                this.jobForm.get('durationMinutes').setValue(this.jobForm.get('durationMinutes').value || 0);
            }
        });

        this.jobForm.get('durationMinutes').valueChanges.subscribe(value => {
            if (value === null || value === '') {
                this.jobForm.get('durationHours').setValue(this.jobForm.get('durationHours').value || 0);
            }
        });



        this.loadGroups();
        this.formReady = true;

        // Load Presets
        this._schedulePresetsService.getSchedulePresets()
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((presets) => {
                // No need to store presets locally as they're already in the service
                this._changeDetectorRef.markForCheck();
            });

        // Initialize the filtered presets observable
        this.initPresetSearch();




    }
    /**
       * On destroy
       */
    ngOnDestroy(): void {
        // Unsubscribe from real-time data
        this._scheduledJobsService.unsubscribeFromRealTimeData();

        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();

        // Clean up form and UI
        this.cleanupForm();

        // Dispose overlays
        if (this._tagsPanelOverlayRef) {
            this._tagsPanelOverlayRef.dispose();
        }

        // Destroy the drag helper if it exists
        if (this.drag) {
            try {
                this.drag.destroy();
                this.drag = null;
            } catch (error) {
                console.warn('Error destroying drag helper:', error);
            }
        }

        // Clean up scheduler instance
        if (this.scheduler && this.scheduler.instance) {
            try {
                // Remove any event listeners
                this.scheduler.instance.removeAllListeners();

                // Unmask if masked
                if (typeof this.scheduler.instance.unmaskBody === 'function') {
                    this.scheduler.instance.unmaskBody();
                }

                // Clear any pending operations
                if (this.scheduler.instance.eventStore) {
                    this.scheduler.instance.eventStore.commit();
                }
            } catch (error) {
                console.warn('Error cleaning up scheduler:', error);
            }
        }
    }

    ngAfterViewInit(): void {
        // Load jobs and other data after view init
        setTimeout(() => {
            this.loadJobList(new Date(), new Date(), null);
            this.loadPendingJobList();
            this.loadingJobTemplates();

            if (this.scheduler?.instance) {
                this.schedulerClickHandler();
                try {
                    this.scheduler.instance.scrollToNow({ block: 'center', animate: true });
                } catch (error) {
                    console.log(error);
                }

                // Wait for scheduler to be fully rendered before initializing drag
                setTimeout(() => {
                    // Initialize drag functionality after scheduler is fully rendered
                    if (this.selectedTab === 0) {
                        this.initDrag();
                    }
                }, 2000);
            }
        }, 500);




    }


    loadGroups() {
        this.isLoading = true;
        this._fuseLoadingService.show();

        this._groupService.getGroups().pipe(
            tap((res) => {
                if (!res || res.length === 0) {
                    this.isLoading = false;
                    this._fuseLoadingService.hide();
                }
            }),
            filter((groups) => groups.length > 0),
            switchMap(() => this._groupService.groups$.pipe(takeUntil(this._unsubscribeAll))),
            tap((groups) => {
                this.treeData = this.convertToTreeData(groups);
                this._fuseLoadingService.hide();
                this._changeDetectorRef.markForCheck();
            }),
            takeUntil(this._unsubscribeAll)
        ).subscribe({
            complete: () => {
                this.isLoading = false;
                this._fuseLoadingService.hide();
                this._changeDetectorRef.markForCheck();
            }
        });
    }





    onTabClick(tabIndex: number): void {
        this.selectedTab = tabIndex;
        if (tabIndex === 0) {


            setTimeout(() => {
                this.loadPendingJobList();
                this.loadingJobTemplates();
                this.loadJobList(new Date(), new Date(), null);
                this.schedulerClickHandler();

                this.scheduler.instance.scrollToNow({ block: 'center', animate: true });

            });




        } else if (tabIndex === 1) {



            setTimeout(() => {
                const _start = new Date();
                const _end = new Date();
                this.selectTabCalendarPreset({ start: _start, end: _end });
                this.loadJobList(_start, _end, null);
            });
        }


    }
    schedulerClickHandler() {
        if (this.scheduler.instance) {
            try {
                this.scheduler.instance.on({
                    beforeEventEdit: this.editSchedule.bind(this),
                    // React to click events
                    eventClick: ({ eventRecord, eventElement }) => {


                        this.loadSelectedEvent(eventRecord, eventRecord.id, null);

                    },

                    eventDrag: ({ newResource }) => {
                        //   console.log('Row mouse enter detected on event:', newResource);
                        // Get mouse position
                        //this._scheduledJobsService.pauseRealTimeData();
                        const mouseX = newResource.clientX;
                        const mouseY = newResource.clientY;

                        // Find the cell under the mouse
                        const cells = document.querySelectorAll('.b-sch-timeaxis-cell');
                        let hoveredCell = null;
                        cells.forEach(cell => {
                            const rect = cell.getBoundingClientRect();
                            if (mouseX >= rect.left && mouseX <= rect.right && mouseY >= rect.top && mouseY <= rect.bottom) {
                                hoveredCell = cell;
                            }
                        });

                        // Update the background color
                        if (this.lastHoveredCell && this.lastHoveredCell !== hoveredCell) {
                            this.lastHoveredCell.style.backgroundColor = ''; // Reset previous cell
                        }
                        if (hoveredCell) {
                            hoveredCell.style.backgroundColor = '#bcc4f5'; // Set new cell
                            this.lastHoveredCell = hoveredCell;
                        }
                    },

                    afterEventDrop: (eventRecord) => {
                        // console.log('Drop detected on event:', eventRecord);

                        this._scheduledJobsService.unsubscribeFromRealTimeData();
                        this._scheduledJobsService.listenToRealTimeData(this.schedulerSelectedDate);
                    },
                    // React to double click events
                    eventDblClick: ({ eventRecord, eventElement }) => {
                        //   console.log('Double-click detected on event:', eventRecord);

                    },
                    // React to  click events on the scheduler background
                    scheduleClick: ({ date, resourceRecord, event }) => {
                        //   console.log('Scheduler was clicked at date:', date);



                        this.addNewJob(date, resourceRecord);
                        this._changeDetectorRef.detectChanges();
                    },

                    afterDragCreate: ({ eventRecord, resourceRecord, eventElement }) => {
                        // console.log('Drag-create detected on event:', eventRecord.data.startDate);




                        const duration = this.convertDurationToHoursAndMinutes(eventRecord.data.duration * 24);
                        //TODO: Adds Dummy
                        // console.log('durationObj on Scheduler:', duration);

                        this.addNewJob(eventRecord.data.startDate, resourceRecord, eventRecord.data.endDate, duration);


                        this._changeDetectorRef.detectChanges();
                    },
                    // React to double click events on the scheduler background



                    scheduleDblClick: (eventContext) => {
                        //  console.log('Double-click detected on the scheduler.', eventContext);


                    },
                    // rowMouseEnter: ({ eventRecord, cellElement }) => {
                    //     console.log('Row mouse enter detected on event:', cellElement);
                    // }
                    // On Event resize
                    beforeEventResize: ({ eventRecord }) => {
                        const jobStatus = eventRecord.data.jobStatus;
                        if (jobStatus === 'finished') {
                            this._snackBar.open('Job already finished and cannot be edited', 'Close', { duration: 3000 });
                            return false;
                        }
                        return true;
                    },
                    beforeEventDrag: ({ eventRecord }) => {
                        this._teamMembersService.pauseRealTimeUpdates();
                        this._scheduledJobsService.pauseRealTimeData();
                        if (!eventRecord || !eventRecord.length) {
                            return;
                        }
                        //console.log('before', eventRecord.data.jobStatus);
                        const jobStatus = eventRecord.data.jobStatus;
                        if (jobStatus !== 'pending' && jobStatus !== 'added' && jobStatus !== 'approved') {
                            return false;
                        }
                        //   this._scheduledJobsService.pauseRealTimeData();
                        this.scheduler.instance.maskBody('<div style="text-align: center">Updating...</div>');
                        return true;
                    },
                    beforeeventdropfinalize: ({ context }) => {
                        // Show loading mask before the drop is processed
                        context.async = true;
                        this._teamMembersService.pauseRealTimeUpdates();
                        this._scheduledJobsService.pauseRealTimeData();
                        // `true` to perform the drop, `false` to ignore it
                        this.scheduler.instance.maskBody('<div style="text-align: center">Updating...</div>');
                        this._changeDetectorRef.detectChanges();

                        context.finalize(true);



                    },
                    eventDrop: ({ eventRecords, startDate }) => {

                        // add loading to bryntum Scheduler
                        this.scheduler.instance.maskBody('<div style="text-align: center">Updating...</div>');
                        this._changeDetectorRef.detectChanges();
                        this._changeDetectorRef.markForCheck();

                        if (!eventRecords || !eventRecords.length) {
                            return;
                        }
                        const eventRecord = eventRecords[0];
                        if (!eventRecord || !eventRecord.data) {
                            return;
                        }

                        const startTime = this.getRoundedTime(eventRecord.data.startDate);
                        const element = this.scheduler.instance.getElementFromEventRecord(eventRecord);
                        if (element) {
                            element.classList.add('b-sch-event-selected');
                        }
                        this.updateOnDragEvent(
                            eventRecord.data.id,
                            eventRecord.data.resourceId,
                            eventRecord.data.startDate,
                            startTime
                        );

                    },
                    eventResizeEnd: ({ eventRecord }) => {

                        const startTime = this.getRoundedTime(eventRecord.data.startDate);
                        const totalHours = eventRecord.duration * 24; // Convert fraction of a day to hours
                        const hours = Math.floor(totalHours); // Extract whole hours
                        const minutes = Math.round((totalHours - hours) * 60);
                        // console.log(hours, minutes);
                        this.updateOnDragEvent(eventRecord.data.id, eventRecord.data.resourceId, eventRecord.data.startDate, startTime, hours, minutes);
                    },
                    eventDragAbort: () => {
                        // Resume updates if drag is aborted
                        // Resume updates if drag is aborted
                        this.resumeAllRealTimeUpdates();
                        this.scheduler.instance.unmaskBody();

                    }



                });
            } catch (error) {

            }
        }
    }
    private resumeAllRealTimeUpdates(): void {
        this._teamMembersService.resumeRealTimeUpdates();
        this._scheduledJobsService.enableRealTimeData();
        this._scheduledJobsService.listenToRealTimeData(this.schedulerSelectedDate);
    }
    /**
     * Initialize EventStore and ResourceStore for direct data management
     */
    private initializeStores(): void {
        // Initialize EventStore with CustomEventModel
        this.eventStore = new EventStore({
            modelClass: CustomEventModel,
            autoCommit: true,
            syncDataOnLoad: true,
            data: []
        });

        // Initialize ResourceStore with CustomResourceModel
        this.resourceStore = new ResourceStore({
            modelClass: CustomResourceModel,
            autoCommit: true,
            syncDataOnLoad: true,
            data: []
        });

        // Update scheduler config to use the stores
        this.schedulerConfig.eventStore = this.eventStore;
        this.schedulerConfig.resourceStore = this.resourceStore;
    }

    private setupFilteredOptions() {
        try {
            return this.customerInputCtrl.valueChanges.pipe(

                startWith(''),
                map(value => typeof value === 'string' ? value : value?.name),
                map(name => name ? this._filterCustomers(name) : this.customers.slice())
            );
        } catch (error) {
            console.error('Error:', error);
        }


    }
    private _filterCustomers(value: string): Customer[] {
        const filterValue = value.toLowerCase().trim();

        return this.customers.filter(customer => {
            const nameMatch = customer.name.toLowerCase().trim().includes(filterValue);

            const phoneNumberMatch = customer.phoneNumbers?.some(phoneNumberObj => {

                let phoneNum;
                if (typeof phoneNumberObj === 'string') {
                    try {
                        // Parse the JSON string to an object
                        phoneNum = JSON.parse(phoneNumberObj);
                        //  console.log(phoneNum.phoneNumber);
                        return phoneNum.phoneNumber.includes(filterValue); // Return the result here
                    } catch (error) {
                        console.error('Error parsing phone number JSON:', error);
                        // Handle the error or return a default value
                        phoneNum = { phoneNumber: 'Invalid Number', label: '' };
                    }
                }

                return false; // Return false if phoneNum is not defined

            });

            return nameMatch || phoneNumberMatch;
        });
    }






    private _filterTeamMembers(value: string): TeamMember[] {
        const filterValue = value.toLowerCase();
        return this.teamMembers.filter(teamMember => teamMember.name.toLowerCase().includes(filterValue));
    }
    private _hoursFilter(value: string): string[] {
        const filterValue = value.toLowerCase();

        return this.hoursList.filter(option => option.toLowerCase().includes(filterValue));
    }

    displayFn(customer: Customer): string {
        // console.log(customer);
        return customer && customer.name ? customer.name : '';
    }

    onHoursSelected(event: MatAutocompleteSelectedEvent): void {

        this.jobForm.controls.startTime.setValue(event.option.value);
        this.newJob.startTime = event.option.value;
        event.option.deselect();
    }
    onTeamMemberSelected(event: MatAutocompleteSelectedEvent): void {

        const teamMember = event.option.value as TeamMember;
        this.jobForm.controls.teamMemberID.setValue(teamMember.$id);
        this.newJob.teamMemberID = teamMember.$id;
        event.option.deselect();
        // console.log(teamMember);

    }

    onCustomerSelected(event: MatAutocompleteSelectedEvent): void {
        const customer = event.option.value as Customer;
        this.jobForm.controls.customerId.setValue(customer.$id);
        //Load list of existing address for select
        //console.log(customer);
        this.selectedCustomer = customer;
        this.selectedAddresses = this.selectedCustomer.addresses.map(item => {
            // Check if item is a string before parsing
            if (typeof item === 'string') {
                try {
                    return JSON.parse(item);
                } catch (error) {
                    console.error('Error parsing JSON:', item, error);
                    return null;  // or however you want to handle parsing errors
                }
            } else if (typeof item === 'object') {
                return item;  // Item is already an object, no parsing needed
            } else {
                console.error('Unexpected item type:', typeof item, item);
                return null;  // or however you want to handle unexpected item types
            }
        });

        event.option.deselect();

    }

    formatPhoneNumbers(phoneNumbers: string[]): string {
        if (!phoneNumbers) {
            return '';
        }
        return phoneNumbers.map(phoneNumberString => {
            let phoneNumberObj;

            // Check if phoneNumberString is a JSON string
            if (typeof phoneNumberString === 'string' && phoneNumberString.trim().startsWith('{')) {
                try {
                    phoneNumberObj = JSON.parse(phoneNumberString);
                } catch (error) {
                    console.error('Error parsing phone number JSON', error);
                    // Handle the error or return a placeholder/fallback
                    return 'Invalid Number';
                }
            } else {
                // If it's not a string, assume it's already an object
                phoneNumberObj = phoneNumberString;
            }

            return `${phoneNumberObj.phoneNumber} (${phoneNumberObj.label})`;
        }).join(', ');
    }





    setAddress(item: any): void {
        //console.log(item);
        this.selectedAddress = item;
        this.jobForm.controls.jobAddress.setValue(item.address);
        this.jobForm.controls.latLon.setValue(item.latLon);
        this.markerPositions = []
        const [latStr, lngStr] = item.latLon.split(', ');
        const position: google.maps.LatLngLiteral = {
            lat: parseFloat(latStr),
            lng: parseFloat(lngStr),
        };

        const newMarker = {
            position,
            options: { animation: google.maps.Animation.BOUNCE },
        };

        this.markerPositions.push(newMarker.position);
        this.center = newMarker.position;
        this.zoom = 18;


    }


    onPendingCopyToggle(event: MatSlideToggleChange): void {
        const isChecked = event.checked;
        this.makePendingCopy = isChecked;
        if (isChecked) {
            const jobTitle = this.jobForm.controls.jobTitle.value;

            // Set 'pendingTitle' to the same value as 'jobTitle'
            this.jobForm.patchValue({
                pendingTitle: jobTitle
            });
        }


        // console.log('Toggle is checked:', isChecked);
    }
    clearAddress() {
        this.selectedAddress = null;
        this.jobForm.controls.jobAddress.setValue('');
        this.jobForm.controls.latLon.setValue('');
    }

    onAutocompleteSelected(location: any) {

        console.log(location);

        this.jobForm.controls.jobAddress.setValue(location.address);
        this.jobForm.controls.latLon.setValue(location.latLon);
        // Assuming 'addresses' is a FormArray inside your form
        this.selectedAddress = {
            address: location.address,
            latLon: location.latLon,
        };

        this.markerPositions = []
        const [latStr, lngStr] = location.latLon.split(', ');
        const position: google.maps.LatLngLiteral = {
            lat: parseFloat(latStr),
            lng: parseFloat(lngStr),
        };

        const newMarker = {
            position,
            options: { animation: google.maps.Animation.BOUNCE },
        };

        this.markerPositions.push(newMarker.position);
        this.center = newMarker.position;
        this.zoom = 18;
    }
    editSchedule(event: any): boolean {

        const
            { target, eventRecord, resourceRecord, eventEdit } = event

        return false;

    };


    handleSelectedFiles(files: File[]): void {
        // process the selected files

        if (files.length > 0) {
            this.selectedFiles = files.map(file => {
                const url = URL.createObjectURL(file);
                return { filename: file.name, file: file, url: url };
            });
            //  console.log(this.selectedFiles);
        } else {
            this.selectedFiles = [];
        }
    }




    cancelUpdateDetails() {
        // Just clean up the form without changing the date
        this.cleanupForm();

        // Only reload the job list if it's empty
        if (this.scheduledJobList.length === 0) {
            // This loads jobs for the current date (without changing to today)
            this.loadJobList(this.schedulerSelectedDate, this.schedulerSelectedDate, null);
        }

        // Load pending jobs and templates if needed for the UI
        this.loadPendingJobList();
        this.loadingJobTemplates();
    }

    async cleanupForm(): Promise<void> {
        if (this.selectedNewEvent && !this.editMode) {
            // Remove from our EventStore directly
            if (this.eventStore) {
                this.eventStore.remove(this.selectedNewEvent.id);
                this.eventStore.commit();
            }
            this.selectedNewEvent = null;
        }
        if (this.assignTaskId.length > 0) {
            // Remove from our EventStore directly
            if (this.eventStore) {
                this.eventStore.remove(this.assignTaskId);
                this.eventStore.commit();
            }
            this.selectedNewEvent = null;
            this.assignTaskId = '';
        }
        this.selectedNewEvent = null;
        this.newJob = this.initializeToNull<ScheduledJob>();
        this.jobForm.reset({
            durationHours: [1],
            durationMinutes: [0],
            priority: [0],

            status: true,
            pendingCopyToggle: false,
        });
        this.selectedCustomer = null;
        this.customerInputCtrl.reset();
        this.teamMemberInputCtrl.reset();
        this.hoursInputCtrl.reset();

        this.makePendingCopy = false;
        this.editTemplate = false;

        this.selectedAddress = null;
        this.selectedAddresses = [];
        this.fileInputDropzone.reset();
        this.selectedFiles = [];
        this.center = { lat: -24, lng: 134 };
        this.zoom = 4;
        this.markerPositions = [];

        this.clearTeamMemberInput();
        this.clearCustomerInput();
        this.jobDrawer.close();
        this._changeDetectorRef.markForCheck();
        this._changeDetectorRef.detectChanges();
        this.isSaving = false;
        this.formReady = false;
        //this._schedulerService.cleanUp();
        if (this.selectedTab === 0 && this.eventStore) {
            // Update the EventStore with the current events
            this.eventStore.data = this.events;
            this.eventStore.commit();
        }

        //Reload List
        // this.loadJobList(this.schedulerSelectedDate)

        this._scheduledJobsService.unsubscribeFromRealTimeData();
        this._scheduledJobsService.listenToRealTimeData(this.schedulerSelectedDate);

        this.jobForm.enable();
        this.teamMemberInputCtrl.enable();
        this.customerInputCtrl.enable();
        this.hoursInputCtrl.enable();
        this.hoursInputCtrl.enable();


    }

    clearTeamMemberInput() {
        this.teamMemberInputCtrl.setValue('');
        this.newJob.teamMemberID = null;
        this.jobForm.get('teamMemberID').setValue('');
        this.jobForm.get('jobStatus').setValue('pending');
        this.jobForm.get('dispatchStatus').setValue('');
        this.newJob.jobStatus = 'pending';
        this.newJob.dispatchStatus = '';
        //  console.log(this.teamMembers);
        this.teamMembersFilteredOptions = this.teamMemberInputCtrl.valueChanges
            .pipe(
                startWith(''),
                map(value => value ? (typeof value === 'string' ? value : value.name) : ''),
                map(name => name ? this._filterTeamMembers(name) : this.teamMembers.slice())
            );
    }

    clearCustomerInput() {
        this.customerInputCtrl.setValue('');
        this.newJob.customerId = null;
        this.selectedCustomer = null;
        this.selectedAddresses = [];
        this.jobForm.get('customerId').setValue('');

        // Set up the filtered options observable
        this.customersFilteredOptions = this.customerInputCtrl.valueChanges
            .pipe(
                startWith(''),
                map(value => value ? (typeof value === 'string' ? value : value.name) : ''),
                map(name => name ? this._filterCustomers(name) : this.customers.slice())
            );
    }
    viewPastJob() {
        // this.selectedTab = 1;
        this.jobDrawer.close();
        //console.log(this.selectedCustomer);

        let dialogRef = this._orderHistoryDialog.open(OrderHistoryComponent, {
            data: { customerId: this.selectedCustomer.$id },

        });
        dialogRef.afterClosed().subscribe(result => {
            if (result) {

                this.loadSelectedEvent(
                    result,
                    result.$id
                )


            }


        });



    }

    setJobPriority(priority): void {
        // Set the value
        this.jobForm.get('priority').setValue(priority);
        this.newJob.priority = priority;
    }




    onDueDateSelect(event: MatDatepickerInputEvent<Date>) {
        const selectedDate = event.value;
        if (selectedDate) {
            this.newJob.dueDate = selectedDate;
        }
    }
    /**
   * Check if the Job is overdue or not
   */
    isOverdue(): boolean {
        return DateTime.fromJSDate(this.newJob.dueDate).startOf('day') < DateTime.now().startOf('day');
    }

    isOverdueDate(date: Date): boolean {
        return DateTime.fromJSDate(date).startOf('day') < DateTime.now().startOf('day');
    }



    eventClickHandler(event: { eventRecord?: EventModel }): void {
        const { eventRecord } = event;


        //  console.log(`Clicked ${eventRecord!.name}`)
    };



    //initializeToNull
    initializeToNull<T>(): T {
        const proxy = new Proxy({
            jobTags: [], // Initialize jobTags as empty array
            attachedFiles: [], // Also initialize attachedFiles as empty array
        }, {
            get: (target, prop) => {
                return prop in target ? target[prop] : null;
            }
        });

        // Create a new object with null values
        const obj = Object.assign({}, proxy);
        return obj as T;
    }

    onSchedulerDateChange(event: any) {
        const selectedDate = DateHelper.clearTime(event.value);
        this.schedulerSelectedDate = new Date(selectedDate);

        // Unsubscribe first to avoid multiple subscriptions
        this._scheduledJobsService.unsubscribeFromRealTimeData();

        // Load jobs for the new date
        this.loadJobList(this.schedulerSelectedDate, this.schedulerSelectedDate, null);

        // Subscribe to real-time updates for the new date
        this._scheduledJobsService.listenToRealTimeData(this.schedulerSelectedDate);

        this.scheduler.instance.setTimeSpan(this.schedulerSelectedDate);

        this.schedulerSelectedDate.setHours(new Date().getHours(), 0, 0, 0);
        this.scheduler.instance.scrollToDate(this.schedulerSelectedDate, { block: 'center', animate: true });
    }


    onPrevClick(): void {
        this.scheduler.instance.shiftPrevious();
        this.schedulerSelectedDate = new Date(
            this.schedulerSelectedDate.setDate(this.schedulerSelectedDate.getDate() - 1)
        );
        this.loadJobList(this.schedulerSelectedDate, this.schedulerSelectedDate, null);

        this._scheduledJobsService.unsubscribeFromRealTimeData();
        this._scheduledJobsService.listenToRealTimeData(this.schedulerSelectedDate);


        this.schedulerSelectedDate.setHours(new Date().getHours(), 0, 0, 0);
        this.scheduler.instance.scrollToDate(this.schedulerSelectedDate, { block: 'center', animate: true });

    }


    onTodayClick(): void {

        const selectedDate = DateHelper.clearTime(new Date()); // Get current date
        this.schedulerSelectedDate = new Date(selectedDate);

        this.loadJobList(this.schedulerSelectedDate, this.schedulerSelectedDate, null);
        this._scheduledJobsService.unsubscribeFromRealTimeData();

        this._scheduledJobsService.listenToRealTimeData(this.schedulerSelectedDate);
        this.scheduler.instance.setTimeSpan(this.schedulerSelectedDate);

        //this.scheduler.instance.scrollToDate(this.schedulerSelectedDate, { block: 'center' });
        this.scheduler.instance.scrollToNow({ block: 'center', animate: true });

    }

    onNextClick(): void {
        this.scheduler.instance.shiftNext();

        this.schedulerSelectedDate = new Date(
            this.schedulerSelectedDate.setDate(this.schedulerSelectedDate.getDate() + 1)
        );

        //  console.log('onNextClick', this.scheduler.instance.startDate, this.schedulerSelectedDate)
        this.loadJobList(this.schedulerSelectedDate, this.schedulerSelectedDate, null);

        this._scheduledJobsService.unsubscribeFromRealTimeData();
        this._scheduledJobsService.listenToRealTimeData(this.schedulerSelectedDate);

        this.schedulerSelectedDate.setHours(new Date().getHours(), 0, 0, 0);
        this.scheduler.instance.scrollToDate(this.schedulerSelectedDate, { block: 'center', animate: true });

    }


    initDrag(): void {
        try {
            // Don't initialize drag if not on the scheduler tab
            if (this.selectedTab != 0) {
                return;
            }

            // Clean up any existing drag helper to prevent memory leaks
            if (this.drag) {
                try {
                    this.drag.destroy();
                    this.drag = null;
                } catch (error) {
                    console.warn('Error destroying existing drag helper:', error);
                }
            }

            // Check if scheduler is fully initialized
            if (!this.scheduler || !this.scheduler.instance) {
                // Instead of retrying immediately, wait for the scheduler to be fully rendered
                console.log('Waiting for scheduler to be fully initialized...');
                setTimeout(() => this.initDrag(), 1000);
                return;
            }

            // Wait for the scheduler to be fully rendered and ready
            // Check if the scheduler DOM element is visible in the document
            try {
                const schedulerElement = document.querySelector('.b-scheduler');
                if (!schedulerElement || !this.scheduler.instance.isVisible) {
                    console.log('Waiting for scheduler to be fully rendered...');
                    setTimeout(() => this.initDrag(), 1000);
                    return;
                }
            } catch (error) {
                console.warn('Error checking scheduler visibility:', error);
                setTimeout(() => this.initDrag(), 1000);
                return;
            }

            // Get the scroll manager directly from the instance
            const scrollManager = this.scheduler.instance.scrollManager;

            if (!scrollManager) {
                // If scroll manager is still not available, try a different approach
                console.log('Creating drag helper without scroll manager...');

                this.drag = new DragHelper({
                    callOnFunctions: true,
                    cloneTarget: true,
                    autoSizeClonedTarget: false,
                    mode: 'translateXY',
                    dropTargetSelector: '.b-timeline-subgrid',
                    targetSelector: '.event-list-item',
                    constrain: false,
                    removeProxyAfterDrop: true,
                });
            } else {
                // Create the drag helper with scroll manager
                this.drag = new DragHelper({
                    callOnFunctions: true,
                    cloneTarget: true,
                    autoSizeClonedTarget: false,
                    mode: 'translateXY',
                    dropTargetSelector: '.b-timeline-subgrid',
                    targetSelector: '.event-list-item',
                    scrollManager: scrollManager as ScrollManager,
                    constrain: false,
                    removeProxyAfterDrop: true,
                });
            }

            this.drag.on({
                dragstart: this.handleDragStart.bind(this),
                drag: this.handleDrag.bind(this),
                drop: this.handleDrop.bind(this),
                dragend: this.handleDragEnd.bind(this),
                thisObj: this,

            });
        } catch (e) {
            console.error('Error initializing drag:', e);
        }
    }

    private handleDragStart({ context }: any): void {
        try {
            if (this.selectedTab != 0) {
                return; // Exit if the current tab is not the one that requires drag functionality
            }

            // Get the task from the HTML element
            context.task = this.getEventFromHtmlTag(context.grabbed);

            // Check if scheduler instance exists before accessing its properties
            if (this.scheduler?.instance?.element) {
                this.scheduler.instance.element.classList.add('b-dragging-event');
            }
        } catch (e) {
            console.error('Error handling drag start:', e);
        }
    }

    private handleDrag({ context }): void {
        try {
            // Ensure context exists
            if (!context) {
                console.error('Context is undefined in handleDrag');
                return;
            }

            // Safely extract task from context
            const task = context.task;
            if (!task) {
                console.warn('Task is undefined or null during drag - skipping drag handling');
                return;
            }

            // Pause real-time data updates during drag
            this._scheduledJobsService.pauseRealTimeData();

            // Ensure scheduler instance and required methods are available
            if (!this.scheduler) {
                console.warn('Scheduler component is not available');
                return;
            }

            if (!this.scheduler.instance) {
                console.warn('Scheduler instance is not available');
                return;
            }

            if (typeof this.scheduler.instance.getDateFromCoordinate !== 'function') {
                console.warn('getDateFromCoordinate method is not available on scheduler instance');
                return;
            }

            // Get element position safely
            const elementX = context.element ? DomHelper.getTranslateX(context.element) : null;
            if (elementX === null) {
                console.warn('Could not get element X position');
                return;
            }

            // Get date from coordinate with proper error handling
            let startDate;
            try {
                startDate = this.scheduler.instance.getDateFromCoordinate(elementX, 'round', false);
            } catch (error) {
                console.error('Error getting date from coordinate:', error);
                return;
            }

            // Only calculate endDate if startDate exists and task has duration properties
            const endDate = (startDate && task.duration && task.durationUnit) ?
                DateHelper.add(startDate, task.duration, task.durationUnit) : null;

            // Get resource if target exists
            const resource = context.target && this.scheduler.instance.resolveResourceRecord(context.target);

            // Save reference to resource for use in onTaskDrop
            context.resource = resource;

            // Always set valid to true to allow the drop
            context.valid = true;

            // Get mouse position
            const mouseX = context.clientX;
            const mouseY = context.clientY;

            // Find the cell under the mouse
            const cells = document.querySelectorAll('.b-sch-timeaxis-cell');
            let hoveredCell = null;
            cells.forEach(cell => {
                const rect = cell.getBoundingClientRect();
                if (mouseX >= rect.left && mouseX <= rect.right && mouseY >= rect.top && mouseY <= rect.bottom) {
                    hoveredCell = cell;
                }
            });

            // Update the background color
            if (this.lastHoveredCell && this.lastHoveredCell !== hoveredCell) {
                this.lastHoveredCell.style.backgroundColor = ''; // Reset previous cell
            }
            if (hoveredCell) {
                hoveredCell.style.backgroundColor = '#bcc4f5'; // Set new cell
                this.lastHoveredCell = hoveredCell;
            }
            let dummyEvent = this.scheduler.instance.eventStore.getById('dummy') as EventModel;
            const resourceRecord = context.target ? this.scheduler.instance.resolveResourceRecord(context.target) : null;
            const date = this.scheduler.instance.getDateFromCoordinate(
                DomHelper.getTranslateX(context.element), 'round', false
            );

            if (!dummyEvent && resourceRecord && date) {
                // Create dummy event if it doesn't exist
                const dummyEvent = {
                    id: 'dummy',
                    resourceId: resourceRecord.id,
                    startDate: date,
                    endDate: new Date(date.getTime() + 1 * 60 * 60 * 1000), // Assuming 1 hour duration
                    name: task.name + '| ' + task.customerName,
                    eventColor: 'orange',



                };

                this.scheduler.instance.eventStore.add(dummyEvent);

            } else if (dummyEvent && resourceRecord && date) {
                // Update dummy event if it exists
                dummyEvent.resourceId = resourceRecord.id;
                dummyEvent.startDate = date;
                dummyEvent.endDate = new Date(date.getTime() + 1 * 60 * 60 * 1000); // Update the end date as well
            }

        } catch (e) {
            console.error('Error updating drag:', e);
        }

    }


    private handleDrop({ context }): void {
        try {
            // Ensure context exists
            if (!context) {
                console.error('Context is undefined in handleDrop');
                return;
            }

            // Always clean up the dummy event first to prevent UI issues
            try {
                if (this.scheduler?.instance?.eventStore) {
                    const dummyEvent = this.scheduler.instance.eventStore.getById('dummy');
                    if (dummyEvent) {
                        this.scheduler.instance.eventStore.remove(dummyEvent);
                    }
                }
            } catch (cleanupError) {
                console.warn('Error cleaning up dummy event:', cleanupError);
            }

            // Re-enable real-time data updates regardless of drop outcome
            this._scheduledJobsService.enableRealTimeData();

            // Ensure the scheduler is unmasked if it was masked
            if (this.scheduler?.instance?.unmaskBody) {
                try {
                    this.scheduler.instance.unmaskBody();
                } catch (error) {
                    console.warn('Error unmasking scheduler body:', error);
                }
            }

            // Force UI update
            this._changeDetectorRef.markForCheck();
            this._changeDetectorRef.detectChanges();

            // Restart real-time data subscription with current date
            this._scheduledJobsService.unsubscribeFromRealTimeData();
            this._scheduledJobsService.listenToRealTimeData(this.schedulerSelectedDate);

            // Extract context properties safely
            const valid = context.valid;
            const target = context.target;

            // Process the drop if valid and has a target
            if (valid && target) {
                this.processValidDrop(context);
            } else {
                // Abort the drag operation if not valid
                if (this.drag) {
                    this.drag.abort();
                }

                // Clean up UI elements
                if (this.scheduler?.instance) {
                    // Disable tooltip if it exists
                    if (this.scheduler.instance.features?.eventTooltip) {
                        this.scheduler.instance.features.eventTooltip.disabled = false;
                    }

                    // Remove dragging class if element exists
                    if (this.scheduler.instance.element) {
                        this.scheduler.instance.element.classList.remove('b-dragging-event');
                    }

                    // Remove dummy event if eventStore exists
                    if (this.scheduler.instance.eventStore) {
                        try {
                            this.scheduler.instance.eventStore.remove('dummy');
                        } catch (removeError) {
                            console.warn('Error removing dummy event:', removeError);
                        }
                    }
                }
            }

            // Reset the background color of the cell
            if (this.lastHoveredCell) {
                this.lastHoveredCell.style.backgroundColor = ''; // Reset to original color
            }
        } catch (e) {
            console.error('Error handling drop:', e);

            // Attempt cleanup even if there was an error
            try {
                if (this.scheduler?.instance) {
                    if (this.scheduler.instance.element) {
                        this.scheduler.instance.element.classList.remove('b-dragging-event');
                    }
                    if (this.scheduler.instance.eventStore) {
                        this.scheduler.instance.eventStore.remove('dummy');
                    }
                }
            } catch (cleanupError) {
                console.warn('Error during cleanup:', cleanupError);
            }
        }
    }
    private handleDragEnd(): void {
        // Reset any highlighted cells
        if (this.lastHoveredCell) {
            this.lastHoveredCell.style.backgroundColor = ''; // Reset to original color
            this.lastHoveredCell = null;
        }

        // Resume real-time updates
        this._scheduledJobsService.enableRealTimeData();
    }

    private processValidDrop(context: any): void {
        try {
            // Ensure context exists
            if (!context) {
                console.error('Context is undefined in processValidDrop');
                return;
            }

            const { task, target } = context;

            // Ensure task exists
            if (!task) {
                console.warn('Task is undefined in processValidDrop');
                if (context.finalize && typeof context.finalize === 'function') {
                    context.finalize();
                }
                return;
            }

            // Ensure scheduler instance and required methods are available
            if (!this.scheduler) {
                console.warn('Scheduler component is not available');
                if (context.finalize && typeof context.finalize === 'function') {
                    context.finalize();
                }
                return;
            }

            if (!this.scheduler.instance) {
                console.warn('Scheduler instance is not available');
                if (context.finalize && typeof context.finalize === 'function') {
                    context.finalize();
                }
                return;
            }

            if (typeof this.scheduler.instance.getDateFromCoordinate !== 'function') {
                console.warn('getDateFromCoordinate method is not available on scheduler instance');
                if (context.finalize && typeof context.finalize === 'function') {
                    context.finalize();
                }
                return;
            }

            // Get element position safely
            const elementX = context.element ? DomHelper.getTranslateX(context.element) : null;
            if (elementX === null) {
                console.warn('Could not get element X position');
                if (context.finalize && typeof context.finalize === 'function') {
                    context.finalize();
                }
                return;
            }

            // Get date from coordinate with proper error handling
            let date;
            try {
                date = this.scheduler.instance.getDateFromCoordinate(elementX, 'round', false);
            } catch (error) {
                console.error('Error getting date from coordinate:', error);
                if (context.finalize && typeof context.finalize === 'function') {
                    context.finalize();
                }
                return;
            }

            // Try to resolve event record if target exists
            let targetEventRecord = null;
            if (target && this.scheduler.instance.resolveEventRecord) {
                try {
                    targetEventRecord = this.scheduler.instance.resolveEventRecord(
                        target.closest('.b-nested-events-parent')
                    );
                } catch (err) {
                    console.warn('Could not resolve event record:', err);
                }
            }

            // Only proceed if we have a valid date and resource
            if (date && context.resource) {
                this.updateTaskWithNewDate(task, date, context.resource);
                this.assignTaskToTeamMember(task);
            } else {
                console.warn('Missing date or resource for drop operation');
            }

            // Handle dropping on an existing event if needed
            if (targetEventRecord) {
                // Handle the scenario of dropping on an existing event
                // For example: WidgetHelper.toast(`Dropped on ${targetEventRecord.name}`);
                console.log('Dropped on existing event:', targetEventRecord);
            }

            // Always finalize the drop operation
            context.finalize();
        } catch (e) {
            console.error('Error processing valid drop:', e);
            // Try to finalize even if there's an error
            try {
                context.finalize();
            } catch (finalizeError) {
                console.error('Error finalizing drop:', finalizeError);
            }
        }
    }

    private updateTaskWithNewDate(task: any, date: Date, resource): void {
        task.startDate = date;
        task.endDate = new Date(task.startDate.getTime() + 1 * 60 * 60 * 1000); // Assuming 1 hour duration
        task.resourceId = resource.id;
        task.resource = resource;
        task.duration = 1;
        task.durationUnit = 'h';
        // Additional logic if needed, e.g., updating the event store
    }


    getEventFromHtmlTag(element: HTMLElement): Partial<CustomEventModel> | null {
        // Query for the hidden input element with class name 'eventID' within the passed element
        const inputElement = element.querySelector('input.eventID') as HTMLInputElement;

        // Ensure the input element was found and has a value
        if (inputElement && inputElement.value) {
            // Parse the value to a number assuming it's an ID
            const eventId = Number(inputElement.value);

            // Find the corresponding event config in the pendingEvents array
            if (!this.jobTemplateListVisible) {
                const eventConfig = this.pendingEvents.find(event => event.id === inputElement.value);

                // Return the found event config, or null if none was found
                return eventConfig || null;
            } else {
                const eventConfig = this.jobsTemplates.find(event => event.id === inputElement.value);

                // Return the found event config, or null if none was found
                return eventConfig || null;
            }
        }

        // If the input element wasn't found or didn't have a value, return null
        return null;
    }



    /**
        * Open compose dialog
        */
    openComposeDialog(mode: string): void {
        // Open the dialog
        if (mode === 'new') {
            const dialogRef = this._matDialog.open(SchedulerContactsComponent);
            dialogRef.afterClosed()
                .subscribe((result) => {
                    //  console.log(result);
                    //  console.log('Compose dialog was closed!');
                    dialogRef.close();
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();
                });
        } else if (mode === 'edit') {
            const dialogRef = this._matDialog.open(SchedulerContactsComponent, {
                data: mode === 'edit' ? { selectedCustomer: this.selectedCustomer } : {}
            });

            dialogRef.afterClosed()
                .subscribe((result) => {
                    //  console.log(result);
                    //  console.log('Compose dialog was closed!');
                    this.clearCustomerInput();

                    dialogRef.close();
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();
                });
        }

    }



    //selectedTableFilter


    getApprovedStatus(approved: string): number {
        if (approved === 'all') {
            return this.scheduledJobList.length;

        }
        return this.scheduledJobList.filter(item => item.jobStatus === approved).length;
    }



    applyPendingFilter(event: Event) {
        const filterValue = (event.target as HTMLInputElement).value.trim().toLowerCase();
        this.filterPendingText = filterValue;
        this.filteredPendingEvents = this.pendingEvents.filter(event =>
            event.name.toLowerCase().includes(filterValue) ||
            event.customerName.toLowerCase().includes(filterValue) ||
            event.address.toLowerCase().includes(filterValue) ||
            event.customerPhone.toLowerCase().includes(filterValue)
        );
    }

    clearFilter() {
        this.filterPendingText = '';
        this.filteredPendingEvents = [...this.pendingEvents];
    }

    applyRecurringFilter(event: Event) {
        const filterValue = (event.target as HTMLInputElement).value.trim().toLowerCase();
        this.filterJobsTemplatesText = filterValue;
        this.filteredJobsTemplates = this.jobsTemplates.filter(event =>
            event.name.toLowerCase().includes(filterValue) ||
            event.customerName.toLowerCase().includes(filterValue) ||
            event.address.toLowerCase().includes(filterValue) ||
            event.customerPhone.toLowerCase().includes(filterValue)
        );

    }
    clearRecurringFilter() {
        this.filterJobsTemplatesText = '';
        this.filteredJobsTemplates = [...this.jobsTemplates];

    }


    //Job List  TAB Calendar

    // onSelectedJobLisCalendarChange(selectedDate: Date) {

    //     this.loadJobList(selectedDate, null);
    //     //  console.log('onSelectedJobLisCalendarChange:', selectedDate);
    //     (selectedChange)="
    //     onSelectedJobLisCalendarChange($event)
    // "
    // [(selected)]="selectedJobListDate"

    // }

    // #tabCalendar

    rangeChanged(selectedDate: Date) {

        if (
            this.calenderSelectedDate &&
            this.calenderSelectedDate.start &&
            selectedDate > this.calenderSelectedDate.start &&
            !this.calenderSelectedDate.end
        ) {
            this.calenderSelectedDate = new DateRange(
                this.calenderSelectedDate.start,
                selectedDate
            );
        } else {
            this.calenderSelectedDate = new DateRange(selectedDate, null);
        }
        //console.log(this.calenderSelectedDate.start, this.calenderSelectedDate.end)
        this.loadJobList(this.calenderSelectedDate.start, this.calenderSelectedDate.end, null);
    }

    selectTabCalendarPreset(presetDateRange: { start: Date; end: Date }) {
        this.calenderSelectedDate = new DateRange<Date>(
            presetDateRange.start,
            presetDateRange.end
        );

        // Jump into month of presetDateRange.start
        if (presetDateRange.start && this.calendar)
            this.calendar._goToDateInView(presetDateRange.start, 'month');
        this.loadJobList(presetDateRange.start, presetDateRange.end, null);
        this._changeDetectorRef.markForCheck();
        this._changeDetectorRef.detectChanges();
    }



    /// Load JobList Table ///
    loadJobList(selectedDate: Date, endDate: Date, clientID?: string, jobStatus?: string) {
        if (!endDate) {
            endDate = selectedDate;
        }
        this._fuseLoadingService.show();
        this.loadingData = true;
        this.scheduledJobList = [];
        // Store the current date for filtering
        this.schedulerSelectedDate = new Date(selectedDate);

        // Clear the EventStore before loading new data
        if (this.eventStore) {
            this.eventStore.removeAll();
        }

        const jobsObservable = clientID
            ? this._scheduledJobsService.getScheduledJobs(null, null, clientID, jobStatus || null)
            : this._scheduledJobsService.getScheduledJobs(selectedDate, endDate, null, jobStatus || null);

        jobsObservable.pipe(
            switchMap(scheduledJobs => {
                if (!scheduledJobs || scheduledJobs.length === 0) {
                    return of([]); // Returns an observable of an empty array if there are no scheduled jobs
                }
                return this._scheduledJobsService.scheduledJobs$.pipe(takeUntil(this._unsubscribeAll));
            }),
            takeUntil(this._unsubscribeAll)
        ).subscribe(scheduledJobs => {
            this.scheduledJobList = scheduledJobs;

            if (this.selectedTab === 0) {
                this.processEventsForScheduler(selectedDate);
            }

            this.finalizeDataLoad();
        });
    }

    private processEventsForScheduler(selectedDate: Date) {
        // Clear existing events if any
        this.events = [];

        // Clear the EventStore directly
        if (this.eventStore) {
            this.eventStore.removeAll();
        }

        // Also clear the scheduler instance if it exists
        if (this.scheduler?.instance?.eventStore) {
            this.scheduler.instance.eventStore.removeAll();
            this.scheduler.instance.events = [];
        }

        // Showing loading indication in the scheduler
        //  this.scheduler.instance.maskBody('<div style="text-align: center">Loading...</div>');

        // Process each job item to create an event for the scheduler
        const eventsToAdd = [];

        this.scheduledJobList.forEach(item => {
            if (item.teamMemberID && item.teamMemberID !== '') {
                const _startDate = this.setTimeToDate(item.dueDate, item.startTime);
                const _endDate = this.addDurationToDate(_startDate, item.durationHours, item.durationMinutes);
                const _customer = this.getCustomerName(item.customerId);
                const _IconColorStatus = this.getEventAttributes(item);

                const eventModel = new CustomEventModel({
                    id: item.$id,
                    resourceId: item.teamMemberID,
                    startDate: _startDate,
                    endDate: _endDate,
                    eventColor: _IconColorStatus.eventColor,
                    name: item.jobTitle,
                    icon: _IconColorStatus.icon,
                    address: item.jobAddress,
                    customerName: _customer.name,
                    customerPhone: _customer.contact,
                    jobId: item.jobNumber,
                    jobStatus: item.jobStatus,
                    progress: item.driverNotes && item.driverNotes.length > 0 ? 100 : 0,
                    driverAttachedFiles: item.driverAttachedFiles && item.driverAttachedFiles.length > 0 ? true : false,
                    eventStyle: 'colored',
                });

                // Add to local events array for backward compatibility
                this.events.push(eventModel);

                // Add to the collection for batch addition to EventStore
                eventsToAdd.push(eventModel);
            }
        });

        // Add all events to the EventStore in a single batch operation
        if (this.eventStore && eventsToAdd.length > 0) {
            this.eventStore.add(eventsToAdd);
            this.eventStore.commit(); // Commit changes to update the UI
        }

    }


    private finalizeDataLoad() {
        this._fuseLoadingService.hide();
        this.loadingData = false;
        this._changeDetectorRef.markForCheck();
        //this._changeDetectorRef.detectChanges();
    }

    loadPendingJobList() {
        this._fuseLoadingService.show();
        this.loadingPendingData = true;
        if (this.selectedTab === 0) {
            this._scheduledJobsService.getScheduledPendingJobs()
                .pipe(
                    switchMap(scheduledJobs => {
                        if (!scheduledJobs || scheduledJobs.length === 0) {
                            return of([]); // Returns an observable of an empty array if there are no scheduled jobs
                        }
                        return this._scheduledJobsService.scheduledPendingJobs$.pipe(takeUntil(this._unsubscribeAll));
                    }),
                    takeUntil(this._unsubscribeAll)
                )
                .subscribe(scheduledJobs => {
                    this.pendingEvents = scheduledJobs
                        .filter(item => !item.teamMemberID && item.jobStatus === 'pending') //
                        .map(item => {
                            const _startDate = this.setTimeToDate(item.dueDate, item.startTime);
                            const today = new Date(item.dueDate);
                            today.setHours(0, 0, 0, 0);
                            const _customer = this.getCustomerName(item.customerId);

                            return {
                                id: item.$id,
                                startDate: _startDate,
                                eventColor: 'gray',
                                name: item.jobTitle,
                                icon: 'fa-solid fa-check',
                                address: item.jobAddress,
                                customerName: _customer.name,
                                customerPhone: _customer.contact,
                                jobId: item.jobNumber,
                                dueDate: today,
                                jobStatus: item.jobStatus
                            };
                        });
                    this.filteredPendingEvents = this.pendingEvents;
                    this.pendingCount = this.pendingEvents.length;
                    this.handlePostLoadActions();
                });
        }
    }

    private handlePostLoadActions() {
        this._fuseLoadingService.hide();
        this.loadingPendingData = false;

        if (this.drag && this.selectedTab === 0) {
            this.drag.destroy();
            this.drag = null;
            this.initDrag();
        } else {
            this.initDrag();
        }

        this._changeDetectorRef.markForCheck();
        this._changeDetectorRef.detectChanges();
    }

    loadingJobTemplates() {
        this._fuseLoadingService.show();
        this.loadingTemplates = true;
        this._scheduledJobsService.getJobsTemplates()
            .pipe(
                switchMap(jobsTemplates => {
                    if (!jobsTemplates || jobsTemplates.length === 0) {
                        return of([]); // Returns an observable of an empty array if there are no scheduled jobs
                    }
                    return this._scheduledJobsService.jobsTemplates$.pipe(takeUntil(this._unsubscribeAll));
                }),
                takeUntil(this._unsubscribeAll)
            )
            .subscribe(jobsTemplates => {
                this.jobsTemplates = jobsTemplates
                    .filter(item => item.jobStatus === 'template')
                    .map(item => {
                        const _startDate = this.setTimeToDate(item.dueDate, item.startTime);
                        const today = new Date(item.dueDate);
                        today.setHours(0, 0, 0, 0);
                        const _customer = this.getCustomerName(item.customerId);

                        return {
                            id: item.$id,
                            startDate: _startDate,
                            eventColor: 'gray',
                            name: item.jobTitle,
                            icon: 'fa-solid fa-predecessor',
                            address: item.jobAddress,
                            customerName: _customer.name,
                            customerPhone: _customer.contact,
                            jobId: item.jobNumber,
                            dueDate: today,
                            jobStatus: item.jobStatus
                        };
                    });
                this.filteredJobsTemplates = this.jobsTemplates;
                this.handleTemplatePostLoadActions();

            });

    }

    private handleTemplatePostLoadActions() {
        this._fuseLoadingService.hide();
        this.loadingTemplates = false;

        if (this.drag && this.selectedTab === 0) {
            this.drag.destroy();
            this.drag = null;
            this.initDrag();
        } else {
            this.initDrag();
        }

        this._changeDetectorRef.markForCheck();
        this._changeDetectorRef.detectChanges();
    }

    /// CURD OPERATIONS START HERE //

    async addNewJob(date?, resourceRecord?, endDate?, duration?) {





        this.newJob = this.initializeToNull<ScheduledJob>();

        this.editMode = false;
        // const nowDate = new Date();
        // const formattedTime = nowDate.toLocaleTimeString('en-AU', { hour: '2-digit', minute: '2-digit' }).toUpperCase();

        const roundedTime = this.getRoundedTime(new Date());
        this.newJob.startTime = roundedTime;
        this.hoursInputCtrl.setValue(roundedTime);
        //console.log('Drag Con', resourceRecord)
        this.newJob.teamMemberID = null;
        if (date && resourceRecord) {
            //  console.log('resourceRecord date:', resourceRecord);
            const newEvent = {
                resourceId: resourceRecord.id,  // Assuming resourceRecord contains the resource id
                startDate: date,  // This line sets the startDate to where the user clicked
                endDate: endDate ?? new Date(date.getTime() + 1 * 60 * 60 * 1000),  // 30 minutes later
                name: '[New Job]',
                eventColor: 'light-gray',
                id: 'new',

                // ... other event properties
            };


            const roundedTime = this.getRoundedTime(date);
            this.newJob.startTime = roundedTime;
            this.hoursInputCtrl.setValue(roundedTime);
            this.newJob.teamMemberID = resourceRecord.id;
            this.jobForm.controls.teamMemberID.setValue(resourceRecord.$id);

            this.newJob.dueDate = date;




            if (this.teamMembersFilteredOptions) {
                this.teamMembersFilteredOptions.pipe(
                    catchError((error) => {
                        console.error('Error:', error);
                        // Handle the error here, for example by returning an empty array
                        return of([]);
                    })
                ).subscribe((filteredOptions) => {
                    const selectedTeamMember = filteredOptions.find(
                        (teamMember) => teamMember.$id === resourceRecord.id
                    );

                    if (selectedTeamMember) {
                        this.teamMemberInputCtrl.setValue(selectedTeamMember); // Update the form control with the selectedTeamMember;
                    }


                });
            }
            // Add the new event to the event store

            this.selectedNewEvent = newEvent;
            if (!endDate) {
                this.scheduler.instance.eventStore.add(newEvent);
            } else {
                this.jobForm.controls.durationHours.setValue(duration.durationHours);
                this.jobForm.controls.durationMinutes.setValue(duration.durationMinutes);
                this.newJob.durationHours = duration.durationHours;
                this.newJob.durationMinutes = duration.durationMinutes;

            }


        }

        this.jobForm.controls.durationHours.setValue(1);
        this.jobForm.controls.durationMinutes.setValue(0);

        this.newJob.organisationID = this._organisationID;
        this.newJob.$id = null;
        if (!date) {
            const roundedTime = this.getRoundedTime(new Date());
            this.newJob.startTime = roundedTime;
            this.hoursInputCtrl.setValue(roundedTime);
            const nowDate = new Date();
            nowDate.setHours(0, 0, 0, 0);
            this.newJob.dueDate = nowDate;
            this.newJob.teamMemberID = null;
        }
        this.newJob.jobStatus = 'pending';
        this.setJobPriority(0);
        this.newJob.priority = 0;

        this.newJob.jobCost = 0;

        this.editMode = false;
        this.jobForm.patchValue(this.newJob);
        this.jobDrawer.open();
        this.newJob.jobNumber = await this.jobNumberService.generateJobNumber();

        this.jobForm.patchValue(this.newJob);


    }





    assignTaskToTeamMember(task) {
        //  console.log('called:' + task);
        if (task.resourceId && task.id && task.jobStatus !== 'template') {
            this.assignTaskId = task.id;

            this.loadSelectedEvent(task, task.id, task.resourceId);


            const newEvent = {
                resourceId: task.resourceId,  // Assuming resourceRecord contains the resource id
                startDate: task.startDate,  // This line sets the startDate to where the user clicked
                endDate: new Date(task.startDate.getTime() + 1 * 60 * 60 * 1000),  // 30 minutes later
                name: task.name,
                eventColor: 'light-gray',
                id: task.id,

                // ... other event properties
            };

            this.scheduler.instance.features.eventTooltip.disabled = false;
            this.scheduler.instance.element.classList.remove('b-dragging-event');
            this.scheduler.instance.eventStore.remove('dummy');

            this.scheduler.instance.eventStore.add(newEvent);



        } else if (task.id && task.jobStatus === 'template') {
            this.pendingLoading = true;
            this._changeDetectorRef.markForCheck();
            this._changeDetectorRef.detectChanges();
            this._fuseLoadingService.show();
            this._scheduledJobsService.getJobsTemplate(task.id).subscribe(
                (_scheduledJob) => {
                    // console.log('create and add new event:', _scheduledJob);
                    _scheduledJob.jobStatus = 'added';
                    _scheduledJob.$id = null;
                    _scheduledJob.teamMemberID = task.resourceId;

                    const roundedTime = this.getRoundedTime(task.startDate);

                    _scheduledJob.startTime = roundedTime;
                    _scheduledJob.dueDate = task.startDate;

                    this._fuseLoadingService.show();
                    this._scheduledJobsService.createScheduledJob(_scheduledJob).subscribe(
                        () => {
                            this.upDateAddressIfNotExist(_scheduledJob.jobAddress, _scheduledJob.customerId, _scheduledJob.latLon);
                            this._fuseLoadingService.hide();
                            this.scheduler.instance.features.eventTooltip.disabled = false;
                            this.scheduler.instance.element.classList.remove('b-dragging-event');
                            this.scheduler.instance.eventStore.remove('dummy');

                            this.loadJobList(this.schedulerSelectedDate, this.schedulerSelectedDate, null);
                            this.cleanupForm();
                            this.pendingLoading = false;
                            this._changeDetectorRef.markForCheck();
                            this._changeDetectorRef.detectChanges();

                        }
                    )
                });

        }

    }


    loadSelectedEvent(eventRecord, rowID: string, teamMemberID?: string) {


        if (this.assignTaskId.length > 0 || teamMemberID === 'pending') {

            this.pendingLoading = true;

            this._fuseLoadingService.show();
            this._scheduledJobsService.getScheduledJob(rowID).subscribe(
                (_scheduledJob) => {
                    _scheduledJob.$id = rowID;
                    if (teamMemberID !== 'pending') {
                        _scheduledJob.teamMemberID = teamMemberID;
                        const roundedTime = this.getRoundedTime(eventRecord.startDate);
                        this.newJob.startTime = roundedTime;
                        _scheduledJob.startTime = roundedTime;
                        _scheduledJob.dueDate = eventRecord.startDate;
                        this.hoursInputCtrl.setValue(roundedTime);

                    }
                    if (_scheduledJob.jobCost === null) {
                        _scheduledJob.jobCost = 0;
                    }
                    this.setLoadedEventForm(_scheduledJob);
                    this.pendingLoading = false;
                    this._fuseLoadingService.hide();


                });

        } else if (eventRecord && eventRecord.jobStatus === 'template') {
            this.pendingLoading = true;

            this._fuseLoadingService.show();
            this._scheduledJobsService.getJobsTemplate(rowID).subscribe(
                (_scheduledJob) => {
                    _scheduledJob.$id = rowID;

                    _scheduledJob.teamMemberID = teamMemberID;
                    const roundedTime = this.getRoundedTime(eventRecord.startDate);
                    this.newJob.startTime = roundedTime;
                    _scheduledJob.startTime = roundedTime;
                    _scheduledJob.dueDate = eventRecord.startDate;
                    this.hoursInputCtrl.setValue(roundedTime);

                    this.setLoadedEventForm(_scheduledJob);
                    this.pendingLoading = false;
                    this._fuseLoadingService.hide();
                    this.editTemplate = true;
                }
            )

        }

        else {
            this._scheduledJobsService.getScheduledJob(rowID).subscribe(
                (_scheduledJob) => {

                    this.setLoadedEventForm(_scheduledJob);
                }
            )


        }


    }

    setLoadedEventForm(_scheduledJob) {

        try {



            if (_scheduledJob) {

                this.preloadedFiles = [];

                this.newJob = _scheduledJob;

                this.hoursInputCtrl.setValue(_scheduledJob.startTime);
                this.teamMembersFilteredOptions.pipe(take(1)).subscribe((filteredOptions) => {
                    const selectedTeamMember = filteredOptions.find(
                        (teamMember) => teamMember.$id === _scheduledJob.teamMemberID
                    );

                    if (selectedTeamMember) {
                        this.teamMemberInputCtrl.setValue(selectedTeamMember); // Update the form control with the selectedTeamMember;
                        this.jobForm.controls.teamMemberID.setValue(selectedTeamMember.$id);
                        this.newJob.teamMemberID = selectedTeamMember.$id;
                    }


                });
                this.customersFilteredOptions.pipe(take(1)).subscribe((filteredOptions) => {
                    const selectedCustomer = filteredOptions.find(
                        (customer) => customer.$id === _scheduledJob.customerId
                    );
                    if (selectedCustomer) {
                        this.customerInputCtrl.setValue(selectedCustomer); // Update the form control with the selectedCustomer;
                        this.jobForm.controls.customerId.setValue(selectedCustomer.$id);
                        this.newJob.customerId = selectedCustomer.$id;

                    }
                });

                this.selectedAddress = _scheduledJob.jobAddress;
                this.markerPositions = []
                const [latStr, lngStr] = _scheduledJob.latLon.split(', ');
                const position: google.maps.LatLngLiteral = {
                    lat: parseFloat(latStr),
                    lng: parseFloat(lngStr),
                };

                const newMarker = {
                    position,
                    options: { animation: google.maps.Animation.BOUNCE },
                };

                this.markerPositions.push(newMarker.position);
                this.center = newMarker.position;
                this.zoom = 18;
                this.setJobPriority(_scheduledJob.priority);


                this.jobForm.patchValue(_scheduledJob);
                //  console.log(_scheduledJob.attachedFiles);
                if (_scheduledJob.attachedFiles !== null && _scheduledJob.attachedFiles.length > 0) {
                    this.preloadedFiles = _scheduledJob.attachedFiles.map(fileString => {
                        let fileObject = JSON.parse(fileString);
                        return {
                            filename: fileObject.fileName,
                            fileUrl: fileObject.fileUrl,
                            fileId: fileObject.fileId
                        };
                    });
                }
                // console.log(_scheduledJob.attachedFiles); // Check if this logs the expected data
                this.makePendingCopy = false;
                this.editMode = true;

                this.jobForm.get('dueDate').setValue(_scheduledJob.dueDate);
                this.dueDatePicker.select(new Date(_scheduledJob.dueDate));
                // this.newJob.$id = _scheduledJob.id;
                // this.jobForm.controls.$id.setValue(_scheduledJob.id);
                if (_scheduledJob.customerId) {
                    this.selectedCustomer = this.customers.find(item => item.$id === _scheduledJob.customerId);
                    // console.log(this.selectedCustomer, _scheduledJob.customerId);
                    if (this.selectedCustomer) {
                        this.selectedAddresses = this.selectedCustomer.addresses.map(item => {
                            // Check if item is a string before parsing
                            if (typeof item === 'string') {
                                try {
                                    return JSON.parse(item);
                                } catch (error) {
                                    console.error('Error parsing JSON:', item, error);
                                    return null;  // or however you want to handle parsing errors
                                }
                            } else if (typeof item === 'object') {
                                return item;  // Item is already an object, no parsing needed
                            } else {
                                console.error('Unexpected item type:', typeof item, item);
                                return null;  // or however you want to handle unexpected item types
                            }
                        });
                    } else {
                        _scheduledJob.customerId = null;
                    }
                }
                if (_scheduledJob.jobCost == null) {

                    this.jobForm.controls.jobCost.setValue(0);
                }
                this.selectedNewEvent = _scheduledJob;
                if (this.newJob.jobStatus === 'finished') {
                    console.log(this.newJob.jobStatus);
                    this.teamMemberInputCtrl.disable();
                    this.customerInputCtrl.enable();
                    this.hoursInputCtrl.disable();
                    this.dueDatePicker.disabled = true;
                    this.jobForm.disable();
                    // Enable specific controls
                    const enabledControls = ['jobTitle', 'jobNotes', 'adminNote', 'jobNumber', 'orderNumber', 'jobCost'];
                    enabledControls.forEach(controlName => {
                        this.jobForm.get(controlName)?.enable();
                    });
                } else {

                    this.dueDatePicker.disabled = false;

                }

                this.jobDrawer.open();
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();


            }
        } catch (error) {
            console.error(error);
        }
    }

    updateDetails(approved?: string) {
        try {
            const data = JSON.parse(JSON.stringify(this.jobForm.getRawValue()));
            const initialJob: ScheduledJob = data;
            // Check address
            if (!initialJob.latLon) {
                // show warning
                this._fuseConfirmationService.open({
                    title: 'Warning - Invalid Address',
                    message: 'Select a valid address from the Google suggestions.',
                    icon: {
                        show: true,
                        name: 'heroicons_outline:map-pin',
                        color: 'warning',

                    },
                    actions: {
                        confirm: {
                            show: false,
                            label: 'Ok',
                            color: 'primary',
                        },
                        cancel: {
                            show: false,
                            label: 'Cancel',
                        },
                    },
                    dismissible: true,
                });
                return;
            }

            if (approved === 'approved') {
                initialJob.jobStatus = 'approved';
            }

            this.isSaving = true;
            this.assignTaskId = '';
            if (!initialJob.$id) {

                if (approved === 'approved') {
                    initialJob.jobStatus = 'approved';

                    this.saveNewJob(initialJob, approved);
                } else {
                    this.saveNewJob(initialJob);
                }

            } else if (this.editMode) {
                //console.log('before edit', initialJob);
                this.updateExistingJob(initialJob);
            } else {
                this.isSaving = false;
            }
        } catch (error) {
            console.error('Error updating details:', error);
            this.isSaving = false;
        }

    }

    saveNewJob(job: ScheduledJob, approved?: string): void {


        this._fuseLoadingService.show();
        if (job.teamMemberID !== null && job.teamMemberID !== '') {
            job.jobStatus = 'added';
        } else {
            job.jobStatus = 'pending';
        }
        if (approved === 'approved') {
            job.jobStatus = 'approved';
        }

        job.organisationID = this._organisationID;
        job.$id = null;
        const _date = new Date(job.dueDate);
        _date.setHours(0, 0, 0, 0);
        job.dueDate = _date;
        job.startTime = this.hoursInputCtrl.value;
        //console.log(job);
        if (!this.selectedFiles || this.selectedFiles.length == 0) {

            this._scheduledJobsService.createScheduledJob(job).subscribe((jobU) => {
                this.upDateAddressIfNotExist(job.jobAddress, job.customerId, job.latLon);
                this._fuseLoadingService.hide();
                this.cleanupForm();
                if (jobU.jobStatus == 'approved') {


                    this.dispatchItem(jobU);
                }
                if (jobU.jobStatus == 'pending') {
                    this.loadPendingJobList();
                }

                // If scheduledJobList is empty, call onTodayClick
                if (this.scheduledJobList.length === 0) {
                    console.log('Today clicked', this.scheduledJobList);
                    this.onTodayClick();
                }

                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
                this.jobDrawer.close();
            }, error => {
                console.error('Error saving Job:', error);
            });

        } else {

            const _attachedFiles = [];
            this.selectedFiles.forEach((file) => {

                this._scheduledJobsService.uploadFile(file.file).subscribe(
                    (result) => {
                        let fileObject = { fileName: file.filename, fileUrl: result.fileUrl, fileId: result.fileId };
                        let fileString = JSON.stringify(fileObject);

                        _attachedFiles.push(fileString);
                        if (_attachedFiles.length == this.selectedFiles.length) {
                            job.attachedFiles = _attachedFiles;
                            this._scheduledJobsService.createScheduledJob(job).subscribe((jobU) => {
                                this.upDateAddressIfNotExist(job.jobAddress, job.customerId, jobU.latLon);
                                if (jobU.jobStatus == 'approved') {
                                    this.dispatchItem(jobU);
                                }
                                if (jobU.jobStatus == 'pending') {
                                    this.loadPendingJobList();
                                }

                                // If scheduledJobList is empty, call onTodayClick
                                if (this.scheduledJobList.length === 0) {
                                    console.log('Today clicked', this.scheduledJobList);
                                    this.onTodayClick();
                                }
                                this._fuseLoadingService.hide();
                                this.cleanupForm();
                                this._changeDetectorRef.markForCheck();
                                this._changeDetectorRef.detectChanges();
                                this.jobDrawer.close();
                            }, error => {
                                console.error('Error Saving job after uploading files:', error);
                            });
                        }

                    }, error => {
                        console.error('Error uploading file:', error);
                    })
            });
        }

        if (this.makePendingCopy) {
            // Create a deep copy of the job object
            const pendingJob = job;

            // Modify the copy for the pending job
            pendingJob.jobStatus = 'pending';
            pendingJob.teamMemberID = null;
            pendingJob.jobTitle = this.jobForm.controls.pendingTitle.value;

            // Save the pending job using the copied and modified object
            this._scheduledJobsService.createScheduledJob(pendingJob).subscribe((job) => {
                this._fuseLoadingService.hide();
                this.cleanupForm();
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
                this.jobDrawer.close();
            }, error => {
                console.error('Error Saving job makePendingCopy', error);
            });
        }


    }



    updateExistingJob(job: ScheduledJob): void {
        this._fuseLoadingService.show();
        if (job.teamMemberID !== null && job.teamMemberID !== '') {
            if (job.jobStatus == 'pending') {
                job.jobStatus = 'added';
            }

        } else {
            job.jobStatus = 'pending';
            job.dispatchStatus = null;
        }
        //  console.log(job);
        if (job.$id.length > 3) {

            if (!this.selectedFiles || this.selectedFiles.length == 0) {



                this._scheduledJobsService.updateScheduledJob(job.$id, job).subscribe((jobU) => {
                    this.upDateAddressIfNotExist(job.jobAddress, job.customerId, job.latLon);
                    this._fuseLoadingService.hide();
                    if (job.jobStatus == 'approved') {
                        //  console.log('jobU', jobU);
                        this.dispatchItem(job);

                    }
                    // Load Scheduled Jobs

                    // this.loadJobList(this.schedulerSelectedDate, this.schedulerSelectedDate, null);


                    this.cleanupForm();
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();
                    this.jobDrawer.close();
                }, error => {
                    console.error('Error Update Job:', error);
                });

            } else {

                const _attachedFiles = [];
                this.selectedFiles.forEach((file) => {

                    this._scheduledJobsService.uploadFile(file.file).subscribe(
                        (result) => {
                            let fileObject = { fileName: file.filename, fileUrl: result.fileUrl, fileId: result.fileId };
                            let fileString = JSON.stringify(fileObject);
                            _attachedFiles.push(fileString);
                            job.attachedFiles.push(fileString);
                            if (_attachedFiles.length == this.selectedFiles.length) {

                                this._scheduledJobsService.updateScheduledJob(job.$id, job).subscribe((jobU) => {
                                    this.upDateAddressIfNotExist(job.jobAddress, job.customerId, job.latLon);
                                    if (job.jobStatus == 'approved') {
                                        this.dispatchItem(job);
                                    }
                                    // Load Scheduled Jobs
                                    //un subscribe

                                    //  this.loadJobList(this.schedulerSelectedDate, this.schedulerSelectedDate, null);


                                    this._fuseLoadingService.hide();
                                    this.cleanupForm();
                                    this._changeDetectorRef.markForCheck();
                                    this._changeDetectorRef.detectChanges();
                                    this.jobDrawer.close();
                                }, error => {
                                    console.error('Error Update job after uploading files:', error);
                                });
                            }

                        }, error => {
                            console.error('Error uploading file:', error);
                        })
                });
            }



        }
    }



    updateTemplate() {
        const template = this.jobForm.getRawValue();
        this.isSaving = true;
        this.assignTaskId = '';

        if (template.$id == null || template.$id == '') {
            this.saveNewJobTemplate(template);
        } else if (this.editTemplate) {
            this.updateExistingJobTemplate(template);
        } else { this.isSaving = false; }
    }

    saveNewJobTemplate(template: ScheduledJob) {
        this._fuseLoadingService.show();
        template.jobStatus = 'template';
        template.organisationID = this._organisationID;
        template.$id = null;
        const _date = new Date(template.dueDate);
        _date.setHours(0, 0, 0, 0);
        template.dueDate = _date;
        template.startTime = this.hoursInputCtrl.value;

        if (!this.selectedFiles || this.selectedFiles.length == 0) {

            this._scheduledJobsService.createJobsTemplate(template).subscribe((jobTemplate) => {
                this._fuseLoadingService.hide();
                this.cleanupForm();
                this.loadingJobTemplates();

                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
                this.jobDrawer.close();
            }, error => {
                console.error('Error saving Job Template:', error);
            });

        } else {
            const _attachedFiles = [];
            this.selectedFiles.forEach((file) => {

                this._scheduledJobsService.uploadFile(file.file).subscribe(
                    (result) => {
                        let fileObject = { fileName: file.filename, fileUrl: result.fileUrl, fileId: result.fileId };
                        let fileString = JSON.stringify(fileObject);
                        _attachedFiles.push(fileString);
                        if (_attachedFiles.length == this.selectedFiles.length) {
                            template.attachedFiles = _attachedFiles;
                            this._scheduledJobsService.createJobsTemplate(template).subscribe((jobTemplate) => {
                                this._fuseLoadingService.hide();
                                this.loadingJobTemplates();
                                this.cleanupForm();
                                this._changeDetectorRef.markForCheck();
                                this._changeDetectorRef.detectChanges();
                                this.jobDrawer.close();
                            }, error => {
                                console.error('Error saving Job Template:', error);
                            });
                        }

                    }, error => {
                        console.error('Error uploading file:', error);
                    });
            })
        }


    }

    updateExistingJobTemplate(template: ScheduledJob) {

        this._fuseLoadingService.show();
        template.jobStatus = 'template';
        if (template.$id.length > 3) {


            if (!this.selectedFiles || this.selectedFiles.length == 0) {

                this._scheduledJobsService.updateJobTemplate(template.$id, template).subscribe((jobTemplate) => {
                    this._fuseLoadingService.hide();
                    this.loadingJobTemplates();
                    this.cleanupForm();
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();
                    this.jobDrawer.close();
                }, error => {
                    console.error('Error Update Job Template:', error);
                });
            } else {
                const _attachedFiles = [];
                this.selectedFiles.forEach((file) => {

                    this._scheduledJobsService.uploadFile(file.file).subscribe(
                        (result) => {
                            let fileObject = { fileName: file.filename, fileUrl: result.fileUrl, fileId: result.fileId };
                            let fileString = JSON.stringify(fileObject);
                            _attachedFiles.push(fileString);
                            template.attachedFiles.push(fileString);
                            if (_attachedFiles.length == this.selectedFiles.length) {
                                console.log(template);
                                this._scheduledJobsService.updateJobTemplate(template.$id, template).subscribe((jobTemplate) => {
                                    this._fuseLoadingService.hide();
                                    this.loadingJobTemplates();
                                    this.cleanupForm();
                                    this._changeDetectorRef.markForCheck();
                                    this._changeDetectorRef.detectChanges();
                                    this.jobDrawer.close();
                                }, error => {
                                    console.error('Error Update Job Template:', error);
                                });
                            }
                        }, error => {
                            console.error('Error uploading file:', error);
                        });
                })
            }
        }
    }
    addNewJobTemplate() {

        this.newJob = this.initializeToNull<ScheduledJob>();
        const roundedTime = this.getRoundedTime(new Date());
        this.newJob.startTime = roundedTime;
        this.hoursInputCtrl.setValue(roundedTime);
        this.newJob.teamMemberID = null;
        this.jobForm.controls.durationHours.setValue(1);
        this.jobForm.controls.durationMinutes.setValue(0);
        this.newJob.organisationID = this._organisationID;
        this.newJob.$id = null;
        this.newJob.jobStatus = 'template';
        this.jobForm.patchValue(this.newJob);
        this.setJobPriority(0);
        this.newJob.priority = 0;
        this.editTemplate = true;
        this.jobDrawer.open();
    }

    updateOnDragEvent(jobId: string, teamMemberID: string, dueDate: Date, startTime: string, durationHours?: number, durationMinutes?: number) {


        this._fuseLoadingService.show();
        const _job = this.scheduledJobList.find(job => job.$id === jobId);

        if (!_job) return;
        _job.teamMemberID = teamMemberID;
        _job.dueDate = dueDate;
        _job.startTime = startTime;

        if (durationHours != null && durationMinutes != null) {
            _job.durationHours = durationHours;
            _job.durationMinutes = durationMinutes;

        }

        this._scheduledJobsService.updateScheduledJob(_job.$id, _job).subscribe((job) => {

            this._fuseLoadingService.hide();
            this.cleanupForm();
            this._teamMembersService.resumeRealTimeUpdates();
            this._changeDetectorRef.markForCheck();
            this._changeDetectorRef.detectChanges();
            // remove loading to bryntum Scheduler
            this.scheduler.instance.unmaskBody();
        })

    }

    approveJob(status) {

        if (this.newJob) {

            if (this.newJob.jobStatus === 'added' || this.newJob.jobStatus === 'pending') {
                this.newJob.jobStatus = 'approved';


            } else if (this.newJob.jobStatus === 'approved') {
                this.newJob.jobStatus = 'added';
                this.newJob.dispatchStatus = null;
            }

            this.updateExistingJob(this.newJob);

        }

    }

    deleteItem(row?) {
        if (row) {
            //  console.log(row);
            this.selectedNewEvent = row;
        }
        //  console.log(this.selectedNewEvent);
        let deleteTitle = '';
        let deleteMessage = '';
        if (this.selectedNewEvent) {
            if (this.selectedNewEvent.jobStatus === 'finished') {
                deleteMessage = 'Warning! This Job Status is Finished. Do you really want to delete this job? Please note that this action is irreversible.';
                deleteTitle = 'Warning! This Job Status is Finished, this job will be permanently removed and will not be recorded.';

            } else {
                deleteMessage = 'Do you really want to delete this job? Please note that this action is irreversible.';
                deleteTitle = 'This job will be permanently removed and will not be recorded.';
            }
            const confirmation = this._fuseConfirmationService.open({
                title: deleteTitle,
                message: deleteMessage,
                actions: {
                    confirm: {
                        label: 'Confirm Delete',
                    },
                },
                icon: {
                    show: true,
                    name: 'heroicons_outline:trash',
                },
            });
            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                this._fuseLoadingService.show();
                // If the confirm button pressed...
                if (result === 'confirmed') {
                    // console.log(this.selectedNewEvent);

                    // first Delete event

                    if (this.selectedNewEvent.attachedFiles && this.selectedNewEvent.attachedFiles.length > 0) {
                        let fileArray = this.selectedNewEvent.attachedFiles; // This is already an array


                        this._scheduledJobsService.deleteScheduledJob(this.selectedNewEvent.$id).subscribe((result) => {
                            if (result) {
                                fileArray.forEach((file) => {
                                    const fileObject = JSON.parse(file);
                                    this._scheduledJobsService.deleteFile(fileObject.fileId).subscribe((deleteResult) => {
                                        // Handle successful deletion if needed
                                    });
                                });

                                this._fuseLoadingService.hide();
                                this.cleanupForm();
                                this._changeDetectorRef.markForCheck();
                                this._changeDetectorRef.detectChanges();
                                this.jobDrawer.close();
                            } else {
                                this._fuseLoadingService.hide();
                                this.cleanupForm();
                                this._changeDetectorRef.markForCheck();
                                this._changeDetectorRef.detectChanges();
                                this.jobDrawer.close();
                            }
                        });
                    } else {

                        //  console.log('Start Delete:', this.selectedNewEvent);
                        this._scheduledJobsService.deleteScheduledJob(this.selectedNewEvent.$id).subscribe((result) => {
                            this._fuseLoadingService.hide();
                            this.cleanupForm();
                            this._changeDetectorRef.markForCheck();
                            this._changeDetectorRef.detectChanges();
                            this.jobDrawer.close();
                        });
                    }



                    this.cleanupForm();
                    this._fuseLoadingService.hide();
                } else { this._fuseLoadingService.hide(); }

            });

        } else { this.cleanupForm(); }

    }


    deleteTemplate() {

        if (this.selectedNewEvent) {
            const confirmation = this._fuseConfirmationService.open({
                title: 'This job Template will be permanently removed and will not be recorded.',
                message: `Do you really want to delete this? Please note that this action is irreversible.`,
                actions: {
                    confirm: {
                        label: 'Confirm Delete',
                    },
                },
                icon: {
                    show: true,
                    name: 'heroicons_outline:trash',
                },
            });
            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                this._fuseLoadingService.show();
                // If the confirm button pressed...
                if (result === 'confirmed') {
                    // console.log(this.selectedNewEvent);

                    // first Delete Template Job

                    if (this.selectedNewEvent.attachedFiles && this.selectedNewEvent.attachedFiles.length > 0) {
                        this._scheduledJobsService.deleteJobsTemplates(this.selectedNewEvent.id).subscribe((result) => {
                            this.selectedNewEvent.attachedFiles.forEach((file) => {
                                const fileObject = JSON.parse(file);
                                this._scheduledJobsService.deleteFile(fileObject.fileId).subscribe((result) => {



                                });
                            });
                            this._fuseLoadingService.hide();
                            this.cleanupForm();
                            this._changeDetectorRef.markForCheck();
                            this._changeDetectorRef.detectChanges();
                            this.jobDrawer.close();
                        });

                    } else {

                        //  console.log('Start Delete:', this.selectedNewEvent);
                        this._fuseLoadingService.show();
                        this._scheduledJobsService.deleteJobsTemplates(this.selectedNewEvent.$id).subscribe((result) => {
                            this._fuseLoadingService.hide();
                            this.cleanupForm();
                            this._changeDetectorRef.markForCheck();
                            this._changeDetectorRef.detectChanges();
                            this.jobDrawer.close();
                        });
                    }



                    this.cleanupForm();
                    this._fuseLoadingService.hide();
                } else { this._fuseLoadingService.hide(); }

            });

        } else { this.cleanupForm(); }


    }



    upDateAddressIfNotExist(selectedAddress: string, selectedCustomer: string, latLon: string) {

        //  console.log(selectedAddress, selectedCustomer, latLon);

        try {
            if (selectedAddress.length > 0 && selectedCustomer.length > 0) {

                const _customer = this.customers.find(item => item.$id === selectedCustomer);

                if (_customer.addresses.length > 0) {
                    // Normalize the addresses to objects
                    _customer.addresses = _customer.addresses?.map(str =>
                        typeof str === 'string' ? JSON.parse(str) : str
                    );

                    _customer.phoneNumbers = _customer.phoneNumbers?.map(str =>
                        typeof str === 'string' ? JSON.parse(str) : str
                    );
                    _customer.emails = _customer.emails?.map(str =>
                        typeof str === 'string' ? JSON.parse(str) : str
                    );
                    // Check if the selectedAddress exists in any of the normalized address objects
                    const isExist = _customer.addresses?.some(item => {
                        // console.log(item.address, selectedAddress);
                        return item.address === selectedAddress;
                    });

                    // console.log(isExist);
                    if (!isExist) {
                        //Add Address

                        _customer.addresses.push({
                            address: selectedAddress,
                            label: 'Add By App',
                            latLon: latLon
                        });
                        //  console.log(_customer);

                        this._customerService.updateCustomer(selectedCustomer, _customer).subscribe(
                            (customer) => {
                                this._changeDetectorRef.markForCheck();
                                this._changeDetectorRef.detectChanges();
                            }
                        )


                    }
                } else {
                    _customer.phoneNumbers = _customer.phoneNumbers?.map(str =>
                        typeof str === 'string' ? JSON.parse(str) : str
                    );
                    _customer.emails = _customer.emails?.map(str =>
                        typeof str === 'string' ? JSON.parse(str) : str
                    );
                    _customer.addresses.push({
                        address: selectedAddress,
                        label: 'Add By App',
                        latLon: latLon
                    });
                    // console.log(_customer);

                    this._customerService.updateCustomer(selectedCustomer, _customer).subscribe(
                        (customer) => {
                            this._changeDetectorRef.markForCheck();
                            this._changeDetectorRef.detectChanges();
                        }
                    )

                }

            }
        } catch (error) {
            // Handle or log the error
            console.error('Error occurred:', error);

        }

    }



    dispatchItem(rowData: any) {
        //console.log(rowData);
        let _scheduledJob = this.scheduledJobList.find(item => item.$id === rowData.$id);
        if (rowData.jobStatus === 'approved') {
            _scheduledJob = rowData;
        }
        if (_scheduledJob) {
            this._fuseLoadingService.show();
            _scheduledJob.jobStatus = 'approved';
            _scheduledJob.dispatchStatus = 'dispatched';
            this._scheduledJobsService.updateScheduledJob(_scheduledJob.$id, _scheduledJob).subscribe(
                (job) => {
                    this._fuseLoadingService.hide();
                    const _deviceTokenId = this.teamMembers.find(item => item.$id === _scheduledJob.teamMemberID).deviceTokenId;
                    if (_deviceTokenId) {
                        this.fcmNotification = {
                            body: `📢 Your scheduled job has been dispatched.\n🧑‍💼 Customer Name: ${this.customers.find(item => item.$id === job.customerId).name || ''}\n📍 Job Address: ${job.jobAddress}`,
                            title: `🚛 ${job.jobTitle} Dispatched`,
                            to: _deviceTokenId,
                            type: 'schedule',
                        };
                        this._fcmNotificationService.sendNotification(this.fcmNotification);
                        if (this.editMode) {
                            this.newJob.dispatchStatus = 'dispatched';
                            this.newJob.jobStatus = 'approved';
                        }

                    }
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();

                }
            );
        }
    }






    removeFile(index: number): void {
        URL.revokeObjectURL(this.selectedFiles[index].url);
        this.selectedFiles.splice(index, 1);
    }

    onRemovePreloadedFile(fileId: string): void {
        this._fuseLoadingService.show()
        // Call your service to delete the file from the server
        //console.log(this.selectedNewEvent);
        this.isSaving = true;



        this._scheduledJobsService.deleteFile(fileId).subscribe({

            next: (response) => {
                //   console.log('File removed successfully', response);

                if (this.editTemplate) {
                    this._scheduledJobsService.getJobsTemplate(this.selectedNewEvent.$id).subscribe((_scheduledJob) => {


                        if (_scheduledJob && _scheduledJob.attachedFiles) {
                            // Parse each JSON string in attachedFiles and filter out the file with the given fileId
                            _scheduledJob.attachedFiles = _scheduledJob.attachedFiles
                                .map(fileString => JSON.parse(fileString))
                                .filter(fileObject => fileObject.fileId !== fileId)
                                .map(fileObject => JSON.stringify(fileObject)); // Convert back to JSON strings
                            // console.log(_scheduledJob, this.selectedNewEvent);

                            this._scheduledJobsService.updateJobTemplate(this.selectedNewEvent.$id, _scheduledJob).subscribe(
                                (job) => {
                                    this._fuseLoadingService.hide();
                                    this.isSaving = false;

                                    this.selectedNewEvent.attachedFiles = _scheduledJob.attachedFiles;
                                    this.newJob.attachedFiles = _scheduledJob.attachedFiles;
                                    this.jobForm.controls.attachedFiles.setValue(_scheduledJob.attachedFiles);
                                    this._changeDetectorRef.markForCheck();
                                    this._changeDetectorRef.detectChanges();

                                }
                            ), error => {
                                console.error('Error saving Job:', error);
                                this._fuseLoadingService.hide();
                                this.isSaving = false;

                            };

                        }

                    })

                } else {



                    this._scheduledJobsService.getScheduledJob(this.selectedNewEvent.$id).subscribe((_scheduledJob) => {


                        if (_scheduledJob && _scheduledJob.attachedFiles) {
                            // Parse each JSON string in attachedFiles and filter out the file with the given fileId
                            _scheduledJob.attachedFiles = _scheduledJob.attachedFiles
                                .map(fileString => JSON.parse(fileString))
                                .filter(fileObject => fileObject.fileId !== fileId)
                                .map(fileObject => JSON.stringify(fileObject)); // Convert back to JSON strings
                            // console.log(_scheduledJob, this.selectedNewEvent);

                            this._scheduledJobsService.updateScheduledJob(this.selectedNewEvent.$id, _scheduledJob).subscribe(
                                (job) => {
                                    this._fuseLoadingService.hide();
                                    this.isSaving = false;


                                    this.selectedNewEvent.attachedFiles = _scheduledJob.attachedFiles;
                                    this.newJob.attachedFiles = _scheduledJob.attachedFiles;
                                    this.jobForm.controls.attachedFiles.setValue(_scheduledJob.attachedFiles);
                                    this._changeDetectorRef.markForCheck();
                                    this._changeDetectorRef.detectChanges();

                                }
                            ), error => {
                                console.error('Error saving Job:', error);
                                this._fuseLoadingService.hide();
                                this.isSaving = false;

                            };

                        }

                    })
                }
                // Find the scheduled job using the selected event's ID


                // Handle successful removal
            },
            error: (error) => {
                this._fuseLoadingService.hide();
                console.error('Error removing file', error);
                // Handle errors here
            }
        });
    }


    showGroups() {

        // this.resources = this.resources.filter(r => r.id.contains(["64fa7f43d5161bcab2c5"]);
        console.log(this.resources);
    }

    onGroupSelectChange(selectedGroupIds: string[]) {
        //  console.log('Selected IDs:', selectedGroupIds);
        this.selectedGroupIds = selectedGroupIds;

        this.resources = this.resourcesFilter.filter(resource =>
            resource.groupIds.some(groupId => this.selectedGroupIds.includes(groupId))
        );


    }

    clearGroupFilter() {
        this.selectedGroupIds = [];
        this.resources = this.resourcesFilter;
    }

    // Utility Functions ###//////////////

    generateHoursList() {
        const hours = ['12', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11'];
        const minutes = ['00', '15', '30', '45'];
        const periods = ['AM', 'PM'];

        this.hoursList = [];

        periods.forEach(period => {
            hours.forEach(hour => {
                minutes.forEach(minute => {
                    this.hoursList.push(`${hour}:${minute} ${period}`);
                });
            });
        });
    }
    getRoundedTime(date) {
        let hours = date.getHours();
        let minutes = date.getMinutes();
        let period = 'AM';

        // Round minutes to the nearest 15-minute interval
        if (minutes <= 14) {
            minutes = 0;
        } else if (minutes <= 29) {
            minutes = 15;
        } else if (minutes <= 44) {
            minutes = 30;
        } else {
            minutes = 45;
        }

        // Adjust hours and period (AM/PM)
        if (hours >= 12) {
            period = 'PM';
            if (hours > 12) {
                hours -= 12;
            }
        } else if (hours === 0) {
            hours = 12;
        }

        // Format hours and minutes as 2-digit strings
        const hoursString = hours.toString().padStart(2, '0');
        const minutesString = minutes.toString().padStart(2, '0');

        // Construct the formatted time string
        const formattedTime = `${hoursString}:${minutesString} ${period}`;
        return formattedTime;
    }

    setTimeToDate(dateInput, timeStr) {
        // Ensure that dateInput is a Date object
        // console.log(dateInput)
        // console.log(timeStr)

        const date = new Date(dateInput);
        date.setHours(0, 0, 0, 0); // Resets seconds and milliseconds to 0

        let [time, modifier] = timeStr.split(' ');
        let [hours, minutes] = time.split(':').map(Number);

        // Convert 12-hour time to 24-hour time if necessary
        if (modifier.toUpperCase() === 'PM' && hours < 12) {
            hours += 12;
        } else if (modifier.toUpperCase() === 'AM' && hours === 12) {
            hours = 0;
        }

        date.setHours(hours, minutes, 0, 0); // Resets seconds and milliseconds to 0
        return date;
    }
    addDurationToDate(dateInput, hours, minutes) {
        // Ensure that dateInput is a Date object
        let date = new Date(dateInput);

        return new Date(date.getTime() + hours * 60 * 60 * 1000 + minutes * 60 * 1000);
    }
    convertDurationToHoursAndMinutes(duration) {
        const hours = Math.floor(duration);
        const minutes = Math.round((duration - hours) * 60);
        return { durationHours: hours, durationMinutes: minutes };
    }


    formatAmPm(date) {
        let hours = date.getHours();
        let minutes = date.getMinutes();
        const amPm = hours >= 12 ? 'PM' : 'AM';
        hours = hours % 12;
        hours = hours ? hours : 12; // the hour '0' should be '12'
        minutes = minutes < 10 ? '0' + minutes : minutes;
        const strTime = hours.toString().padStart(2, '0') + ':' + minutes.toString().padStart(2, '0') + ' ' + amPm;
        return strTime;
    }
    getCustomerName(id: string): { name: string, contact: string } {
        try {
            if (this.customers == null) {
                return { name: '', contact: '' };
            }

            const customer = this.customers.find(item => item.$id === id);
            if (customer) {
                let number;

                // Check if the phone number is a string and attempt to parse it
                if (typeof customer.phoneNumbers[0] === 'string') {
                    try {
                        number = JSON.parse(customer.phoneNumbers[0]);
                    } catch (error) {
                        console.error('Error parsing phone number', error);
                        // Handle the error or return a placeholder/fallback
                        return { name: customer.name, contact: 'Invalid Contact' };
                    }
                } else {
                    // If it's not a string, assume it's already an object
                    number = customer.phoneNumbers[0];
                }

                return { name: customer.name, contact: number.phoneNumber };
            } else {
                return { name: '', contact: '' };
            }
        } catch (error) {
            console.error('Error parsing customer ID', error);
            // Handle the error or return a placeholder/fallback
            return { name: '', contact: '' };
        }

    }

    getTeamMemberInfo(id: string): { name: string, avatar: string | null, fullName: string } {
        const teamMember = this.teamMembers.find(item => item.$id === id);

        if (!teamMember) {
            return { name: '', avatar: null, fullName: '' }; // Return empty if no team member found
        }

        // Fetch avatar URL if avatarImageId is present
        const avatar = teamMember.avatarImageId
            ? this._teamMembersService.getFilePreview(teamMember.avatarImageId, 50, 50)
            : null;

        // Use avatar name or generate initials if avatarImageId/avatar is absent
        const name = teamMember.avatar || (avatar ? '' : this.generateInitials(teamMember.name));

        return {
            name: name,
            avatar: avatar,
            fullName: teamMember.name
        };
    }

    // Helper function to generate initials from a full name
    private generateInitials(fullName: string): string {
        const nameParts = fullName.split(' ');
        const initials = nameParts.length > 1
            ? `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}` // First and last initials
            : nameParts[0][0]; // Only first initial if single part
        return initials.toUpperCase();
    }
    filterByRangeDate(selectedItem: string) {
        const today = new Date();

        switch (selectedItem) {
            case 'today':
                this.selectTabCalendarPreset({ start: today, end: today });
                break;
            case 'yesterday':
                const yesterday = new Date(today);
                yesterday.setDate(today.getDate() - 1);
                this.selectTabCalendarPreset({ start: yesterday, end: yesterday });
                break;
            case 'tomorrow':
                const tomorrow = new Date(today);
                tomorrow.setDate(today.getDate() + 1);
                this.selectTabCalendarPreset({ start: tomorrow, end: tomorrow });
                break;
            case 'thisweek':
                const startOfThisWeek = new Date(today);
                startOfThisWeek.setDate(today.getDate() - today.getDay()); // Sunday
                const endOfThisWeek = new Date(startOfThisWeek);
                endOfThisWeek.setDate(startOfThisWeek.getDate() + 6); // Saturday
                this.selectTabCalendarPreset({ start: startOfThisWeek, end: endOfThisWeek });
                break;
            case 'lastweek':
                const startOfLastWeek = new Date(today);
                startOfLastWeek.setDate(today.getDate() - today.getDay() - 7); // Last week's Sunday
                const endOfLastWeek = new Date(startOfLastWeek);
                endOfLastWeek.setDate(startOfLastWeek.getDate() + 6); // Last week's Saturday
                this.selectTabCalendarPreset({ start: startOfLastWeek, end: endOfLastWeek });
                break;
            case 'nextweek':
                const startOfNextWeek = new Date(today);
                startOfNextWeek.setDate(today.getDate() - today.getDay() + 7); // Next week's Sunday
                const endOfNextWeek = new Date(startOfNextWeek);
                endOfNextWeek.setDate(startOfNextWeek.getDate() + 6); // Next week's Saturday
                this.selectTabCalendarPreset({ start: startOfNextWeek, end: endOfNextWeek });
                break;
            case 'thismonth':
                const startOfThisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
                const endOfThisMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0); // Last day of this month
                this.selectTabCalendarPreset({ start: startOfThisMonth, end: endOfThisMonth });
                break;
            case 'lastmonth':
                const startOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0); // Last day of last month
                this.selectTabCalendarPreset({ start: startOfLastMonth, end: endOfLastMonth });
                break;
            case 'nextmonth':
                const startOfNextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
                const endOfNextMonth = new Date(today.getFullYear(), today.getMonth() + 2, 0); // Last day of next month
                this.selectTabCalendarPreset({ start: startOfNextMonth, end: endOfNextMonth });
                break;
            case 'thisyear':
                const startOfThisYear = new Date(today.getFullYear(), 0, 1); // January 1st
                const endOfThisYear = new Date(today.getFullYear(), 11, 31); // December 31st
                this.selectTabCalendarPreset({ start: startOfThisYear, end: endOfThisYear });
                break;
            case 'lastyear':
                const startOfLastYear = new Date(today.getFullYear() - 1, 0, 1); // January 1st of last year
                const endOfLastYear = new Date(today.getFullYear() - 1, 11, 31); // December 31st of last year
                this.selectTabCalendarPreset({ start: startOfLastYear, end: endOfLastYear });
                break;
            default:
                this.selectTabCalendarPreset({ start: today, end: today });
                break;
        }
    }


    // Helper Job status lookup to get event color and icon
    getEventAttributes(item) {
        let eventColor = 'gray';
        let icon = 'fa-solid fa-clock';

        const dispatchAttributes = this.dispatchStatusLookup[item.dispatchStatus || 'null'];
        const jobAttributes = this.jobStatusLookup[item.jobStatus];

        if (dispatchAttributes) {
            eventColor = dispatchAttributes.color;
            icon = dispatchAttributes.icon;
        }

        if (jobAttributes) {
            eventColor = jobAttributes.color;
            if (jobAttributes.icon) {
                icon = jobAttributes.icon;
            }
        }

        return { eventColor, icon };
    }

    getDriverFileUrl(fileString: string): string {
        const [fileID, fileUrl] = fileString.split('|');
        return fileUrl;
    }
    convertToTreeData(data: any[]): TreeNode[] {
        const itemsById: { [key: string]: TreeNode } = {};
        const rootItems: TreeNode[] = [];

        // First pass: create nodes for each item
        if (!data) {
            return [];
        }
        data.forEach(item => {
            itemsById[item.$id] = {
                id: item.$id,
                name: item.groupName,
                parentID: item.parentID,
                children: []
            };
        });

        // Second pass: assign children to their parents
        data.forEach(item => {
            if (item.parentID) {
                const parent = itemsById[item.parentID];
                if (parent) {
                    parent.children.push(itemsById[item.$id]);
                } else {
                    // Handle the case where the parentID is invalid or missing in the dataset
                    console.warn(`Missing parent with ID: ${item.parentID}`);
                    rootItems.push(itemsById[item.$id]); // Optional: add it as a root item if the parent is missing
                }
            } else {
                rootItems.push(itemsById[item.$id]);
            }
        });

        return rootItems;
    }


    showAllPendingJobs() {
        this.loadJobList(null, null, null, "pending");
        //jobStatus = "pending"
    }

    selectAllText(event: FocusEvent) {
        const inputElement = event.target as HTMLInputElement;
        inputElement.select();
    }
    /////// TAG SEction ///////

    /**
        * Open tags panel
        */
    openTagsPanel(): void {
        // Create the overlay
        this._tagsPanelOverlayRef = this._overlay.create({
            backdropClass: '',
            hasBackdrop: true,
            scrollStrategy: this._overlay.scrollStrategies.block(),
            positionStrategy: this._overlay.position()
                .flexibleConnectedTo(this._tagsPanelOrigin.nativeElement)
                .withFlexibleDimensions(true)
                .withViewportMargin(64)
                .withLockedPosition(true)
                .withPositions([
                    {
                        originX: 'start',
                        originY: 'bottom',
                        overlayX: 'start',
                        overlayY: 'top',
                    },
                ]),
        });

        // Subscribe to the attachments observable
        this._tagsPanelOverlayRef.attachments().subscribe(() => {
            // Add a class to the origin
            this._renderer2.addClass(this._tagsPanelOrigin.nativeElement, 'panel-opened');

            // Focus to the search input once the overlay has been attached
            this._tagsPanelOverlayRef.overlayElement.querySelector('input').focus();
        });

        // Create a portal from the template
        const templatePortal = new TemplatePortal(this._tagsPanel, this._viewContainerRef);

        // Attach the portal to the overlay
        this._tagsPanelOverlayRef.attach(templatePortal);

        // Subscribe to the backdrop click
        this._tagsPanelOverlayRef.backdropClick().subscribe(() => {
            // Remove the class from the origin
            this._renderer2.removeClass(this._tagsPanelOrigin.nativeElement, 'panel-opened');

            // If overlay exists and attached...
            if (this._tagsPanelOverlayRef && this._tagsPanelOverlayRef.hasAttached()) {
                // Detach it
                this._tagsPanelOverlayRef.detach();

                // Reset the tag filter
                this.filteredTags = this.tags;

                // Toggle the edit mode off
                this.tagsEditMode = false;
            }

            // If template portal exists and attached...
            if (templatePortal && templatePortal.isAttached) {
                // Detach it
                templatePortal.detach();
            }
        });
    }

    /**
     * Toggle the tags edit mode
     */
    toggleTagsEditMode(): void {
        this.tagsEditMode = !this.tagsEditMode;
    }

    /**
     * Filter tags
     *
     * @param event
     */
    filterTags(event): void {
        // Get the value
        const value = event.target.value.toLowerCase();

        // Filter the tags
        this.filteredTags = this.tags.filter(tag => tag.title.toLowerCase().includes(value));
        // Mark for check to ensure change detection
        this._changeDetectorRef.markForCheck();
    }

    /**
     * Filter tags input key down event
     *
     * @param event
     */
    filterTagsInputKeyDown(event): void {
        // Return if the pressed key is not 'Enter'
        if (event.key !== 'Enter') {
            return;
        }

        // If there is no tag available...
        if (this.filteredTags.length === 0) {
            // Create the tag
            this.createTag(event.target.value);

            // Clear the input
            event.target.value = '';

            // Return
            return;
        }

        // If there is a tag...
        const tag = this.filteredTags[0];
        const isTagApplied = this.newJob.jobTags.find(id => id === tag.$id);

        // If the found tag is already applied to the contact...
        if (isTagApplied) {
            // Remove the tag from the contact
            this.removeTagFromJob(tag);
        }
        else {
            // Otherwise add the tag to the contact
            this.addTagToJob(tag);
        }
    }

    /**
     * Create a new tag
     *
     * @param title
     */
    createTag(title: string): void {
        const tag = {
            organisationID: this.newJob.organisationID,
            title: title,
        };

        this._scheduledJobsService.createJobTag(tag).subscribe((response) => {
            // Add the tag to the contact
            this.addTagToJob(response);
        });

        // Create tag on the server
        // this._contactsService.createTag(tag)
        //     .subscribe((response) =>
        //     {
        //         // Add the tag to the contact
        //         this.addTagToJob(response);
        //     });
    }

    /**
     * Update the tag title
     *
     * @param tag
     * @param event
     */
    updateTagTitle(tag: JobTags, event): void {
        // Update the title on the tag
        tag.title = event.target.value;

        this._scheduledJobsService.updateJobTag(tag.$id, tag).subscribe((response) => {
            // Mark for check
            this._changeDetectorRef.markForCheck();
        });
    }

    /**
     * Delete the tag
     *
     * @param tag
     */
    deleteTag(tag: JobTags): void {
        // Delete the tag from the server


        this._scheduledJobsService.deleteJobTag(tag.$id).subscribe((response) => {

            // Mark for check
            this._changeDetectorRef.markForCheck();
        });


    }

    /**
     * Add tag to the contact
     *
     * @param tag
     */
    addTagToJob(tag: JobTags): void {
        // Ensure jobTags array exists
        if (!this.newJob.jobTags) {
            this.newJob.jobTags = [];
        }

        // Add the tag if it doesn't already exist
        if (!this.newJob.jobTags.includes(tag.$id)) {
            this.newJob.jobTags.unshift(tag.$id);
        }

        // Update the job form
        this.jobForm.get('jobTags').patchValue(this.newJob.jobTags);

        // Mark for check to ensure change detection
        this._changeDetectorRef.markForCheck();
    }

    /**
     * Remove tag from the contact
     *
     * @param tag
     */
    removeTagFromJob(tag: JobTags): void {
        // Ensure jobTags array exists
        if (!this.newJob.jobTags) {
            this.newJob.jobTags = [];
            return;
        }

        // Remove the tag
        const index = this.newJob.jobTags.indexOf(tag.$id);
        if (index > -1) {
            this.newJob.jobTags.splice(index, 1);
        }

        // Update the job form
        this.jobForm.get('jobTags').patchValue(this.newJob.jobTags);

        // Mark for check to ensure change detection
        this._changeDetectorRef.markForCheck();
    }

    /**
     * Toggle contact tag
     *
     * @param tag
     */
    toggleJobTag(tag: JobTags): void {
        if (!this.newJob.jobTags) {
            this.newJob.jobTags = [];
        }

        const tagIndex = this.newJob.jobTags.indexOf(tag.$id);
        if (tagIndex > -1) {
            this.removeTagFromJob(tag);
        } else {
            this.addTagToJob(tag);
        }

        // Mark for check to ensure change detection
        this._changeDetectorRef.markForCheck();
    }

    /**
     * Should the create tag button be visible
     *
     * @param inputValue
     */
    shouldShowCreateTagButton(inputValue: string): boolean {
        if (!inputValue || !this.tags) {
            return false;
        }
        return !this.tags.some(tag => tag.title.toLowerCase() === inputValue.toLowerCase());
    }
    trackByFn(index: number, item: any): any {
        return item || index;
    }


    /**
     * Schedule Presets
     *
     * **/


    initPresetSearch(): void {
        this.filteredPresets$ = this.presetSearchControl.valueChanges.pipe(
            startWith(''),
            switchMap(value => {
                const filterValue = typeof value === 'string' ? value.toLowerCase() : '';

                return this._schedulePresetsService.schedulePresets$.pipe(
                    map(presets => {
                        if (!presets) return [];
                        return presets.filter(preset =>
                            preset.presetName.toLowerCase().includes(filterValue) ||
                            (preset.description && preset.description.toLowerCase().includes(filterValue))
                        );
                    })
                );
            })
        );
    }

    // Filter presets based on the search term
    // Filter presets based on the search term
    filterPresets(value: string | SchedulePreset): Observable<SchedulePreset[]> {
        let filterValue = '';

        if (typeof value === 'string') {
            filterValue = value.toLowerCase();
        } else if (value) {
            return of([value]);
        }

        return this._schedulePresetsService.schedulePresets$.pipe(
            map(presets => {
                if (!presets) return [];
                return presets.filter(preset =>
                    preset.presetName.toLowerCase().includes(filterValue) ||
                    preset.description?.toLowerCase().includes(filterValue)
                );
            })
        );
    }

    // Handle preset selection from autocomplete
    onPresetSelected(event: MatAutocompleteSelectedEvent): void {
        const selectedPreset = event.option.value as SchedulePreset;

        // Apply the selected preset
        this.applySchedulePreset(selectedPreset.$id);

        // Add to recent presets
        this.addToRecentPresets(selectedPreset);

        // Clear the search input
        this.presetSearchControl.setValue('');
    }

    // Load recently used presets from local storage
    private loadRecentPresets(): void {
        try {
            const savedPresets = localStorage.getItem('recentPresets');
            if (savedPresets) {
                this.recentPresets = JSON.parse(savedPresets);
            } else {
                this.recentPresets = [];
            }
        } catch (error) {
            console.error('Error loading recent presets:', error);
            this.recentPresets = [];
        }
    }

    // Add a preset to the recently used list
    private addToRecentPresets(preset: SchedulePreset): void {
        // Remove if already exists to avoid duplicates
        this.recentPresets = this.recentPresets.filter(p => p.$id !== preset.$id);

        // Add to the beginning of the array
        this.recentPresets.unshift(preset);

        // Limit to 5 recent presets
        if (this.recentPresets.length > 5) {
            this.recentPresets = this.recentPresets.slice(0, 5);
        }

        // Save to local storage
        try {
            localStorage.setItem('recentSchedulePresets', JSON.stringify(this.recentPresets));
        } catch (error) {
            console.error('Error saving recent presets:', error);
        }
    }

    openSchedulePresetsDialog(preset = null): void {
        const dialogRef = this._matDialog.open(SchedulePresetsComponent, {
            width: '800px',
            maxHeight: '90vh',
            data: {
                preset: preset,
                teamMembers: this.teamMembers,
                jobTemplates: this.jobsTemplates,
                organisationID: this._organisationID
            },
            panelClass: 'schedule-presets-dialog'
        });

        dialogRef.afterClosed().subscribe(result => {
            if (result) {
                // If preset was deleted
                if (result.deleted) {
                    this._snackBar.open('Schedule preset deleted successfully', 'Close', {
                        duration: 3000
                    });
                } else {
                    // Refresh data if needed
                    this._snackBar.open('Schedule preset saved successfully', 'Close', {
                        duration: 3000
                    });
                }
            }
        });
    }

    loadSchedulePreset(presetId: string): void {
        this._fuseLoadingService.show();
        this._schedulePresetsService.schedulePreset = { $id: presetId } as any;

        this._schedulePresetsService.getSchedulePreset()
            .pipe(take(1))
            .subscribe({
                next: (preset) => {
                    this._fuseLoadingService.hide();
                    this.openSchedulePresetsDialog(preset);
                },
                error: (error) => {
                    this._fuseLoadingService.hide();
                    console.error('Error loading preset:', error);
                    this._snackBar.open('Error loading schedule preset', 'Close', {
                        duration: 3000
                    });
                }
            });
    }

    //  method to load and apply a preset to the schedule
    applySchedulePreset(presetId: string): void {
        // Get the preset data first
        this._schedulePresetsService.schedulePreset = { $id: presetId } as any;

        this._schedulePresetsService.getSchedulePreset()
            .pipe(take(1))
            .subscribe({
                next: (preset) => {
                    // Show confirmation dialog
                    const dialogRef = this._matDialog.open(ApplyPresetDialogComponent, {
                        width: '450px',
                        data: {
                            presetName: preset.presetName,
                            hoursList: this.hoursList
                        }
                    });

                    dialogRef.afterClosed().subscribe(result => {
                        if (result) {
                            this.processPresetApplication(
                                preset,
                                result.applyMethod,
                                result.startTime,
                                result.jobStatus
                            );
                        }
                    });
                },
                error: (error) => {
                    console.error('Error loading preset:', error);
                    this._snackBar.open('Error loading schedule preset', 'Close', {
                        duration: 3000
                    });
                }
            });
    }
    private processPresetApplication(
        preset: SchedulePreset,
        applyMethod: 'order' | 'templateTime',
        startTime?: string,
        jobStatus: 'added' | 'approved' = 'added' // Added job status parameter with default
    ): void {
        this._fuseLoadingService.show();

        // Verify that this preset belongs to the current organization
        if (preset.organisationID !== this._organisationID) {
            this._fuseLoadingService.hide();
            this._snackBar.open('Cannot apply preset from another organization', 'Close', {
                duration: 3000
            });
            return;
        }

        // Process all template items from the preset
        const templateItems = preset.templateItems.map(item => {
            return typeof item === 'string' ? JSON.parse(item) : item;
        });

        // Group and process items by team member
        let processedItems = 0;
        const totalItems = templateItems.length;

        if (totalItems === 0) {
            this._fuseLoadingService.hide();
            this._snackBar.open('Schedule preset has no template items', 'Close', {
                duration: 3000
            });
            return;
        }

        // Group template items by team member
        const groupedItems = {};
        templateItems.forEach(item => {
            const teamMemberId = item.teamMemberId;
            if (!groupedItems[teamMemberId]) {
                groupedItems[teamMemberId] = [];
            }
            groupedItems[teamMemberId].push(item);
        });

        // Keep track of current time for each team member
        const teamMemberTimes = {};

        // Process each team member's items
        Object.keys(groupedItems).forEach(teamMemberId => {
            const teamItems = groupedItems[teamMemberId];

            // Sort items by position if apply by order
            if (applyMethod === 'order') {
                teamItems.sort((a, b) => (a.position || 0) - (b.position || 0));
            }

            // Initialize current time for this team member
            teamMemberTimes[teamMemberId] = startTime ?
                this.setTimeToDate(new Date(), startTime) : new Date();

            // Process each item for this team member
            teamItems.forEach(item => {
                const jobTemplateId = item.jobTemplateId;

                // Get the job template
                this._scheduledJobsService.getJobsTemplate(jobTemplateId).subscribe(
                    (_scheduledJob) => {
                        // Verify that this job template belongs to the current organization
                        if (_scheduledJob.organisationID !== this._organisationID) {
                            processedItems++;
                            console.error('Skipping job template from different organization:', jobTemplateId);
                            return;
                        }

                        // Set job status based on parameter
                        _scheduledJob.jobStatus = jobStatus;
                        _scheduledJob.$id = null;
                        _scheduledJob.teamMemberID = teamMemberId;

                        // If job status is approved, set dispatch status
                        if (jobStatus === 'approved') {
                            _scheduledJob.dispatchStatus = 'dispatched';
                        }

                        // Ensure the organization ID is set to the current one
                        _scheduledJob.organisationID = this._organisationID;

                        // Set today's date as schedule selected date
                        const today = this.schedulerSelectedDate || new Date();
                        today.setHours(0, 0, 0, 0);
                        _scheduledJob.dueDate = today;

                        if (applyMethod === 'order') {
                            // If applying by order, set the calculated start time
                            _scheduledJob.startTime = this.formatAmPm(teamMemberTimes[teamMemberId]);

                            // Calculate the next start time based on this job's duration
                            const durationMillis = (_scheduledJob.durationHours * 60 * 60 * 1000) +
                                (_scheduledJob.durationMinutes * 60 * 1000);
                            teamMemberTimes[teamMemberId] = new Date(
                                teamMemberTimes[teamMemberId].getTime() + durationMillis
                            );
                        }
                        // If applying by template time, use the original template time

                        // Create the job
                        this._scheduledJobsService.createScheduledJob(_scheduledJob).subscribe(
                            (newJob) => {
                                processedItems++;

                                // If job status is approved, send notification
                                if (jobStatus === 'approved') {
                                    this.sendJobNotification(newJob, teamMemberId);
                                }

                                // If all items have been processed
                                if (processedItems === totalItems) {
                                    this.finalizePresetApplication(preset);
                                }
                            },
                            error => {
                                console.error('Error creating job from template:', error);
                                processedItems++;

                                if (processedItems === totalItems) {
                                    this._fuseLoadingService.hide();
                                    this._snackBar.open('Error applying some items from preset', 'Close', {
                                        duration: 3000
                                    });

                                    // Refresh the schedule anyway
                                    this.loadJobList(this.schedulerSelectedDate, this.schedulerSelectedDate, null);
                                }
                            }
                        );
                    },
                    error => {
                        console.error('Error loading job template:', error);
                        processedItems++;

                        if (processedItems === totalItems) {
                            this._fuseLoadingService.hide();
                            this._snackBar.open('Error applying preset', 'Close', {
                                duration: 3000
                            });
                        }
                    }
                );
            });
        });
    }

    // Helper function to send job notification when job status is approved
    private sendJobNotification(job: ScheduledJob, teamMemberId: string): void {
        const teamMember = this.teamMembers.find(tm => tm.$id === teamMemberId);
        if (!teamMember || !teamMember.deviceTokenId) {
            return;
        }

        const customer = this.customers.find(c => c.$id === job.customerId);
        const customerName = customer ? customer.name : '';

        this.fcmNotification = {
            body: `📢 Your scheduled job has been dispatched.\n🧑‍💼 Customer Name: ${customerName}\n📍 Job Address: ${job.jobAddress}`,
            title: `🚛 ${job.jobTitle} Dispatched`,
            to: teamMember.deviceTokenId,
            type: 'schedule',
        };

        this._fcmNotificationService.sendNotification(this.fcmNotification);
    }

    // Helper function to finalize preset application
    private finalizePresetApplication(preset: SchedulePreset): void {
        this._fuseLoadingService.hide();
        this._snackBar.open(`Applied preset: ${preset.presetName}`, 'Close', {
            duration: 3000
        });

        // Store this preset as recently used
        this.updateRecentPreset(preset);

        // Refresh the schedule
        this.loadJobList(this.schedulerSelectedDate, this.schedulerSelectedDate, null);
    }

    private updateRecentPreset(preset: SchedulePreset): void {
        try {
            // Get current recent presets from localStorage
            let recentPresets = [];
            const savedPresets = localStorage.getItem('recentPresets');

            if (savedPresets) {
                recentPresets = JSON.parse(savedPresets);
            }

            // Remove this preset if it already exists in the recent list
            recentPresets = recentPresets.filter(p => p.$id !== preset.$id);

            // Add the preset to the beginning of the array
            recentPresets.unshift({
                $id: preset.$id,
                presetName: preset.presetName,
                description: preset.description
            });

            // Limit to 4 recent presets
            if (recentPresets.length > 4) {
                recentPresets = recentPresets.slice(0, 4);
            }

            // Save back to localStorage
            localStorage.setItem('recentPresets', JSON.stringify(recentPresets));

            // Update the component's recent presets
            this.recentPresets = recentPresets;
            this._changeDetectorRef.markForCheck();
        } catch (error) {
            console.error('Error updating recent presets:', error);
        }
    }
}

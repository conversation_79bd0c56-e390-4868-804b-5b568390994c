
import { SchedulerConfig, EventModel, EventModelConfig, ResourceModelConfig, ResourceTimeRangeModelConfig, Date<PERSON><PERSON><PERSON>, StringHelper, CellTooltip, But<PERSON> } from '@bryntum/scheduler';

import { CustomResourceModel } from './tooltip/customResourceModel';
import { CustomEventModel } from './tooltip/customEventModel';

const colors = {
    offDuty: 'red',
    onDuty: 'blue',
    driving: 'green',
    breaking: 'deep-orange',

};
export const SchedulerBaseConfig: Partial<SchedulerConfig> = {



    startDate: DateHelper.clearTime(new Date()),
    endDate: DateHelper.clearTime(new Date()),

    columns: [
        // {
        //     text: 'Team Member',
        //     field: 'name',
        //     width: 180,
        //     align: 'center',
        //     type: 'resourceInfo',
        //     editor: { editable: false, },
        //     hidden: true,


        // },
        // {
        //     type: 'action',
        //     text: 'Actions',
        //     actions: [{
        //         cls: 'b-fa b-fa-plus',
        //         visible: ({ record }) => record.canAdd,
        //         onClick: ({ record }) => console.log(`Adding ${record.name}`)
        //     }, {
        //         cls: 'b-fa b-fa-pencil',
        //         tooltip: 'Edit note',
        //         onClick: ({ record }) => console.log(`Editing ${record.name}`)
        //     }]
        // },
        {
            text: 'Team Member',
            field: 'name',
            width: 180,
            // filterable: {
            //     filterField: {
            //         type: 'button',
            //         multiSelect: true,
            //         editable: false,
            //         text: 'OK',
            //     },
            //     //  filterFn : ({ record, value }) => !value.length || value.includes(record.city)
            // },

            editor: { editable: false, },
            renderer({ record }) {
                const taskCount = record.events.length
                // console.log(record);
                return {
                    className: 'employee',
                    children: [
                        // Employee image

                        record.imageUrl ? {
                            tag: 'img',
                            className: `b-sch-foreground-${colors[record.dutyStatus] ?? 'red'}`,
                            src: record.imageUrl
                        } : {
                            tag: 'div',
                            className: `border-foreground-${colors[record.dutyStatus] ?? 'red'} employee-initial`,
                            text: record.name.split(' ') // Split name into parts
                                .filter((n) => n) // Filter out any empty strings just in case
                                .map((n) => n[0]) // Map to first character of each part
                                .join('') // Join them together
                                .toUpperCase() // Convert to uppercase
                        },
                        // Employee info
                        {
                            className: 'name',
                            text: record.name
                        },
                        {
                            tag: 'a', // Use an anchor tag to make it clickable
                            className: 'dutyStatus',
                            href: `livemap/?teamMember=${record.id}`,
                            text: record.dutyStatus
                        },

                        // Assignment info
                        {
                            tag: 'i',
                            className: 'tasks b-fa b-fa-tasks',
                            dataset: {
                                btip: `${taskCount} assigned task${taskCount !== 1 ? 's' : ''}`
                            },
                            text: taskCount
                        },

                    ],

                };
            }

        }

    ],

    eventStore: {
        modelClass: CustomEventModel,
        syncDataOnLoad: true,
        autoCommit: true,
        autoLoad: true,
        autoCommitTimeout: 1000,

    },
    resourceStore: {
        syncDataOnLoad: true,
        modelClass: CustomResourceModel,
        autoCommit: true,
    },

    enableRecurringEvents: true,
    viewPreset: {
        base: 'hourAndDay',
        tickWidth: 180,
        tickHeight: 20,
        headers: [                     // This defines your header rows from top to bottom

            {
                unit: 'hour',
                dateFormat: 'HH:mm'
            }
        ],
    },

    eventRenderer: ({ eventRecord, resourceRecord, renderData }) => {
        const customEventRecord = eventRecord as CustomEventModel;
        renderData.cls[`b-type-${customEventRecord.type}`] = 1;

        return [
            {
                class: 'header',
                children: [
                    // Progress bar
                    {
                        class: 'progress-outer',
                        children: [
                            {
                                class: 'progress-fill',
                                style: {
                                    width: (customEventRecord.progress || 0) + '%'
                                }
                            },

                        ]
                    },
                    // Driver attached files icon
                    customEventRecord.driverAttachedFiles ? {
                        tag: 'i',
                        class: 'fa-duotone fa-comment-image',

                    } : null,
                    // Time display
                    {
                        class: 'time-display',
                        children: [
                            {
                                tag: 'span',
                                class: 'time-text',
                                text: DateHelper.format(customEventRecord.startDate as Date, 'LST')
                            }
                        ]
                    },
                    // Status icon
                    {
                        class: customEventRecord.icon
                    }
                ]
            },
            {
                class: 'event-content',
                children: [
                    {
                        class: 'event-title-section',
                        children: [
                            // Meeting icon if applicable
                            customEventRecord.type === 'meeting' ? {
                                class: 'b-fa b-fa-people-group meeting-icon'
                            } : null,
                            // Job title
                            {
                                tag: 'div',
                                class: 'title-container',
                                children: [
                                    {
                                        tag: 'span',
                                        class: 'job-title font-bold',
                                        text: customEventRecord.name ?? '(No Title)'
                                    },
                                    // Customer name in new line
                                    {
                                        tag: 'div',
                                        class: 'customer-name text-sm',
                                        text: customEventRecord.customerName ? `Customer: ${customEventRecord.customerName}` : ''
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ];
    },

    rowHeight: 70,
    barMargin: 5,
    stickyHeaders: true,
    fillTicks: true,
    multiEventSelect: false,
    zoomOnMouseWheel: false,
    zoomOnTimeAxisDoubleClick: false,
    createEventOnDblClick: false,
    features: {
        scheduleContext: {
            disabled: true,
            triggerEvent: 'hover',
            renderer: (context, element) => {
                element.innerText = '😎';
                element.classList.add('drop-target');
                console.log(context);
                console.log(element);


            }
        },
        filterBar: {
            compactMode: true,
        },

        eventMenu: false,
        scheduleMenu: false,
        resourceMenu: false,
        cellMenu: false,
        headerMenu: false,
        headerZoom: false,

        timeAxisHeaderMenu: false,
        cellEdit: false,
        timeRanges: {
            showHeaderElements: true,
            showCurrentTimeLine: true,
            enableResizing: false,


        },

        scheduleTooltip: false,

        regionResize: !1,

        eventDrag: {
            // Custom tooltip for when an event is dragged
            tooltipTemplate: ({ eventRecord, startDate }) => {
                return StringHelper.xss`
                <div style="margin-bottom:0.8em">${eventRecord.name}</div>
                <i style="margin-right:0.5em" class="b-icon b-icon-clock"></i>${DateHelper.format(startDate, 'HH:mm')}
            `;
            }
        },
        eventResize: true,
        eventDragSelect: false,
        eventTooltip: {
            template: (data: {
                eventRecord: EventModel;
                startDate: Date;
                endDate: Date;
                startText: string;
                endText: string;
                tip: any;
            }): string => {
                const customEventRecord = data.eventRecord as CustomEventModel;

                return `
                <div class="w-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                    <!-- Header Section with Flex Layout -->
                    <div class="p-4 border-b dark:border-gray-700">
                        <div class="flex flex-col gap-1 justify-between">
                            <!-- Status and Icon Row -->
                            <div class="flex items-center justify-between w-full">
                                <div class="px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap uppercase ${customEventRecord.jobStatus?.toLowerCase() === 'finished' ? 'bg-green-100 text-green-800' :
                        customEventRecord.jobStatus?.toLowerCase() === 'approved' ? 'bg-blue-100 text-blue-800' :
                            customEventRecord.jobStatus?.toLowerCase() === 'onmyway' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-gray-100 text-gray-800'
                    }">
                                    ${customEventRecord.jobStatus || 'No Status'}
                                </div>
                                ${customEventRecord.resource?.iconCls ?
                        `<i class="text-green-600 ${customEventRecord.resource.iconCls}"></i>` :
                        ''
                    }
                            </div>
                            
                            <!-- Title -->
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white break-words ">
                                ${customEventRecord.name}
                            </h3>
                        </div>
                    </div>
        
                    <!-- Time and Date Section -->
                    <div class="p-4 bg-gray-50 dark:bg-gray-700">
                        <div class="flex items-center text-gray-600 dark:text-gray-300 overflow-hidden">
                            <i class="far fa-clock flex-shrink-0 mr-2"></i>
                            <span class="truncate">${DateHelper.format(customEventRecord.startDate as Date, 'LST')} - ${DateHelper.format(customEventRecord.endDate as Date, 'LST')}</span>
                        </div>
                        <div class="mt-1 text-gray-500 dark:text-gray-400">
                            ${DateHelper.format(customEventRecord.startDate as Date, 'ddd, DD MMM')}
                        </div>
                    </div>
        
                    <!-- Customer Info Section -->
                    <div class="p-4 border-t border-gray-200 dark:border-gray-700">
                        <div class="space-y-3">
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Customer Details</h4>
                                <p class="text-gray-900 dark:text-white break-words">
                                    ${customEventRecord.customerName}
                                </p>
                                <p class="text-gray-600 dark:text-gray-300 break-words">
                                    ${customEventRecord.customerPhone}
                                </p>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Job Location</h4>
                                <p class="text-gray-900 dark:text-white break-words max-h-20 overflow-y-auto">
                                    ${customEventRecord.address}
                                </p>
                            </div>
                        </div>
                    </div>
        
                    <!-- Assigned Team Member -->
                    <div class="p-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
                        <div class="flex items-center">
                            ${customEventRecord.resource?.imageUrl ?
                        `<img class="h-10 w-10 rounded-full object-cover border-2 border-white dark:border-gray-600 flex-shrink-0" 
                                      src="${customEventRecord.resource.imageUrl}" 
                                      alt="${customEventRecord.resource.name}">` :
                        `<div class="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center flex-shrink-0">
                                    <span class="text-lg font-medium text-gray-600 dark:text-gray-200">
                                        ${customEventRecord.resource?.name.charAt(0)}
                                    </span>
                                </div>`
                    }
                            <div class="ml-3 min-w-0">
                                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    ${customEventRecord.resource?.name}
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Assigned Team Member</p>
                            </div>
                        </div>
                    </div>
                </div>`;
            },
            anchor: false,
            anchorToTarget: false,
            trackMouse: true,
        },


    },

    suppressFit: true,
    maintainSelectionOnDatasetChange: true,
    enableEventAnimations: true,
}

import { Component, Inject, OnInit } from '@angular/core';

import { FormsModule, ReactiveFormsModule, FormControl, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatAutocompleteModule } from '@angular/material/autocomplete';

@Component({
    selector: 'app-apply-preset-dialog',
    templateUrl: './apply-preset-dialog.component.html',
    standalone: true,
    imports: [
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatRadioModule,
    MatFormFieldModule,
    MatInputModule,
    MatAutocompleteModule
]
})
export class ApplyPresetDialogComponent implements OnInit {
    applyMethod: 'order' | 'templateTime' = 'order';
    jobStatus: 'added' | 'approved' = 'added';
    startTimeControl = new FormControl('', [Validators.required]);
    hoursList: string[] = [];

    constructor(
        public dialogRef: MatDialogRef<ApplyPresetDialogComponent>,
        @Inject(MAT_DIALOG_DATA) public data: { presetName: string, hoursList: string[] }
    ) {
        this.hoursList = data.hoursList;
    }

    ngOnInit(): void {
        // Initialize with default start time (current time rounded to nearest 15 min)
        const now = new Date();
        let hours = now.getHours();
        const minutes = Math.floor(now.getMinutes() / 15) * 15;
        const period = hours >= 12 ? 'PM' : 'AM';

        // Convert to 12-hour format
        hours = hours % 12;
        hours = hours ? hours : 12; // 0 should be 12

        // Format the time
        const formattedHours = hours.toString().padStart(2, '0');
        const formattedMinutes = minutes.toString().padStart(2, '0');
        const defaultTime = `${formattedHours}:${formattedMinutes} ${period}`;

        // Set the default time
        this.startTimeControl.setValue(defaultTime);
    }

    onNoClick(): void {
        this.dialogRef.close();
    }
    trackByFn(index: number, item: any): any {
        return item || index;
    }
}

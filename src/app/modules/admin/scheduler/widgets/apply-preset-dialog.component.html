<!-- Updated apply-preset-dialog.component.html with job status option -->

<div class="flex flex-col p-6 max-w-md">
  <div class="text-lg font-medium mb-4">
    Apply Schedule Preset: {{ data.presetName }}
  </div>

  <!-- Application Method -->
  <div class="mb-4">
    <div class="text-sm font-medium mb-2">Application Method</div>
    <mat-radio-group [(ngModel)]="applyMethod" class="flex flex-col gap-3">
      <mat-radio-button value="order" checked>
        <div class="flex flex-col">
          <span class="font-medium">Apply by Order</span>
          <span class="text-xs text-gray-600"
            >Items will be grouped by team member and applied in
            order, starting at the selected time</span
            >
          </div>
        </mat-radio-button>

        <mat-radio-button value="templateTime">
          <div class="flex flex-col">
            <span class="font-medium">Apply by Template Time</span>
            <span class="text-xs text-gray-600"
              >Each item will use its original template time</span
              >
            </div>
          </mat-radio-button>
        </mat-radio-group>
      </div>

      <!-- Job Status -->
      <div class="mb-4">
        <div class="text-sm font-medium mb-2">Job Status</div>
        <mat-radio-group [(ngModel)]="jobStatus" class="flex flex-col gap-3">
          <mat-radio-button value="added" checked>
            <div class="flex flex-col">
              <span class="font-medium">Add Jobs</span>
              <span class="text-xs text-gray-600"
                >Jobs will be created with 'Added' status</span
                >
              </div>
            </mat-radio-button>

            <mat-radio-button value="approved">
              <div class="flex flex-col">
                <span class="font-medium">Approve & Dispatch Jobs</span>
                <span class="text-xs text-gray-600"
                  >Jobs will be created with 'Approved' status and
                  dispatched</span
                  >
                </div>
              </mat-radio-button>
            </mat-radio-group>
          </div>

          <!-- Start Time (only for order method) -->
          @if (applyMethod === 'order') {
            <div class="mb-6">
              <mat-form-field class="w-full">
                <mat-label>Start Time</mat-label>
                <input
                  matInput
                  [formControl]="startTimeControl"
                  [matAutocomplete]="auto"
                  placeholder="Select a start time"
                  />
                <mat-autocomplete #auto="matAutocomplete">
                  @for (time of hoursList; track trackByFn(index, time)) {
                    <mat-option [value]="time">{{ time }}</mat-option>
                  }
                </mat-autocomplete>
              </mat-form-field>
            </div>
          }

          <div class="flex justify-end gap-2">
            <button mat-button mat-dialog-close>Cancel</button>
            <button
              mat-flat-button
              color="primary"
              [disabled]="applyMethod === 'order' && !startTimeControl.valid"
            [mat-dialog-close]="{
                applyMethod: applyMethod,
                startTime: startTimeControl.value,
                jobStatus: jobStatus
            }"
              >
              Apply
            </button>
          </div>
        </div>

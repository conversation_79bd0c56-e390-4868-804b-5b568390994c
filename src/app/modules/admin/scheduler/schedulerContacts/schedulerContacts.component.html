<mat-dialog-content class="flex w-full">
    <div class="flex flex-col w-full min-w-140">
        <ng-container>
            <!-- Header -->
            <div
                class="relative w-full h-18 sm:h-20 bg-accent-700 dark:bg-accent-700"
            >
                <div
                    class="flex items-center justify-end w-full max-w-3xl mx-auto pt-6"
                >
                    <button
                        mat-icon-button
                        [matTooltip]="'Close'"
                        (click)="cancelUpdateDetails()"
                    >
                        <mat-icon
                            class="text-white"
                            [svgIcon]="'heroicons_outline:x-mark'"
                        ></mat-icon>
                    </button>
                </div>
            </div>

            <!-- Contact form -->
            <div
                class="relative flex flex-col flex-auto items-center px-6 sm:px-12"
            >
                <div class="w-full max-w-3xl">
                    <form [formGroup]="contactForm">
                        <!-- Avatar -->
                        <div class="flex flex-auto items-end -mt-16">
                            <div
                                class="relative flex items-center justify-center w-32 h-32 rounded-full overflow-hidden ring-4 ring-bg-card"
                            >
                                <!-- Upload / Remove avatar -->
                                <div
                                    class="absolute inset-0 bg-black bg-opacity-50 z-10"
                                ></div>
                                <div
                                    class="absolute inset-0 flex items-center justify-center z-20"
                                >
                                    <div>
                                        <input
                                            id="avatar-file-input"
                                            class="absolute h-0 w-0 opacity-0 invisible pointer-events-none"
                                            type="file"
                                            [multiple]="false"
                                            [accept]="'image/jpeg, image/png'"
                                            (change)="
                                                uploadAvatar(
                                                    avatarFileInput.files
                                                )
                                            "
                                            #avatarFileInput
                                        />
                                        <label
                                            class="flex items-center justify-center w-10 h-10 rounded-full cursor-pointer hover:bg-hover"
                                            for="avatar-file-input"
                                            matRipple
                                        >
                                            <mat-icon
                                                class="text-white"
                                                [svgIcon]="
                                                    'heroicons_outline:camera'
                                                "
                                            ></mat-icon>
                                        </label>
                                    </div>
                                    <div>
                                        <button
                                            mat-icon-button
                                            (click)="removeAvatar()"
                                        >
                                            <mat-icon
                                                class="text-white"
                                                [svgIcon]="
                                                    'heroicons_outline:trash'
                                                "
                                            ></mat-icon>
                                        </button>
                                    </div>
                                </div>
                                <!-- Image/Letter -->
                                @if (contact.avatar) {
                                    <img
                                        class="object-cover w-full h-full"
                                        [src]="contact.avatar"
                                    />
                                }
                                @if (!contact.avatar && contact.name) {
                                    <div
                                        class="flex items-center justify-center w-full h-full rounded overflow-hidden uppercase text-8xl font-bold leading-none bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                    >
                                        {{ contact.name.charAt(0) }}
                                    </div>
                                }
                            </div>
                        </div>

                        <!-- Name -->
                        <div class="mt-8">
                            <mat-form-field
                                class="w-full"
                                [subscriptSizing]="'dynamic'"
                            >
                                <mat-label>Name</mat-label>
                                <mat-icon
                                    matPrefix
                                    class="hidden sm:flex icon-size-5"
                                    [svgIcon]="'heroicons_solid:user-circle'"
                                ></mat-icon>
                                <input
                                    matInput
                                    [formControlName]="'name'"
                                    [placeholder]="'Name'"
                                    [spellcheck]="false"
                                />
                            </mat-form-field>
                        </div>

                        <!-- Tags -->
                        <div class="flex flex-wrap items-center -m-1.5 mt-6">
                            <!-- Tags -->
                            @if (contact.tags.length) {
                                @for (
                                    tag of contact.tags
                                        | fuseFindByKey: "$id" : tags;
                                    track trackByFn($index, tag)
                                ) {
                                    <div
                                        class="flex items-center justify-center px-4 m-1.5 rounded-full leading-9 text-gray-500 bg-gray-100 dark:text-gray-300 dark:bg-gray-700"
                                    >
                                        <span
                                            class="text-md font-medium whitespace-nowrap"
                                            >{{ tag ? tag.title : "" }}</span
                                        >
                                    </div>
                                }
                            }
                            <!-- Tags panel and its button -->
                            <div
                                class="flex items-center justify-center px-4 m-1.5 rounded-full leading-9 cursor-pointer text-gray-500 bg-gray-100 dark:text-gray-300 dark:bg-gray-700"
                                (click)="openTagsPanel()"
                                #tagsPanelOrigin
                            >
                                @if (contact.tags.length) {
                                    <mat-icon
                                        class="icon-size-5"
                                        [svgIcon]="
                                            'heroicons_solid:pencil-square'
                                        "
                                    ></mat-icon>
                                    <span
                                        class="ml-1.5 text-md font-medium whitespace-nowrap"
                                        >Edit</span
                                    >
                                }

                                @if (!contact.tags.length) {
                                    <mat-icon
                                        class="icon-size-5"
                                        [svgIcon]="
                                            'heroicons_solid:plus-circle'
                                        "
                                    ></mat-icon>
                                    <span
                                        class="ml-1.5 text-md font-medium whitespace-nowrap"
                                        >Add</span
                                    >
                                }

                                <!-- Tags panel -->
                                <ng-template #tagsPanel>
                                    <div
                                        class="w-60 rounded border shadow-md bg-card"
                                    >
                                        <!-- Tags panel header -->
                                        <div class="flex items-center m-3 mr-2">
                                            <div class="flex items-center">
                                                <mat-icon
                                                    class="icon-size-5"
                                                    [svgIcon]="
                                                        'heroicons_solid:magnifying-glass'
                                                    "
                                                ></mat-icon>
                                                <div class="ml-2">
                                                    <input
                                                        class="w-full min-w-0 py-1 border-0"
                                                        type="text"
                                                        placeholder="Enter tag name"
                                                        (input)="
                                                            filterTags($event)
                                                        "
                                                        (keydown)="
                                                            filterTagsInputKeyDown(
                                                                $event
                                                            )
                                                        "
                                                        [maxLength]="30"
                                                        #newTagInput
                                                    />
                                                </div>
                                            </div>
                                            <button
                                                class="ml-1"
                                                mat-icon-button
                                                (click)="toggleTagsEditMode()"
                                            >
                                                @if (!tagsEditMode) {
                                                    <mat-icon
                                                        class="icon-size-5"
                                                        [svgIcon]="
                                                            'heroicons_solid:pencil-square'
                                                        "
                                                    ></mat-icon>
                                                }
                                                @if (tagsEditMode) {
                                                    <mat-icon
                                                        class="icon-size-5"
                                                        [svgIcon]="
                                                            'heroicons_solid:check'
                                                        "
                                                    ></mat-icon>
                                                }
                                            </button>
                                        </div>
                                        <div
                                            class="flex flex-col max-h-64 py-2 border-t overflow-y-auto"
                                        >
                                            <!-- Tags -->
                                            @if (!tagsEditMode) {
                                                @for (
                                                    tag of filteredTags;
                                                    track trackByFn($index, tag)
                                                ) {
                                                    <div
                                                        class="flex items-center h-10 min-h-10 pl-1 pr-4 cursor-pointer hover:bg-hover"
                                                        (click)="
                                                            toggleContactTag(
                                                                tag
                                                            )
                                                        "
                                                        matRipple
                                                    >
                                                        <mat-checkbox
                                                            class="flex items-center h-10 min-h-10 pointer-events-none"
                                                            [checked]="
                                                                contact.tags.includes(
                                                                    tag.$id
                                                                )
                                                            "
                                                            [color]="'primary'"
                                                            [disableRipple]="
                                                                true
                                                            "
                                                        >
                                                        </mat-checkbox>
                                                        <div>
                                                            {{
                                                                tag
                                                                    ? tag.title
                                                                    : ""
                                                            }}
                                                        </div>
                                                    </div>
                                                }
                                            }
                                            <!-- Tags editing -->
                                            @if (tagsEditMode) {
                                                <div class="py-2 space-y-2">
                                                    @for (
                                                        tag of filteredTags;
                                                        track trackByFn(
                                                            $index,
                                                            tag
                                                        )
                                                    ) {
                                                        <div
                                                            class="flex items-center"
                                                        >
                                                            <mat-form-field
                                                                class="fuse-mat-dense w-full mx-4"
                                                                [subscriptSizing]="
                                                                    'dynamic'
                                                                "
                                                            >
                                                                <input
                                                                    matInput
                                                                    [value]="
                                                                        tag.title
                                                                    "
                                                                    (input)="
                                                                        updateTagTitle(
                                                                            tag,
                                                                            $event
                                                                        )
                                                                    "
                                                                />
                                                                <button
                                                                    mat-icon-button
                                                                    (click)="
                                                                        deleteTag(
                                                                            tag
                                                                        )
                                                                    "
                                                                    matSuffix
                                                                >
                                                                    <mat-icon
                                                                        class="icon-size-5 ml-2"
                                                                        [svgIcon]="
                                                                            'heroicons_solid:trash'
                                                                        "
                                                                    ></mat-icon>
                                                                </button>
                                                            </mat-form-field>
                                                        </div>
                                                    }
                                                </div>
                                            }
                                            <!-- Create tag -->
                                            @if (
                                                shouldShowCreateTagButton(
                                                    newTagInput.value
                                                )
                                            ) {
                                                <div
                                                    class="flex items-center h-10 min-h-10 -ml-0.5 pl-4 pr-3 leading-none cursor-pointer hover:bg-hover"
                                                    (click)="
                                                        createTag(
                                                            newTagInput.value
                                                        );
                                                        newTagInput.value = ''
                                                    "
                                                    matRipple
                                                >
                                                    <mat-icon
                                                        class="mr-2 icon-size-5"
                                                        [svgIcon]="
                                                            'heroicons_solid:plus-circle'
                                                        "
                                                    ></mat-icon>
                                                    <div class="break-all">
                                                        Create "<b>{{
                                                            newTagInput.value
                                                        }}</b
                                                        >"
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </ng-template>
                            </div>
                        </div>

                        <!-- Company -->
                        <div class="mt-8">
                            <mat-form-field
                                class="w-full"
                                [subscriptSizing]="'dynamic'"
                            >
                                <mat-label>Company</mat-label>
                                <mat-icon
                                    matPrefix
                                    class="hidden sm:flex icon-size-5"
                                    [svgIcon]="
                                        'heroicons_solid:building-office-2'
                                    "
                                ></mat-icon>
                                <input
                                    matInput
                                    [formControlName]="'company'"
                                    [placeholder]="'Company'"
                                />
                            </mat-form-field>
                        </div>

                        <!-- Emails -->
                        <div class="mt-8">
                            <div class="space-y-4">
                                @for (
                                    email of contactForm.get("emails")[
                                        "controls"
                                    ];
                                    track trackByFn(i, email);
                                    let i = $index;
                                    let first = $first;
                                    let last = $last
                                ) {
                                    <div class="flex">
                                        <mat-form-field
                                            class="flex-auto"
                                            [subscriptSizing]="'dynamic'"
                                        >
                                            @if (first) {
                                                <mat-label>Email</mat-label>
                                            }
                                            <mat-icon
                                                matPrefix
                                                class="hidden sm:flex icon-size-5"
                                                [svgIcon]="
                                                    'heroicons_solid:envelope'
                                                "
                                            ></mat-icon>
                                            <input
                                                matInput
                                                [formControl]="
                                                    email.get('email')
                                                "
                                                [placeholder]="'Email address'"
                                                [spellcheck]="false"
                                            />
                                        </mat-form-field>
                                        <mat-form-field
                                            class="flex-auto w-full max-w-24 sm:max-w-40 ml-2 sm:ml-4"
                                            [subscriptSizing]="'dynamic'"
                                        >
                                            @if (first) {
                                                <mat-label>Label</mat-label>
                                            }
                                            <mat-icon
                                                matPrefix
                                                class="hidden sm:flex icon-size-5"
                                                [svgIcon]="
                                                    'heroicons_solid:tag'
                                                "
                                            ></mat-icon>
                                            <input
                                                matInput
                                                [formControl]="
                                                    email.get('label')
                                                "
                                                [placeholder]="'Label'"
                                            />
                                        </mat-form-field>
                                        <!-- Remove email -->
                                        @if (!(first && last)) {
                                            <div
                                                class="flex items-center w-10 pl-2"
                                                [ngClass]="{ 'mt-6': first }"
                                            >
                                                <button
                                                    class="w-8 h-8 min-h-8"
                                                    mat-icon-button
                                                    (click)="
                                                        removeEmailField(i)
                                                    "
                                                    matTooltip="Remove"
                                                >
                                                    <mat-icon
                                                        class="icon-size-5"
                                                        [svgIcon]="
                                                            'heroicons_solid:trash'
                                                        "
                                                    ></mat-icon>
                                                </button>
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                            <div
                                class="group inline-flex items-center mt-2 -ml-4 py-2 px-4 rounded cursor-pointer"
                                (click)="addEmailField()"
                            >
                                <mat-icon
                                    class="icon-size-5"
                                    [svgIcon]="'heroicons_solid:plus-circle'"
                                ></mat-icon>
                                <span
                                    class="ml-2 font-medium text-secondary group-hover:underline"
                                    >Add an email address</span
                                >
                            </div>
                        </div>

                        <!-- Phone numbers -->
                        <div class="mt-8">
                            <div class="space-y-4">
                                @for (
                                    phoneNumber of contactForm.get(
                                        "phoneNumbers"
                                    )["controls"];
                                    track trackByFn(i, phoneNumber);
                                    let i = $index;
                                    let first = $first;
                                    let last = $last
                                ) {
                                    <div class="relative flex">
                                        <mat-form-field
                                            class="flex-auto"
                                            [subscriptSizing]="'dynamic'"
                                        >
                                            @if (first) {
                                                <mat-label>Phone</mat-label>
                                            }
                                            <input
                                                matInput
                                                [formControl]="
                                                    phoneNumber.get(
                                                        'phoneNumber'
                                                    )
                                                "
                                                [placeholder]="'Phone'"
                                            />
                                        </mat-form-field>
                                        <mat-form-field
                                            class="flex-auto w-full max-w-24 sm:max-w-40 ml-2 sm:ml-4"
                                            [subscriptSizing]="'dynamic'"
                                        >
                                            @if (first) {
                                                <mat-label>Label</mat-label>
                                            }
                                            <mat-icon
                                                matPrefix
                                                class="hidden sm:flex icon-size-5"
                                                [svgIcon]="
                                                    'heroicons_solid:tag'
                                                "
                                            ></mat-icon>
                                            <input
                                                matInput
                                                [formControl]="
                                                    phoneNumber.get('label')
                                                "
                                                [placeholder]="'Label'"
                                            />
                                        </mat-form-field>
                                        <!-- Remove phone number -->
                                        @if (!(first && last)) {
                                            <div
                                                class="flex items-center w-10 pl-2"
                                                [ngClass]="{ 'mt-6': first }"
                                            >
                                                <button
                                                    class="w-8 h-8 min-h-8"
                                                    mat-icon-button
                                                    (click)="
                                                        removePhoneNumberField(
                                                            i
                                                        )
                                                    "
                                                    matTooltip="Remove"
                                                >
                                                    <mat-icon
                                                        class="icon-size-5"
                                                        [svgIcon]="
                                                            'heroicons_solid:trash'
                                                        "
                                                    ></mat-icon>
                                                </button>
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                            <div
                                class="group inline-flex items-center mt-2 -ml-4 py-2 px-4 rounded cursor-pointer"
                                (click)="addPhoneNumberField()"
                            >
                                <mat-icon
                                    class="icon-size-5"
                                    [svgIcon]="'heroicons_solid:plus-circle'"
                                ></mat-icon>
                                <span
                                    class="ml-2 font-medium text-secondary group-hover:underline"
                                    >Add a phone number</span
                                >
                            </div>
                        </div>

                        <!-- Address -->
                        <div class="mt-8">
                            <div class="space-y-4">
                                @for (
                                    address of contactForm.get("addresses")[
                                        "controls"
                                    ];
                                    track trackByFn(i, address);
                                    let i = $index;
                                    let first = $first;
                                    let last = $last
                                ) {
                                    <div class="flex">
                                        <mat-form-field
                                            class="flex-auto"
                                            [subscriptSizing]="'dynamic'"
                                        >
                                            @if (first) {
                                                <mat-label>Address</mat-label>
                                            }
                                            <mat-icon
                                                matPrefix
                                                class="hidden sm:flex icon-size-5"
                                                [svgIcon]="
                                                    'heroicons_solid:map-pin'
                                                "
                                            ></mat-icon>
                                            <input
                                                matInput
                                                [formControl]="
                                                    address.get('address')
                                                "
                                                [placeholder]="'Address'"
                                                [spellcheck]="false"
                                                matMapsAutocomplete
                                                [country]="'au'"
                                                (onAutocompleteSelected)="
                                                    onAutocompleteSelected(
                                                        $event,
                                                        i
                                                    )
                                                "
                                            />
                                        </mat-form-field>
                                        <mat-form-field
                                            class="flex-auto w-full max-w-24 sm:max-w-40 ml-2 sm:ml-4"
                                            [subscriptSizing]="'dynamic'"
                                        >
                                            @if (first) {
                                                <mat-label>Label</mat-label>
                                            }
                                            <mat-icon
                                                matPrefix
                                                class="hidden sm:flex icon-size-5"
                                                [svgIcon]="
                                                    'heroicons_solid:tag'
                                                "
                                            ></mat-icon>
                                            <input
                                                matInput
                                                [formControl]="
                                                    address.get('label')
                                                "
                                                [placeholder]="'Label'"
                                            />
                                        </mat-form-field>
                                        <!-- Remove address -->
                                        @if (!(first && last)) {
                                            <div
                                                class="flex items-center w-10 pl-2"
                                                [ngClass]="{ 'mt-6': first }"
                                            >
                                                <button
                                                    class="w-8 h-8 min-h-8"
                                                    mat-icon-button
                                                    (click)="
                                                        removeAddressField(i)
                                                    "
                                                    matTooltip="Remove"
                                                >
                                                    <mat-icon
                                                        class="icon-size-5"
                                                        [svgIcon]="
                                                            'heroicons_solid:trash'
                                                        "
                                                    ></mat-icon>
                                                </button>
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                            <div
                                class="group inline-flex items-center mt-2 -ml-4 py-2 px-4 rounded cursor-pointer"
                                (click)="addAddressField()"
                            >
                                <mat-icon
                                    class="icon-size-5"
                                    [svgIcon]="'heroicons_solid:plus-circle'"
                                ></mat-icon>
                                <span
                                    class="ml-2 font-medium text-secondary group-hover:underline"
                                    >Add an address</span
                                >
                            </div>
                        </div>

                        <!-- Birthday -->
                        <div class="mt-8">
                            <mat-form-field
                                class="w-full"
                                [subscriptSizing]="'dynamic'"
                            >
                                <mat-label>Birthday</mat-label>
                                <mat-icon
                                    matPrefix
                                    class="hidden sm:flex icon-size-5"
                                    [svgIcon]="'heroicons_solid:cake'"
                                ></mat-icon>
                                <input
                                    matInput
                                    [matDatepicker]="birthdayDatepicker"
                                    [formControlName]="'birthday'"
                                    [placeholder]="'Birthday'"
                                />
                                <mat-datepicker-toggle
                                    matSuffix
                                    [for]="birthdayDatepicker"
                                >
                                </mat-datepicker-toggle>
                                <mat-datepicker
                                    #birthdayDatepicker
                                ></mat-datepicker>
                            </mat-form-field>
                        </div>

                        <!-- Notes -->
                        <div class="mt-8">
                            <mat-form-field
                                class="w-full"
                                [subscriptSizing]="'dynamic'"
                            >
                                <mat-label>Notes</mat-label>
                                <mat-icon
                                    matPrefix
                                    class="hidden sm:flex icon-size-5"
                                    [svgIcon]="
                                        'heroicons_solid:bars-3-bottom-left'
                                    "
                                ></mat-icon>
                                <textarea
                                    matInput
                                    [formControlName]="'notes'"
                                    [placeholder]="'Notes'"
                                    [rows]="5"
                                    [spellcheck]="false"
                                    cdkTextareaAutosize
                                ></textarea>
                            </mat-form-field>
                        </div>
                        <!--Chat phon number -->
                        <div class="mt-8">
                            <mat-form-field
                                class="w-full"
                                [subscriptSizing]="'dynamic'"
                            >
                                <mat-label>Chat phone number</mat-label>
                                <mat-icon
                                    matPrefix
                                    class="hidden sm:flex icon-size-5"
                                    [svgIcon]="
                                        'heroicons_solid:chat-bubble-left-right'
                                    "
                                ></mat-icon>
                                <input
                                    matInput
                                    [formControlName]="'chatPhoneNumber'"
                                    [placeholder]="'Chat phone number'"
                                />
                            </mat-form-field>
                        </div>

                        <!-- Actions -->
                        <div
                            class="flex items-center mt-10 -mx-6 sm:-mx-12 py-4 pr-4 pl-1 sm:pr-12 sm:pl-7 border-t bg-gray-50 dark:bg-transparent"
                        >
                            <!-- Cancel -->
                            <button
                                class="ml-auto"
                                mat-button
                                [matTooltip]="'Cancel'"
                                (click)="cancelUpdateDetails()"
                            >
                                Cancel
                            </button>
                            <!-- Save -->
                            <button
                                class="ml-2"
                                mat-flat-button
                                [color]="'primary'"
                                [disabled]="contactForm.invalid || isSaving"
                                [matTooltip]="'Save'"
                                (click)="updateContact()"
                            >
                                @if (isSaving) {
                                    <div class="flex items-center">
                                        <mat-spinner
                                            diameter="20"
                                        ></mat-spinner>
                                        <span class="ml-2"> Saving...</span>
                                    </div>
                                } @else {
                                    Save
                                }
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </ng-container>
    </div>
</mat-dialog-content>

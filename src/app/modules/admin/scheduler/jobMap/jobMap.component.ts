import {
    <PERSON>mpo<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    OnInit,
    ViewEncapsulation,
    ViewChild,
    AfterViewInit,
    ChangeDetectorRef,
    ChangeDetectionStrategy,
    Input,
    SimpleChanges,
    OnChanges,
    ViewChildren,
    QueryList,
} from '@angular/core';
import { GoogleMap, MapInfoWindow, MapMarker } from '@angular/google-maps';
import { FuseScrollbarDirective } from '@fuse/directives/scrollbar';
import { ScheduledJob } from 'app/core/databaseModels/scheduledJobs/scheduledJob.types';
import { BehaviorSubject, combineLatest, Subject, takeUntil } from 'rxjs';
import { PendingJobListComponent } from '../../livemap/pendingJobList/pendingJobList.component';
import { FormsModule } from '@angular/forms';
import { DatePipe } from '@angular/common';
import { DomSanitizer } from '@angular/platform-browser';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CustomerService } from 'app/core/databaseModels/customers/customers.service';
import { Customer } from 'app/core/databaseModels/customers/contacts.types';
import { Router } from '@angular/router';

// Add Marker interface
interface Marker {
    position: google.maps.LatLngLiteral;
    title: string;
    options: google.maps.MarkerOptions;
    teamMemberId: string;
    infoContent?: string;
    job?: ScheduledJob;  // Add job reference to marker
    customer?: { name: string; phone: string }; // Store customer info
}

@Component({
    selector: 'job-map',
    templateUrl: './jobMap.component.html',
    styleUrls: ['./jobMap.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    exportAs: 'jobMap',
    imports: [
        FormsModule,
        GoogleMap,
        MapMarker,
        MatButtonModule,
        MapInfoWindow,
        FuseScrollbarDirective,
        PendingJobListComponent,
        MatTooltipModule,
    ],
    providers: [DatePipe]
})
export class JobMapComponent implements OnInit, OnDestroy, OnChanges, AfterViewInit {
    @Input() scheduledJobs: ScheduledJob[] = [];
    private _unsubscribeAll: Subject<any> = new Subject();
    private customersLoaded$ = new BehaviorSubject<boolean>(false);
    customers: Customer[] = [];
    selectedMarker: Marker = null;
    @ViewChild(GoogleMap, { static: false }) set imap(m: GoogleMap) {
        if (m) {
            Promise.resolve().then(() => {
                this.map = m; // Store reference to map
                if (this.map && this.shouldFitMapToMarkers) {
                    setTimeout(() => {
                        this.fitScheduledJobMapToMarkers(this.map); // Fit map to markers conditionally
                    }, 1000);
                }
            });
        }
    }

    @ViewChild(MapInfoWindow) infoWindow: MapInfoWindow;
    @ViewChildren(MapMarker) mapMarkers: QueryList<MapMarker>; // Access MapMarker instances

    map: GoogleMap | null = null; // Reference to the map
    scheduledMarkers: Marker[] = [];
    shouldFitMapToMarkers = true; // Flag to control fitting map to markers
    infoWindowContent;
    display: any;
    center: google.maps.LatLngLiteral = {
        lat: -24,
        lng: 134,
    };
    zoom = 4;
    mapId = '9852b6410bcc430e';
    options: google.maps.MapOptions = {
        disableDefaultUI: true,
        fullscreenControl: false,
        mapId: this.mapId,
        gestureHandling: 'greedy',
        center: this.center,
        zoom: this.zoom,
        controlSize: 24,
        heading: 0,
        tilt: 65,
        mapTypeControlOptions: {
            position: google.maps.ControlPosition.RIGHT_BOTTOM,
        },
        zoomControlOptions: {
            position: google.maps.ControlPosition.RIGHT_BOTTOM,
        },
    };

    filteredScheduledJobs: ScheduledJob[] = [];
    searchText: string = '';

    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _customerService: CustomerService,
        private _router: Router,
        private datePipe: DatePipe,
        private sanitizer: DomSanitizer
    ) { }
    ngAfterViewInit(): void {

    }




    ngOnInit(): void {
        // Load Customers first (using cache if available)
        this._customerService.customers$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe(customers => {
                if (customers) {
                    this.customers = customers;
                    this.customersLoaded$.next(true);
                    this._changeDetectorRef.markForCheck();

                    // Log loading progress
                    if (this._customerService.isLoadingAllCustomers()) {
                        console.log(`Map - Loading customers: ${customers.length}/${this._customerService.totalCustomers}`);
                    } else if (this._customerService.isAllCustomersLoaded()) {
                        console.log(`Map - All customers loaded: ${customers.length} total`);
                    }
                }
            });

        // Trigger customer loading (will use cache if available)
        // This will automatically start loading all customers in the background
        this._customerService.getCustomers(1, 1000).subscribe();

        // Combine customers loaded and scheduled jobs changes
        combineLatest([
            this.customersLoaded$
        ]).pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe(([customersLoaded]) => {
            if (customersLoaded) {
                this.filteredScheduledJobs = this.scheduledJobs;
                this.updateScheduledJobs();
                this._changeDetectorRef.detectChanges();
            }
        });

        // Add event listener for custom event
        document.addEventListener('openJobDetails', (event: any) => {
            const jobId = event.detail;
            this.openJobDetails(jobId);
        });
    }

    ngOnDestroy(): void {
        //  this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['scheduledJobs']) {
            // Handle the update
            this.filterScheduledJobs();
            this._changeDetectorRef.detectChanges();
        }
    }

    moveMap(event: google.maps.MapMouseEvent) {
        if (event.latLng != null) this.center = event.latLng.toJSON();
    }

    move(event: google.maps.MapMouseEvent) {
        if (event.latLng != null) this.display = event.latLng.toJSON();
    }



    filterScheduledJobs(): void {
        if (!this.searchText) {
            this.filteredScheduledJobs = this.scheduledJobs;
        } else {
            this.filteredScheduledJobs = this.scheduledJobs.filter(member =>
                member.jobTitle.toLowerCase().includes(this.searchText.toLowerCase())
            );
        }
        this.updateScheduledJobs();
    }

    resetSearch(): void {
        this.searchText = '';
        this.filteredScheduledJobs = this.scheduledJobs;
        this.updateScheduledJobs();
    }
    updateScheduledJobs(): void {
        if (!this.customers.length) {
            return;
        }

        const markerPromises = this.filteredScheduledJobs.map(job => {
            const customer = this.getCustomerName(job.customerId);
            return this.createCustomScheduledJobMarkerIcon(job).then(iconUrl => {
                const latLon = job.latLon.split(',');
                return {
                    position: { lat: parseFloat(latLon[0]), lng: parseFloat(latLon[1]) },
                    title: job.jobTitle,
                    options: { icon: iconUrl },
                    teamMemberId: job.$id,
                    job: job,
                    customer: customer
                };
            });
        });

        Promise.all(markerPromises).then(resolvedMarkers => {
            this.scheduledMarkers = resolvedMarkers;
            this.fitScheduledJobMapToMarkers(this.map);
            this._changeDetectorRef.detectChanges();
        }).catch(error => {
            console.error("Error creating markers:", error);
        });
    }

    openInfo(marker: MapMarker, markerData: Marker): void {
        this.selectedMarker = markerData;
        const infoContent = this.getInfoWindowContentScheduledJob(markerData.job, markerData.customer);
        this.infoWindowContent = this.sanitizer.bypassSecurityTrustHtml(infoContent);
        this.infoWindow.open(marker);
        this._changeDetectorRef.detectChanges();
    }

    zoomToJob(jobId: string) {
        const index = this.filteredScheduledJobs.findIndex(job => job.$id === jobId);
        if (index !== -1) {
            const markerData = this.scheduledMarkers[index];
            if (markerData && markerData.job) {
                if (!markerData.job.latLon) {
                    return;
                }
                const location = markerData.job.latLon.split(',');
                this.center = { lat: parseFloat(location[0]), lng: parseFloat(location[1]) };
                this.map.googleMap.moveCamera({
                    center: this.center,
                    zoom: 18,
                    tilt: 65,
                });
                this.zoom = 18;

                // Get the corresponding MapMarker
                const markerRef = this.mapMarkers.toArray()[index];
                if (markerRef) {
                    setTimeout(() => {
                        this.openInfo(markerRef, markerData);
                    }, 500);
                }
            }
        }
    }

    getInfoWindowContentScheduledJob(job: ScheduledJob, customer: { name: string; phone: string }): string {
        if (!job || !customer) return '';

        return `
        <div class="p-4 max-w-sm rounded-lg overflow-hidden shadow-lg bg-white">
            <div class="flex items-center justify-between mb-2">
                <div class="font-bold text-xl">${job.jobTitle || ''}</div>
                <div class="px-2 py-1 rounded-full text-xs font-semibold text-white capitalize" 
                     style="background-color: ${this.getStatusColor(job.jobStatus)}">
                    ${job.jobStatus || 'Unknown'}
                </div>
            </div>
            
            <div class="space-y-2 text-gray-700">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-calendar-alt w-5 text-gray-500"></i>
                    <span>${this.formatDate(job.dueDate)}</span>
                </div>
                
                <div class="flex items-center space-x-2">
                    <i class="fas fa-map-marker-alt w-5 text-gray-500"></i>
                    <span>${job.jobAddress || 'No address'}</span>
                </div>
                
                <div class="flex items-center space-x-2">
                    <i class="fas fa-user w-5 text-gray-500"></i>
                    <span>${customer?.name || 'N/A'}</span>
                </div>
                
                <div class="flex items-center space-x-2">
                    <i class="fas fa-phone w-5 text-gray-500"></i>
                    <span>${customer?.phone || 'N/A'}</span>
                </div>
                
                ${job.adminNote ? `
                <hr class="my-2 border-gray-200">
                <div class="text-sm">
                    <div class="font-medium mb-1">Description:</div>
                    <p class="text-gray-600">${job.adminNote}</p>
                </div>` : ''}
                
                <div class="mt-3 flex justify-end">
                    <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                            onclick="document.dispatchEvent(new CustomEvent('openJobDetails', { detail: '${job.$id}' }))">
                        View Details
                    </button>
                </div>
            </div>
        </div>`;
    }

    createCustomScheduledJobMarkerIcon(job: ScheduledJob): Promise<string> {
        return new Promise((resolve, reject) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const totalRadius = 18; // Total radius including padding
            const radius = 15; // Radius of the ring
            canvas.width = totalRadius * 2;
            canvas.height = totalRadius * 2;

            // Draw the outer ring with color status and white background
            ctx.beginPath();
            ctx.arc(totalRadius, totalRadius, radius, 0, 2 * Math.PI);
            ctx.fillStyle = 'white';
            ctx.fill();
            ctx.lineWidth = 3;
            ctx.strokeStyle = this.getStatusColor(job.jobStatus);
            ctx.stroke();

            // Draw initials if no image
            ctx.fillStyle = "#000"; // Text color
            ctx.font = "13px Arial";
            ctx.textAlign = "center";
            ctx.textBaseline = "middle";
            const initials = job.jobTitle.split(' ').map(n => n[0]).join('').toUpperCase();
            ctx.fillText(initials, totalRadius, totalRadius);

            resolve(canvas.toDataURL());
        });
    }



    private formatDate(date: Date): string {
        return this.datePipe.transform(date, 'MMM dd, yyyy \'at\' hh:mm a') || '';
    }

    getStatusColor(status: string): string {
        switch (status?.toLowerCase()) {
            case 'added':
                return '#808080'; // Gray
            case 'pending':
                return '#808080'; // Orange
            case 'approved':
                return '#0000ff'; // Blue
            case 'started':
                return '#00ff00'; // Amber
            case 'onmyway':
                return '#fde047'; // Yellow
            case 'finished':
                return '#00ff00'; // Green
            case 'completed':
                return '#4CAF50'; // Green
            case 'cancelled':
                return '#F44336'; // Red
            default:
                return '#808080'; // Gray
        }
    }

    fitScheduledJobMapToMarkers(map: GoogleMap): void {
        const bounds = new google.maps.LatLngBounds();
        this.scheduledMarkers.forEach(marker => {
            if (marker.position.lat && marker.position.lng) {
                bounds.extend(marker.position);
            }
        });

        if (!bounds.isEmpty()) {
            setTimeout(() => {
                map.fitBounds(bounds, { top: 50, right: 50, bottom: 50, left: 50 });
            }, 1000);
        }
    }

    refreshZoom() {
        this.fitScheduledJobMapToMarkers(this.map);
    }

    openJobDetails(jobId: string): void {
        // Implement your logic to open job details
        //console.log('Open job details for:', jobId);
        this._router.navigate(['/scheduler'], { queryParams: { scheduleID: jobId } });
    }

    trackByTeamMemberId(index: number, marker: Marker): string {
        return marker.teamMemberId;
    }

    getCustomerName(customerId: string): { name: string; phone: string } {
        const customer = this.customers.find(c => c.$id === customerId);
        let phone = null;

        if (customer && customer.phoneNumbers && customer.phoneNumbers.length > 0) {
            try {
                const phoneObj = typeof customer.phoneNumbers[0] === 'string'
                    ? JSON.parse(customer.phoneNumbers[0])
                    : customer.phoneNumbers[0];
                phone = phoneObj.phoneNumber || null;
            } catch (error) {
                console.error('Error parsing phone number JSON:', error);
            }
        }

        return {
            name: customer?.name || 'Loading...',
            phone: phone || 'Loading...',
        };
    }


}

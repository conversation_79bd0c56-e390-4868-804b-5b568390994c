<div class="flex flex-auto w-full h-full relative bottom-2">
    <!-- Grid Container -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 w-full h-full mb-5">
        <!-- Left Column -->
        <div
            class="bg-white h-full overflow-hidden dark:bg-gray-800 p-6 rounded-lg shadow-lg lg:col-span-1 flex flex-col"
        >
            <!-- Search Input -->
            <div class="relative mb-2 mt-2">
                <input
                    type="text"
                    id="search"
                    name="search"
                    placeholder="Search..."
                    class="w-full pl-10 pr-10 py-2 border-2 border-gray-300 rounded-xl focus:outline-none focus:border-blue-500 dark:bg-gray-700 dark:text-white dark:border-gray-600"
                    [(ngModel)]="searchText"
                    (keyup)="filterScheduledJobs()"
                />
                <button
                    class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                    (click)="resetSearch()"
                >
                    <i
                        class="fas fa-times text-gray-500 dark:text-gray-400"
                    ></i>
                </button>
                <div
                    class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                >
                    <i
                        class="fas fa-search text-gray-500 dark:text-gray-400"
                    ></i>
                </div>
            </div>

            <!-- Scrollable Area -->
            <div
                class="flex-grow overflow-y-auto"
                fuseScrollbar
                [fuseScrollbarOptions]="{ wheelPropagation: true }"
            >
                <div class="snap-y">
                    @for (
                        scheduledJobs of filteredScheduledJobs;
                        track scheduledJobs.$id
                    ) {
                        <p>
                            <pendingJob-list
                                class="snap-start cursor-pointer"
                                [pendingJob]="scheduledJobs"
                                (zoomToJob)="zoomToJob($event)"
                            />
                        </p>
                    } @empty {
                        <div class="flex flex-col flex-1 gap-5 sm:p-2">
                            <!-- Loading skeleton content -->
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Right Column (Google Map) -->
        <div
            class="lg:col-span-3 h-full flex flex-grow rounded-2xl overflow-hidden"
        >
            <google-map
                class="flex flex-grow w-full h-full"
                [width]="'100%'"
                [height]="'vh85'"
                [center]="center"
                [zoom]="zoom"
                [options]="options"
                (mapClick)="moveMap($event)"
                (mapMousemove)="move($event)"
            >
                @for (marker of scheduledMarkers; track marker.teamMemberId) {
                    <map-marker
                        [position]="marker.position"
                        [title]="marker.title"
                        [options]="marker.options"
                        (mapClick)="
                            marker && marker.job
                                ? openInfo(markerRef, marker)
                                : null
                        "
                        #markerRef="mapMarker"
                    >
                    </map-marker>
                }
                <map-info-window #infoWindow>
                    <div [innerHTML]="infoWindowContent"></div>
                </map-info-window>
            </google-map>
            <div class="absolute right-10 top-10">
                <button
                    class="bg-primary-700 text-primary-100"
                    (click)="refreshZoom()"
                    mat-mini-fab
                    extended
                    aria-label="Stop Tracking"
                >
                    <i
                        class="fa-duotone fa-solid fa-magnifying-glass-location"
                    ></i>
                </button>
            </div>
        </div>
    </div>
</div>

import { TextFieldModule } from '@angular/cdk/text-field';
import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, Renderer2, TemplateRef, ViewChild, ViewContainerRef, ViewEncapsulation, importProvidersFrom } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormArray, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule, MatRippleModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FuseLoadingService } from '@fuse/services/loading';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { Subject } from 'rxjs';

///
import { VehicleService } from 'app/core/databaseModels/vehicle/vehicle.service';
import { Vehicle } from 'app/core/databaseModels/vehicle/vehicle.type';
///

@Component({
    selector: 'vehicle-details',
    templateUrl: './vehicle-details.component.html',
    styleUrls: ['./vehicle-details.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    exportAs: 'member-details',
    imports: [MatButtonModule,
        MatTooltipModule,
        MatIconModule,
        FormsModule,
        ReactiveFormsModule,
        MatRippleModule,
        MatFormFieldModule,
        MatInputModule,
        MatCheckboxModule,
        MatSelectModule,
        MatOptionModule,
        MatDatepickerModule,
        TextFieldModule,
        DatePipe,
        NgxMaskDirective],
    providers: [provideNgxMask()]
})
export class VehicleDetailsComponent implements OnInit, OnDestroy {
    @Input({ required: true }) vehicle: Vehicle | null = null;
    @Input() newItem: boolean | null = false;
    @Output() closeDrawer = new EventEmitter<string>();
    @ViewChild('avatarFileInput') private _avatarFileInput: ElementRef;

    private _unsubscribeAll: Subject<any> = new Subject<any>();

    editMode: boolean = false;
    detailsForm: UntypedFormGroup;

    selectedImage: any;

    constructor(private _formBuilder: UntypedFormBuilder,
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseLoadingService: FuseLoadingService,
        private _vehicleService: VehicleService
    ) {

    }

    ngOnInit(): void {
        console.log(this.vehicle);
        //create the member details form
        this.detailsForm = this._formBuilder.group({
            $id: [this.vehicle?.$id],
            organisationID: [this.vehicle?.organisationID],
            groupIDs: [[]],
            vehicleName: ['', [Validators.required]],
            vehicleNumber: ['', [Validators.required]],
            registrationNumber: ['', [Validators.required]],
            costPerKilometre: [''],
            vin: [''],
            make: [''],
            model: [''],
            year: [''],
            fuelType: [''],
            note: [''],
            gpsTrackerID: [''],
            avatar: [''],
            avatarImageId: [''],
            odometer: [''],
            odometerReading: [''],
            hoursOfEngine: [''],
            hoursOfEngineReading: [''],
            lastUpdate: [''],
            status: [true],
        });
        if (this.newItem) {
            //  console.log(this.vehicle)
            this.toggleEditMode(true);
        } else { this.toggleEditMode(false); }

    }

    toggleEditMode(mode: boolean) {
        this.editMode = mode;
        this.detailsForm.patchValue(this.vehicle);
        // Mark for check
        this._changeDetectorRef.markForCheck();

    }
    ngOnDestroy(): void {
        this.editMode = false;
        this.newItem = false;
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }


    cancelUpdateDetails() {
        this.closeDrawer.emit('cancel');
    }

    showEditMode() {
        this.newItem = false;

        this.toggleEditMode(true);
    }
    updateDetails() {
        const vehicle = this.detailsForm.getRawValue();
        //console.log(vehicle);
        if (vehicle.$id == null && this.newItem == true) {
            this.saveNew(vehicle);
        } else {
            this.saveEdited(vehicle);
        }
    }

    private saveNew(vehicle: Vehicle) {
        this._fuseLoadingService.show();
        if (!this.selectedImage) {
            vehicle.costPerKilometre = vehicle.costPerKilometre.toString();
            this._vehicleService.createVehicle(vehicle).subscribe(result => {

                this._fuseLoadingService.hide();
                // Mark for check
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
                this.closeDrawer.emit('newITEM');
            });
        }
        else {
            this._vehicleService.uploadFile(this.selectedImage).subscribe(
                result => {
                    vehicle.avatar = result.fileUrl;
                    vehicle.avatarImageId = result.fileId;
                    vehicle.costPerKilometre = vehicle.costPerKilometre.toString();
                    this._vehicleService.createVehicle(vehicle).subscribe(result => {

                        this._fuseLoadingService.hide();
                        // Mark for check
                        this._changeDetectorRef.markForCheck();
                        this._changeDetectorRef.detectChanges();
                        this.closeDrawer.emit('newITEM');
                    });

                }, error => {
                    console.error('Error uploading file:', error);
                }
            )
        }
    }


    private saveEdited(vehicle: Vehicle) {
        this._fuseLoadingService.show();
        //console.log(teamMember);
        if (this.selectedImage) {
            //First Upload Image and Get Url then update
            let _oldImageID = vehicle.avatarImageId
            this._vehicleService.uploadFile(this.selectedImage).subscribe(result => {
                vehicle.avatar = result.fileUrl;
                vehicle.avatarImageId = result.fileId;
                vehicle.costPerKilometre = vehicle.costPerKilometre.toString();
                this._vehicleService.updateVehicle(vehicle.$id, vehicle).subscribe(result => {
                    this._fuseLoadingService.hide();
                    // Mark for check
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();
                    //then delete old image after upload new image and save change
                    this._vehicleService.deleteFile(_oldImageID).subscribe(result => {
                        this.closeDrawer.emit('update');
                    });

                });

            });

        } else {

            this._vehicleService.updateVehicle(vehicle.$id, vehicle).subscribe(result => {
                this._fuseLoadingService.hide();
                // Mark for check
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
                this.closeDrawer.emit('update');
            });
        }

    }

    /**
      * Upload avatar
      *
      * @param fileList
      */

    uploadAvatar(fileList: FileList): void {
        // Return if canceled
        if (!fileList.length) {
            return;
        }

        const allowedTypes = ['image/jpeg', 'image/png'];
        const file = fileList[0];

        // Return if the file is not allowed
        if (!allowedTypes.includes(file.type)) {
            return;
        }

        // update the avatar url from file
        this.vehicle.avatar = URL.createObjectURL(file);

        this.selectedImage = file;
    }

    /**
     * Remove the avatar
     */
    removeAvatar(): void {
        // Get the form control for 'avatar'
        const avatarFormControl = this.detailsForm.get('avatar');

        // Set the avatar as null
        avatarFormControl.setValue(null);

        // Set the file input value as null
        this._avatarFileInput.nativeElement.value = null;

        // Update the contact
        this.vehicle.avatar = null;



    }



}

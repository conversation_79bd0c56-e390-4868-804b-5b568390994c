<div class="flex flex-col w-full">
    <!-- View mode -->
    @if (!editMode) {
        <!-- Header -->
        <div
            class="relative w-full h-18 sm:h-20 bg-accent-700 dark:bg-accent-700"
        >
            <!-- Close button -->
            <div
                class="flex items-center justify-end w-full max-w-3xl mx-auto pt-6"
            >
                <button
                    mat-icon-button
                    [matTooltip]="'Close'"
                    (click)="cancelUpdateDetails()"
                >
                    <mat-icon
                        class="text-white"
                        [svgIcon]="'heroicons_outline:x-mark'"
                    ></mat-icon>
                </button>
            </div>
        </div>
        <!-- Items -->
        <div
            class="relative flex flex-col flex-auto items-center p-6 pt-0 sm:p-12 sm:pt-0"
        >
            <div class="w-full max-w-3xl">
                <!-- Avatar and actions -->
                <div class="flex flex-auto items-end -mt-16">
                    <!-- Avatar -->
                    <div
                        class="flex items-center justify-center w-32 h-32 rounded-full overflow-hidden ring-4 ring-bg-card"
                    >
                        @if (vehicle.avatar) {
                            <img
                                class="object-cover w-full h-full"
                                [src]="vehicle.avatar"
                            />
                        }
                        @if (!vehicle.avatar && vehicle.vehicleName) {
                            <div
                                class="flex items-center justify-center w-full h-full rounded overflow-hidden uppercase text-8xl font-bold leading-none bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                            >
                                {{ vehicle.vehicleName.charAt(0) }}
                            </div>
                        }
                    </div>
                    <!-- Actions -->
                    <div class="flex items-center ml-auto mb-1">
                        <button mat-stroked-button (click)="showEditMode()">
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_solid:pencil-square'"
                            ></mat-icon>
                            <span class="ml-2">Edit</span>
                        </button>
                    </div>
                </div>
                <!-- Name -->
                <div class="mt-3 text-4xl font-bold truncate">
                    {{ vehicle.vehicleName }}
                </div>
                <div class="flex flex-col mt-4 pt-6 border-t space-y-8">
                    <!--  vehicle Number -->
                    @if (vehicle.vehicleNumber) {
                        <div class="flex sm:items-center">
                            <i class="icon-size-5 fa-thin fa-hashtag"></i>
                            <label
                                class="capitalize ml-5 font-bold border-l-4 pl-5 border-indigo-500"
                            >
                                Vehicle Number:
                            </label>
                            <div class="ml-6">
                                {{
                                    vehicle.vehicleNumber
                                        ? vehicle.vehicleNumber
                                        : "N/A"
                                }}
                            </div>
                        </div>
                    }
                    <!--  groupIDs -->
                    @if (vehicle.groupIDs) {
                        <div class="flex sm:items-center">
                            <i class="icon-size-5 fa-thin fa-list-tree"></i>
                            <label
                                class="capitalize ml-5 font-bold border-l-4 pl-5 border-indigo-500"
                            >
                                Group Selection:
                            </label>
                            <div class="ml-6">
                                @for (group of vehicle.groupIDs; track group) {
                                    <div
                                        class="flex items-center justify-center py-1 px-3 mr-3 mb-3 rounded-full leading-normal text-gray-500 bg-gray-100 dark:text-gray-300 dark:bg-gray-700"
                                    >
                                        <span
                                            class="text-sm font-medium whitespace-nowrap"
                                            >{{ group.groupName }}</span
                                        >
                                    </div>
                                }
                            </div>
                        </div>
                    }
                    <!--  registration Number -->
                    @if (vehicle.registrationNumber) {
                        <div class="flex sm:items-center">
                            <i class="icon-size-5 fa-thin fa-input-numeric"></i>
                            <label
                                class="capitalize ml-5 font-bold border-l-4 pl-5 border-indigo-500"
                            >
                                registration Number:
                            </label>
                            <div class="ml-6">
                                {{
                                    vehicle.registrationNumber
                                        ? vehicle.registrationNumber
                                        : "N/A"
                                }}
                            </div>
                        </div>
                    }
                    <!-- costPer Kilometre -->
                    @if (vehicle.costPerKilometre) {
                        <div class="flex sm:items-center">
                            <i
                                class="icon-size-5 fa-light fa-circle-dollar-to-slot"
                            ></i>
                            <label
                                class="capitalize ml-5 font-bold border-l-4 pl-5 border-indigo-500"
                            >
                                cost Per Kilometre: $
                            </label>
                            <div class="ml-6 leading-6">
                                {{ vehicle.costPerKilometre }}
                            </div>
                        </div>
                    }
                    <!-- vin -->
                    @if (vehicle.vin) {
                        <div class="flex sm:items-center">
                            <i class="icon-size-5 fa-thin fa-barcode-scan"></i>
                            <label
                                class="capitalize ml-5 font-bold border-l-4 pl-5 border-indigo-500"
                            >
                                VIN Number:
                            </label>
                            <div class="ml-6 leading-6">
                                {{ vehicle.vin }}
                            </div>
                        </div>
                    }
                    <!-- make -->
                    @if (vehicle.make) {
                        <div class="flex sm:items-center">
                            <i
                                class="icon-size-5 fa-sharp fa-light fa-industry-windows"
                            ></i>
                            <label
                                class="capitalize ml-5 font-bold border-l-4 pl-5 border-indigo-500"
                            >
                                vehicle Make:
                            </label>
                            <div class="ml-6 leading-6">
                                {{ vehicle.make }}
                            </div>
                        </div>
                    }
                    <!-- model -->
                    @if (vehicle.model) {
                        <div class="flex sm:items-center">
                            <i
                                class="icon-size-5 fa-brands fa-buromobelexperte"
                            ></i>
                            <label
                                class="capitalize ml-5 font-bold border-l-4 pl-5 border-indigo-500"
                            >
                                vehicle model:
                            </label>
                            <div class="ml-6 leading-6">
                                {{ vehicle.model }}
                            </div>
                        </div>
                    }
                    <!-- year -->
                    @if (vehicle.year) {
                        <div class="flex sm:items-center">
                            <i
                                class="hidden sm:flex icon-size-5 fa-thin fa-calendar-days"
                            ></i>
                            <label
                                class="capitalize ml-5 font-bold border-l-4 pl-5 border-indigo-500"
                            >
                                vehicle year:
                            </label>
                            <div class="ml-6 leading-6">
                                {{ vehicle.year | date: "dd mm, yyyy" }}
                            </div>
                        </div>
                    }
                    <!-- fuelType -->
                    @if (vehicle.fuelType) {
                        <div class="flex sm:items-center">
                            <i class="icon-size-5 fa-thin fa-gas-pump"></i>
                            <label
                                class="capitalize ml-5 font-bold border-l-4 pl-5 border-indigo-500"
                            >
                                vehicle type of fuel:
                            </label>
                            <div class="ml-6 leading-6">
                                {{ vehicle.fuelType }}
                            </div>
                        </div>
                    }
                    <!-- odometer -->
                    @if (vehicle.odometer) {
                        <div class="flex sm:items-center">
                            <i class="icon-size-5 fa-thin fa-gauge-min"></i>
                            <label
                                class="capitalize ml-5 font-bold border-l-4 pl-5 border-indigo-500"
                            >
                                Vehicle odometer:
                            </label>
                            <div class="ml-6 leading-6">
                                {{ vehicle.odometer }}
                            </div>
                        </div>
                    }
                    <!--  odometerReading -->
                    @if (vehicle.odometerReading) {
                        <div class="flex sm:items-center">
                            <i
                                class="icon-size-5 fa-duotone fa-solid fa-gauge-min"
                            ></i>
                            <label
                                class="capitalize ml-5 font-bold border-l-4 pl-5 border-indigo-500"
                            >
                                odometer Reading date:
                            </label>
                            <div class="ml-6 leading-6">
                                {{
                                    vehicle.odometerReading
                                        | date: "dd MMM, yyyy 'at' hh:mm a"
                                }}
                            </div>
                        </div>
                    }
                    <!-- hoursOfEngine -->
                    @if (vehicle.hoursOfEngine) {
                        <div class="flex sm:items-center">
                            <i
                                class="hidden sm:flex icon-size-5 fa-thin fa-engine"
                            ></i>
                            <label
                                class="capitalize ml-5 font-bold border-l-4 pl-5 border-indigo-500"
                            >
                                vehicle hours Of Engine:
                            </label>
                            <div class="ml-6 leading-6">
                                {{ vehicle.hoursOfEngine }}
                            </div>
                        </div>
                    }
                    <!--  hoursOfEngineReading -->
                    @if (vehicle.hoursOfEngineReading) {
                        <div class="flex sm:items-center">
                            <i
                                class="hidden sm:flex icon-size-5 fa-thin fa-engine"
                            ></i>
                            <label
                                class="capitalize ml-5 font-bold border-l-4 pl-5 border-indigo-500"
                            >
                                hours Of Engine Reading date:
                            </label>
                            <div class="ml-6 leading-6">
                                {{
                                    vehicle.hoursOfEngineReading
                                        | date: "dd MMM, yyyy 'at' hh:mm a"
                                }}
                            </div>
                        </div>
                    }
                    <!-- gpsTrackerID -->
                    @if (vehicle.gpsTrackerID) {
                        <div class="flex sm:items-center">
                            <i class="icon-size-5 fa-thin fa-satellite"></i>
                            <label
                                class="ml-5 font-bold border-l-4 pl-5 border-indigo-500"
                            >
                                GPS Tracker IMEI ID:
                            </label>
                            <div class="ml-6 leading-6">
                                {{ vehicle.gpsTrackerID }}
                            </div>
                        </div>
                    }
                    <!-- Notes -->
                    @if (vehicle.note) {
                        <div class="flex">
                            <mat-icon
                                [svgIcon]="
                                    'heroicons_outline:bars-3-bottom-left'
                                "
                            ></mat-icon>
                            <div
                                class="max-w-none ml-6 prose prose-sm"
                                [innerHTML]="vehicle.note"
                            ></div>
                        </div>
                    }
                    <!--  lastUpdate -->
                    @if (vehicle.lastUpdate) {
                        <div class="flex sm:items-center">
                            <i
                                class="hidden sm:flex icon-size-5 fa-thin fa-calendar-days"
                            ></i>
                            <label
                                class="capitalize ml-5 font-bold border-l-4 pl-5 border-indigo-500"
                            >
                                last Update:
                            </label>
                            <div class="ml-6 leading-6">
                                {{
                                    vehicle.lastUpdate
                                        | date: "dd MMM, yyyy 'at' hh:mm a"
                                }}
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    }
    <!-- Edit mode -->
    @if (editMode) {
        <!-- Header -->
        <div
            class="relative w-full h-18 sm:h-20 bg-accent-700 dark:bg-accent-700"
        >
            <div
                class="flex items-center justify-end w-full max-w-3xl mx-auto pt-6"
            >
                <button
                    mat-icon-button
                    [matTooltip]="'Close'"
                    (click)="cancelUpdateDetails()"
                >
                    <mat-icon
                        class="text-white"
                        [svgIcon]="'heroicons_outline:x-mark'"
                    ></mat-icon>
                </button>
            </div>
        </div>
        <!-- removeAvatar form -->
        <div
            class="relative flex flex-col flex-auto items-center px-6 sm:px-12"
        >
            <div class="w-full max-w-3xl">
                <form [formGroup]="detailsForm">
                    <!-- Avatar -->
                    <div class="flex flex-auto items-end -mt-16">
                        <div
                            class="relative flex items-center justify-center w-32 h-32 rounded-full overflow-hidden ring-4 ring-bg-card"
                        >
                            <!-- Upload / Remove avatar -->
                            <div
                                class="absolute inset-0 bg-black bg-opacity-50 z-10"
                            ></div>
                            <div
                                class="absolute inset-0 flex items-center justify-center z-20"
                            >
                                <div>
                                    <input
                                        id="avatar-file-input"
                                        class="absolute h-0 w-0 opacity-0 invisible pointer-events-none"
                                        type="file"
                                        [multiple]="false"
                                        [accept]="'image/jpeg, image/png'"
                                        (change)="
                                            uploadAvatar(avatarFileInput.files)
                                        "
                                        #avatarFileInput
                                    />
                                    <label
                                        class="flex items-center justify-center w-10 h-10 rounded-full cursor-pointer hover:bg-hover"
                                        for="avatar-file-input"
                                        matRipple
                                    >
                                        <mat-icon
                                            class="text-white"
                                            [svgIcon]="
                                                'heroicons_outline:camera'
                                            "
                                        ></mat-icon>
                                    </label>
                                </div>
                                <div>
                                    <button
                                        mat-icon-button
                                        (click)="removeAvatar()"
                                    >
                                        <mat-icon
                                            class="text-white"
                                            [svgIcon]="
                                                'heroicons_outline:trash'
                                            "
                                        ></mat-icon>
                                    </button>
                                </div>
                            </div>
                            <!-- Image/Letter -->
                            @if (vehicle.avatar) {
                                <img
                                    class="object-cover w-full h-full"
                                    [src]="vehicle.avatar"
                                />
                            }
                            @if (!vehicle.avatar && vehicle.vehicleName) {
                                <div
                                    class="flex items-center justify-center w-full h-full rounded overflow-hidden uppercase text-8xl font-bold leading-none bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                >
                                    {{ vehicle.vehicleName.charAt(0) }}
                                </div>
                            }
                        </div>
                    </div>
                    <!-- vehicleName -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Vehicle Name</mat-label>
                            <i
                                matPrefix
                                class="hidden mr-5 sm:flex icon-size-5 fa-sharp fa-regular fa-car"
                            ></i>
                            <input
                                matInput
                                [formControlName]="'vehicleName'"
                                [placeholder]="'Vehicle Name'"
                                [spellcheck]="false"
                            />
                        </mat-form-field>
                    </div>
                    <!-- Vehicle Number -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Vehicle Number</mat-label>
                            <i
                                matPrefix
                                class="hidden mr-5 sm:flex icon-size-5 fa-thin fa-hashtag"
                            ></i>
                            <input
                                matInput
                                [formControlName]="'vehicleNumber'"
                                [placeholder]="'Vehicle Number'"
                            />
                        </mat-form-field>
                    </div>
                    <!-- Registration Number -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Registration Number</mat-label>
                            <i
                                matPrefix
                                class="hidden mr-5 sm:flex icon-size-5 fa-thin fa-input-numeric"
                            ></i>
                            <input
                                matInput
                                [formControlName]="'registrationNumber'"
                                [placeholder]="'Registration Number'"
                            />
                        </mat-form-field>
                    </div>
                    <!-- CostPer Kilometre -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Hourly Rate</mat-label>
                            <mat-icon
                                matPrefix
                                class="hidden sm:flex icon-size-5"
                                [svgIcon]="'heroicons_outline:currency-dollar'"
                            ></mat-icon>
                            <input
                                prefix="$ "
                                mask="0*.00"
                                [allowNegativeNumbers]="false"
                                class="text-right"
                                matInput
                                [formControlName]="'costPerKilometre'"
                                [placeholder]="'Cost Per Kilometre'"
                            />
                        </mat-form-field>
                    </div>
                    <!-- assigned Vehicle Start Date -->
                    <!-- <div class="mt-8">
            <mat-form-field
              class="w-full"
              [subscriptSizing]="'dynamic'"
              >
              <mat-label>Assigned Vehicle</mat-label>
              <mat-icon
                matPrefix
                class="hidden sm:flex icon-size-5"
                [svgIcon]="'mat_outline:emoji_transportation'"
              ></mat-icon>
              <input
                matInput
                [ngxMatDatetimePicker]="picker"
                [placeholder]="'Assigned Vehicle Start Date'"
                [formControlName]="'assignedVehicleStartDate'"
                />
              <ngx-mat-datepicker-toggle
                matSuffix
                [for]="picker"
              ></ngx-mat-datepicker-toggle>
              <ngx-mat-datetime-picker #picker>
              </ngx-mat-datetime-picker>
            </mat-form-field>
          </div> -->
                    <!-- Vin -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>VIN Number</mat-label>
                            <i
                                matPrefix
                                class="hidden sm:flex icon-size-5 mr-5 fa-thin fa-barcode-scan"
                            ></i>
                            <input
                                matInput
                                [formControlName]="'vin'"
                                [placeholder]="'VIN Number'"
                            />
                        </mat-form-field>
                    </div>
                    <!-- make -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Vehicle Make</mat-label>
                            <i
                                matPrefix
                                class="mr-5 hidden sm:flex icon-size-5 fa-sharp fa-light fa-industry-windows"
                            ></i>
                            <input
                                matInput
                                [formControlName]="'make'"
                                [placeholder]="'Vehicle Make'"
                            />
                        </mat-form-field>
                    </div>
                    <!-- model -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Vehicle Model</mat-label>
                            <i
                                matPrefix
                                class="mr-5 hidden sm:flex icon-size-5 fa-brands fa-buromobelexperte"
                            ></i>
                            <input
                                matInput
                                [formControlName]="'model'"
                                [placeholder]="'Vehicle Model'"
                            />
                        </mat-form-field>
                    </div>
                    <!-- year -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Vehicle Make Year</mat-label>
                            <i
                                matPrefix
                                class="hidden sm:flex icon-size-5 mr-5 fa-thin fa-calendar-days"
                            ></i>
                            <input
                                matInput
                                [matDatepicker]="yearPiker"
                                [placeholder]="'Vehicle Make Year'"
                                [formControlName]="'year'"
                            />
                            <mat-datepicker-toggle matSuffix [for]="yearPiker">
                            </mat-datepicker-toggle>
                            <mat-datepicker #yearPiker></mat-datepicker>
                        </mat-form-field>
                    </div>
                    <!-- fuelType -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Vehicle fuel Type</mat-label>
                            <i
                                matPrefix
                                class="hidden sm:flex icon-size-5 mr-5 fa-thin fa-gas-pump"
                            ></i>
                            <mat-select
                                [placeholder]="'Vehicle fuel type'"
                                [formControlName]="'fuelType'"
                            >
                                <mat-option [value]=""></mat-option>
                                <mat-option [value]="'Unleaded'"
                                    >Unleaded</mat-option
                                >
                                <mat-option [value]="'Diesel'"
                                    >Diesel</mat-option
                                >
                                <mat-option [value]="'Electric'"
                                    >Electric</mat-option
                                >
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <!-- odometer -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Vehicle Odometer</mat-label>
                            <i
                                matPrefix
                                class="mr-5 hidden sm:flex icon-size-5 fa-solid fa-gauge-min"
                            ></i>
                            <input
                                matInput
                                [formControlName]="'odometer'"
                                [placeholder]="'Vehicle odometer'"
                            />
                        </mat-form-field>
                    </div>
                    <!-- odometerReading -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Odometer Reading</mat-label>
                            <i
                                matPrefix
                                class="mr-5 hidden sm:flex icon-size-5 fa-solid fa-gauge-min"
                            ></i>
                            <input
                                matInput
                                [matDatepicker]="odometerpicker"
                                [placeholder]="'Odometer Reading Date'"
                                [formControlName]="'odometerReading'"
                            />
                            <mat-datepicker-toggle
                                matSuffix
                                [for]="odometerpicker"
                            >
                            </mat-datepicker-toggle>
                            <mat-datepicker #odometerpicker></mat-datepicker>
                        </mat-form-field>
                    </div>
                    <!-- hoursOfEngine -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Vehicle Hours Of Engine</mat-label>
                            <i
                                matPrefix
                                class="mr-5 hidden sm:flex icon-size-5 fa-thin fa-engine"
                            ></i>
                            <input
                                matInput
                                [formControlName]="'hoursOfEngine'"
                                [placeholder]="'Vehicle hours of engine'"
                            />
                        </mat-form-field>
                    </div>
                    <!-- hoursOfEngineReading -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Hours Of Engine Reading</mat-label>
                            <i
                                matPrefix
                                class="mr-5 hidden sm:flex icon-size-5 fa-thin fa-engine"
                            ></i>
                            <input
                                matInput
                                [matDatepicker]="hoursOfEngine"
                                [placeholder]="'Hours Of Engine Reading Date'"
                                [formControlName]="'hoursOfEngineReading'"
                            />
                            <mat-datepicker-toggle
                                matSuffix
                                [for]="hoursOfEngine"
                            >
                            </mat-datepicker-toggle>
                            <mat-datepicker #hoursOfEngine></mat-datepicker>
                        </mat-form-field>
                    </div>
                    <!-- Notes -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Notes</mat-label>
                            <mat-icon
                                matPrefix
                                class="hidden sm:flex icon-size-5"
                                [svgIcon]="'heroicons_solid:bars-3-bottom-left'"
                            ></mat-icon>
                            <textarea
                                matInput
                                [formControlName]="'note'"
                                [placeholder]="'Notes'"
                                [rows]="5"
                                [spellcheck]="false"
                                cdkTextareaAutosize
                            ></textarea>
                        </mat-form-field>
                    </div>
                    <!-- gpsTrackerID -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>GPS TRACKER Hardware IMEI</mat-label>
                            <i
                                matPrefix
                                class="mr-5 hidden sm:flex icon-size-5 fa-thin fa-satellite"
                            ></i>
                            <input
                                matInput
                                [formControlName]="'gpsTrackerID'"
                                [placeholder]="'GPS DEVICE IMEI'"
                            />
                        </mat-form-field>
                    </div>
                    <!-- lastUpdate -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Last Update</mat-label>
                            <i
                                matPrefix
                                class="hidden sm:flex icon-size-5 mr-5 fa-thin fa-calendar-days"
                            ></i>
                            <input
                                matInput
                                [matDatepicker]="lastUpdatepicker"
                                [placeholder]="'Last Update Date'"
                                [formControlName]="'lastUpdate'"
                            />
                            <mat-datepicker-toggle
                                matSuffix
                                [for]="lastUpdatepicker"
                            >
                            </mat-datepicker-toggle>
                            <mat-datepicker #lastUpdatepicker></mat-datepicker>
                        </mat-form-field>
                    </div>
                    <!-- Actions -->
                    <div
                        class="flex items-center mt-10 -mx-6 sm:-mx-12 py-4 pr-4 pl-1 sm:pr-12 sm:pl-7 border-t bg-gray-50 dark:bg-transparent"
                    >
                        <!-- Cancel -->
                        <button
                            class="ml-auto"
                            mat-button
                            [matTooltip]="'Cancel'"
                            (click)="cancelUpdateDetails()"
                        >
                            Cancel
                        </button>
                        <!-- Save -->
                        <button
                            class="ml-2"
                            mat-flat-button
                            [color]="'primary'"
                            [disabled]="detailsForm.invalid"
                            [matTooltip]="'Save'"
                            (click)="updateDetails()"
                        >
                            Save
                        </button>
                    </div>
                </form>
            </div>
        </div>
    }
</div>

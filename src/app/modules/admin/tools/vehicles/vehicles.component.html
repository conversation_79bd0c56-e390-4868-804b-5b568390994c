<div class="absolute inset-0 top-20 flex flex-col min-w-0 overflow-hidden">
  <mat-drawer-container
    class="flex-auto h-full bg-card dark:bg-transparent"
    (backdropClick)="onBackdropClicked()"
    [hasBackdrop]="'true'"
    >
    <!-- Drawer -->
    <mat-drawer
      class="w-full sm:w-128 dark:bg-gray-900"
      [mode]="drawerMode"
      [opened]="isDrawerOpen"
      (opened)="onDrawerOpened()"
      (closed)="onDrawerClosed()"
      [position]="'end'"
      [disableClose]="true"
      #matDrawer
      >
      <!-- Drawer content -->
      @if (isDrawerOpen) {
        <div>
          <vehicle-details
            [vehicle]="selectedVehicle"
            [newItem]="isNewItem"
            (closeDrawer)="closeDrawer($event)"
            />
        </div>
      }
    </mat-drawer>

    <mat-drawer-content class="flex flex-col">
      <!-- Main -->
      <!-- Header -->
      <div
        class="flex flex-col sm:flex-row items-start sm:items-center sm:justify-between py-8 px-6 md:px-8"
        >
        <!-- Title -->
        <div>
          <div
            class="text-4xl font-extrabold tracking-tight leading-none"
          ></div>
          <div class="ml-0.5 font-medium text-secondary">
            <span>Manage your vehicles from the list below!</span>
          </div>
        </div>
        <!-- Actions -->
        <div class="mt-4 sm:mt-0">
          <!-- Add section button -->
          <button
            mat-flat-button
            [color]="'accent'"
            (click)="addNewItem()"
            >
            <i class="fa-duotone fa-car-bus"></i>
            <span class="ml-2 mr-1">Add Vehicles</span>
          </button>
        </div>
      </div>
      @if (vehicles && vehicles.length > 0) {
        <div class="flex-auto bg-gray-100 p-4">
          <!--Body-->
          <div class="w-full h-full mt-8">
            <!-- Recent transactions table -->
            <div
              class="xl:col-span-2 flex flex-col flex-auto bg-card shadow rounded-2xl overflow-hidden"
              >
              <div
                class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b"
                >
                <!-- Title -->
                <div
                  class="text-lg font-extrabold tracking-tight"
                  >
                  vehicle List
                  <div class="text-secondary font-medium">
                    Active, Passive
                  </div>
                </div>
                <!-- Actions -->
                <div
                  class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4"
                  >
                  <!-- Search -->
                  <mat-form-field
                    class="fuse-mat-dense fuse-mat-rounded min-w-64"
                    [subscriptSizing]="'dynamic'"
                    >
                    <mat-icon
                      class="icon-size-5"
                      matPrefix
                                            [svgIcon]="
                                                'heroicons_solid:magnifying-glass'
                                            "
                    ></mat-icon>
                    <input
                      matInput
                      (keyup)="applyFilter($event)"
                      [autocomplete]="'off'"
                      [placeholder]="'Search ...'"
                      #input
                      />
                  </mat-form-field>
                  <button
                    mat-icon-button
                    [matMenuTriggerFor]="moreMenu"
                    >
                    <i
                      class="ml-3 icon-size-5 fa-duotone fa-file-export"
                    ></i>
                  </button>
                  <mat-menu #moreMenu="matMenu">
                    <button
                      mat-menu-item
                      (click)="exportMaterialTable('pdf')"
                      >
                      <i
                        class="fa-duotone fa-file-pdf"
                      ></i>
                      <span class="ml-3">PDF</span>
                    </button>
                    <button
                      mat-menu-item
                                            (click)="
                                                exportMaterialTable('excel')
                                            "
                      >
                      <i
                        class="fa-duotone fa-file-excel"
                      ></i>
                      <span class="ml-3">EXCEL</span>
                    </button>
                  </mat-menu>
                </div>
              </div>

              <div class="overflow-x-auto mx-6">
                <div class="w-full bg-transparent">
                  <table
                    mat-table
                    [dataSource]="dataSource"
                    matSort
                    >
                    <!-- Avatar Column -->
                    <ng-container matColumnDef="avatar">
                      <th
                        mat-header-cell
                        *matHeaderCellDef
                        >
                        Image
                      </th>
                      <td mat-cell *matCellDef="let row">
                        <div
                          class="flex flex-0 items-center justify-center w-10 h-10 rounded-full overflow-hidden"
                          >
                          @if (row.avatar) {
                            <img
                              class="object-cover w-full h-full"
                              [src]="row.avatar"
                              alt="row avatar"
                              />
                          }
                          @if (!row.avatar) {
                            <div
                              class="flex items-center justify-center w-full h-full rounded-full text-lg uppercase bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                              >
                              {{
                              row.vehicleName.charAt(
                              0
                              )
                              }}
                            </div>
                          }
                        </div>
                      </td>
                    </ng-container>
                    <!-- Vehicle Number Column -->
                    <ng-container
                      matColumnDef="vehicleNumber"
                      >
                      <th
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                        >
                        Vehicle #
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row.vehicleNumber }}
                      </td>
                    </ng-container>

                    <!--Vehicle Name Column -->
                    <ng-container
                      matColumnDef="vehicleName"
                      >
                      <th
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                        >
                        Vehicle Name
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row.vehicleName }}
                      </td>
                    </ng-container>

                    <!-- Registration Number Column -->
                    <ng-container
                      matColumnDef="registrationNumber"
                      >
                      <th
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                        >
                        Registration Number
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row.registrationNumber }}
                      </td>
                    </ng-container>

                    <!-- Group Members Column -->
                    <ng-container
                      matColumnDef="currentDriver"
                      >
                      <th
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                        >
                        Current Driver
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row.currentDriver }}
                      </td>
                    </ng-container>
                    <!-- Group Members Column -->
                    <ng-container
                      matColumnDef="groupMembers"
                      >
                      <th
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                        >
                        Groups
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row.groupMembers }}
                      </td>
                    </ng-container>

                    <!-- Year & Make Members Column -->
                    <ng-container
                      matColumnDef="yearsAndMake"
                      >
                      <th
                        mat-header-cell
                        *matHeaderCellDef
                        mat-sort-header
                        >
                        Year & Make
                      </th>
                      <td mat-cell *matCellDef="let row">
                        {{ row.yearsAndMake }}
                      </td>
                    </ng-container>

                    <!-- Actions Column -->
                    <ng-container matColumnDef="actions">
                      <th
                        mat-header-cell
                        *matHeaderCellDef
                        >
                        Actions
                      </th>
                      <td mat-cell *matCellDef="let row">
                        <button
                          mat-icon-button
                          (click)="viewDetails(row)"
                          >
                          <mat-icon
                                                        [svgIcon]="
                                                            'heroicons_outline:pencil'
                                                        "
                          ></mat-icon>
                        </button>
                        <button
                          mat-icon-button
                                                    [matMenuTriggerFor]="
                                                        moreMenu
                                                    "
                          >
                          <mat-icon
                                                        [svgIcon]="
                                                            'heroicons_outline:ellipsis-vertical'
                                                        "
                          ></mat-icon>
                        </button>
                        <mat-menu #moreMenu="matMenu">
                          <button
                            mat-menu-item
                                                        (click)="
                                                            deleteItem(row)
                                                        "
                            >
                            <mat-icon
                                                            [svgIcon]="
                                                                'heroicons_outline:trash'
                                                            "
                            ></mat-icon>
                            <span>Delete</span>
                          </button>
                        </mat-menu>
                      </td>
                    </ng-container>

                    <tr
                      mat-header-row
                      *matHeaderRowDef="displayedColumns"
                    ></tr>
                    <tr
                      mat-row
                                            *matRowDef="
                                                let row;
                                                columns: displayedColumns
                                            "
                    ></tr>

                    <!-- Row shown when there is no matching data. -->
                    @if (isLoading) {
                      <div
                        class="flex justify-center p-10"
                        >
                        <mat-spinner
                          class="spinnr"
                          [diameter]="24"
                          >
                        </mat-spinner>
                        <div class="ml-5 text-center">
                          Loading...
                        </div>
                      </div>
                    } @else {
                      <tr class="mat-row">
                        <td
                          class="mat-cell"
                          colspan="4"
                          >
                          No data matching the filter
                          "{{ input.value }}"
                        </td>
                      </tr>
                    }
                  </table>

                  <mat-paginator
                    [pageSize]="20"
                    [pageSizeOptions]="[5, 10, 20, 50, 100]"
                    aria-label="Select page of users"
                  ></mat-paginator>
                </div>
              </div>
            </div>
          </div>
        </div>
      } @else {
        <div
          class="flex flex-auto flex-col items-center justify-center bg-gray-100 dark:bg-transparent"
          >
          <mat-icon
            class="icon-size-24"
            [svgIcon]="'mat_outline:directions_car_filled'"
          ></mat-icon>
          <div
            class="mt-4 text-2xl font-semibold tracking-tight text-secondary"
            >
            @if (isLoading) {
              <div class="flex justify-center p-10">
                <mat-spinner class="spinnr" [diameter]="24">
                </mat-spinner>
                <div class="ml-5 text-center">Loading...</div>
              </div>
            } @else {
              Add a Vehicle to start planning!
            }
          </div>
        </div>
      }
    </mat-drawer-content>
  </mat-drawer-container>
</div>

import { TextFieldModule } from '@angular/cdk/text-field';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { MatDrawer, MatSidenavModule } from '@angular/material/sidenav';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { FuseLoadingService } from '@fuse/services/loading';
import { FuseMediaWatcherService } from '@fuse/services/media-watcher';
import { VehicleService } from 'app/core/databaseModels/vehicle/vehicle.service';
import { Vehicle } from 'app/core/databaseModels/vehicle/vehicle.type';
import { MatMenuModule } from '@angular/material/menu';
import { Subject, takeUntil } from 'rxjs';
import { VehicleDetailsComponent } from './vehicle-details/vehicle-details.component';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';




@Component({
    selector: 'tools-vehicles',
    templateUrl: './vehicles.component.html',
    styleUrls: ['./vehicles.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [VehicleDetailsComponent,
        MatPaginatorModule,
        MatSortModule,
        MatMenuModule,
        MatTableModule,
        MatSidenavModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        TextFieldModule,
        MatSelectModule,
        MatOptionModule,
        MatButtonModule,
        MatProgressSpinnerModule]
})

export class VehiclesComponent implements OnInit, OnDestroy, AfterViewInit {
    @ViewChild('matDrawer', { static: true }) matDrawer: MatDrawer;
    @ViewChild(MatPaginator) paginator: MatPaginator;
    @ViewChild(MatSort) sort: MatSort;

    private _unsubscribeAll: Subject<any> = new Subject<any>();
    displayedColumns: string[] = ['avatar', 'vehicleNumber', 'vehicleName', 'registrationNumber', 'currentDriver', 'groupMembers', 'yearsAndMake', 'actions'];
    dataSource: MatTableDataSource<Vehicle>;

    drawerMode: 'side' | 'over';
    isLoading: boolean = false;
    isDrawerOpen: boolean = false;

    vehicles: Vehicle[];
    selectedVehicle: Vehicle;
    isNewItem: boolean = false;


    constructor(
        private _fuseMediaWatcherService: FuseMediaWatcherService,
        private _changeDetectorRef: ChangeDetectorRef,
        private _matDialog: MatDialog,
        private _fuseLoadingService: FuseLoadingService,
        private _fuseConfirmationService: FuseConfirmationService,
        private _vehicleService: VehicleService
    ) {


    }


    ngOnInit(): void {



        this.loadVehicles();
        this.drawerMode = 'over';
        this._fuseMediaWatcherService.onMediaQueryChange$('(min-width: 1440px)')
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((state) => {
                // Calculate the drawer mode
                this.drawerMode = 'over';
            });

    }
    ngAfterViewInit(): void {

    }
    loadVehicles() {
        this.isLoading = true;
        this._fuseLoadingService.show();
        this._vehicleService.getVehicles()
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((vehicles) => {

                this.vehicles = vehicles;
                this.dataSource = new MatTableDataSource(this.vehicles);
                this.isLoading = false;
                this._fuseLoadingService.hide();
                if (this.vehicles.length > 0) {
                    this.dataSource.paginator = this.paginator;
                    this.dataSource.sort = this.sort;
                }
                // Mark for check
                this._changeDetectorRef.markForCheck();

            });
    }
    applyFilter(event: Event) {
        const filterValue = (event.target as HTMLInputElement).value;
        this.dataSource.filter = filterValue.trim().toLowerCase();

        if (this.dataSource.paginator) {
            this.dataSource.paginator.firstPage();
        }
    }
    onBackdropClicked() {
        this.matDrawer.close();

    }

    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    addNewItem() {
        this.selectedVehicle = this.initializeToNull<Vehicle>();
        this.selectedVehicle.$id = null;
        this._vehicleService.getOrganisationID().subscribe(organisationID => {
            this.selectedVehicle.groupIDs = [];
            this.selectedVehicle.organisationID = organisationID;
            this.isNewItem = true;
            this.matDrawer.open();

        })
    }



    viewDetails(rowData: any) {
        this.selectedVehicle = rowData;
        this.isNewItem = false;
        this._changeDetectorRef.markForCheck();
        this.matDrawer.open();

    }

    //  the delete function
    deleteItem(rowData: any) {
        // Open the confirmation dialog
        const confirmation = this._fuseConfirmationService.open({
            title: 'Delete confirmation!',
            message: `Are you sure you want to delete <i class="text-semibold text-red-600">${rowData.vehicleName}</i>? This action cannot be undone!`,
            actions: {
                confirm: {
                    label: 'Delete',
                },
            },
            icon: {
                show: true,
                name: 'heroicons_outline:trash',
            },
        });
        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            this._fuseLoadingService.show();
            // If the confirm button pressed...
            if (result === 'confirmed') {

                this._vehicleService.deleteVehicle(rowData.$id).subscribe(result => {

                    this._fuseLoadingService.hide();

                    // Mark for check
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();
                    //  console.log('Deleting item:', rowData);

                })


            } else { this._fuseLoadingService.hide(); }

        });

    }

    closeDrawer(mode: string) {
        //  console.log(mode);
        this.matDrawer.close();
        this.loadVehicles();
        this._changeDetectorRef.markForCheck();
        this._changeDetectorRef.detectChanges();
    }

    onDrawerOpened() {
        this.isDrawerOpen = true;
    }

    onDrawerClosed() {
        this.isDrawerOpen = false;
    }
    //Export table

    exportTable(exportType: string) {
        console.log(exportType);
    }
    //initializeToNull
    initializeToNull<T>(): T {
        const proxy = new Proxy({}, {
            get: () => null
        });
        // Create a new object with null values
        const obj = Object.assign({}, proxy);
        return obj as T;
    }

}

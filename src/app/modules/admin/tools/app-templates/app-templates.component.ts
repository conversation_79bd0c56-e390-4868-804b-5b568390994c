import { TextFieldModule } from '@angular/cdk/text-field';
import { ChangeDetectionStrategy, Component, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';

import { FuseMediaWatcherService } from '@fuse/services/media-watcher';
import { Subject, takeUntil } from 'rxjs';
import { DriverChecklistComponent } from './driver-checklist/driver-checklist.component';
import { CompletionTermsComponent } from './completion_terms/completion_terms.component';

@Component({
    selector: 'tools-app-templates',
    templateUrl: './app-templates.component.html',
    styleUrls: ['./app-templates.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [FormsModule, MatTabsModule, DriverChecklistComponent, CompletionTermsComponent, ReactiveFormsModule, MatFormFieldModule, MatIconModule, MatInputModule, TextFieldModule, MatSelectModule, MatOptionModule, MatButtonModule]
})
export class AppTemplatesComponent implements OnInit, OnDestroy {



    constructor(
        private _fuseMediaWatcherService: FuseMediaWatcherService,
    ) {



    }
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    ngOnInit(): void {

    }


    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }
}

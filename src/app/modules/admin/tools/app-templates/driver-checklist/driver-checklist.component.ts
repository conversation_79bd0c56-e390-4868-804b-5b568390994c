
import { Component, OnInit, ViewEncapsulation, OnChanges, SimpleChanges, ChangeDetectorRef, ViewChild } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { FuseLoadingService } from '@fuse/services/loading';
import { DriverChecklistService } from 'app/core/databaseModels/driverChecklist/driverChecklist.service';
import { DriverChecklist } from 'app/core/databaseModels/driverChecklist/driverChecklist.types';
import { Subject, catchError, switchMap, takeUntil, throwError } from 'rxjs';
import { FileInputDropzoneComponent } from 'app/modules/widgets/file-input-dropzone/file-input-dropzone.component';
import { FileInputDropzoneModule } from 'app/modules/widgets/file-input-dropzone/file-input-dropzone.module';
import { DriverChecklistDocService } from 'app/core/databaseModels/driverChecklistDoc/driverChecklistDoc.service';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { forEach } from 'lodash';
import { DriverChecklistDoc } from 'app/core/databaseModels/driverChecklistDoc/driverChecklistDoc.types';

@Component({
    selector: 'driver-checklist',
    templateUrl: './driver-checklist.component.html',
    encapsulation: ViewEncapsulation.None,
    imports: [FormsModule,
        MatProgressSpinnerModule,
        FileInputDropzoneModule,
        ReactiveFormsModule,
        MatTooltipModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        MatButtonModule,
        MatSelectModule,
        MatOptionModule,
    ]
})
export class DriverChecklistComponent implements OnInit, OnChanges {
    private _unsubscribeAll: Subject<any> = new Subject();

    @ViewChild('fileInputDropzone') fileInputDropzone: FileInputDropzoneComponent;
    selectedFiles: Array<{ filename: string, file: File, url: string }> = [];
    preloadedFiles: Array<{
        filename: string,
        fileUrl: string,
        fileId: string,
        docId: string,
        docIndex: number
    }> = [];


    driverChecklists: DriverChecklist[];
    driverChecklistDocs: DriverChecklistDoc[];
    selectedDriverChecklist: DriverChecklist;
    detailsForm: UntypedFormGroup;
    _organisationID: string;

    editMode: boolean = false;
    loadingData: boolean = false;
    isSaving: boolean = false;


    constructor(
        private _driverChecklistService: DriverChecklistService,
        private _driverChecklistDocService: DriverChecklistDocService,
        private _changeDetectorRef: ChangeDetectorRef,
        private _formBuilder: UntypedFormBuilder,
        private _fuseLoadingService: FuseLoadingService,
        private _fuseConfirmationService: FuseConfirmationService,
        private _bottomSheet: MatBottomSheet,

    ) {

    }

    ngOnInit(): void {
        this._driverChecklistService.getOrganisationID().subscribe((organisationID) => {
            this._organisationID = organisationID;
            // console.log(this._organisationID);
        });
        this._fuseLoadingService.show();
        this.loadingData = true;
        this._driverChecklistService.listenToRealTimeData();
        this._driverChecklistService.getDriverChecklists().pipe(
            switchMap(() => this._driverChecklistService.driverChecklists$),
            takeUntil(this._unsubscribeAll),
            catchError(error => {
                console.error('Error:', error);
                return throwError(error);
            })
        ).subscribe(driverChecklists => {
            this.driverChecklists = driverChecklists;
            this.loadingData = false;
            // this.selectedDriverChecklist = this.driverChecklists[0];
            this._changeDetectorRef.markForCheck();
            this._changeDetectorRef.detectChanges();
            this._fuseLoadingService.hide();

        })

        // Load Driver Checklist Docs
        this.loadDriverCheckListDoc();



        this.detailsForm = this._formBuilder.group({
            $id: [''],
            organisationID: [''],
            checklistNote: ['', [Validators.required]],
            status: [true],
        })

    }
    ngOnChanges(changes: SimpleChanges): void {

    }

    loadDriverCheckListDoc() {
        this._driverChecklistDocService.getDriverChecklistDocs().pipe(
            switchMap(() => this._driverChecklistDocService.driverChecklistDocs$),
            takeUntil(this._unsubscribeAll),
            catchError(error => {
                console.error('Error:', error);
                return throwError(() => error);
            })
        ).subscribe(driverChecklistDocs => {
            this.driverChecklistDocs = driverChecklistDocs;

            this.preloadedFiles = this.driverChecklistDocs.reduce((acc, doc, docIndex) => {
                if (doc.attachedDocs && Array.isArray(doc.attachedDocs)) {
                    doc.attachedDocs.forEach((fileString, fileIndex) => {
                        try {
                            const fileObject = JSON.parse(fileString);
                            acc.push({
                                filename: fileObject.fileName,
                                fileUrl: fileObject.fileUrl,
                                fileId: fileObject.fileId,
                                docId: doc.$id,
                                docIndex: fileIndex
                            });
                        } catch (e) {
                            console.error('Error parsing attachedDocs:', e);
                        }
                    });
                }
                return acc;
            }, []);

            this._changeDetectorRef.markForCheck();
            this._changeDetectorRef.detectChanges();
        });
    }
    editItem(driverChecklist: DriverChecklist) {
        this.editMode = true;
        //console.log(driverChecklist);

        this.detailsForm.patchValue(driverChecklist);
        // focus on input

    }

    updateDetails() {
        const driverChecklist = this.detailsForm.getRawValue();

        // console.log(job);
        if (driverChecklist.$id == null || driverChecklist.$id == '') {
            this.saveNewItem(driverChecklist);
        } else if (this.editMode) {
            this.updateExistingItem(driverChecklist);
        }
    }
    saveNewItem(driverChecklist: DriverChecklist) {
        this._fuseLoadingService.show();
        this.isSaving = true;
        driverChecklist.$id = null;
        driverChecklist.organisationID = this._organisationID;
        //console.log(driverChecklist);
        if (driverChecklist.checklistNote.length > 0) {

            this._driverChecklistService.createDriverChecklist(driverChecklist).subscribe(result => {
                this._fuseLoadingService.hide();
                // Mark for check
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
                this.isSaving = false;
                this.editMode = false;
                this.cleanupForm();
            }, error => {
                console.error('Error saving checklist:', error);
            });
        }


    }
    updateExistingItem(driverChecklist: DriverChecklist) {
        try {
            this._fuseLoadingService.show();
            this.isSaving = true;
            this._driverChecklistService.updateDriverChecklist(driverChecklist.$id, driverChecklist).subscribe(result => {
                this._fuseLoadingService.hide();
                // Mark for check
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
                this.isSaving = false;
                this.editMode = false;
                this.cleanupForm();
            }, error => {
                console.error('Error saving checklist:', error);
            });
        } catch (error) {
            console.error('Error updating checklist:', error);
        }

    }


    deleteItem(id: string) {
        try {
            console.log('Delete:', id);
            const confirmation = this._fuseConfirmationService.open({
                title: 'This checklist item  will be permanently removed and will not be recorded.',
                message: `Do you really want to delete this? Please note that this action is irreversible.`,
                actions: {
                    confirm: {
                        label: 'Confirm Delete',
                    },
                },
                icon: {
                    show: true,
                    name: 'heroicons_outline:trash',
                },
            });

            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {

                    this._fuseLoadingService.show();
                    this.isSaving = true;
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();
                    this._driverChecklistService.deleteDriverChecklist(id).subscribe(result => {
                        this._fuseLoadingService.hide();
                        // Mark for check
                        this._changeDetectorRef.markForCheck();
                        this._changeDetectorRef.detectChanges();
                        this.isSaving = false;
                        this.cleanupForm();
                    }, error => {
                        console.error('Error saving checklist:', error);
                    });
                } else {
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();

                    this.cleanupForm();
                }
            })


        } catch (error) {
            console.error('Error deleting checklist:', error);
        }
    }

    async cleanupForm(): Promise<void> {
        this.selectedDriverChecklist = null;
        this.detailsForm.reset();
        this.isSaving = false;
    }


    //===================FILE UPLOAD=========================== //

    previewFileUrl(fileUrl: string): string {

        if (fileUrl) {
            return fileUrl.replace('/view?', '/preview?width=250&');
        }
        return '';
    }

    openBottomSheet(): void {
        const bottomSheetRef = this._bottomSheet.open(FileInputDropzoneComponent);
        bottomSheetRef.afterDismissed().subscribe(result => {
            if (result && result.length > 0) {
                // Upload files using the existing `uploadFiles` function
                this.uploadFiles(result, (attachedFiles) => {
                    const driverChecklistDoc: DriverChecklistDoc = {
                        $id: null,
                        organisationID: this._organisationID,
                        attachedDocs: attachedFiles,
                    };

                    this._driverChecklistDocService.createDriverChecklistDoc(driverChecklistDoc).subscribe(() => {
                        this.loadDriverCheckListDoc();
                        this._changeDetectorRef.markForCheck();
                        this._changeDetectorRef.detectChanges();
                    });
                });
            }
        });
    }

    private uploadFiles(files: any[], callback: (attachedFiles: any[]) => void): void {
        const _attachedFiles: any[] = [];
        const uploading = this._fuseConfirmationService.open({
            title: "Uploading...",
            message: "Please wait while we upload your files.",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "info"
            },
            actions: {
                confirm: {
                    show: false,
                    label: "Remove",
                    color: "warn"
                },
                cancel: {
                    show: false,
                    label: "Cancel"
                }
            },
            dismissible: false
        });

        files.forEach((file) => {
            this._driverChecklistDocService.uploadFile(file).subscribe(result => {
                const fileObject = { fileName: file.name, fileUrl: result.fileUrl, fileId: result.fileId };
                _attachedFiles.push(JSON.stringify(fileObject));

                if (_attachedFiles.length === files.length) {
                    uploading.close();
                    callback(_attachedFiles);
                }
            }, error => console.error('Error uploading file:', error));
        });
    }


    deleteFile(fileId: string): void {
        const fileToDelete = this.preloadedFiles.find(file => file.fileId === fileId);

        if (fileToDelete) {
            const confirmation = this._fuseConfirmationService.open({
                title: 'This File will be permanently removed and will not be recorded.',
                message: `Do you really want to delete this? Please note that this action is irreversible.`,
                actions: {
                    confirm: {
                        label: 'Confirm Delete',
                    },
                },
                icon: {
                    show: true,
                    name: 'heroicons_outline:trash',
                },
            });

            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {
                    const deleting = this._fuseConfirmationService.open({
                        title: "Deleting...",
                        message: "Please wait while we delete your files.",
                        icon: {
                            show: true,
                            name: "heroicons_outline:exclamation-triangle",
                            color: "info"
                        },
                        actions: {
                            confirm: {
                                show: false,
                                label: "Remove",
                                color: "warn"
                            },
                            cancel: {
                                show: false,
                                label: "Cancel"
                            }
                        },
                        dismissible: false
                    });
                    this._driverChecklistDocService.deleteFile(fileId).subscribe(() => {
                        const doc = this.driverChecklistDocs.find(d => d.$id === fileToDelete.docId);
                        if (doc) {
                            // Remove the file from attachedDocs
                            doc.attachedDocs.splice(fileToDelete.docIndex, 1);

                            if (doc.attachedDocs.length === 0) {
                                // If no files are left, delete the document
                                this._driverChecklistDocService.deleteDriverChecklistDoc(doc.$id).subscribe(() => {
                                    this.driverChecklistDocs = this.driverChecklistDocs.filter(d => d.$id !== doc.$id);
                                    this.preloadedFiles = this.preloadedFiles.filter(file => file.fileId !== fileId);
                                    this._changeDetectorRef.markForCheck();
                                    this._changeDetectorRef.detectChanges();
                                    deleting.close();
                                });
                            } else {
                                // If files are still present, update the document
                                this._driverChecklistDocService.updateDriverChecklistDoc(doc.$id, doc).subscribe(() => {
                                    this.preloadedFiles = this.preloadedFiles.filter(file => file.fileId !== fileId);
                                    this._changeDetectorRef.markForCheck();
                                    this._changeDetectorRef.detectChanges();
                                    deleting.close();
                                });
                            }
                        }
                    });
                }
            });
        }
    }




}

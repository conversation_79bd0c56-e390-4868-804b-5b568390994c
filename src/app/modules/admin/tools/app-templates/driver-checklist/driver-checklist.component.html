<div>
    <div class="flex flex-col sm:flex-row items-start justify-between">
        <div class="text-lg font-medium tracking-tight leading-6 truncate">
            Driver Checklist
        </div>
        <div class="mt-3 sm:mt-0 sm:ml-2"></div>
    </div>
    <!-- Add Checklist -->
    <div class="w-full mt-10">
        <form [formGroup]="detailsForm">
            <mat-form-field class="w-full" [subscriptSizing]="'dynamic'">
                <mat-label>Add Checklist</mat-label>
                <mat-icon
                    class="icon-size-5"
                    [svgIcon]="'heroicons_solid:adjustments-horizontal'"
                    matPrefix
                ></mat-icon>
                <input
                    matInput
                    [formControlName]="'checklistNote'"
                    [placeholder]="'write here'"
                />
                @if (isSaving) {
                    <mat-progress-spinner
                        matSuffix
                        [diameter]="24"
                        [mode]="'indeterminate'"
                    ></mat-progress-spinner>
                } @else {
                    <button
                        [disabled]="detailsForm.invalid"
                        [matTooltip]="editMode ? 'Update' : 'Save'"
                        (click)="updateDetails()"
                        mat-icon-button
                        matSuffix
                    >
                        @if (editMode) {
                            <mat-icon
                                class="icon-size-5 text-green-600"
                                [svgIcon]="'heroicons_solid:check'"
                            ></mat-icon>
                        } @else {
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_solid:plus-circle'"
                            ></mat-icon>
                        }
                    </button>
                }
            </mat-form-field>
        </form>
        <div class="flex flex-col mt-8 divide-y border-t border-b">
            <div
                class="max-h-80 scroll-auto scroll-ps-6 md:scroll-auto h-full overflow-y-auto overscroll-y-contain sm:overscroll-auto"
            >
                @for (checklist of driverChecklists; track checklist) {
                    <ng-container>
                        <div
                            class="flex flex-col sm:flex-row sm:items-center py-6"
                        >
                            <div class="flex items-center">
                                <div class="ml-4">
                                    <div class="font-medium">
                                        {{ checklist.checklistNote }}
                                    </div>
                                </div>
                            </div>
                            <div
                                class="flex items-center mt-4 sm:mt-0 sm:ml-auto"
                            >
                                <div
                                    class="order-2 sm:order-1 ml-4 sm:ml-0"
                                ></div>
                                <div class="order-1 sm:order-2 sm:ml-3">
                                    <button
                                        mat-icon-button
                                        (click)="
                                            editMode = true; editItem(checklist)
                                        "
                                    >
                                        <mat-icon
                                            class="text-hint"
                                            [svgIcon]="
                                                'heroicons_outline:pencil'
                                            "
                                        ></mat-icon>
                                    </button>
                                    <button
                                        mat-icon-button
                                        (click)="deleteItem(checklist.$id)"
                                    >
                                        <mat-icon
                                            class="text-hint"
                                            [svgIcon]="
                                                'heroicons_outline:trash'
                                            "
                                        ></mat-icon>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                } @empty {
                    <li class="text-center">There is no checklist.</li>
                }
                @if (loadingData) {
                    <div class="w-full">
                        @for (_ of [].constructor(5); track _; let i = $index) {
                            <div class="border-b-2 mb-5 border-indigo-300">
                                <div class="flex flex-col flex-1 gap-5 sm:p-2">
                                    <div class="flex flex-col flex-1 gap-3">
                                        <div
                                            class="w-3/5 bg-gray-200 animate-pulse h-14 rounded-2xl"
                                        ></div>
                                        <div
                                            class="w-full h-3 bg-gray-200 animate-pulse rounded-2xl"
                                        ></div>
                                        <div
                                            class="w-4/5 h-3 bg-gray-200 animate-pulse rounded-2xl"
                                        ></div>
                                        <div
                                            class="w-full h-3 bg-gray-200 animate-pulse rounded-2xl"
                                        ></div>
                                        <div
                                            class="w-1/5 h-3 bg-gray-200 animate-pulse rounded-2xl"
                                        ></div>
                                    </div>
                                    <div class="flex gap-3 mt-auto">
                                        <div
                                            class="w-20 h-8 bg-gray-200 rounded-full animate-pulse"
                                        ></div>
                                        <div
                                            class="w-20 h-8 bg-gray-200 rounded-full animate-pulse"
                                        ></div>
                                        <div
                                            class="w-20 h-8 ml-auto bg-gray-200 rounded-full animate-pulse"
                                        ></div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
    <div class="flex flex-col flex-auto mt-6"></div>
    <!-- Documents -->
    <div class="flex flex-auto flex-col">
        <div class="flex justify-between items-center">
            <div class="text-lg font-medium">Checklist Documents</div>
            <div class="mt-4 sm:mt-0">
                <!-- Upload button -->
                <button
                    mat-flat-button
                    [color]="'primary'"
                    (click)="openBottomSheet()"
                >
                    <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                    <span class="ml-2 mr-1">Upload file</span>
                </button>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-4 gap-1 mt-4">
        @for (file of preloadedFiles; track file) {
            @switch (file.filename.split(".").pop()) {
                @case ("pdf") {
                    <div class="group relative mt-2 h-full min-h-[100px]">
                        <div
                            class="absolute w-full h-full bg-gray-900/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg flex flex-col items-center justify-center space-y-2"
                        >
                            <a
                                [href]="file.fileUrl"
                                target="_blank"
                                class="inline-flex items-center justify-center rounded-full h-10 w-10 bg-white/30 hover:bg-white/50 focus:ring-4 focus:outline-none dark:text-white focus:ring-gray-50"
                            >
                                <i
                                    class="fa-duotone fa-download icon-size-5 text-white"
                                ></i>
                            </a>
                            <i
                                class="text-xs font-thin text-ellipsis overflow-hidden text-white ml-2 mr-2"
                            >
                                {{ file.filename }}
                            </i>
                        </div>
                        <div
                            class="flex items-center justify-center rounded-lg w-full h-full text-white font-bold bg-red-600"
                        >
                            PDF
                        </div>
                        <!-- Delete Icon -->
                        <button
                            class="absolute top-2 right-2 inline-flex items-center justify-center rounded-full h-6 w-6 bg-white/30 hover:bg-white/50 focus:ring-4 focus:outline-none focus:ring-gray-50"
                            (click)="deleteFile(file.fileId)"
                        >
                            <i class="fa-duotone fa-trash-alt text-white"></i>
                        </button>
                    </div>
                }
                @default {
                    <div class="group relative mt-2 h-full min-h-[100px]">
                        <div
                            class="absolute w-full h-full bg-gray-900/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg flex items-center justify-center"
                        >
                            <a
                                [href]="file.fileUrl"
                                target="_blank"
                                class="inline-flex items-center justify-center rounded-full h-10 w-10 bg-white/30 hover:bg-white/50 focus:ring-4 focus:outline-none dark:text-white focus:ring-gray-50"
                            >
                                <i
                                    class="fa-duotone fa-download icon-size-5 text-white"
                                ></i>
                            </a>
                        </div>
                        <img
                            class="rounded-lg w-full h-full object-cover"
                            [src]="previewFileUrl(file.fileUrl)"
                        />
                        <!-- Delete Icon -->
                        <button
                            class="absolute top-2 right-2 inline-flex items-center justify-center rounded-full h-6 w-6 bg-white/30 hover:bg-white/50 focus:ring-4 focus:outline-none focus:ring-gray-50"
                            (click)="deleteFile(file.fileId)"
                        >
                            <i class="fa-duotone fa-trash-alt text-white"></i>
                        </button>
                    </div>
                }
            }
        } @empty {
            <span>There are no items.</span>
        }
    </div>
</div>

<div class="absolute inset-0 top-20 flex flex-col min-w-0">
    <!-- Main -->
    <div class="flex flex-col">
        <div class="h-full max-w-screen-xl mx-auto">
            <mat-tab-group
                class="sm:px-2"
                mat-stretch-tabs="false"
                [animationDuration]="'0'"
            >
                <!-- Mobile App Setting -->
                <mat-tab>
                    <ng-template mat-tab-label>
                        <span class="inline-flex items-center space-x-2">
                            <span class="">Mobile App Settings</span>
                            <i class="fa-duotone fa-laptop-mobile"></i>
                        </span>
                    </ng-template>
                    <ng-template matTabContent>
                        <div
                            class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 w-full min-w-0 mt-10"
                        >
                            <driver-checklist
                                class="sm:col-span-2 md:col-span-4 lg:col-span-2 flex flex-col flex-auto p-6 bg-card shadow rounded-2xl overflow-hidden"
                            />
                            <completion-terms
                                class="sm:col-span-2 md:col-span-4 lg:col-span-2 flex flex-col flex-auto p-6 bg-card shadow rounded-2xl overflow-hidden"
                            />
                        </div>
                    </ng-template>
                </mat-tab>
                <!-- Email Template's -->
                <mat-tab>
                    <ng-template mat-tab-label>
                        <span class="inline-flex items-center space-x-2">
                            <span class="">Email Template's</span>
                            <i class="fa-duotone fa-envelopes"></i>
                        </span>
                    </ng-template>
                    <ng-template
                        matTabContent
                        class="flex flex-col h-full scroll-auto scroll-ps-6 md:scroll-auto"
                    >
                    </ng-template>
                </mat-tab>
            </mat-tab-group>
        </div>
    </div>
</div>


import { Component, OnInit, ViewEncapsulation, ChangeDetectorRef, ChangeDetectionStrategy } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { FuseLoadingService } from '@fuse/services/loading';
import { OrganisationsService } from 'app/core/databaseModels/organisations/organisations.service';
import { Organisation } from 'app/core/databaseModels/organisations/organisations.types';
import { QuillModule } from 'ngx-quill'
import { Subject, catchError, switchMap, takeUntil, throwError } from 'rxjs';

@Component({
    selector: 'completion-terms',
    templateUrl: './completion_terms.component.html',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [FormsModule,
        ReactiveFormsModule,
        QuillModule,
        FormsModule,
        MatProgressSpinnerModule,
        ReactiveFormsModule,
        MatTooltipModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        MatButtonModule,
        MatSelectModule,
        MatOptionModule,
    ]
})
export class CompletionTermsComponent implements OnInit {

    private _unsubscribeAll: Subject<any> = new Subject();
    organisations: Organisation
    selectedOrganisationID: string = '';
    isSaving: boolean = false;

    editorContent;
    editorConfig = {
        toolbar: [
            ['bold', 'italic', 'underline', 'strike'],        // toggled buttons
            ['blockquote', 'code-block'],

            [{ 'header': 1 }, { 'header': 2 }],               // custom button values
            [{ 'list': 'ordered' }, { 'list': 'bullet' }],
            [{ 'script': 'sub' }, { 'script': 'super' }],      // superscript/subscript
            [{ 'indent': '-1' }, { 'indent': '+1' }],          // outdent/indent
            [{ 'direction': 'rtl' }],                         // text direction

            [{ 'size': ['small', false, 'large', 'huge'] }],  // custom dropdown
            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],

            [{ 'color': [] }, { 'background': [] }],          // dropdown with defaults from theme
            [{ 'font': [] }],
            [{ 'align': [] }],

            ['clean'],
        ],

    };

    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _formBuilder: UntypedFormBuilder,
        private _fuseLoadingService: FuseLoadingService,
        private _fuseConfirmationService: FuseConfirmationService,
        private _organisationsService: OrganisationsService
    ) {

    }
    ngOnInit(): void {
        this.loadContent();
    }
    loadContent() {
        // Load your content into the editor
        // This could come from a local variable, an API call, etc.

        this._organisationsService.get().subscribe(organisation => {


            if (organisation) {
                this.organisations = organisation;
                this.selectedOrganisationID = organisation.$id;
                this.editorContent = this.organisations.jobCompletionTerms;
                this._changeDetectorRef.markForCheck();


                // console.log(this.editorContent);
            }
        })
        //   this.editorContent = '<p>Hello, this is some <strong>sample content</strong>.</p>';
    }

    saveContent() {
        this._fuseLoadingService.show();
        this.isSaving = true;
        // console.log('Saving content:', this.editorContent);
        if (this.organisations) {
            this.organisations.jobCompletionTerms = this.editorContent;
            this._organisationsService.updateOrganisation(this.selectedOrganisationID, this.organisations).subscribe(
                () => {
                    this._fuseLoadingService.hide();
                    this.isSaving = false;
                    this._changeDetectorRef.markForCheck();
                }
            );
        }
    }

    onEditorContentChange(updatedContent: string): void {
        // console.log(updatedContent);
        this.editorContent = updatedContent;
        // Manually trigger change detection if needed
        this._changeDetectorRef.markForCheck();
    }
}

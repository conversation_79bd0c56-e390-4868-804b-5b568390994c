<div>
    <div class="flex flex-col sm:flex-row items-start justify-between">
        <div class="text-lg font-medium tracking-tight leading-6 truncate">
            Job Completion and Terms Agreement
        </div>
        <div class="mt-3 sm:mt-0 sm:ml-2"></div>
    </div>

    <div class="flex flex-col flex-auto mt-6">
        <quill-editor
            class="w-full h-full"
            [modules]="editorConfig"
            [sanitize]="true"
            [(ngModel)]="editorContent"
            (ngModelChange)="onEditorContentChange($event)"
        >
        </quill-editor>
        <!-- <pre>new text : <code>{{ editorContent }}</code></pre> -->
        <!-- Actions -->
        <div
            class="flex items-center mt-10 -mx-6 sm:-mx-12 py-4 pr-4 pl-1 sm:pr-12 sm:pl-7 border-t dark:bg-transparent"
        >
            @if(isSaving){

            <mat-progress-spinner
                matSuffix
                [diameter]="24"
                [mode]="'indeterminate'"
            ></mat-progress-spinner>

            } @else{
            <!-- Cancel -->
            <button
                class="ml-auto"
                mat-button
                [matTooltip]="'Cancel'"
                (click)="loadContent()"
            >
                Cancel
            </button>
            <!-- Save -->
            <button
                class="ml-2"
                mat-flat-button
                [color]="'primary'"
                [matTooltip]="'Save'"
                (click)="saveContent()"
            >
                Save</button
            >}
        </div>
    </div>
</div>

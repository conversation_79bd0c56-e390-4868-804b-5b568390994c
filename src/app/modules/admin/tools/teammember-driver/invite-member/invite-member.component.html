<div class="flex flex-col max-w-240 md:min-w-160 max-h-screen -m-6">
    <!-- Header -->
    <div
        class="flex flex-0 items-center justify-between h-16 pr-3 sm:pr-5 pl-6 sm:pl-8 bg-primary text-on-primary"
    >
        <div class="text-lg font-medium">Invite New Member</div>
        <button mat-icon-button (click)="closeDialog()" [tabIndex]="-1">
            <mat-icon
                class="text-current"
                [svgIcon]="'heroicons_outline:x-mark'"
            ></mat-icon>
        </button>
    </div>

    <!-- Compose form -->
    <form
        class="flex flex-col flex-auto p-6 sm:p-8 overflow-y-auto"
        [formGroup]="inviteForm"
    >
        <!-- To -->
        <mat-form-field>
            <mat-label>Email</mat-label>
            <input matInput [formControlName]="'to'" />
        </mat-form-field>

        <!-- Subject -->
        <mat-form-field>
            <mat-label>Subject</mat-label>
            <input matInput [formControlName]="'subject'" />
        </mat-form-field>

        <!-- Body -->
        <mat-form-field>
            <mat-label>Your Message!</mat-label>
            <textarea
                class="mt-2"
                matInput
                [formControlName]="'body'"
                cdkTextareaAutosize
                [cdkAutosizeMinRows]="5"
            ></textarea>
        </mat-form-field>

        <!-- Actions -->
        <div
            class="flex flex-col sm:flex-row sm:items-center justify-between mt-4 sm:mt-6"
        >
            <div class="-ml-2"></div>

            <div class="flex items-center mt-4 sm:mt-0">
                <!-- Discard -->
                <button
                    class="ml-auto sm:ml-0"
                    mat-stroked-button
                    (click)="closeDialog()"
                >
                    Discard
                </button>

                <!-- Send -->
                <button
                    class="order-first sm:order-last"
                    mat-flat-button
                    [color]="'primary'"
                    (click)="send()"
                >
                    Send
                </button>
            </div>
        </div>
    </form>
</div>

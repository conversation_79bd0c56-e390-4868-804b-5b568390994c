
import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';

@Component({
    selector: 'app-invite-member',
    templateUrl: './invite-member.component.html',
    styleUrls: ['./invite-member.component.scss'], encapsulation: ViewEncapsulation.None,
    imports: [MatButtonModule, MatIconModule, FormsModule, ReactiveFormsModule, MatFormFieldModule, MatInputModule]
})
export class InviteMemberComponent implements OnInit {
    inviteForm: UntypedFormGroup;

    /**
    * Constructor
    */
    constructor(
        public matDialogRef: MatDialogRef<InviteMemberComponent>,
        private _formBuilder: UntypedFormBuilder,
    ) {
    }
    ngOnInit(): void {
        // Create the form
        this.inviteForm = this._formBuilder.group({
            to: ['', [Validators.required, Validators.email]],
            subject: [''],
            body: ['', [Validators.required]],
        });
    }
    closeDialog(): void {

        // Close the dialog
        this.matDialogRef.close();
    }

    /**
     * Send the message
     */
    send(): void {
        this.closeDialog();
    }
}

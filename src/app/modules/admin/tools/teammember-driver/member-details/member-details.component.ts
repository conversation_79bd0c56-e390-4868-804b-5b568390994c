import { TextFieldModule } from '@angular/cdk/text-field';
import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, Renderer2, TemplateRef, ViewChild, ViewContainerRef, ViewEncapsulation, importProvidersFrom } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormArray, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule, MatRippleModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FuseLoadingService } from '@fuse/services/loading';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { Subject, filter, switchMap, takeUntil, tap } from 'rxjs';


import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';
import { TeamMembersService } from 'app/core/databaseModels/teammembers/teammembers.service';
import { MatMapsAutocompleteModule } from 'app/modules/widgets/mat-maps-autocomplete/mat-maps-autocomplete.module';
import { CustomMatSelectTreeModule } from 'app/modules/widgets/custom-matselect/custom-matselect-tree.module';
import { TreeNode } from 'app/modules/widgets/custom-matselect/select-check-all-tree/select-check-all-tree.component';
import { GroupService } from 'app/core/databaseModels/groups/groups.service';
import { FileInputDropzoneComponent } from 'app/modules/widgets/file-input-dropzone/file-input-dropzone.component';
import { FileInputDropzoneModule } from 'app/modules/widgets/file-input-dropzone/file-input-dropzone.module';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@Component({
    selector: 'member-details',
    templateUrl: './member-details.component.html',
    styleUrls: ['./member-details.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    exportAs: 'member-details',
    imports: [
        MatButtonModule,
        MatTooltipModule,
        MatIconModule,
        FormsModule,
        ReactiveFormsModule,
        MatRippleModule,
        MatFormFieldModule,
        MatInputModule,
        MatCheckboxModule,
        MatSelectModule,
        MatOptionModule,
        MatDatepickerModule,
        TextFieldModule,
        NgxMaskDirective,
        DatePipe,
        MatMapsAutocompleteModule,
        FileInputDropzoneModule,
        MatProgressSpinnerModule,
        CustomMatSelectTreeModule,
    ],
    providers: [provideNgxMask()]
})
export class MemberDetailsComponent implements OnInit, OnDestroy {
    @Input({ required: true }) teamMember: TeamMember | null = null;
    @Input() newItem: boolean | null = false;
    @Output() closeDrawer = new EventEmitter<string>();
    @ViewChild('avatarFileInput') private _avatarFileInput: ElementRef;
    @ViewChild('fileInputDropzone') fileInputDropzone: FileInputDropzoneComponent;

    private _unsubscribeAll: Subject<any> = new Subject<any>();
    selectedFiles: Array<{ filename: string, file: File, url: string }> = [];
    preloadedFiles: Array<{ filename: string, fileUrl: string, fileId: string }> = [];


    editMode: boolean = false;
    selectedImage: any;
    memberDetailsForm: UntypedFormGroup;
    constructor(private _formBuilder: UntypedFormBuilder,
        private _changeDetectorRef: ChangeDetectorRef,
        private _teamMembersService: TeamMembersService,
        private _fuseLoadingService: FuseLoadingService,
        private _groupService: GroupService) {

    }
    isLoading: boolean = false;
    isSaving: boolean = false;

    treeData = [];
    selectedGroupIds = [];


    ngOnInit(): void {
        //create the member details form
        this.memberDetailsForm = this._formBuilder.group({
            $id: [this.teamMember?.$id],
            organisationID: [this.teamMember?.organisationID],
            name: ['', [Validators.required]],
            email: ['', [Validators.required, Validators.email]],
            phone: ['', [Validators.required]],
            driverLicense: [''],
            assignedVehicle: [''],
            assignedVehicleStartDate: [''],
            assignedVehicleEndDate: [''],
            assignmentHistory: this._formBuilder.array([]),
            employeeNumber: [''],
            homeAddresses: [''],
            homeLatLon: [''],
            hourlyRate: [''],
            avatar: [''],
            avatarImageId: [''],
            notes: [''],
            approved: [''],
            status: [true],
            deviceTokenId: [''],
            groupMemberIDs: [''],
            attachedDocs: [[]],

        });
        if (this.newItem) {

            this.toggleEditMode(true);


        } else {
            this.toggleEditMode(false);


        }
        //console.log(this.teamMember)


        this.loadGroups();
    }

    loadGroups() {
        this.isLoading = true;
        this._fuseLoadingService.show();

        this._groupService.getGroups().pipe(
            tap((res) => {
                if (!res || res.length === 0) {
                    this.isLoading = false;
                    this._fuseLoadingService.hide();
                }
            }),
            filter((groups) => groups.length > 0),
            switchMap(() => this._groupService.groups$.pipe(takeUntil(this._unsubscribeAll))),
            tap((groups) => {
                this.treeData = this.convertToTreeData(groups);
                this.selectedGroupIds = this.teamMember?.groupMemberIDs || [];
                // console.log('selectedG:', this.teamMember?.groupMemberIDs);
                this._fuseLoadingService.hide();
                this._changeDetectorRef.markForCheck();
            }),
            takeUntil(this._unsubscribeAll)
        ).subscribe({
            complete: () => {
                this.isLoading = false;
                this._fuseLoadingService.hide();
                this._changeDetectorRef.markForCheck();
            }
        });
    }


    ngOnDestroy(): void {
        this.editMode = false;
        this.newItem = false;
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }
    showEditMode() {
        this.newItem = false;

        this.preloadedFiles = [];
        if (this.teamMember.attachedDocs) {
            this.preloadedFiles = this.teamMember.attachedDocs.map(fileString => {
                let fileObject = JSON.parse(fileString);
                return {
                    filename: fileObject.fileName,
                    fileUrl: fileObject.fileUrl,
                    fileId: fileObject.fileId
                };
            });
        }
        this.toggleEditMode(true);
    }
    toggleEditMode(mode: boolean) {

        this.editMode = mode;


        this.memberDetailsForm.patchValue(this.teamMember);

        // Mark for check
        this._changeDetectorRef.markForCheck();

    }




    onDateChange(selectedDate: any) {
        // const date = DateTime.fromISO(selectedDate.startDate);
        // console.log(date)
        // if (date.isValid) {
        //     this.memberDetailsForm.get('assignedVehicleStartDate').setValue(date.toFormat(this.locale.format));
        //     console.log(this.memberDetailsForm.get('assignedVehicleStartDate'));

        // }
        // console.log(selectedDate.startDate.format())
        if (this.editMode) {
            // console.log(selectedDate)
            this.memberDetailsForm.get('assignedVehicleStartDate').setValue(selectedDate.startDate.format())
        }


    }


    cancelUpdateDetails() {
        this.closeDrawer.emit('cancel');
    }

    updateDetails() {
        const teamMember = this.memberDetailsForm.getRawValue();
        // console.log(teamMember);

        teamMember.groupMemberIDs = this.selectedGroupIds;
        if (teamMember.$id == null && this.newItem == true) {
            this.saveNew(teamMember);
        } else {
            this.saveEdited(teamMember);
        }


    }

    formatHourlyRate(): void {
        const control = this.memberDetailsForm.get('hourlyRate');
        if (control && control.value) {
            let value = control.value.toString().replace(/\D/g, ''); // Remove non-digit characters
            if (value) {
                // Convert to a number and format to 2 decimal places
                const formattedValue = (parseFloat(value) / 100).toFixed(2);
                control.setValue(formattedValue, { emitEvent: false });
            }
        }
    }
    private saveNew(teamMember: TeamMember) {

        teamMember.name = teamMember.name.trimEnd();
        this._fuseLoadingService.show();
        this.isSaving = true;
        teamMember.hourlyRate = teamMember.hourlyRate.toString();

        // Function to handle the completion of member creation
        const finalizeCreation = () => {
            this._fuseLoadingService.hide();
            this._changeDetectorRef.markForCheck();
            this._changeDetectorRef.detectChanges();
            this.closeDrawer.emit('newITEM');
        };

        // Function to handle file uploads
        const uploadFiles = (files: any[], callback: (attachedFiles: any[]) => void) => {
            const _attachedFiles = [];
            files.forEach((file) => {
                this._teamMembersService.uploadFile(file.file).subscribe(result => {
                    let fileObject = { fileName: file.filename, fileUrl: result.fileUrl, fileId: result.fileId };
                    _attachedFiles.push(JSON.stringify(fileObject));

                    if (_attachedFiles.length === files.length) {
                        callback(_attachedFiles);
                    }
                }, error => console.error('Error uploading file:', error));
            });
        };

        // Handle no selected image scenario
        if (!this.selectedImage) {
            if (!this.selectedFiles || this.selectedFiles.length === 0) {
                this._teamMembersService.createTeamMember(teamMember).subscribe(() => finalizeCreation());
            } else {
                uploadFiles(this.selectedFiles, (attachedFiles) => {
                    teamMember.attachedDocs = attachedFiles;
                    this._teamMembersService.createTeamMember(teamMember).subscribe(() => finalizeCreation());
                });
            }
        } else {
            // Handle selected image scenario
            this._teamMembersService.uploadFile(this.selectedImage).subscribe(result => {
                teamMember.avatar = result.fileUrl;
                teamMember.avatarImageId = result.fileId;

                if (!this.selectedFiles || this.selectedFiles.length === 0) {
                    this._teamMembersService.createTeamMember(teamMember).subscribe(() => finalizeCreation());
                } else {
                    uploadFiles(this.selectedFiles, (attachedFiles) => {
                        teamMember.attachedDocs = attachedFiles;
                        this._teamMembersService.createTeamMember(teamMember).subscribe(() => finalizeCreation());
                    });
                }
            }, error => console.error('Error uploading file:', error));
        }
    }

    private saveEdited(teamMember: TeamMember) {
        teamMember.name = teamMember.name.trimEnd();
        this._fuseLoadingService.show();
        this.isSaving = true;
        teamMember.hourlyRate = teamMember.hourlyRate.toString();

        // Function to handle the completion of member update
        const finalizeUpdate = () => {
            this._fuseLoadingService.hide();
            this._changeDetectorRef.markForCheck();
            this._changeDetectorRef.detectChanges();
            this.closeDrawer.emit('update');
        };

        // Function to handle file uploads
        const uploadFiles = (files: any[], callback: (attachedFiles: any[]) => void) => {
            const _attachedFiles = [];
            files.forEach((file) => {
                this._teamMembersService.uploadFile(file.file).subscribe(result => {
                    let fileObject = { fileName: file.filename, fileUrl: result.fileUrl, fileId: result.fileId };
                    _attachedFiles.push(JSON.stringify(fileObject));

                    if (_attachedFiles.length === files.length) {
                        callback(_attachedFiles);
                    }
                }, error => console.error('Error uploading file:', error));
            });
        };

        // Handle image update
        const handleImageUpdate = (oldImageId: string) => {
            if (oldImageId) {
                this._teamMembersService.deleteFile(oldImageId).subscribe(
                    () => finalizeUpdate(),
                    error => console.error('Error deleting old image:', error)
                );
            } else {
                finalizeUpdate();
            }
        };

        // Check if an image is selected
        if (this.selectedImage) {
            const oldImageId = teamMember.avatarImageId;

            // Upload the new image
            this._teamMembersService.uploadFile(this.selectedImage).subscribe(result => {
                teamMember.avatar = result.fileUrl;
                teamMember.avatarImageId = result.fileId;

                if (this.selectedFiles && this.selectedFiles.length > 0) {
                    // Upload additional files if available
                    uploadFiles(this.selectedFiles, (newAttachedFiles) => {
                        // Merge new files with existing ones
                        teamMember.attachedDocs = teamMember.attachedDocs || [];
                        teamMember.attachedDocs.push(...newAttachedFiles);

                        this._teamMembersService.updateTeamMember(teamMember.$id, teamMember).subscribe(() => {
                            handleImageUpdate(oldImageId);
                        });
                    });
                } else {
                    // Update the team member
                    this._teamMembersService.updateTeamMember(teamMember.$id, teamMember).subscribe(() => {
                        handleImageUpdate(oldImageId);
                    });
                }
            }, error => console.error('Error uploading image:', error));
        } else {
            // No new image selected
            if (this.selectedFiles && this.selectedFiles.length > 0) {
                // Upload additional files if available
                uploadFiles(this.selectedFiles, (newAttachedFiles) => {
                    // Merge new files with existing ones
                    teamMember.attachedDocs = teamMember.attachedDocs || [];
                    teamMember.attachedDocs.push(...newAttachedFiles);

                    this._teamMembersService.updateTeamMember(teamMember.$id, teamMember).subscribe(() => {
                        finalizeUpdate();
                    });
                });
            } else {
                // Update the team member without any image or additional file changes
                this._teamMembersService.updateTeamMember(teamMember.$id, teamMember).subscribe(() => {
                    finalizeUpdate();
                });
            }
        }
    }




    /**
   * Upload avatar
   *
   * @param fileList
   */

    uploadAvatar(fileList: FileList): void {
        // Return if canceled
        if (!fileList.length) {
            return;
        }

        const allowedTypes = ['image/jpeg', 'image/png'];
        const file = fileList[0];

        // Return if the file is not allowed
        if (!allowedTypes.includes(file.type)) {
            return;
        }

        // update the avatar url from file
        this.teamMember.avatar = URL.createObjectURL(file);

        this.selectedImage = file;
    }

    /**
     * Remove the avatar
     */
    removeAvatar(): void {
        // Get the form control for 'avatar'
        const avatarFormControl = this.memberDetailsForm.get('avatar');

        // Set the avatar as null
        avatarFormControl.setValue(null);

        // Set the file input value as null
        this._avatarFileInput.nativeElement.value = null;

        // Update the contact
        this.teamMember.avatar = null;
        this.selectedImage = null;


    }
    onAutocompleteSelected(location: any) {
        this.memberDetailsForm.controls.homeAddresses.setValue(location.address);
        this.memberDetailsForm.controls.homeLatLon.setValue(location.latLon);
    }


    onGroupSelectChange(selectedGroupIds: string[]) {
        //   console.log('Selected IDs:', selectedGroupIds);
        this.selectedGroupIds = selectedGroupIds;

    }

    convertToTreeData(data: any[]): TreeNode[] {
        const itemsById: { [key: string]: TreeNode } = {};
        const rootItems: TreeNode[] = [];

        // First pass: create nodes for each item
        if (!data) {
            return [];
        }
        data.forEach(item => {
            itemsById[item.$id] = {
                id: item.$id,
                name: item.groupName,
                parentID: item.parentID,
                children: []
            };
        });

        // Second pass: assign children to their parents
        data.forEach(item => {
            if (item.parentID) {
                const parent = itemsById[item.parentID];
                if (parent) {
                    parent.children.push(itemsById[item.$id]);
                } else {
                    // Handle the case where the parentID is invalid or missing in the dataset
                    console.warn(`Missing parent with ID: ${item.parentID}`);
                    rootItems.push(itemsById[item.$id]); // Optional: add it as a root item if the parent is missing
                }
            } else {
                rootItems.push(itemsById[item.$id]);
            }
        });

        return rootItems;
    }


    handleSelectedFiles(files: File[]): void {
        // process the selected files

        if (files.length > 0) {
            this.selectedFiles = files.map(file => {
                const url = URL.createObjectURL(file);
                return { filename: file.name, file: file, url: url };
            });
            //  console.log(this.selectedFiles);
        } else {
            this.selectedFiles = [];
        }
    }


    onRemovePreloadedFile(fileId: string): void {
        this._fuseLoadingService.show();
        this.isSaving = true;

        // Call the service to delete the file from the server
        this._teamMembersService.deleteFile(fileId).subscribe({
            next: () => {
                // Remove the file from the teamMember.attachedDocs array
                this.teamMember.attachedDocs = this.teamMember.attachedDocs.filter(file => {
                    const fileObject = JSON.parse(file);
                    return fileObject.fileId !== fileId;
                });

                // Update the team member if needed (optional step, depending on your logic)
                this._teamMembersService.updateTeamMember(this.teamMember.$id, this.teamMember).subscribe(() => {
                    // Hide the loading indicator and reset flags
                    this._fuseLoadingService.hide();
                    this.isSaving = false;
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();
                });
            },
            error: (err) => {
                console.error('Error deleting file:', err);
                this._fuseLoadingService.hide();
                this.isSaving = false;
            }
        });
    }


    fileDetails(file: string): any {

        return JSON.parse(file);
    }

    previewFileUrl(file: string): string {
        const fileDetail = this.fileDetails(file);
        if (fileDetail && fileDetail.fileUrl) {
            return fileDetail.fileUrl.replace('/view?', '/preview?width=250&');
        }
        return '';
    }



}

<div class="flex flex-col w-full">
  <!-- View mode -->
  @if (!editMode) {
    <!-- Header -->
    <div
      class="relative w-full h-18 sm:h-20 bg-accent-700 dark:bg-accent-700"
      >
      <!-- Close button -->
      <div
        class="flex items-center justify-end w-full max-w-3xl mx-auto pt-6"
        >
        <button
          mat-icon-button
          [matTooltip]="'Close'"
          (click)="cancelUpdateDetails()"
          >
          <mat-icon
            class="text-white"
            [svgIcon]="'heroicons_outline:x-mark'"
          ></mat-icon>
        </button>
      </div>
    </div>
    <!-- Team member -->
    <div
      class="relative flex flex-col flex-auto items-center p-6 pt-0 sm:p-12 sm:pt-0"
      >
      <div class="w-full max-w-3xl">
        <!-- Avatar and actions -->
        <div class="flex flex-auto items-end -mt-16">
          <!-- Avatar -->
          <div
            class="flex items-center justify-center w-32 h-32 rounded-full overflow-hidden ring-4 ring-bg-card"
            >
            @if (teamMember.avatar) {
              <img
                class="object-cover w-full h-full"
                [src]="teamMember.avatar"
                />
            }
            @if (!teamMember.avatar && teamMember.name) {
              <div
                class="flex items-center justify-center w-full h-full rounded overflow-hidden uppercase text-8xl font-bold leading-none bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                >
                {{ teamMember.name.charAt(0) }}
              </div>
            }
          </div>
          <!-- Actions -->
          <div class="flex items-center ml-auto mb-1">
            <button mat-stroked-button (click)="showEditMode()">
              <mat-icon
                class="icon-size-5"
                [svgIcon]="'heroicons_solid:pencil-square'"
              ></mat-icon>
              <span class="ml-2">Edit</span>
            </button>
          </div>
        </div>
        <!-- Name -->
        <div class="mt-3 text-4xl font-bold truncate">
          {{ teamMember.name }}
        </div>
        <div class="flex flex-col mt-4 pt-6 border-t space-y-8">
          <!-- driver License -->
          @if (teamMember.driverLicense) {
            <div class="flex sm:items-center">
              <i class="icon-size-5 fa-thin fa-id-card"></i>
              <div class="ml-6">
                {{
                teamMember.driverLicense
                ? teamMember.driverLicense
                : "N/A"
                }}
              </div>
            </div>
          }
          <!-- driver assignedVehicle -->
          @if (teamMember.assignedVehicle) {
            <div class="flex sm:items-center">
              <mat-icon
                svgIcon="mat_outline:emoji_transportation"
              ></mat-icon>
              <div class="ml-6">
                {{
                teamMember.assignedVehicle
                ? teamMember.assignedVehicle
                : "N/A"
                }}
              </div>
            </div>
          }
          <!-- driver assignedVehicleStartDate -->
          @if (teamMember.assignedVehicleStartDate) {
            <div class="flex sm:items-center">
              <mat-icon
                [svgIcon]="'heroicons_outline:calendar'"
              ></mat-icon>
              <div class="ml-6 leading-6">
                {{
                teamMember.assignedVehicleStartDate
                | date: "dd MMM, yyyy 'at' hh:mm a"
                }}
              </div>
            </div>
          }
          <!-- Emails -->
          @if (teamMember.email) {
            <div class="flex sm:items-center">
              <mat-icon
                [svgIcon]="'heroicons_outline:envelope'"
              ></mat-icon>
              <div class="ml-6 leading-6">
                {{ teamMember.email }}
              </div>
            </div>
          }
          <!-- Phone -->
          @if (teamMember.phone) {
            <div class="flex sm:items-center">
              <mat-icon
                [svgIcon]="'heroicons_outline:phone'"
              ></mat-icon>
              <div class="ml-6 leading-6">
                {{ teamMember.phone }}
              </div>
            </div>
          }
          <!-- employee Number -->
          @if (teamMember.employeeNumber) {
            <div class="flex sm:items-center">
              <mat-icon
                [svgIcon]="'heroicons_outline:briefcase'"
              ></mat-icon>
              <div class="ml-6 leading-6">
                {{ teamMember.employeeNumber }}
              </div>
            </div>
          }
          <!-- hourly Rate -->
          @if (teamMember.hourlyRate) {
            <div class="flex sm:items-center">
              <mat-icon
                svgIcon="heroicons_outline:currency-dollar"
              ></mat-icon>
              <div class="ml-6 leading-6">
                $ {{ teamMember.hourlyRate }}
              </div>
            </div>
          }
          <!-- Home Address -->
          @if (teamMember.homeAddresses) {
            <div class="flex sm:items-center">
              <mat-icon
                [svgIcon]="'heroicons_outline:map-pin'"
              ></mat-icon>
              <div class="ml-6 leading-6">
                {{ teamMember.homeAddresses }}
              </div>
            </div>
          }
          <!-- Notes -->
          @if (teamMember.notes) {
            <div class="flex">
              <mat-icon
                                [svgIcon]="
                                    'heroicons_outline:bars-3-bottom-left'
                                "
              ></mat-icon>
              <div
                class="max-w-none ml-6 prose prose-sm"
                [innerHTML]="teamMember.notes"
              ></div>
            </div>
          }
          <!-- Documents -->
          <div class="text-lg font-medium">Documents</div>
          <div class="grid grid-cols-4 gap-1 mt-4">
            @for (file of teamMember.attachedDocs; track file) {
              @switch (
                fileDetails(file).fileName.split(".").pop()
                ) {
                @case ("pdf") {
                  <div
                    class="group relative mt-2 h-full min-h-[100px]"
                    >
                    <div
                      class="absolute w-full h-full bg-gray-900/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg flex flex-col items-center justify-center space-y-2"
                      >
                      <a
                                                [href]="
                                                    fileDetails(file).fileUrl
                                                "
                        target="_blank"
                        class="inline-flex items-center justify-center rounded-full h-10 w-10 bg-white/30 hover:bg-white/50 focus:ring-4 focus:outline-none dark:text-white focus:ring-gray-50"
                        >
                        <i
                          class="fa-duotone fa-download icon-size-5 text-white"
                        ></i>
                      </a>
                      <i
                        class="text-xs font-thin text-ellipsis overflow-hidden text-white ml-2 mr-2"
                        >
                        {{ fileDetails(file).fileName }}
                      </i>
                    </div>
                    <div
                      class="flex items-center justify-center rounded-lg w-full h-full text-white font-bold bg-red-600"
                      >
                      PDF
                    </div>
                  </div>
                }
                @default {
                  <div
                    class="group relative mt-2 h-full min-h-[100px]"
                    >
                    <div
                      class="absolute w-full h-full bg-gray-900/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg flex items-center justify-center"
                      >
                      <a
                                                [href]="
                                                    fileDetails(file).fileUrl
                                                "
                        target="_blank"
                        class="inline-flex items-center justify-center rounded-full h-10 w-10 bg-white/30 hover:bg-white/50 focus:ring-4 focus:outline-none dark:text-white focus:ring-gray-50"
                        >
                        <i
                          class="fa-duotone fa-download icon-size-5 text-white"
                        ></i>
                      </a>
                    </div>
                    <img
                      class="rounded-lg w-full h-full object-cover"
                      [src]="previewFileUrl(file)"
                      />
                  </div>
                }
              }
              } @empty {
              <span>There are no items.</span>
            }
          </div>
        </div>
      </div>
    </div>
  }
  <!-- Edit mode -->
  @if (editMode) {
    <!-- Header -->
    <div
      class="relative w-full h-18 sm:h-20 bg-accent-700 dark:bg-accent-700"
      >
      <div
        class="flex items-center justify-end w-full max-w-3xl mx-auto pt-6"
        >
        <button
          mat-icon-button
          [matTooltip]="'Close'"
          (click)="cancelUpdateDetails()"
          >
          <mat-icon
            class="text-white"
            [svgIcon]="'heroicons_outline:x-mark'"
          ></mat-icon>
        </button>
      </div>
    </div>
    <!-- removeAvatar form -->
    <div
      class="relative flex flex-col flex-auto items-center px-6 sm:px-12"
      >
      <div class="w-full max-w-3xl">
        <form [formGroup]="memberDetailsForm">
          <!-- Avatar -->
          <div class="flex flex-auto items-end -mt-16">
            <div
              class="relative flex items-center justify-center w-32 h-32 rounded-full overflow-hidden ring-4 ring-bg-card"
              >
              <!-- Upload / Remove avatar -->
              <div
                class="absolute inset-0 bg-black bg-opacity-50 z-10"
              ></div>
              <div
                class="absolute inset-0 flex items-center justify-center z-20"
                >
                <div>
                  <input
                    id="avatar-file-input"
                    class="absolute h-0 w-0 opacity-0 invisible pointer-events-none"
                    type="file"
                    [multiple]="false"
                    [accept]="'image/jpeg, image/png'"
                                        (change)="
                                            uploadAvatar(avatarFileInput.files)
                                        "
                    #avatarFileInput
                    />
                  <label
                    class="flex items-center justify-center w-10 h-10 rounded-full cursor-pointer hover:bg-hover"
                    for="avatar-file-input"
                    matRipple
                    >
                    <mat-icon
                      class="text-white"
                                            [svgIcon]="
                                                'heroicons_outline:camera'
                                            "
                    ></mat-icon>
                  </label>
                </div>
                <div>
                  <button
                    mat-icon-button
                    (click)="removeAvatar()"
                    >
                    <mat-icon
                      class="text-white"
                                            [svgIcon]="
                                                'heroicons_outline:trash'
                                            "
                    ></mat-icon>
                  </button>
                </div>
              </div>
              <!-- Image/Letter -->
              @if (teamMember.avatar) {
                <img
                  class="object-cover w-full h-full"
                  [src]="teamMember.avatar"
                  />
              }
              @if (!teamMember.avatar && teamMember.name) {
                <div
                  class="flex items-center justify-center w-full h-full rounded overflow-hidden uppercase text-8xl font-bold leading-none bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                  >
                  {{ teamMember.name.charAt(0) }}
                </div>
              }
            </div>
          </div>
          <!-- Name -->
          <div class="mt-8">
            <mat-form-field
              class="w-full"
              [subscriptSizing]="'dynamic'"
              >
              <mat-label>Name</mat-label>
              <mat-icon
                matPrefix
                class="hidden sm:flex icon-size-5"
                [svgIcon]="'heroicons_solid:user-circle'"
              ></mat-icon>
              <input
                matInput
                [formControlName]="'name'"
                [placeholder]="'Name'"
                [spellcheck]="false"
                />
            </mat-form-field>
          </div>
          <!-- Driver License -->
          <div class="mt-8">
            <mat-form-field
              class="w-full"
              [subscriptSizing]="'dynamic'"
              >
              <mat-label>Driver License</mat-label>
              <i
                matPrefix
                class="hidden sm:flex icon-size-5 icon-size-5 fa-thin fa-id-card mr-5"
                [svgIcon]="'heroicons_solid:briefcase'"
              ></i>
              <input
                matInput
                [formControlName]="'driverLicense'"
                [placeholder]="'Driver License Number'"
                />
            </mat-form-field>
          </div>
          <!-- assignedVehicle -->
          <div class="mt-8">
            <mat-form-field
              class="w-full"
              [subscriptSizing]="'dynamic'"
              >
              <mat-label>Assigned Vehicle</mat-label>
              <mat-icon
                matPrefix
                class="hidden sm:flex icon-size-5"
                [svgIcon]="'mat_outline:emoji_transportation'"
              ></mat-icon>
              <input
                matInput
                [formControlName]="'assignedVehicle'"
                [placeholder]="'Assigned Vehicle'"
                />
            </mat-form-field>
          </div>
          <!-- assigned Vehicle Start Date -->
          <!-- <div class="mt-8">
          <mat-form-field
            class="w-full"
            [subscriptSizing]="'dynamic'"
            >
            <mat-label>Assigned Vehicle</mat-label>
            <mat-icon
              matPrefix
              class="hidden sm:flex icon-size-5"
              [svgIcon]="'mat_outline:emoji_transportation'"
            ></mat-icon>
            <input
              matInput
              [ngxMatDatetimePicker]="picker"
              [placeholder]="'Assigned Vehicle Start Date'"
              [formControlName]="'assignedVehicleStartDate'"
              />
            <ngx-mat-datepicker-toggle
              matSuffix
              [for]="picker"
            ></ngx-mat-datepicker-toggle>
            <ngx-mat-datetime-picker #picker>
            </ngx-mat-datetime-picker>
          </mat-form-field>
        </div> -->
        <div class="mt-8">
          <mat-form-field
            class="w-full"
            [subscriptSizing]="'dynamic'"
            >
            <mat-label>Assigned Vehicle</mat-label>
            <mat-icon
              matPrefix
              class="hidden sm:flex icon-size-5"
              [svgIcon]="'mat_outline:emoji_transportation'"
            ></mat-icon>
            <input
              matInput
              [matDatepicker]="picker"
              [placeholder]="'Assigned Vehicle Start Date'"
              [formControlName]="'assignedVehicleStartDate'"
              />
            <mat-datepicker-toggle matSuffix [for]="picker">
            </mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>
        </div>
        <!-- Phone numbers -->
        <div class="mt-8">
          <mat-label>Phone</mat-label>
          <mat-form-field
            class="w-full"
            [subscriptSizing]="'dynamic'"
            >
            <mat-icon
              matPrefix
              class="hidden sm:flex icon-size-5"
              [svgIcon]="'heroicons_solid:phone'"
            ></mat-icon>
            <input
              type="tel"
              mask="0000 000 000"
              matInput
              [formControlName]="'phone'"
              [placeholder]="'phone'"
              />
          </mat-form-field>
          <mat-label class="font-bold m-2"
            >Phone number *</mat-label
            >
            <!-- <ngx-material-intl-tel-input
            class="w-full"
            fieldControlName="phone"
            [required]="true"
            [autoIpLookup]="true"
            includeDialCode="true"
            textLabels="false"
            numberValidation="true"
            >
          </ngx-material-intl-tel-input> -->
        </div>
        <!-- Emails -->
        <div class="mt-8">
          <mat-form-field
            class="w-full"
            [subscriptSizing]="'dynamic'"
            >
            <mat-label>Email</mat-label>
            <mat-icon
              matPrefix
              class="hidden sm:flex icon-size-5"
              [svgIcon]="'heroicons_solid:envelope'"
            ></mat-icon>
            <input
              type="email"
              matInput
              [formControlName]="'email'"
              [placeholder]="'email'"
              />
          </mat-form-field>
        </div>
        <!-- Hourly Rate -->
        <div class="mt-8">
          <mat-form-field
            class="w-full"
            [subscriptSizing]="'dynamic'"
            >
            <mat-label>Hourly Rate</mat-label>
            <mat-icon
              matPrefix
              class="hidden sm:flex icon-size-5"
              [svgIcon]="'heroicons_outline:currency-dollar'"
            ></mat-icon>
            <input
              prefix="$ "
              mask="0*.00"
              class="text-right"
              matInput
              [formControlName]="'hourlyRate'"
              [placeholder]="'Hourly Rate'"
              (blur)="formatHourlyRate()"
              />
          </mat-form-field>
        </div>
        <!-- Address -->
        <div class="mt-8">
          <mat-form-field
            class="w-full"
            [subscriptSizing]="'dynamic'"
            >
            <mat-label>Address</mat-label>
            <mat-icon
              matPrefix
              class="hidden sm:flex icon-size-5"
              [svgIcon]="'heroicons_solid:map-pin'"
            ></mat-icon>
            <input
              matInput
              [formControlName]="'homeAddresses'"
              [spellcheck]="false"
              matMapsAutocomplete
              [country]="'au'"
              [placeholder]="'Enter your home address'"
                                (onAutocompleteSelected)="
                                    onAutocompleteSelected($event)
                                "
              />
          </mat-form-field>
        </div>
        <!--Groups -->
        <div class="mt-8">
          <mat-label>Assign Groups</mat-label>
          <div class="mt-2">
            <app-select-check-all-tree
              [data]="treeData"
              [selectedIds]="selectedGroupIds"
              (modelChange)="onGroupSelectChange($event)"
              >
            </app-select-check-all-tree>
          </div>
        </div>
        <!-- Attach Documents -->
        <div class="mt-8">
          <label class="font-medium">Attach Documents:</label>
          <file-input-dropzone
            [multiple]="true"
            (selectedFiles)="handleSelectedFiles($event)"
            [preloadedFiles]="preloadedFiles"
                            (removePreloadedFile)="
                                onRemovePreloadedFile($event)
                            "
            #fileInputDropzone
          ></file-input-dropzone>
        </div>
        <!-- Notes -->
        <div class="mt-8">
          <mat-form-field
            class="w-full"
            [subscriptSizing]="'dynamic'"
            >
            <mat-label>Notes</mat-label>
            <mat-icon
              matPrefix
              class="hidden sm:flex icon-size-5"
              [svgIcon]="'heroicons_solid:bars-3-bottom-left'"
            ></mat-icon>
            <textarea
              matInput
              [formControlName]="'notes'"
              [placeholder]="'Notes'"
              [rows]="5"
              [spellcheck]="false"
              cdkTextareaAutosize
            ></textarea>
          </mat-form-field>
        </div>
        <!-- Actions -->
        @if (isSaving) {
          <div class="flex justify-center p-10">
            <mat-spinner class="spinnr" [diameter]="24">
            </mat-spinner>
            <div class="ml-5 text-center">Saving...</div>
          </div>
        } @else {
          <div
            class="flex items-center mt-10 -mx-6 sm:-mx-12 py-4 pr-4 pl-1 sm:pr-12 sm:pl-7 border-t bg-gray-50 dark:bg-transparent"
            >
            <!-- Cancel -->
            <button
              class="ml-auto"
              mat-button
              [matTooltip]="'Cancel'"
              (click)="cancelUpdateDetails()"
              >
              Cancel
            </button>
            <!-- Save -->
            <button
              class="ml-2"
              mat-flat-button
              [color]="'primary'"
              [disabled]="memberDetailsForm.invalid"
              [matTooltip]="'Save'"
              (click)="updateDetails()"
              >
              Save
            </button>
          </div>
        }
      </form>
    </div>
  </div>
}
</div>

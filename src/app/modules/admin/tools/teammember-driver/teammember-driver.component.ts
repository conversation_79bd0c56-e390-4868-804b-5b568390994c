import { TextFieldModule } from '@angular/cdk/text-field';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDrawer, MatSidenavModule } from '@angular/material/sidenav';
import { FuseMediaWatcherService } from '@fuse/services/media-watcher';
import { MatDialog } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { Subject, catchError, forkJoin, takeUntil, throwError } from 'rxjs';
import { InviteMemberComponent } from './invite-member/invite-member.component';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MemberDetailsComponent } from './member-details/member-details.component';
import { TeamMembersService } from 'app/core/databaseModels/teammembers/teammembers.service';
import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';
import { FuseLoadingService } from '@fuse/services/loading';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { OrganisationsService } from 'app/core/databaseModels/organisations/organisations.service';
import { Organisation } from 'app/core/databaseModels/organisations/organisations.types';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Router } from '@angular/router';


@Component({
    selector: 'tools-teammember-driver',
    templateUrl: './teammember-driver.component.html',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [MemberDetailsComponent,
        MatPaginatorModule,
        MatSortModule,
        MatMenuModule,
        MatTableModule,
        MatSidenavModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        TextFieldModule,
        MatSelectModule,
        MatOptionModule,
        MatButtonModule,
        MatProgressSpinnerModule]
})
export class TeammemberDriverComponent implements OnInit, OnDestroy, AfterViewInit {

    @ViewChild(MatPaginator) paginator: MatPaginator;
    @ViewChild(MatSort) sort: MatSort;
    @ViewChild('matDrawer', { static: true }) matDrawer: MatDrawer;

    private _unsubscribeAll: Subject<any> = new Subject<any>();
    displayedColumns: string[] = ['avatar', 'name', 'email', 'phone', 'actions'];
    dataSource: MatTableDataSource<TeamMember>;

    drawerMode: 'side' | 'over';
    isLoading: boolean = false; // Initially, data is loading
    isDrawerOpen: boolean = false;

    teamMembers: TeamMember[] = [];
    selectedTeamMember: TeamMember// = <TeamMember>{};
    isNewItem: boolean = false;
    teamMemberCredits: number = 0;
    remainingCredits: number = 0;
    selectedOrganisations = {} as Organisation;
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseMediaWatcherService: FuseMediaWatcherService,
        private _matDialog: MatDialog,
        private _fuseLoadingService: FuseLoadingService,
        private _fuseConfirmationService: FuseConfirmationService,
        private _teamMembersService: TeamMembersService,
        private _organisationsService: OrganisationsService,
        private _router: Router,
    ) {




    }


    ngOnInit(): void {


        // Get initial data
        this.loadTeamMembers();

        this.drawerMode = 'over';
        this._fuseMediaWatcherService.onMediaQueryChange$('(min-width: 1440px)')
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((state) => {
                // Calculate the drawer mode
                this.drawerMode = 'over';
            });


    }


    ngAfterViewInit(): void {

    }
    loadTeamMembers() {
        this.isLoading = true;
        this._fuseLoadingService.show();

        this.dataSource = new MatTableDataSource<TeamMember>([]);
        this._teamMembersService.getTeamMembers()
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((teamMembers) => {
                this.teamMembers = teamMembers;
                // Assign the data to the data source for the table to render
                this.dataSource.data = this.teamMembers;

                this._fuseLoadingService.hide();
                if (this.teamMembers.length > 0) {
                    this.dataSource.paginator = this.paginator;
                    this.dataSource.sort = this.sort;
                }
                // Mark for check
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
                this.loadOrganisation();


            });
    }

    loadOrganisation() {

        this._fuseLoadingService.show();
        this._organisationsService.get()
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe(organisations => {
                this.selectedOrganisations = organisations;
                this.teamMemberCredits = this.selectedOrganisations.teamMemberCredits;
                this.remainingCredits = this.teamMemberCredits - this.teamMembers.length;

                this.isLoading = false;
                this._fuseLoadingService.hide();

                // Mark for check
                this._changeDetectorRef.markForCheck();
            });
    }


    applyFilter(event: Event) {
        const filterValue = (event.target as HTMLInputElement).value;
        this.dataSource.filter = filterValue.trim().toLowerCase();

        if (this.dataSource.paginator) {
            this.dataSource.paginator.firstPage();
        }
    }
    onBackdropClicked() {
        this.matDrawer.close();

    }


    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._teamMembersService.unsubscribe();
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    getApprovedStatus(approved: string): number {
        return this.teamMembers.filter(item => item.approved === approved).length;
    }

    previewTeamMemberFileUrl(fileId: string): string {
        if (fileId) {
            return this._teamMembersService.getFilePreview(fileId, 50, 50);
        }
    }

    //add new member function
    addNewMember() {

        this.selectedTeamMember = this.initializeToNull<TeamMember>();
        this.selectedTeamMember.$id = null;
        this._teamMembersService.getOrganisationID().subscribe(organisationID => {

            this.selectedTeamMember.organisationID = organisationID;
            this.selectedTeamMember.approved = 'Pending';
            this.isNewItem = true;

            //console.log(this.selectedTeamMember);
            //console.log(this.isNewItem);
            this.matDrawer.open();
        });

    }

    //the view details  function

    viewDetails(rowData: any) {
        // Mark for check
        this.isNewItem = false;
        // console.log(rowData);
        this.selectedTeamMember = rowData;
        this._changeDetectorRef.markForCheck();
        this.matDrawer.toggle();

    }


    //  the delete function
    deleteItem(rowData: any) {
        // Open the confirmation dialog
        const confirmation = this._fuseConfirmationService.open({
            title: 'Delete confirmation!',
            message: `Are you sure you want to delete <i class="text-semibold text-red-600">${rowData.name}</i>? This action cannot be undone!`,
            actions: {
                confirm: {
                    label: 'Delete',
                },
            },
            icon: {
                show: true,
                name: 'heroicons_outline:trash',
            },
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            if (result === 'confirmed') {
                this._fuseLoadingService.show();

                // Start the deletion process
                this._teamMembersService.deleteTeamMember(rowData.$id).subscribe({
                    next: () => {
                        // Delete the avatar image if it exists
                        if (rowData.avatarImageId) {
                            this._teamMembersService.deleteFile(rowData.avatarImageId).subscribe({
                                next: () => this.deleteAttachedDocs(rowData),
                                error: (err) => this.handleError(err),
                            });
                        } else {
                            this.deleteAttachedDocs(rowData);
                        }
                    },
                    error: (err) => this.handleError(err),
                });
            }
        });
    }

    // Function to delete attached documents
    private deleteAttachedDocs(rowData: any): void {
        if (rowData.attachedDocs && rowData.attachedDocs.length > 0) {
            const deletionObservables = rowData.attachedDocs.map((file: string) => {
                const fileObject = JSON.parse(file);
                return this._teamMembersService.deleteFile(fileObject.fileId);
            });

            forkJoin(deletionObservables).subscribe({
                next: () => this.finalizeDeletion(),
                error: (err) => this.handleError(err),
            });
        } else {
            this.finalizeDeletion();
        }
    }

    // Function to finalize the deletion process
    private finalizeDeletion(): void {
        this._fuseLoadingService.hide();
        this._changeDetectorRef.markForCheck();
        this._changeDetectorRef.detectChanges();
    }

    // Function to handle errors
    private handleError(error: any): void {
        console.error('Error during deletion:', error);
        this._fuseLoadingService.hide();
    }




    //Export table

    exportTable(exportType: string) {
        console.log(exportType);
    }
    /**
     * Open compose dialog
     */
    openComposeDialog(): void {
        // Open the dialog
        const dialogRef = this._matDialog.open(InviteMemberComponent);

        dialogRef.afterClosed()
            .subscribe((result) => {
                //  console.log(result);
                console.log('Compose dialog was closed!');
            });
    }

    onDrawerOpened() {
        this.isDrawerOpen = true;
    }

    onDrawerClosed() {
        this.isDrawerOpen = false;
    }
    closeDrawer(mode: string) {
        //  console.log(mode);
        this.matDrawer.close();
        this.loadTeamMembers();
        this._changeDetectorRef.markForCheck();
        this._changeDetectorRef.detectChanges();



    }
    //initializeToNull
    initializeToNull<T>(): T {
        const proxy = new Proxy({}, {
            get: () => null
        });
        // Create a new object with null values
        const obj = Object.assign({}, proxy);
        return obj as T;
    }

    fileDetails(file: string): void {

        return JSON.parse(file);
    }

    navigateToLicenses() {

        this._router.navigate(['/settings'], { queryParams: { panel: 'plan-billing' } });
    }

}

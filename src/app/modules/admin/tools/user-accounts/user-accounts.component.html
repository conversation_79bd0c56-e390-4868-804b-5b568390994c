<div class="absolute inset-0 top-20 flex flex-col min-w-0 overflow-hidden">
    <mat-drawer-container
        class="flex-auto h-full bg-card dark:bg-transparent"
        (backdropClick)="onBackdropClicked()"
        [hasBackdrop]="'true'"
    >
        <!-- Drawer -->
        <mat-drawer
            class="w-full sm:w-128 dark:bg-gray-900"
            [mode]="drawerMode"
            [opened]="isDrawerOpen"
            (opened)="onDrawerOpened()"
            (closed)="onDrawerClosed()"
            [position]="'end'"
            [disableClose]="true"
            #matDrawer
        >
            <!-- Drawer content -->
            @if (isDrawerOpen) {
                <user-details
                    [user]="selectedUser"
                    (closeDrawer)="closeDrawer()"
                    [newItem]="newItem"
                />
            }
        </mat-drawer>

        <mat-drawer-content class="flex flex-col">
            <!-- Main -->
            <!-- Header -->
            <div
                class="flex flex-col sm:flex-row items-start sm:items-center sm:justify-between py-8 px-6 md:px-8"
            >
                <!-- Title -->
                <div>
                    <div
                        class="text-4xl font-extrabold tracking-tight leading-none"
                    ></div>
                    <div class="ml-0.5 font-medium text-secondary">
                        <span>
                            Accounts Manage your Accounts from the list
                            below!</span
                        >
                    </div>
                </div>
                <!-- Actions -->
                <div class="mt-4 sm:mt-0">
                    <!-- Add section button -->
                    <button
                        mat-flat-button
                        [color]="'accent'"
                        (click)="addOrEditAccount()"
                    >
                        <i class="fa-duotone fa-plus"></i>
                        <span class="ml-2 mr-1">Add Accounts</span>
                    </button>
                </div>
            </div>
            <div class="flex-auto bg-gray-100 p-4">
                <!--Body-->
                <div class="w-full h-full mt-8">
                    <!-- Recent transactions table -->
                    <div
                        class="xl:col-span-2 flex flex-col flex-auto bg-card shadow rounded-2xl overflow-hidden"
                    >
                        <div
                            class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b"
                        >
                            <!-- Title -->
                            <div class="text-lg font-extrabold tracking-tight">
                                Member List
                                <div class="text-secondary font-medium">
                                    <span
                                        class="rounded-full bg-green-100 px-2 py-1 text-sm text-on-green-100 mr-1"
                                    >
                                        {{ getApprovedStatus("available") }}
                                        Available
                                    </span>
                                    <span
                                        class="rounded-full bg-primary-100 px-2 py-1 text-sm text-on-primary-100"
                                    >
                                        {{ getApprovedStatus("approved") }}
                                        Approved</span
                                    >
                                </div>
                            </div>
                            <!-- Actions -->
                            <div
                                class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4"
                            >
                                <!-- Search -->
                                <mat-form-field
                                    class="fuse-mat-dense fuse-mat-rounded min-w-64"
                                    [subscriptSizing]="'dynamic'"
                                >
                                    <mat-icon
                                        class="icon-size-5"
                                        matPrefix
                                        [svgIcon]="
                                            'heroicons_solid:magnifying-glass'
                                        "
                                    ></mat-icon>
                                    <input
                                        matInput
                                        (keyup)="applyFilter($event)"
                                        [autocomplete]="'off'"
                                        [placeholder]="'Search ...'"
                                        #input
                                    />
                                </mat-form-field>
                            </div>
                        </div>

                        <div class="overflow-x-auto mx-6">
                            <div class="w-full bg-transparent">
                                @if (users.length == 0) {
                                    @if (isLoading) {
                                        <div class="flex justify-center p-10">
                                            <mat-spinner
                                                class="spinnr"
                                                [diameter]="24"
                                            >
                                            </mat-spinner>
                                            <div class="ml-5 text-center">
                                                Loading...
                                            </div>
                                        </div>
                                    } @else {
                                        <div class="text-center">
                                            No data to show
                                        </div>
                                    }
                                } @else {
                                    <table
                                        mat-table
                                        [dataSource]="dataSource"
                                        matSort
                                    >
                                        <!-- Avatar Column -->
                                        <ng-container matColumnDef="avatar">
                                            <th
                                                mat-header-cell
                                                *matHeaderCellDef
                                            >
                                                Avatar
                                            </th>
                                            <td mat-cell *matCellDef="let row">
                                                <div
                                                    class="flex flex-0 items-center justify-center w-10 h-10 rounded-full overflow-hidden"
                                                >
                                                    @if (row.avatar) {
                                                        <img
                                                            class="object-cover w-full h-full"
                                                            [src]="row.avatar"
                                                            alt="row avatar"
                                                        />
                                                    } @else if (!row.avatar) {
                                                        <div
                                                            class="flex items-center justify-center w-full h-full rounded-full text-lg uppercase bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                                        >
                                                            {{
                                                                row.name.charAt(
                                                                    0
                                                                )
                                                            }}
                                                        </div>
                                                    }
                                                </div>
                                            </td>
                                        </ng-container>

                                        <!-- Name Column -->
                                        <ng-container matColumnDef="name">
                                            <th
                                                mat-header-cell
                                                *matHeaderCellDef
                                                mat-sort-header
                                            >
                                                Name
                                            </th>
                                            <td mat-cell *matCellDef="let row">
                                                {{ row.name }}
                                            </td>
                                        </ng-container>

                                        <!-- email Column -->
                                        <ng-container matColumnDef="email">
                                            <th
                                                mat-header-cell
                                                *matHeaderCellDef
                                                mat-sort-header
                                            >
                                                Email
                                            </th>
                                            <td mat-cell *matCellDef="let row">
                                                <div class="flex">
                                                    @if (!row.verifiedEmail) {
                                                        <div
                                                            class="rounded-full h-2 w-2 bg-red-600 mr-2 mt-2"
                                                        ></div>
                                                        <span
                                                            matTooltip="Unverified Email"
                                                        >
                                                            {{
                                                                row.email
                                                            }}</span
                                                        >
                                                    } @else {
                                                        {{ row.email }}
                                                    }
                                                </div>
                                            </td>
                                        </ng-container>
                                        <!-- phone Column -->
                                        <ng-container matColumnDef="phone">
                                            <th
                                                mat-header-cell
                                                *matHeaderCellDef
                                                mat-sort-header
                                            >
                                                Mobile Phone
                                            </th>
                                            <td mat-cell *matCellDef="let row">
                                                <div class="flex">
                                                    @if (!row.verifiedPhone) {
                                                        <div
                                                            class="rounded-full h-2 w-2 bg-red-600 mr-2 mt-2"
                                                        ></div>
                                                        <span
                                                            matTooltip="Unverified Phone Number"
                                                        >
                                                            {{
                                                                row.phone
                                                            }}</span
                                                        >
                                                    } @else {
                                                        {{ row.phone }}
                                                    }
                                                </div>
                                            </td>
                                        </ng-container>
                                        <!-- defaultUser -->
                                        <ng-container
                                            matColumnDef="defaultUser"
                                        >
                                            <th
                                                mat-header-cell
                                                mat-sort-header
                                                *matHeaderCellDef
                                            >
                                                Default User
                                            </th>
                                            <td mat-cell *matCellDef="let row">
                                                <span
                                                    class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-bold uppercase tracking-wide"
                                                    [ngClass]="{
                                                        'bg-red-200 text-red-800 dark:bg-red-600 dark:text-red-50':
                                                            row.defaultUser ===
                                                            false,
                                                        'bg-green-200 text-green-800 dark:bg-green-600 dark:text-green-50':
                                                            row.defaultUser ===
                                                            true
                                                    }"
                                                >
                                                    <span
                                                        class="whitespace-nowrap leading-relaxed"
                                                        >{{
                                                            row.defaultUser
                                                                ? "Yes"
                                                                : "No"
                                                        }}</span
                                                    >
                                                </span>
                                            </td>
                                        </ng-container>
                                        <!-- Actions Column -->
                                        <ng-container matColumnDef="actions">
                                            <th
                                                mat-header-cell
                                                *matHeaderCellDef
                                            >
                                                Actions
                                            </th>
                                            <td mat-cell *matCellDef="let row">
                                                <button
                                                    mat-icon-button
                                                    (click)="
                                                        addOrEditAccount(row)
                                                    "
                                                >
                                                    <mat-icon
                                                        [svgIcon]="
                                                            'heroicons_outline:pencil'
                                                        "
                                                    ></mat-icon>
                                                </button>
                                                <button
                                                    mat-icon-button
                                                    [matMenuTriggerFor]="
                                                        moreMenu
                                                    "
                                                >
                                                    <mat-icon
                                                        [svgIcon]="
                                                            'heroicons_outline:ellipsis-vertical'
                                                        "
                                                    ></mat-icon>
                                                </button>
                                                <mat-menu #moreMenu="matMenu">
                                                    @if (!row.verifiedEmail) {
                                                        <button
                                                            mat-menu-item
                                                            (click)="
                                                                resentVerificationEmail(
                                                                    row
                                                                )
                                                            "
                                                            [disabled]="
                                                                row.defaultUser
                                                            "
                                                        >
                                                            <mat-icon
                                                                [svgIcon]="
                                                                    'heroicons_outline:envelope'
                                                                "
                                                            ></mat-icon>
                                                            <span
                                                                >Resent
                                                                Verification
                                                                Email</span
                                                            >
                                                        </button>
                                                    }
                                                    <button
                                                        mat-menu-item
                                                        (click)="
                                                            setAsDefaultUser(
                                                                row
                                                            )
                                                        "
                                                        [disabled]="
                                                            row.defaultUser
                                                        "
                                                    >
                                                        <mat-icon
                                                            [svgIcon]="
                                                                'heroicons_outline:lock-closed'
                                                            "
                                                        ></mat-icon>
                                                        <span
                                                            >Set as
                                                            Default</span
                                                        >
                                                    </button>
                                                    <button
                                                        mat-menu-item
                                                        (click)="
                                                            deleteItem(row)
                                                        "
                                                        [disabled]="
                                                            row.defaultUser
                                                        "
                                                    >
                                                        <mat-icon
                                                            [svgIcon]="
                                                                'heroicons_outline:trash'
                                                            "
                                                        ></mat-icon>
                                                        <span>Delete</span>
                                                    </button>
                                                </mat-menu>
                                            </td>
                                        </ng-container>
                                        <tr
                                            mat-header-row
                                            *matHeaderRowDef="displayedColumns"
                                        ></tr>
                                        <tr
                                            mat-row
                                            *matRowDef="
                                                let row;
                                                columns: displayedColumns
                                            "
                                        ></tr>

                                        <!-- Row shown when there is no matching data. -->
                                        <tr class="mat-row" *matNoDataRow>
                                            <td class="mat-cell" colspan="4">
                                                No data matching the filter "{{
                                                    input.value
                                                }}"
                                            </td>
                                        </tr>
                                    </table>
                                }
                                <mat-paginator
                                    [pageSize]="20"
                                    [pageSizeOptions]="[5, 10, 20, 50, 100]"
                                    aria-label="Select page of users"
                                ></mat-paginator>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </mat-drawer-content>
    </mat-drawer-container>
</div>

import { TextFieldModule } from '@angular/cdk/text-field';
import { NgClass } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit, Signal, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatDrawer, MatSidenavModule } from '@angular/material/sidenav';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { FuseLoadingService } from '@fuse/services/loading';
import { FuseMediaWatcherService } from '@fuse/services/media-watcher';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { User } from 'app/core/databaseModels/user/user.types';
import { Subject, takeUntil } from 'rxjs';


import { UserDetailsComponent } from "../user-accounts/user-details/user-details.component";
import { Organisation } from 'app/core/databaseModels/organisations/organisations.types';
import { OrganisationsService } from 'app/core/databaseModels/organisations/organisations.service';
import { MatTooltipModule } from '@angular/material/tooltip';


@Component({
    selector: 'tools-user-accounts',
    templateUrl: './user-accounts.component.html',
    styleUrls: ['./user-accounts.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [MatPaginatorModule,
        MatSortModule,
        MatTableModule,
        MatSidenavModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        TextFieldModule,
        MatSelectModule,
        MatOptionModule,
        MatButtonModule,
        MatMenuModule,
        MatTooltipModule,
        MatProgressSpinnerModule,
        UserDetailsComponent,
        NgClass]
})
export class UserAccountsComponent implements OnInit, OnDestroy, AfterViewInit {
    @ViewChild('matDrawer', { static: true }) matDrawer: MatDrawer;

    @ViewChild(MatPaginator) paginator: MatPaginator;
    @ViewChild(MatSort) sort: MatSort;
    displayedColumns: string[] = ['avatar', 'name', 'email', 'phone', 'defaultUser', 'actions'];
    dataSource: MatTableDataSource<User>;

    users: User[] = [];
    newItem: boolean = false;
    isLoading: boolean = false;
    isDrawerOpen: boolean = false;


    selectedUser: User = {} as User;

    drawerMode: 'side' | 'over';
    drawerOpened: boolean;
    constructor(
        private _fuseMediaWatcherService: FuseMediaWatcherService,
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseLoadingService: FuseLoadingService,
        private _fuseConfirmationService: FuseConfirmationService,
        private _userService: UserService,
        private _organisationService: OrganisationsService,
    ) {

        this.drawerOpened = false;


        this.drawerMode = 'over';
        // Assign the data to the data source for the table to render
        this.dataSource = new MatTableDataSource(this.users);

    }
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    ngOnInit(): void {
        this.drawerMode = 'over';
        this._fuseMediaWatcherService.onMediaQueryChange$('(min-width: 1440px)')
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((state) => {
                // Calculate the drawer mode
                this.drawerMode = 'over';


            });

        this.loadUsers();


    }
    ngAfterViewInit(): void {
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
    }
    applyFilter(event: Event) {
        const filterValue = (event.target as HTMLInputElement).value;
        this.dataSource.filter = filterValue.trim().toLowerCase();

        if (this.dataSource.paginator) {
            this.dataSource.paginator.firstPage();
        }
    }
    onBackdropClicked() {
        this.matDrawer.close();

    }
    onDrawerOpened() {
        this.isDrawerOpen = true;
    }

    onDrawerClosed() {
        this.isDrawerOpen = false;
    }
    /**
     * Drawer opened changed
     *
     * @param opened
     */
    drawerOpenedChanged(opened: boolean): void {
        this.drawerOpened = opened;
    }

    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }


    loadUsers() {
        try {
            this.isLoading = true;
            this._fuseLoadingService.show();
            this._userService.getUsers().subscribe((users) => {
                this.isLoading = false;
                this._fuseLoadingService.hide();
                this.users = users;


                this.dataSource = new MatTableDataSource(this.users);
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();

            });


        } catch (error) {
            this.isLoading = false;
            this._fuseLoadingService.hide();
            console.log(error);
        }
    }


    deleteItem(rowData: any) {
        // Open the confirmation dialog

        const confirmation = this._fuseConfirmationService.open({
            title: 'Delete confirmation!',
            message: `Are you sure you want to delete <i class="text-semibold text-red-600">${rowData.name}</i>? This action cannot be undone!`,
            actions: {
                confirm: {
                    label: 'Delete',
                },
            },
            icon: {
                show: true,
                name: 'heroicons_outline:trash',
            },
        });
        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            this._fuseLoadingService.show();

            // If the confirm button pressed...
            if (result === 'confirmed') {
                this.isLoading = true;

                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
                if (rowData.avatarImageId) {

                    this._userService.deleteFile(rowData.avatarImageId).subscribe(result => {
                        this._userService.delete(rowData).subscribe(result => {
                            this.loadUsers();
                            this._fuseLoadingService.hide();
                            this._changeDetectorRef.markForCheck();
                            this._changeDetectorRef.detectChanges();
                        });
                    })

                } else {

                    this._userService.delete(rowData).subscribe(result => {
                        this.loadUsers();
                        this._fuseLoadingService.hide();
                        this._changeDetectorRef.markForCheck();
                        this._changeDetectorRef.detectChanges();
                    });
                }

            }
            else {
                this._fuseLoadingService.hide();

            }

        });
    }
    addOrEditAccount(rowData?: any) {

        if (rowData) {

            this.newItem = false;
            this.selectedUser = rowData;

            console.log(this.selectedUser);
            this._changeDetectorRef.markForCheck();
            this.matDrawer.toggle();
        } else {

            this.selectedUser = {} as User;

            this._organisationService.getOrganisationID().subscribe(organisationID => {

                this.selectedUser.organisationID = organisationID;
                this.selectedUser.$id = null;

                this.newItem = true;

                this.matDrawer.open();
            })



        }


    }

    getApprovedStatus(status: string) {
        if (status === 'available') {
            return this.users.length;
        } else if (status === 'approved') {
            return this.users.map(item => item.approved).filter(item => item === true).length;
        }
    }


    resentVerificationEmail(rowData: any) { }

    setAsDefaultUser(rowData: any) { }


    closeDrawer() {
        console.log('closeDrawer');
        this.matDrawer.close();
        this.isLoading = true;

        this._changeDetectorRef.markForCheck();
        this._changeDetectorRef.detectChanges();
        this.loadUsers();



    }

}

<div class="flex flex-col w-full">
    @if (user() && editMode == false) {
        <!-- View mode -->
        <!-- Header -->
        <div
            class="relative w-full h-18 sm:h-20 bg-accent-700 dark:bg-accent-700"
        >
            <!-- Close button -->
            <div
                class="flex items-center justify-end w-full max-w-3xl mx-auto pt-6"
            >
                <button
                    class="mr-5"
                    mat-icon-button
                    [matTooltip]="'Close'"
                    (click)="cancelUpdateDetails()"
                >
                    <mat-icon
                        class="text-white"
                        [svgIcon]="'heroicons_outline:x-mark'"
                    ></mat-icon>
                </button>
            </div>
        </div>
        <!--User details-->
        <div
            class="relative flex flex-col flex-auto items-center p-6 pt-0 sm:p-12 sm:pt-0"
        >
            <div class="w-full max-w-3xl">
                <!-- Avatar and actions -->
                <div class="flex flex-auto items-end -mt-16">
                    <!-- Avatar -->
                    <div
                        class="flex items-center justify-center w-32 h-32 rounded-full overflow-hidden ring-4 ring-bg-card"
                    >
                        <!-- User avatar -->
                        @if (user().avatar) {
                            <img
                                class="object-cover w-full h-full"
                                [src]="user().avatar"
                            />
                        } @else if (!user().avatar && user().name) {
                            <div
                                class="flex items-center justify-center w-full h-full rounded overflow-hidden uppercase text-8xl font-bold leading-none bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                            >
                                {{ user().name.charAt(0) }}
                            </div>
                        }
                    </div>
                    <!-- Actions -->
                    <div class="flex items-center ml-auto mb-1">
                        <button mat-stroked-button (click)="showEditMode()">
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_solid:pencil-square'"
                            ></mat-icon>
                            <span class="ml-2">Edit</span>
                        </button>
                    </div>
                </div>

                <!-- Name -->
                <div class="mt-3 text-4xl font-bold truncate">
                    {{ user().name }}
                </div>
                <div class="flex flex-col mt-4 pt-6 border-t space-y-8">
                    <!-- Emails -->
                    @if (user().email) {
                        <div class="flex sm:items-center">
                            <mat-icon
                                [svgIcon]="'heroicons_outline:envelope'"
                            ></mat-icon>
                            <div class="ml-6 leading-6">
                                {{ user().email }}
                                <span
                                    class="ml-2 font-bold h-2 w-2 rounded-full"
                                    [ngClass]="{
                                        'bg-green-500': user().emailVerified,
                                        'bg-red-500': !user().emailVerified
                                    }"
                                    >{{
                                        user().verifiedEmail
                                            ? "verified"
                                            : "unverified"
                                    }}</span
                                >
                            </div>
                        </div>
                    }

                    <!-- Phone -->
                    @if (user().phone) {
                        <div class="flex sm:items-center">
                            <mat-icon
                                [svgIcon]="'heroicons_outline:phone'"
                            ></mat-icon>
                            <div class="ml-6 leading-6">
                                {{ user().phone }}
                                <span
                                    class="ml-2 font-bold h-2 w-2 rounded-full"
                                    [ngClass]="{
                                        'bg-green-500': user().verifiedPhone,
                                        'bg-red-500': !user().verifiedPhone
                                    }"
                                    >{{
                                        user().verifiedPhone
                                            ? "verified"
                                            : "unverified"
                                    }}</span
                                >
                            </div>
                        </div>
                    }
                    <!--approved-->
                    @if (user().approved) {
                        <div class="flex sm:items-center">
                            <mat-icon
                                [svgIcon]="'heroicons_outline:check-circle'"
                            ></mat-icon>
                            <div class="ml-6 leading-6">
                                {{ user().approved ? "Approved" : "Pending" }}
                            </div>
                        </div>
                    }
                    <!--status-->
                    @if (user().status) {
                        <div class="flex sm:items-center">
                            <mat-icon
                                [svgIcon]="'heroicons_outline:check-circle'"
                            ></mat-icon>
                            <div class="ml-6 leading-6">
                                {{ user().status ? "Active" : "Inactive" }}
                            </div>
                        </div>
                    }
                    <!--defaultUser-->
                    @if (user().defaultUser) {
                        <div class="flex sm:items-center">
                            <mat-icon
                                [svgIcon]="'heroicons_outline:check-circle'"
                            ></mat-icon>
                            <div class="ml-6 leading-6">
                                {{ user().defaultUser ? "Yes" : "No" }}
                            </div>
                        </div>
                    }

                    <!-- Notes -->
                    @if (user().notes) {
                        <div class="flex">
                            <mat-icon
                                [svgIcon]="
                                    'heroicons_outline:bars-3-bottom-left'
                                "
                            ></mat-icon>
                            <div
                                class="max-w-none ml-6 prose prose-sm"
                                [innerHTML]="user().notes"
                            ></div>
                        </div>
                    }
                </div>
            </div>
        </div>
    } @else if (editMode) {
        <!-- Edit mode -->
        <!-- Header -->

        <div
            class="relative w-full h-18 sm:h-20 bg-accent-700 dark:bg-accent-700"
        >
            <div
                class="flex items-center justify-end w-full max-w-3xl mx-auto pt-6"
            >
                <button
                    class="mr-5"
                    mat-icon-button
                    [matTooltip]="'Close'"
                    (click)="cancelUpdateDetails()"
                >
                    <mat-icon
                        class="text-white"
                        [svgIcon]="'heroicons_outline:x-mark'"
                    ></mat-icon>
                </button>
            </div>
        </div>

        <!-- User details form -->
        <div
            class="relative flex flex-col flex-auto items-center px-6 sm:px-12"
        >
            <div class="w-full max-w-3xl">
                <form [formGroup]="userDetailsForm">
                    <!-- Avatar -->
                    <div class="flex flex-auto items-end -mt-16">
                        <div
                            class="relative flex items-center justify-center w-32 h-32 rounded-full overflow-hidden ring-4 ring-bg-card"
                        >
                            <!-- Upload / Remove avatar -->
                            <div
                                class="absolute inset-0 bg-black bg-opacity-50 z-10"
                            ></div>
                            <div
                                class="absolute inset-0 flex items-center justify-center z-20"
                            >
                                <div>
                                    <input
                                        id="avatar-file-input"
                                        class="absolute h-0 w-0 opacity-0 invisible pointer-events-none"
                                        type="file"
                                        [multiple]="false"
                                        [accept]="'image/jpeg, image/png'"
                                        (change)="
                                            uploadAvatar(avatarFileInput.files)
                                        "
                                        #avatarFileInput
                                    />
                                    <label
                                        class="flex items-center justify-center w-10 h-10 rounded-full cursor-pointer hover:bg-hover"
                                        for="avatar-file-input"
                                        matRipple
                                    >
                                        <mat-icon
                                            class="text-white"
                                            [svgIcon]="
                                                'heroicons_outline:camera'
                                            "
                                        ></mat-icon>
                                    </label>
                                </div>
                                <div>
                                    <button
                                        mat-icon-button
                                        (click)="removeAvatar()"
                                    >
                                        <mat-icon
                                            class="text-white"
                                            [svgIcon]="
                                                'heroicons_outline:trash'
                                            "
                                        ></mat-icon>
                                    </button>
                                </div>
                            </div>
                            <!-- Image/Letter -->

                            @if (user()) {
                                @if (user().avatar) {
                                    <img
                                        class="object-cover w-full h-full"
                                        [src]="user().avatar"
                                    />
                                }
                                @if (!user().avatar && user().name) {
                                    <div
                                        class="flex items-center justify-center w-full h-full rounded overflow-hidden uppercase text-8xl font-bold leading-none bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                    >
                                        {{ user().name.charAt(0) }}
                                    </div>
                                }
                            }
                        </div>
                    </div>
                    <!-- Name -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Full Name</mat-label>
                            <mat-icon
                                matPrefix
                                class="hidden sm:flex icon-size-5"
                                [svgIcon]="'heroicons_solid:user-circle'"
                            ></mat-icon>
                            <input
                                matInput
                                [formControlName]="'name'"
                                [placeholder]="'Name'"
                                [spellcheck]="false"
                            />
                        </mat-form-field>
                    </div>
                    <!-- Phone numbers -->

                    <div class="mt-8">
                        <mat-label class="font-bold m-2"
                            >Phone number *</mat-label
                        >
                        <ngx-material-intl-tel-input
                            class="w-full"
                            fieldControlName="phone"
                            [required]="true"
                            [autoIpLookup]="true"
                            includeDialCode="true"
                            textLabels="false"
                            numberValidation="true"
                        >
                        </ngx-material-intl-tel-input>
                        @if (
                            userDetailsForm.get("phone").hasError("phoneExists")
                        ) {
                            <mat-error>
                                Phone number already exists.
                            </mat-error>
                        }
                    </div>

                    <!-- Emails -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Email</mat-label>
                            <mat-icon
                                matPrefix
                                class="hidden sm:flex icon-size-5"
                                [svgIcon]="'heroicons_solid:envelope'"
                            ></mat-icon>
                            <input
                                type="email"
                                matInput
                                [formControlName]="'email'"
                                [placeholder]="'email'"
                                [spellcheck]="false"
                            />
                            @if (
                                userDetailsForm
                                    .get("email")
                                    .hasError("emailExists")
                            ) {
                                <mat-error> Email already exists. </mat-error>
                            }
                        </mat-form-field>
                    </div>
                    <!--Approved -->

                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Approved</mat-label>
                            <mat-icon
                                matPrefix
                                class="hidden sm:flex icon-size-5"
                                [svgIcon]="'heroicons_solid:check-circle'"
                            ></mat-icon>
                            <mat-slide-toggle
                                [formControlName]="'approved'"
                                [color]="'primary'"
                                (change)="toggleUserApproved()"
                            >
                                <span class="ml-2">{{
                                    user().approved ? "Approved" : "Pending"
                                }}</span>
                            </mat-slide-toggle>
                            <input matInput #value hidden />
                        </mat-form-field>
                    </div>

                    <!-- Status-->

                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Status</mat-label>
                            <mat-icon
                                matPrefix
                                class="hidden sm:flex icon-size-5"
                                [svgIcon]="'heroicons_solid:identification'"
                            ></mat-icon>
                            <mat-slide-toggle
                                [formControlName]="'status'"
                                [color]="'primary'"
                                (change)="toggleUserStatus()"
                            >
                                <span class="ml-2">{{
                                    user().status ? "Active" : "Inactive"
                                }}</span>
                            </mat-slide-toggle>
                            <input matInput #value hidden />
                        </mat-form-field>
                    </div>
                    <!-- Notes -->
                    <div class="mt-8">
                        <mat-form-field
                            class="w-full"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Notes</mat-label>
                            <mat-icon
                                matPrefix
                                class="hidden sm:flex icon-size-5"
                                [svgIcon]="'heroicons_solid:bars-3-bottom-left'"
                            ></mat-icon>
                            <textarea
                                matInput
                                [formControlName]="'notes'"
                                [placeholder]="'Notes'"
                                [rows]="5"
                                [spellcheck]="false"
                                cdkTextareaAutosize
                            ></textarea>
                        </mat-form-field>
                    </div>

                    <!-- Actions -->
                    @if (isSaving) {
                        <div class="flex justify-center p-10">
                            <mat-spinner class="spinnr" [diameter]="24">
                            </mat-spinner>
                            <div class="ml-5 text-center">Saving...</div>
                        </div>
                    } @else {
                        <div
                            class="flex items-center mt-10 -mx-6 sm:-mx-12 py-4 pr-4 pl-1 sm:pr-12 sm:pl-7 border-t bg-gray-50 dark:bg-transparent"
                        >
                            <!-- Cancel -->
                            <button
                                class="ml-auto"
                                mat-button
                                [matTooltip]="'Cancel'"
                                (click)="cancelUpdateDetails()"
                            >
                                Cancel
                            </button>
                            <!-- Save -->
                            <button
                                class="ml-2"
                                mat-flat-button
                                [color]="'primary'"
                                [disabled]="userDetailsForm.invalid"
                                [matTooltip]="'Save'"
                                (click)="updateDetails()"
                            >
                                Save
                            </button>
                        </div>
                    }
                </form>
            </div>
        </div>
    }
</div>

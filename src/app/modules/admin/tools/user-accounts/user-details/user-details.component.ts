import { TextFieldModule } from '@angular/cdk/text-field';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit, OutputEmitterRef, input, viewChild, output, ElementRef, ChangeDetectorRef } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatRippleModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltip } from '@angular/material/tooltip';
import { FuseLoadingService } from '@fuse/services/loading';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { User } from 'app/core/databaseModels/user/user.types';
import { provideNgxMask } from 'ngx-mask';
import { NgxMaterialIntlTelInputComponent } from 'ngx-material-intl-tel-input';
import { Subject } from 'rxjs';

@Component({
    selector: 'user-details',
    imports: [
        MatIcon,
        MatButton,
        MatTooltip,
        FormsModule,
        ReactiveFormsModule,
        MatRippleModule,
        MatFormFieldModule,
        MatInputModule,
        MatCheckboxModule,
        TextFieldModule,
        MatSlideToggleModule,
        MatProgressSpinnerModule,
        NgxMaterialIntlTelInputComponent,
    ],
    providers: [provideNgxMask()],
    templateUrl: './user-details.component.html',
    styleUrl: './user-details.component.scss'
})
export class UserDetailsComponent implements OnInit, OnDestroy {

    user = input<User>();
    newItem = input<boolean>(false);


    public closeDrawer: OutputEmitterRef<void> = output();

    private _unsubscribeAll: Subject<any> = new Subject<any>();
    private _avatarFileInput = viewChild<ElementRef<HTMLInputElement>>('avatarFileInput');

    selectedImage: any;
    userDetailsForm: UntypedFormGroup;
    editMode: boolean = false;
    isSaving: boolean = false;
    emailChanged: boolean = false;
    phoneChanged: boolean = false;

    _organisationID = '';

    constructor(
        private _formBuilder: UntypedFormBuilder,
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseLoadingService: FuseLoadingService,
        private _userService: UserService,
    ) { }
    ngOnInit(): void {

        this._userService.getOrganisationID().subscribe(organisationID => {
            this._organisationID = organisationID;
        })



        this.userDetailsForm = this._formBuilder.group({
            $id: [''],
            organisationID: [''],
            authId: [''],
            name: ['', [Validators.required]],
            email: ['', [Validators.required, Validators.email]],
            phone: ['', [Validators.required]],
            avatar: [''],
            avatarImageId: [''],
            approved: [''],
            defaultUser: [false],
            userOnlineStatus: [''],
            notes: [''],
            status: [true],
            verifiedPhone: [false],
            verifiedEmail: [false],
        });


        // Add listeners for email and phone changes
        this.userDetailsForm.get('email').valueChanges.subscribe(value => {
            if (this.user() && this.user().email !== value) {
                this.checkExistEmail(value);
            } else {
                this.userDetailsForm.get('email').setErrors(null);
            }
        });

        this.userDetailsForm.get('phone').valueChanges.subscribe(value => {
            if (this.user() && this.user().phone !== value) {
                this.checkExistPhone(value);
            } else {
                this.userDetailsForm.get('phone').setErrors(null);
            }
        });

        if (this.newItem()) {

            this.showEditMode();
        }
    }
    ngOnDestroy(): void {

        this.editMode = false;
        this.userDetailsForm.reset();
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    cancelUpdateDetails() {
        this.editMode = false;
        this.closeDrawer.emit();
    }
    showEditMode() {


        this.editMode = true;
        if (this.newItem()) {


            this.userDetailsForm.reset();
            this._changeDetectorRef.markForCheck();
        } else {
            this.userDetailsForm.patchValue(this.user());
            this._changeDetectorRef.markForCheck();
        }


    }


    removeAvatar(): void {
        // Get the form control for 'avatar'
        const avatarFormControl = this.userDetailsForm.get('avatar');

        // Set the avatar as null
        avatarFormControl.setValue(null);

        // Set the file input value as null
        this._avatarFileInput().nativeElement.value = null;

        // Update the contact
        this.user().avatar = null;
        this.selectedImage = null;


    }

    uploadAvatar(fileList: FileList): void {
        // Return if canceled
        if (!fileList.length) {
            return;
        }

        const allowedTypes = ['image/jpeg', 'image/png'];
        const file = fileList[0];

        // Return if the file is not allowed
        if (!allowedTypes.includes(file.type)) {
            return;
        }

        // update the avatar url from file
        this.user().avatar = URL.createObjectURL(file);

        this.selectedImage = file;
    }

    toggleUserStatus() {
        this.user().status = !this.user().status;
    }
    toggleUserApproved() {
        this.user().approved = !this.user().approved;
    }

    checkExistEmail(email: string) {
        if (email == null) {
            return false;
        }
        this._userService.checkExistingEmail(email.trim()).subscribe(result => {
            if (result) {
                this.userDetailsForm.get('email').setErrors({ emailExists: true });
            } else {
                this.userDetailsForm.get('email').setErrors(null);
            }
        });
    }

    checkExistPhone(phone: string) {
        if (phone == null) {
            return false;
        }
        this._userService.checkExistingPhone(`+${phone.replace(/\D/g, '')}`).subscribe(result => {
            if (result) {
                this.userDetailsForm.get('phone').setErrors({ phoneExists: true });
            } else {
                this.userDetailsForm.get('phone').setErrors(null);
            }
        });
    }

    updateDetails() {
        const oldUserData = this.user();
        const newUserData = this.userDetailsForm.getRawValue();
        newUserData.phone = `+${newUserData.phone.replace(/\D/g, '')}`;
        if (oldUserData.email != newUserData.email) {
            this.user().verifiedEmail = false;
            newUserData.verifiedEmail = false;
            this.emailChanged = true;
        }
        if (oldUserData.phone != newUserData.phone) {
            this.user().verifiedPhone = false;
            newUserData.verifiedPhone = false;
            this.phoneChanged = true;
        }
        this.isSaving = true;
        if (newUserData.$id == null && this.newItem()) {
            this.saveNew(newUserData);
        } else {
            newUserData.organisationID = this._organisationID;

            this.saveEdited(newUserData);
        }
    }
    saveEdited(newUserData: User) {



        try {
            this._fuseLoadingService.show();
            newUserData.organisationID = this._organisationID;
            if (this.selectedImage) {
                let _oldImageID = newUserData.avatarImageId;
                this._userService.uploadFile(this.selectedImage).subscribe(
                    result => {
                        newUserData.avatar = result.fileUrl;
                        newUserData.avatarImageId = result.fileId;
                        this._userService.update(newUserData, this.emailChanged, this.phoneChanged).subscribe(result => {
                            this._fuseLoadingService.hide();
                            // Mark for check
                            this._changeDetectorRef.markForCheck();
                            this._changeDetectorRef.detectChanges();
                            this._userService.deleteFile(_oldImageID).subscribe(result => {

                                this.isSaving = false;
                                this.closeDrawer.emit();
                                this._changeDetectorRef.markForCheck();
                                this._changeDetectorRef.detectChanges();
                            });
                        })
                    })
            } else {
                this._userService.update(newUserData, this.emailChanged, this.phoneChanged).subscribe(result => {
                    this._fuseLoadingService.hide();
                    // Mark for check

                    this.isSaving = false;
                    this.closeDrawer.emit();
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();

                })
            }
        } catch (error) {
            console.error('Error saving user:', error.message);
            this.isSaving = false;

        }

    }
    saveNew(newUserData: User) {
        this._fuseLoadingService.show();
        try {
            newUserData.organisationID = this._organisationID;
            if (this.selectedImage != null) {
                this._userService.uploadFile(this.selectedImage).subscribe(
                    result => {
                        newUserData.avatar = result.fileUrl;
                        newUserData.avatarImageId = result.fileId;
                        this._userService.create(newUserData).subscribe(result => {
                            this._fuseLoadingService.hide();
                            // Mark for check
                            // call createRecovery for new user to create new password
                            this._userService.createRecovery(result.email).subscribe(result => {

                                this.isSaving = false;
                                this._changeDetectorRef.markForCheck();
                                this._changeDetectorRef.detectChanges();
                                this.closeDrawer.emit();
                            })

                        })
                    })
            } else {
                this._userService.create(newUserData).subscribe(result => {
                    this._fuseLoadingService.hide();
                    this.userDetailsForm.reset();
                    this.editMode = false;
                    // Mark for check
                    this._userService.createRecovery(result.email).subscribe(result => {

                        this.isSaving = false;
                        this._changeDetectorRef.markForCheck();
                        this._changeDetectorRef.detectChanges();
                        this.closeDrawer.emit();
                    })
                })
            }

        } catch (error) {
            console.error('Error saving user:', error);
            this.isSaving = false;

        }
    }



}

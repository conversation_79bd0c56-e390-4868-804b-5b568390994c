import { TextFieldModule } from '@angular/cdk/text-field';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { Mat<PERSON>rawer, MatSidenavModule } from '@angular/material/sidenav';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { FuseMediaWatcherService } from '@fuse/services/media-watcher';
import { Subject, of, switchMap, takeUntil } from 'rxjs';
import { FuseLoadingService } from '@fuse/services/loading';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { AssetsService } from 'app/core/databaseModels/assets/assets.service';
import { Assets } from 'app/core/databaseModels/assets/assets.types';
import { AssetsDetailsComponent } from './assets-details/assets-details.component';
import { MatMenuModule } from '@angular/material/menu';

import { QrPrintComponent } from './qr-print/qr-print.component';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';


@Component({
    selector: 'tools-assets',
    templateUrl: './assets.component.html',
    styleUrls: ['./assets.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [AssetsDetailsComponent,
        MatPaginatorModule,
        MatMenuModule,
        MatSortModule,
        MatTableModule,
        MatSidenavModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        TextFieldModule,
        MatSelectModule,
        MatOptionModule,
        MatButtonModule,
        MatProgressSpinnerModule]
})

export class AssetsComponent implements OnInit, OnDestroy, AfterViewInit {
    @ViewChild('matDrawer', { static: true }) matDrawer: MatDrawer;

    @ViewChild(MatPaginator) paginator: MatPaginator;
    @ViewChild(MatSort) sort: MatSort;
    displayedColumns: string[] = ['imageURL', 'assetName', 'tagData', 'currentAddress', 'actions'];
    dataSource: MatTableDataSource<Assets>;


    drawerMode: 'side' | 'over';
    isLoading: boolean = false; // Initially, data is loading
    isDrawerOpen: boolean = false;
    drawerOpened: boolean;
    private _unsubscribeAll: Subject<any> = new Subject<any>();

    assets: Assets[];
    selectedAsset: Assets;
    isNewItem: boolean = false;
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseMediaWatcherService: FuseMediaWatcherService,
        private _matDialog: MatDialog,
        private _fuseLoadingService: FuseLoadingService,
        private _fuseConfirmationService: FuseConfirmationService,
        private _assetsService: AssetsService,

    ) {

        this.drawerOpened = false;


    }

    ngOnInit(): void {
        this.drawerMode = 'over';
        this.isLoading = true;
        this._fuseLoadingService.show();

        this._assetsService.listenToRealTimeData();
        this.dataSource = new MatTableDataSource<Assets>([]);

        // Get the assets data
        this._assetsService.getAssets()
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe(assets => {
                this.assets = assets;
                this.dataSource.data = assets;
                this._fuseLoadingService.hide();
                this.isLoading = false;
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
            });




        this._fuseMediaWatcherService.onMediaQueryChange$('(min-width: 1440px)')
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((state) => {
                // Calculate the drawer mode
                this.drawerMode = 'over';


            });

    }
    ngAfterViewInit(): void {
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
    }
    applyFilter(event: Event) {
        const filterValue = (event.target as HTMLInputElement).value;
        this.dataSource.filter = filterValue.trim().toLowerCase();

        if (this.dataSource.paginator) {
            this.dataSource.paginator.firstPage();
        }
    }
    onBackdropClicked() {
        this.matDrawer.close();

    }
    /**
     * Drawer opened changed
     *
     * @param opened
     */
    drawerOpenedChanged(opened: boolean): void {
        this.drawerOpened = opened;
    }

    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._assetsService.unsubscribe();
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    addNewAsset() {
        this.selectedAsset = this.initializeToNull<Assets>();
        this.selectedAsset.$id = null;
        this._assetsService.getOrganisationID().subscribe(organisationID => {

            this.selectedAsset.organisationID = organisationID;

            this.isNewItem = true;
            this.matDrawer.open();
        })
    }

    viewDetails(rowData: any) {
        // Mark for check
        this.isNewItem = false;
        // console.log(rowData);
        this.selectedAsset = rowData;
        this._changeDetectorRef.markForCheck();
        this.matDrawer.toggle();

    }


    //  the delete function
    deleteItem(rowData: any) {
        // Open the confirmation dialog
        const confirmation = this._fuseConfirmationService.open({
            title: 'Delete confirmation!',
            message: `Are you sure you want to delete <i class="text-semibold text-red-600">${rowData.assetName}</i>? This action cannot be undone!`,
            actions: {
                confirm: {
                    label: 'Delete',
                },
            },
            icon: {
                show: true,
                name: 'heroicons_outline:trash',
            },
        });
        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            this._fuseLoadingService.show();
            // If the confirm button pressed...
            if (result === 'confirmed') {

                this._assetsService.deleteAsset(rowData.$id).subscribe(result => {

                    this._assetsService.deleteFile(rowData.avatarImageId).subscribe(result => {
                        this._fuseLoadingService.hide();

                        // Mark for check
                        this._changeDetectorRef.markForCheck();
                        this._changeDetectorRef.detectChanges();
                        //  console.log('Deleting item:', rowData);

                    });

                })


            } else { this._fuseLoadingService.hide(); }

        });

    }

    onDrawerOpened() {
        this.isDrawerOpen = true;
    }

    onDrawerClosed() {
        this.isDrawerOpen = false;
    }
    closeDrawer(mode: string) {
        //  console.log(mode);
        this.matDrawer.close();
        this._changeDetectorRef.markForCheck();
        this._changeDetectorRef.detectChanges();



    }
    //initializeToNull
    initializeToNull<T>(): T {
        const proxy = new Proxy({}, {
            get: () => null
        });
        // Create a new object with null values
        const obj = Object.assign({}, proxy);
        return obj as T;
    }

    qrGenerator() {
        // create list of assets QR


        const dialogRef = this._matDialog.open(QrPrintComponent);

        dialogRef.afterClosed()
            .subscribe((result) => {
                //  console.log(result);
                console.log('Compose dialog was closed!');
            });
    }




}

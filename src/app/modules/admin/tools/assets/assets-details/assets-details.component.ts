import { TextFieldModule } from '@angular/cdk/text-field';
import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, Renderer2, TemplateRef, ViewChild, ViewContainerRef, ViewEncapsulation, importProvidersFrom } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormArray, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule, MatRippleModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FuseLoadingService } from '@fuse/services/loading';
import { provideNgxMask } from 'ngx-mask';
import { Subject } from 'rxjs';

import { Assets } from 'app/core/databaseModels/assets/assets.types';
import { AssetsService } from 'app/core/databaseModels/assets/assets.service';
import { MatMapsAutocompleteModule } from 'app/modules/widgets/mat-maps-autocomplete/mat-maps-autocomplete.module';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NgxQrcodeStylingComponent, Options } from "ngx-qrcode-styling";

@Component({
    selector: 'assets-details',
    templateUrl: './assets-details.component.html',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    exportAs: 'assets-details',
    imports: [
        MatButtonModule,
        MatTooltipModule,
        MatIconModule,
        FormsModule,
        ReactiveFormsModule,
        MatRippleModule,
        MatFormFieldModule,
        MatInputModule,
        MatCheckboxModule,
        MatSelectModule,
        MatOptionModule,
        MatDatepickerModule,
        TextFieldModule,
        DatePipe,
        MatMapsAutocompleteModule,
        MatProgressSpinnerModule,
        NgxQrcodeStylingComponent,
    ],
    providers: [provideNgxMask()]
})
export class AssetsDetailsComponent implements OnInit, OnDestroy {
    @Input({ required: true }) asset: Assets | null = null;
    @Input() newItem: boolean | null = false;
    @Output() closeDrawer = new EventEmitter<string>();
    @ViewChild('avatarFileInput') private _avatarFileInput: ElementRef;
    private _unsubscribeAll: Subject<any> = new Subject<any>();

    //images/logo/logo.svg

    editMode: boolean = false;
    isSaving: boolean = false;
    selectedImage: any;
    assetsDetailsForm: UntypedFormGroup;
    qrConfig: Options;
    constructor(private _formBuilder: UntypedFormBuilder,
        private _changeDetectorRef: ChangeDetectorRef,
        private _assetsService: AssetsService,
        private _fuseLoadingService: FuseLoadingService,) {

    }
    ngOnInit(): void {

        this.assetsDetailsForm = this._formBuilder.group({
            $id: [this.asset?.$id],
            organisationID: [this.asset?.organisationID],
            groupIDs: [this.asset?.groupIDs],
            assetName: [this.asset?.assetName, [Validators.required]],
            make: [this.asset?.make],
            model: [this.asset?.model],
            tagData: [this.asset?.tagData, [Validators.required]],
            currentLocation: [this.asset?.currentLocation],
            locationDate: [this.asset?.locationDate],
            currentAddress: [this.asset?.currentAddress],
            relatedJobId: [this.asset?.relatedJobId],
            note: [this.asset?.note],
            imageURL: [this.asset?.imageURL],
            status: [this.asset?.status],

        });
        if (this.asset.tagData) {
            this.qrConfig = {
                width: 250,
                height: 250,
                data: this.asset.tagData,
                margin: 0,
                qrOptions: {
                    typeNumber: 0,
                    mode: "Byte",
                    errorCorrectionLevel: "Q"
                },
                imageOptions: {
                    hideBackgroundDots: true,
                    imageSize: 0.4,
                    margin: 0
                },
                dotsOptions: {
                    type: "rounded",
                    color: "#4e397f",
                    gradient: null
                },
                backgroundOptions: {
                    color: "#ffffff"
                },
                image: 'images/logo/logo.svg',

                cornersSquareOptions: {
                    type: "extra-rounded",
                    color: "#000000"
                },

                cornersDotOptions: {
                    type: "square",
                    color: "#000000"
                },


            }
        }
        if (this.newItem) {

            this.toggleEditMode(true);
        } else { this.toggleEditMode(false); }

    }
    ngOnDestroy(): void {
        this.editMode = false;
        this.newItem = false;
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }
    showEditMode() {
        this.newItem = false;

        this.toggleEditMode(true);
    }

    toggleEditMode(mode: boolean) {

        this.editMode = mode;
        this.assetsDetailsForm.patchValue(this.asset);
        // Mark for check
        this._changeDetectorRef.markForCheck();


    }
    onDateChange(selectedDate: any) {

        if (this.editMode) {
            // console.log(selectedDate)
            this.assetsDetailsForm.get('locationDate').setValue(selectedDate.startDate.format())
        }
    }
    cancelUpdateDetails() {
        this.closeDrawer.emit('cancel');
    }

    updateDetails() {
        const asset = this.assetsDetailsForm.getRawValue();
        // console.log(teamMember);


        if (asset.$id == null && this.newItem == true) {
            this.saveNew(asset);
        } else {
            this.saveEdited(asset);
        }
    }


    private saveNew(asset: Assets) {
        this._fuseLoadingService.show();
        this.isSaving = true;
        // console.log(teamMember);
        if (!this.selectedImage) {


            this._assetsService.createAsset(asset).subscribe(result => {

                this._fuseLoadingService.hide();
                // Mark for check
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
                this.closeDrawer.emit('newITEM');
                this.isSaving = false;
            });
        } else {
            this._assetsService.uploadFile(this.selectedImage).subscribe(
                result => {

                    // console.log('File ID:', result.fileId);
                    // console.log('File URL:', result.fileUrl);
                    asset.imageId = result.fileId;
                    asset.imageURL = result.fileUrl;

                    this._assetsService.createAsset(asset).subscribe(result => {

                        this._fuseLoadingService.hide();
                        // Mark for check
                        this._changeDetectorRef.markForCheck();
                        this._changeDetectorRef.detectChanges();
                        this.closeDrawer.emit('newITEM');
                        this.isSaving = false;
                    });

                }, error => {
                    console.error('Error uploading file:', error);
                }
            )
        }
    }

    private saveEdited(asset: Assets) {
        this.isSaving = true;
        this._fuseLoadingService.show();
        //console.log(teamMember);
        if (this.selectedImage) {
            //First Upload Image and Get Url then update
            let _oldImageID = asset.imageId
            this._assetsService.uploadFile(this.selectedImage).subscribe(result => {
                asset.imageURL = result.fileUrl;
                asset.imageId = result.fileId;

                this._assetsService.updateAsset(asset.$id, asset).subscribe(result => {
                    this._fuseLoadingService.hide();
                    this.isSaving = false;
                    // Mark for check
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();
                    //then delete old image after upload new image and save change
                    this._assetsService.deleteFile(_oldImageID).subscribe(result => {
                        this.closeDrawer.emit('update');
                    });

                });

            });

        } else {

            this._assetsService.updateAsset(asset.$id, asset).subscribe(result => {
                this._fuseLoadingService.hide();
                // Mark for check
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
                this.closeDrawer.emit('update');
                this.isSaving = false;
            });
        }

    }

    uploadAvatar(fileList: FileList): void {
        // Return if canceled
        if (!fileList.length) {
            return;
        }

        const allowedTypes = ['image/jpeg', 'image/png'];
        const file = fileList[0];

        // Return if the file is not allowed
        if (!allowedTypes.includes(file.type)) {
            return;
        }

        // update the avatar url from file
        this.asset.imageURL = URL.createObjectURL(file);

        this.selectedImage = file;
    }

    /**
     * Remove the avatar
     */
    removeAvatar(): void {
        // Get the form control for 'avatar'
        const avatarFormControl = this.assetsDetailsForm.get('imageURL');

        // Set the avatar as null
        avatarFormControl.setValue(null);

        // Set the file input value as null
        this._avatarFileInput.nativeElement.value = null;

        // Update the contact
        this.asset.imageURL = null;
        this.selectedImage = null;


    }
    onAutocompleteSelected(location: any) {
        this.assetsDetailsForm.controls.currentAddress.setValue(location.address);
        this.assetsDetailsForm.controls.currentLocation.setValue(location.latLon);
    }

    onDownload(qrcode: any): void {
        qrcode.download(`${this.asset.tagData}.png`).subscribe((res: any) => {
            // TO DO something!
            console.log('download:', res);
        });
    }

}

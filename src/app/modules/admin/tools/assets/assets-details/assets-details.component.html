<div class="flex flex-col w-full">
  <!-- View mode -->
  @if (!editMode) {
    <!-- Header -->
    <div
      class="relative w-full h-18 sm:h-20 bg-accent-700 dark:bg-accent-700"
      >
      <!-- Close button -->
      <div
        class="flex items-center justify-end w-full max-w-3xl mx-auto pt-6"
        >
        <button
          mat-icon-button
          [matTooltip]="'Close'"
          (click)="cancelUpdateDetails()"
          >
          <mat-icon
            class="text-white"
            [svgIcon]="'heroicons_outline:x-mark'"
          ></mat-icon>
        </button>
      </div>
    </div>
    <!-- Asset -->
    <div
      class="relative flex flex-col flex-auto items-center p-6 pt-0 sm:p-12 sm:pt-0"
      >
      <div class="w-full max-w-3xl">
        <!-- Avatar and actions -->
        <div class="flex flex-auto items-end -mt-16">
          <!-- Avatar -->
          <div
            class="flex items-center justify-center w-32 h-32 rounded-full overflow-hidden ring-4 ring-bg-card"
            >
            @if (asset.imageURL) {
              <img
                class="object-cover w-full h-full"
                [src]="asset.imageURL"
                />
            }
            @if (!asset.imageURL && asset.assetName) {
              <div
                class="flex items-center justify-center w-full h-full rounded overflow-hidden uppercase text-8xl font-bold leading-none bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                >
                {{ asset.assetName.charAt(0) }}
              </div>
            }
          </div>
          <!-- Actions -->
          <div class="flex items-center ml-auto mb-1">
            <button mat-stroked-button (click)="showEditMode()">
              <mat-icon
                class="icon-size-5"
                [svgIcon]="'heroicons_solid:pencil-square'"
              ></mat-icon>
              <span class="ml-2">Edit</span>
            </button>
          </div>
        </div>
        <!-- Name -->
        <div class="mt-3 text-4xl font-bold truncate">
          {{ asset.assetName }}
        </div>
        <div class="flex flex-col mt-4 pt-6 border-t space-y-8">
          <!-- make -->
          @if (asset.make) {
            <div class="flex sm:items-center">
              <i class="icon-size-5 fa-duotone fa-box-taped"></i>
              <div class="ml-6">
                {{ asset.make ? asset.make : "N/A" }}
              </div>
            </div>
          }
          <!-- model -->
          @if (asset.model) {
            <div class="flex sm:items-center">
              <i class="fa-duotone fa-shelves"></i>
              <div class="ml-6">
                {{ asset.model ? asset.model : "N/A" }}
              </div>
            </div>
          }
          <!--  locationDate -->
          @if (asset.locationDate) {
            <div class="flex sm:items-center">
              <mat-icon
                [svgIcon]="'heroicons_outline:calendar'"
              ></mat-icon>
              <div class="ml-6 leading-6">
                {{
                asset.locationDate
                | date: "dd MMM, yyyy 'at' hh:mm a"
                }}
              </div>
            </div>
          }
          <!-- tagData -->
          @if (asset.tagData) {
            <div class="flex sm:items-center">
              <i class="fa-duotone fa-qrcode icon-size-5"></i>
              <div class="ml-6 leading-6">
                {{ asset.tagData }}
              </div>
            </div>
          }
          <!-- Phone -->
          @if (asset.phone) {
            <div class="flex sm:items-center">
              <mat-icon
                [svgIcon]="'heroicons_outline:phone'"
              ></mat-icon>
              <div class="ml-6 leading-6">
                {{ asset.phone }}
              </div>
            </div>
          }
          <!-- currentAddress -->
          @if (asset.currentLocation) {
            <div class="flex sm:items-center">
              <mat-icon
                [svgIcon]="'heroicons_outline:map-pin'"
              ></mat-icon>
              <div class="ml-6 leading-6">
                {{ asset.currentAddress }}
              </div>
            </div>
          }
          <!-- note -->
          @if (asset.note) {
            <div class="flex">
              <mat-icon
                                [svgIcon]="
                                    'heroicons_outline:bars-3-bottom-left'
                                "
              ></mat-icon>
              <div
                class="max-w-none ml-6 prose prose-sm"
                [innerHTML]="asset.note"
              ></div>
            </div>
          }
          @if (asset.tagData) {
            <div class="flex">
              <ngx-qrcode-styling
                #qrCode
                [config]="qrConfig"
              ></ngx-qrcode-styling>
            </div>
            <button
              mat-flat-button
              [color]="'accent'"
              (click)="onDownload(qrCode)"
              >
              <i class="fa-duotone fa-plus"></i>
              <span class="ml-2 mr-1">Download QR/TAG</span>
            </button>
          }
        </div>
      </div>
    </div>
  }
  <!-- Edit mode -->
  @if (editMode) {
    <!-- Header -->
    <div
      class="relative w-full h-18 sm:h-20 bg-accent-700 dark:bg-accent-700"
      >
      <div
        class="flex items-center justify-end w-full max-w-3xl mx-auto pt-6"
        >
        <button
          mat-icon-button
          [matTooltip]="'Close'"
          (click)="cancelUpdateDetails()"
          >
          <mat-icon
            class="text-white"
            [svgIcon]="'heroicons_outline:x-mark'"
          ></mat-icon>
        </button>
      </div>
    </div>
    <!-- removeAvatar form -->
    <div
      class="relative flex flex-col flex-auto items-center px-6 sm:px-12"
      >
      <div class="w-full max-w-3xl">
        <form [formGroup]="assetsDetailsForm">
          <!-- Avatar -->
          <div class="flex flex-auto items-end -mt-16">
            <div
              class="relative flex items-center justify-center w-32 h-32 rounded-full overflow-hidden ring-4 ring-bg-card"
              >
              <!-- Upload / Remove avatar -->
              <div
                class="absolute inset-0 bg-black bg-opacity-50 z-10"
              ></div>
              <div
                class="absolute inset-0 flex items-center justify-center z-20"
                >
                <div>
                  <input
                    id="avatar-file-input"
                    class="absolute h-0 w-0 opacity-0 invisible pointer-events-none"
                    type="file"
                    [multiple]="false"
                    [accept]="'image/jpeg, image/png'"
                                        (change)="
                                            uploadAvatar(avatarFileInput.files)
                                        "
                    #avatarFileInput
                    />
                  <label
                    class="flex items-center justify-center w-10 h-10 rounded-full cursor-pointer hover:bg-hover"
                    for="avatar-file-input"
                    matRipple
                    >
                    <mat-icon
                      class="text-white"
                                            [svgIcon]="
                                                'heroicons_outline:camera'
                                            "
                    ></mat-icon>
                  </label>
                </div>
                <div>
                  <button
                    mat-icon-button
                    (click)="removeAvatar()"
                    >
                    <mat-icon
                      class="text-white"
                                            [svgIcon]="
                                                'heroicons_outline:trash'
                                            "
                    ></mat-icon>
                  </button>
                </div>
              </div>
              <!-- Image/Letter -->
              @if (asset.imageURL) {
                <img
                  class="object-cover w-full h-full"
                  [src]="asset.imageURL"
                  />
              }
              @if (!asset.imageURL && asset.assetName) {
                <div
                  class="flex items-center justify-center w-full h-full rounded overflow-hidden uppercase text-8xl font-bold leading-none bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                  >
                  {{ asset.assetName.charAt(0) }}
                </div>
              }
            </div>
          </div>
          <!-- assetName -->
          <div class="mt-8">
            <mat-form-field
              class="w-full"
              [subscriptSizing]="'dynamic'"
              >
              <mat-label>Asset Name</mat-label>
              <i
                matPrefix
                class="hidden sm:flex icon-size-5 fa-duotone fa-hexagon-check mr-4"
              ></i>
              <input
                matInput
                [formControlName]="'assetName'"
                [placeholder]="'assetName'"
                [spellcheck]="false"
                />
            </mat-form-field>
          </div>
          <!-- Make -->
          <div class="mt-8">
            <mat-form-field
              class="w-full"
              [subscriptSizing]="'dynamic'"
              >
              <mat-label>Make</mat-label>
              <i
                matPrefix
                class="hidden sm:flex icon-size-5 icon-size-5 fa-duotone fa-box-taped mr-4"
                [svgIcon]="'heroicons_solid:briefcase'"
              ></i>
              <input
                matInput
                [formControlName]="'make'"
                [placeholder]="'Make'"
                />
            </mat-form-field>
          </div>
          <!-- model -->
          <div class="mt-8">
            <mat-form-field
              class="w-full"
              [subscriptSizing]="'dynamic'"
              >
              <mat-label>Model</mat-label>
              <i
                matPrefix
                class="hidden sm:flex icon-size-5 fa-duotone fa-shelves mr-4"
              ></i>
              <input
                matInput
                [formControlName]="'model'"
                [placeholder]="'Model'"
                />
            </mat-form-field>
          </div>
          <div class="mt-8">
            <mat-form-field
              class="w-full"
              [subscriptSizing]="'dynamic'"
              >
              <mat-label>location Date</mat-label>
              <mat-icon
                matPrefix
                class="hidden sm:flex icon-size-5"
                [svgIcon]="'mat_outline:emoji_transportation'"
              ></mat-icon>
              <input
                matInput
                [matDatepicker]="picker"
                [placeholder]="'Assigned Start Date'"
                [formControlName]="'locationDate'"
                />
              <mat-datepicker-toggle matSuffix [for]="picker">
              </mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </div>
          <!-- tagData -->
          <div class="mt-8">
            <mat-form-field
              class="w-full"
              [subscriptSizing]="'dynamic'"
              >
              <mat-label>QR Data</mat-label>
              <i
                matPrefix
                class="hidden sm:flex icon-size-5 fa-duotone fa-qrcode mr-4"
              ></i>
              <input
                type="text"
                matInput
                [formControlName]="'tagData'"
                [placeholder]="'QR DATA'"
                />
            </mat-form-field>
          </div>
          <!-- currentAddress -->
          <div class="mt-8">
            <mat-form-field
              class="w-full"
              [subscriptSizing]="'dynamic'"
              >
              <mat-label>Current Address</mat-label>
              <mat-icon
                matPrefix
                class="hidden sm:flex icon-size-5"
                [svgIcon]="'heroicons_solid:map-pin'"
              ></mat-icon>
              <input
                matInput
                [formControlName]="'currentAddress'"
                [placeholder]="'home Addresses'"
                [spellcheck]="false"
                matMapsAutocomplete
                [country]="'au'"
                [placeholder]="'Enter address'"
                                (onAutocompleteSelected)="
                                    onAutocompleteSelected($event)
                                "
                />
            </mat-form-field>
          </div>
          <!-- note -->
          <div class="mt-8">
            <mat-form-field
              class="w-full"
              [subscriptSizing]="'dynamic'"
              >
              <mat-label>Note</mat-label>
              <mat-icon
                matPrefix
                class="hidden sm:flex icon-size-5"
                [svgIcon]="'heroicons_solid:bars-3-bottom-left'"
              ></mat-icon>
              <textarea
                matInput
                [formControlName]="'note'"
                [placeholder]="'note'"
                [rows]="5"
                [spellcheck]="false"
                cdkTextareaAutosize
              ></textarea>
            </mat-form-field>
          </div>
          <!-- Actions -->
          @if (isSaving) {
            <div
              class="flex justify-center items-center mt-10 -mx-6 sm:-mx-12 py-4 pr-4 pl-1 sm:pr-12 sm:pl-7 border-t bg-gray-50 dark:bg-transparent"
              >
              <mat-progress-spinner
                [diameter]="24"
                [mode]="'indeterminate'"
              ></mat-progress-spinner>
            </div>
          } @else {
            <div
              class="flex items-center mt-10 -mx-6 sm:-mx-12 py-4 pr-4 pl-1 sm:pr-12 sm:pl-7 border-t bg-gray-50 dark:bg-transparent"
              >
              <!-- Cancel -->
              <button
                class="ml-auto"
                mat-button
                [matTooltip]="'Cancel'"
                (click)="cancelUpdateDetails()"
                >
                Cancel
              </button>
              <!-- Save -->
              <button
                class="ml-2"
                mat-flat-button
                [color]="'primary'"
                [disabled]="assetsDetailsForm.invalid"
                [matTooltip]="'Save'"
                (click)="updateDetails()"
                >
                Save
              </button>
            </div>
          }
        </form>
      </div>
    </div>
  }
</div>

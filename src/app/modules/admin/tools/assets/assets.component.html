<div class="absolute inset-0 top-20 flex flex-col min-w-0 overflow-hidden">
  <mat-drawer-container
    class="flex-auto h-full bg-card dark:bg-transparent"
    (backdropClick)="onBackdropClicked()"
    [hasBackdrop]="'true'"
    >
    <!-- Drawer -->
    <mat-drawer
      class="w-full sm:w-128 dark:bg-gray-900"
      [mode]="drawerMode"
      [opened]="isDrawerOpen"
      (opened)="onDrawerOpened()"
      (closed)="onDrawerClosed()"
      [position]="'end'"
      [disableClose]="true"
      #matDrawer
      >
      <!-- Drawer content -->
      <!-- Drawer content -->
      @if (isDrawerOpen) {
        <div>
          <assets-details
            [asset]="selectedAsset"
            [newItem]="isNewItem"
            (closeDrawer)="closeDrawer($event)"
            />
        </div>
      }
    </mat-drawer>

    <mat-drawer-content class="flex flex-col">
      <!-- Main -->
      <!-- Header -->
      <div
        class="flex flex-col sm:flex-row items-start sm:items-center sm:justify-between py-8 px-6 md:px-8"
        >
        <!-- Title -->
        <div>
          <div
            class="text-4xl font-extrabold tracking-tight leading-none"
          ></div>
          <div class="ml-0.5 font-medium text-secondary">
            <span
              >Manage your Assets/QR/TAG from the list
              below!</span
              >
            </div>
          </div>
          <!-- Actions -->
          <div class="mt-4 sm:mt-0">
            <!-- Add section button -->
            <button
              mat-flat-button
              [color]="'accent'"
              (click)="addNewAsset()"
              >
              <i class="fa-duotone fa-plus"></i>
              <span class="ml-2 mr-1">Add Assets/QR/TAG</span>
            </button>

            <button
              mat-flat-button
              [color]="'primary'"
              (click)="qrGenerator()"
              class="ml-3"
              >
              <i class="fa-duotone fa-solid fa-qrcode"></i>

              <span class="ml-2 mr-1">Print QR List</span>
            </button>
          </div>
        </div>
        @if (assets && assets.length > 0) {
          <div class="flex-auto bg-gray-100 p-4">
            <!--Body-->
            <div class="w-full h-full mt-8">
              <!-- Recent transactions table -->
              <div
                class="xl:col-span-2 flex flex-col flex-auto bg-card shadow rounded-2xl overflow-hidden"
                >
                <div
                  class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b"
                  >
                  <!-- Title -->
                  <div
                    class="text-lg font-extrabold tracking-tight"
                    >
                    Assets/QR/TAG List
                    <div class="text-secondary font-medium">
                      1 pending, 4 approved
                    </div>
                  </div>
                  <!-- Actions -->
                  <div
                    class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4"
                    >
                    <!-- Search -->
                    <mat-form-field
                      class="fuse-mat-dense fuse-mat-rounded min-w-64"
                      [subscriptSizing]="'dynamic'"
                      >
                      <mat-icon
                        class="icon-size-5"
                        matPrefix
                                            [svgIcon]="
                                                'heroicons_solid:magnifying-glass'
                                            "
                      ></mat-icon>
                      <input
                        matInput
                        (keyup)="applyFilter($event)"
                        [autocomplete]="'off'"
                        [placeholder]="'Search ...'"
                        #input
                        />
                    </mat-form-field>
                  </div>
                </div>

                <div class="overflow-x-auto mx-6">
                  <div class="w-full bg-transparent">
                    <table
                      mat-table
                      [dataSource]="dataSource"
                      matSort
                      >
                      <!-- ID Column -->
                      <ng-container matColumnDef="id">
                        <th
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                          >
                          ID
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.id }}
                        </td>
                      </ng-container>

                      <!-- Avatar Column -->
                      <ng-container matColumnDef="imageURL">
                        <th
                          mat-header-cell
                          *matHeaderCellDef
                          >
                          Image
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <div
                            class="flex flex-0 items-center justify-center w-10 h-10 rounded-full overflow-hidden"
                            >
                            @if (row.imageURL) {
                              <img
                                class="object-cover w-full h-full"
                                [src]="row.imageURL"
                                alt="row asset"
                                />
                            }
                            @if (!row.imageURL) {
                              <div
                                class="flex items-center justify-center w-full h-full rounded-full text-lg uppercase bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                >
                                {{
                                row.assetName.charAt(
                                0
                                )
                                }}
                              </div>
                            }
                          </div>
                        </td>
                      </ng-container>

                      <!-- Name Column -->
                      <ng-container matColumnDef="assetName">
                        <th
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                          >
                          Asset Name
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.assetName }}
                        </td>
                      </ng-container>

                      <!-- tagData Column -->
                      <ng-container matColumnDef="tagData">
                        <th
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                          >
                          QR Data
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.tagData }}
                        </td>
                      </ng-container>

                      <!-- Current Address Column -->
                      <ng-container
                        matColumnDef="currentAddress"
                        >
                        <th
                          mat-header-cell
                          *matHeaderCellDef
                          mat-sort-header
                          >
                          Current Address
                        </th>
                        <td mat-cell *matCellDef="let row">
                          {{ row.currentAddress }}
                        </td>
                      </ng-container>
                      <!-- Actions Column -->
                      <ng-container matColumnDef="actions">
                        <th
                          mat-header-cell
                          *matHeaderCellDef
                          >
                          Actions
                        </th>
                        <td mat-cell *matCellDef="let row">
                          <button
                            mat-icon-button
                            (click)="viewDetails(row)"
                            >
                            <mat-icon
                                                        [svgIcon]="
                                                            'heroicons_outline:pencil'
                                                        "
                            ></mat-icon>
                          </button>
                          <button
                            mat-icon-button
                                                    [matMenuTriggerFor]="
                                                        moreMenu
                                                    "
                            >
                            <mat-icon
                                                        [svgIcon]="
                                                            'heroicons_outline:ellipsis-vertical'
                                                        "
                            ></mat-icon>
                          </button>
                          <mat-menu #moreMenu="matMenu">
                            <button
                              mat-menu-item
                                                        (click)="
                                                            deleteItem(row)
                                                        "
                              >
                              <mat-icon
                                                            [svgIcon]="
                                                                'heroicons_outline:trash'
                                                            "
                              ></mat-icon>
                              <span>Delete</span>
                            </button>
                          </mat-menu>
                        </td>
                      </ng-container>
                      <tr
                        mat-header-row
                        *matHeaderRowDef="displayedColumns"
                      ></tr>
                      <tr
                        mat-row
                                            *matRowDef="
                                                let row;
                                                columns: displayedColumns
                                            "
                      ></tr>

                      <!-- Row shown when there is no matching data. -->
                      <tr class="mat-row" *matNoDataRow>
                        <td class="mat-cell" colspan="4">
                          No data matching the filter "{{
                          input.value
                          }}"
                        </td>
                      </tr>
                    </table>

                    <mat-paginator
                      [pageSize]="20"
                      [pageSizeOptions]="[5, 10, 20, 50, 100]"
                      aria-label="Select page of users"
                    ></mat-paginator>
                  </div>
                </div>
              </div>
            </div>
          </div>
        } @else {
          <div
            class="flex flex-auto flex-col items-center justify-center bg-gray-100 dark:bg-transparent"
            >
            <i class="icon-size-24 fa-duotone fa-qrcode"></i>
            <div
              class="mt-4 text-2xl font-semibold tracking-tight text-secondary"
              >
              @if (isLoading) {
                <div class="flex justify-center p-10">
                  <mat-spinner class="spinnr" [diameter]="24">
                  </mat-spinner>
                  <div class="ml-5 text-center">Loading...</div>
                </div>
              } @else {
                Add a Assets/QR/TAG to start locating!
              }
            </div>
          </div>
        }
      </mat-drawer-content>
    </mat-drawer-container>
  </div>

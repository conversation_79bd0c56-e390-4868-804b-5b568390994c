<div class="flex flex-col max-w-240 md:min-w-160 max-h-screen -m-6">
    <!-- Header -->
    <div
        class="flex flex-0 items-center justify-between h-16 pr-3 sm:pr-5 pl-6 sm:pl-8 bg-primary text-on-primary"
    >
        <div class="text-lg font-medium">Print Qr Code</div>
        <button mat-icon-button (click)="closeDialog()" [tabIndex]="-1">
            <mat-icon
                class="text-current"
                [svgIcon]="'heroicons_outline:x-mark'"
            ></mat-icon>
        </button>
    </div>

    <!-- Compose form -->
    <div class="flex flex-col flex-auto p-6 sm:p-8 overflow-y-auto">
        <!-- QR Per Page Selection -->
        <mat-form-field appearance="fill">
            <mat-label>QR Codes Per Page</mat-label>
            <mat-select [(ngModel)]="qrPerPage" required>
                @for (option of qrPerPageOptions; track option) {
                    <mat-option [value]="option">
                        {{ option }}
                    </mat-option>
                }
            </mat-select>
        </mat-form-field>

        <!-- Generate Button -->
        <button
            mat-raised-button
            color="primary"
            (click)="qrGenerator(qrPerPage)"
            [disabled]="isSaving"
        >
            @if (isSaving) {
                <mat-progress-spinner
                    [diameter]="24"
                    [mode]="'indeterminate'"
                ></mat-progress-spinner>
            } @else {
                Generate QR Codes
            }
        </button>
    </div>
</div>

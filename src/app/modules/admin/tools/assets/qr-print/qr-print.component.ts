import { Component, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { NgxQrcodeStylingService, Options } from 'ngx-qrcode-styling';
import jsPDF from 'jspdf';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { AssetsService } from 'app/core/databaseModels/assets/assets.service';
import { Assets } from 'app/core/databaseModels/assets/assets.types';

@Component({
    selector: 'app-qr-print',
    imports: [MatButtonModule,
        MatIconModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatOptionModule,
        MatProgressSpinnerModule,
    ],
    templateUrl: './qr-print.component.html',
    styleUrl: './qr-print.component.scss',
    providers: [NgxQrcodeStylingService]
})
export class QrPrintComponent implements OnInit {
    isSaving: boolean = false;

    // Available options for QR codes per page
    qrPerPageOptions = [1, 2, 4, 8];

    // Model to hold the selected value
    qrPerPage = 1;  // Default value

    constructor(
        public matDialogRef: MatDialogRef<QrPrintComponent>,
        private qrCodeService: NgxQrcodeStylingService,
        private _assetsService: AssetsService,

    ) { }
    ngOnInit(): void {

    }

    closeDialog(): void {

        // Close the dialog
        this.matDialogRef.close();
    }

    qrGenerator(qrPerPage: number): void {
        // Create list of assets QR
        this.isSaving = true;

        // Get the list of assets
        this._assetsService.getAssets().subscribe(assets => {

            // convert assets to QR codes
            if (assets.length > 0) {
                const qrList = assets.map(asset => {
                    return { name: asset.assetName, data: asset.tagData };
                });

                // Call the function to generate QR code PDF
                this.saveQRPDF(qrList, qrPerPage).then(
                    () => {
                        //console.log('done');
                        this.isSaving = false;
                        this.closeDialog();
                    }
                );

            } else {
                this.isSaving = false;
                this.closeDialog();
            }

        });



    }



    async saveQRPDF(qrData: { name: string; data: string }[], qrPerPage: number = 1): Promise<void> {
        const doc = new jsPDF('portrait', 'mm', 'a4');
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        const padding = 10;

        let qrSize;
        let scalingFactor;

        if (qrPerPage === 1) {
            qrSize = Math.min(pageWidth - 2 * padding, pageHeight - 2 * padding);
            scalingFactor = 1.8; // Larger for single QR code
        } else if (qrPerPage === 2) {
            qrSize = (pageHeight - 3 * padding) / 2; // Half-page height for each QR code
            scalingFactor = 2.6; // Scale to fit half the page vertically
        } else if (qrPerPage <= 4) {
            qrSize = Math.min(pageWidth / 2 - 2 * padding, pageHeight / Math.ceil(qrPerPage / 2) - 2 * padding);
            scalingFactor = 4; // Moderate for 3-4 QR codes
        } else if (qrPerPage <= 8) {
            qrSize = Math.min(pageWidth / 2 - 2 * padding, pageHeight / Math.ceil(qrPerPage / 2) - 2 * padding);
            scalingFactor = 6.3; // Smaller for 5-8 QR codes
        } else {
            qrSize = Math.min(pageWidth / 2 - 2 * padding, pageHeight / Math.ceil(qrPerPage / 2) - 2 * padding);
            scalingFactor = 9; // Smallest for 9-10 QR codes
        }

        for (let index = 0; index < qrData.length; index++) {
            const { name, data } = qrData[index];
            const container = document.createElement('div');
            container.style.width = `${qrSize}px`;
            container.style.height = `${qrSize}px`;
            document.body.appendChild(container);

            const config: Options = {
                width: qrSize * scalingFactor,
                height: qrSize * scalingFactor,
                image: 'images/logo/logo.svg',
                data,
                margin: 0,
                qrOptions: {
                    typeNumber: 0,
                    mode: "Byte",
                    errorCorrectionLevel: "Q"
                },
                cornersSquareOptions: {
                    type: "extra-rounded",
                    color: "#000000"
                },
                imageOptions: {
                    hideBackgroundDots: true,
                    imageSize: 0.4,
                    margin: 0
                },
                dotsOptions: {
                    type: "rounded",
                    color: "#4e397f",
                    gradient: null
                },
                cornersDotOptions: {
                    type: "square",
                    color: "#000000"
                },
                frameOptions: {
                    style: 'FE_155',
                    height: qrSize * 5,
                    width: qrSize * 5,
                    x: 25,
                    y: 45,
                    contents: [{
                        stroke: '#009688'
                    }],
                    containers: [{
                        fill: '#009688'
                    }],
                    texts: [{
                        textContent: name,
                        fill: '#FFF',
                        x: '16%',
                        y: '87%',
                        fontSize: '35pt',
                        fontFamily: `'Poppins', sans-serif`,
                        fontWeight: 'bold'
                    }]
                },
            };

            await new Promise((resolve) => {
                this.qrCodeService.create(config, container).subscribe(async () => {
                    try {
                        setTimeout(async () => {
                            const canvas = container.querySelector('canvas');
                            if (canvas) {
                                const imgData = canvas.toDataURL('image/png');

                                let x, y;
                                if (qrPerPage === 1) {
                                    x = (pageWidth - qrSize) / 2;
                                    y = (pageHeight - qrSize) / 2;
                                } else if (qrPerPage === 2) {
                                    // Special layout for 2 QR codes, one at the top and one at the bottom
                                    x = (pageWidth - qrSize) / 2;
                                    y = index % 2 === 0 ? padding : (pageHeight / 2) + padding;
                                } else {
                                    const row = Math.floor((index % qrPerPage) / 2);
                                    const col = index % 2;
                                    x = col * (pageWidth / 2) + padding;
                                    y = row * (pageHeight / Math.ceil(qrPerPage / 2)) + padding;
                                }

                                doc.addImage(imgData, 'PNG', x, y, qrSize, qrSize);

                                document.body.removeChild(container);
                                resolve(null);
                            }
                        }, 1000); // Delay to ensure rendering
                    } catch (error) {
                        console.error('Error capturing QR code:', error);
                        resolve(null);
                    }
                });
            });

            if ((index + 1) % qrPerPage === 0 && index + 1 !== qrData.length) {
                doc.addPage();
            }
        }

        doc.save('qrcodes.pdf');
    }


}

<div class="flex flex-col flex-auto w-full">
    <div class="flex flex-wrap w-full max-w-screen-xl mx-auto p-6 md:p-8">
        <!-- Title and action buttons -->
        <div class="flex items-center justify-between w-full">
            <div class="flex items-center ml-6">
                <button
                    class="hidden sm:inline-flex"
                    mat-flat-button
                    [color]="'accent'"
                    (click)="showEdit('new')"
                >
                    <i
                        class="icon-size-5 mr-5 fa-duotone fa-solid fa-list-tree"
                    ></i>
                    <span class="ml-2">Add New Group</span>
                </button>

                <!-- Actions menu (visible on xs) -->
                <div class="sm:hidden">
                    <button [matMenuTriggerFor]="actionsMenu" mat-icon-button>
                        <mat-icon
                            [svgIcon]="'heroicons_mini:ellipsis-vertical'"
                        ></mat-icon>
                    </button>
                    <mat-menu #actionsMenu="matMenu">
                        <button mat-menu-item>Add new expense</button>
                        <button mat-menu-item>Reports</button>
                        <button mat-menu-item>Settings</button>
                    </mat-menu>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-8 w-full mt-8">
            <!-- Groups List -->
            <div class="flex flex-col flex-auto p-6 bg-card rounded-2xl shadow">
                <div class="flex items-center">
                    <div class="flex flex-col">
                        <div
                            class="mr-4 text-lg font-medium tracking-tight leading-6 truncate"
                        >
                            Existing Group List
                        </div>
                        <div class="text-secondary font-medium"></div>
                    </div>
                </div>
                <div class="mt-6">
                    @if (isLoading) {
                        <div class="flex justify-center">
                            <mat-spinner
                                class="spinnr"
                                [diameter]="24"
                            ></mat-spinner>
                        </div>
                    } @else {
                        @if (myGroup.length == 0) {
                            <div class="flex justify-center">
                                <div class="text-secondary">
                                    There is no group setup yet.
                                </div>
                            </div>
                        } @else {
                            <mat-tree
                                [dataSource]="dataSource"
                                [treeControl]="treeControl"
                            >
                                <!-- Leaf node -->
                                <mat-tree-node
                                    *matTreeNodeDef="let node"
                                    matTreeNodePadding
                                >
                                    <button mat-icon-button disabled>
                                        <mat-icon class="mat-icon-rtl-mirror">
                                            more_horiz
                                        </mat-icon>
                                    </button>
                                    <span
                                        class="ml-2 truncate cursor-pointer"
                                        (click)="selectNode(node)"
                                        >{{ node.groupName }}</span
                                    >
                                </mat-tree-node>
                                <!-- Expandable node -->
                                <mat-tree-node
                                    *matTreeNodeDef="let node; when: hasChild"
                                    matTreeNodePadding
                                >
                                    <button mat-icon-button matTreeNodeToggle>
                                        <mat-icon class="mat-icon-rtl-mirror">
                                            {{
                                                treeControl.isExpanded(node)
                                                    ? "expand_more"
                                                    : "chevron_right"
                                            }}
                                        </mat-icon>
                                    </button>
                                    <span (click)="selectNode(node)">{{
                                        node.groupName
                                    }}</span>
                                </mat-tree-node>
                            </mat-tree>
                        }
                    }
                </div>
            </div>

            <!-- details data -->
            <div
                class="p-6 xl:col-span-2 flex flex-col flex-auto bg-card shadow rounded-2xl overflow-hidden"
            >
                <div class="flex flex-col mt-4 pt-6">
                    <!-- View Mode -->
                    @if (selectedGroup && !editMode) {
                        <div class="p-6">
                            <div class="flex flex-auto items-end -mt-16">
                                <!-- Actions -->
                                <div class="flex items-center ml-auto mb-1">
                                    @if (isDeleting) {
                                        <mat-spinner
                                            class="spinnr"
                                            [diameter]="24"
                                        ></mat-spinner>
                                    } @else {
                                        <button
                                            mat-flat-button
                                            [color]="'accent'"
                                            (click)="showEdit('edit')"
                                        >
                                            <mat-icon
                                                class="icon-size-5"
                                                [svgIcon]="
                                                    'heroicons_solid:pencil-square'
                                                "
                                            ></mat-icon>
                                            <span class="ml-2">Edit</span>
                                        </button>
                                        <button
                                            mat-flat-button
                                            class="ml-5"
                                            [color]="'warn'"
                                            (click)="deleteItem()"
                                        >
                                            <mat-icon
                                                class="icon-size-5"
                                                [svgIcon]="
                                                    'heroicons_solid:trash'
                                                "
                                            ></mat-icon>
                                            <span class="ml-2">Delete</span>
                                        </button>
                                    }
                                </div>
                            </div>
                            <!-- group Name -->
                            @if (selectedGroup.groupName) {
                                <ng-container class="mb-10">
                                    <div class="flex sm:items-center">
                                        <i
                                            class="icon-size-5 fa-duotone fa-solid fa-list-tree"
                                        ></i>
                                        <label
                                            class="capitalize ml-5 font-bold border-l-4 pl-5 border-indigo-500"
                                        >
                                            Group Name:
                                        </label>
                                        <div class="ml-6 leading-6">
                                            {{ selectedGroup.groupName }}
                                        </div>
                                    </div>
                                </ng-container>
                            }
                            @if (selectedGroup.groupName) {
                                <ng-container class="mb-10">
                                    <div class="flex sm:items-center">
                                        <i
                                            class="icon-size-5 fa-duotone fa-solid fa-list-tree"
                                        ></i>
                                        <label
                                            class="capitalize ml-5 font-bold border-l-4 pl-5 border-indigo-500"
                                        >
                                            Parent Group Name:
                                        </label>
                                        <div class="ml-6 leading-6">
                                            {{ parentGroupName }}
                                        </div>
                                    </div>
                                </ng-container>
                            }
                            <!-- description -->
                            @if (selectedGroup.description) {
                                <div class="flex">
                                    <mat-icon
                                        [svgIcon]="
                                            'heroicons_outline:bars-3-bottom-left'
                                        "
                                    ></mat-icon>
                                    <div
                                        class="max-w-none ml-6 prose prose-sm"
                                        [innerHTML]="selectedGroup.description"
                                    ></div>
                                </div>
                            }
                        </div>
                    }

                    <!-- edit mode -->
                    @if (editMode) {
                        <div class="p-6">
                            <div
                                class="relative flex flex-col flex-auto items-center px-6 sm:px-12"
                            >
                                <div class="w-full max-w-3xl">
                                    <form [formGroup]="detailsForm">
                                        <!--  Group Name -->
                                        <div class="mt-8">
                                            <mat-form-field
                                                class="w-full"
                                                [subscriptSizing]="'dynamic'"
                                            >
                                                <mat-label
                                                    >Group Name</mat-label
                                                >
                                                <i
                                                    matPrefix
                                                    class="hidden sm:flex icon-size-5 mr-5 fa-duotone fa-solid fa-list-tree"
                                                ></i>
                                                <input
                                                    matInput
                                                    [formControlName]="
                                                        'groupName'
                                                    "
                                                    [placeholder]="'Group Name'"
                                                    [spellcheck]="false"
                                                />
                                            </mat-form-field>
                                        </div>
                                        <!-- description -->
                                        <div class="mt-8">
                                            <mat-form-field
                                                class="w-full"
                                                [subscriptSizing]="'dynamic'"
                                            >
                                                <mat-label
                                                    >Description</mat-label
                                                >
                                                <mat-icon
                                                    matPrefix
                                                    class="hidden sm:flex icon-size-5"
                                                    [svgIcon]="
                                                        'heroicons_solid:bars-3-bottom-left'
                                                    "
                                                ></mat-icon>
                                                <textarea
                                                    matInput
                                                    [formControlName]="
                                                        'description'
                                                    "
                                                    [placeholder]="
                                                        'Description'
                                                    "
                                                    [rows]="5"
                                                    [spellcheck]="false"
                                                    cdkTextareaAutosize
                                                ></textarea>
                                            </mat-form-field>
                                        </div>
                                        <div class="mt-8 flex items-center">
                                            <mat-checkbox
                                                (change)="
                                                    toggleParentGroup($event)
                                                "
                                                >(Optional) Select Parent
                                                Group<br />
                                                <span
                                                    class="italic text-secondary"
                                                >
                                                    [If you require this group
                                                    name to be a subgroup, check
                                                    this and select the parent
                                                    group]</span
                                                ></mat-checkbox
                                            >
                                        </div>
                                        <!-- Parent Group -->
                                        @if (isParentGroupEnabled) {
                                            <div>
                                                <mat-form-field
                                                    class="w-full mt-5"
                                                    [subscriptSizing]="
                                                        'dynamic'
                                                    "
                                                >
                                                    <mat-label
                                                        >Parent Group</mat-label
                                                    >
                                                    <i
                                                        matPrefix
                                                        class="hidden sm:flex icon-size-5 mr-5 fa-duotone fa-solid fa-list-tree"
                                                    ></i>
                                                    <mat-select
                                                        [formControlName]="
                                                            'parentID'
                                                        "
                                                    >
                                                        @for (
                                                            parents of myGroup;
                                                            track parents
                                                        ) {
                                                            <mat-option
                                                                [value]="
                                                                    parents.$id
                                                                "
                                                                [disabled]="
                                                                    selectedGroup?.$id ===
                                                                    parents.$id
                                                                "
                                                                >{{
                                                                    parents.groupName
                                                                }}</mat-option
                                                            >
                                                        }
                                                    </mat-select>
                                                </mat-form-field>
                                            </div>
                                        }
                                        <!-- Actions -->
                                        <div
                                            class="flex items-center mt-10 -mx-6 sm:-mx-12 py-4 pr-4 pl-1 sm:pr-12 sm:pl-7 border-t dark:bg-transparent"
                                        >
                                            @if (isSaving) {
                                                <div
                                                    class="flex justify-center"
                                                >
                                                    <mat-spinner
                                                        class="spinnr"
                                                        [diameter]="24"
                                                    ></mat-spinner>
                                                </div>
                                            } @else {
                                                <!-- Cancel -->
                                                <button
                                                    class="ml-auto"
                                                    mat-button
                                                    [matTooltip]="'Cancel'"
                                                    (click)="
                                                        cancelUpdateDetails()
                                                    "
                                                >
                                                    Cancel
                                                </button>
                                                <!-- Save -->
                                                <button
                                                    class="ml-2"
                                                    mat-flat-button
                                                    [color]="'primary'"
                                                    [disabled]="
                                                        detailsForm.invalid
                                                    "
                                                    [matTooltip]="'Save'"
                                                    (click)="updateDetails()"
                                                >
                                                    Save
                                                </button>
                                            }
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

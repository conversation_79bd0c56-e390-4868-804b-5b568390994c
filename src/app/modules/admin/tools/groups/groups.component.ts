import { TextFieldModule } from '@angular/cdk/text-field';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTreeFlatDataSource, MatTreeFlattener, MatTreeModule } from '@angular/material/tree';
import { FlatTreeControl } from '@angular/cdk/tree';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { Group } from 'app/core/databaseModels/groups/groups.types';
import { FuseMediaWatcherService } from '@fuse/services/media-watcher';
import { MatDialog } from '@angular/material/dialog';
import { FuseLoadingService } from '@fuse/services/loading';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { Subject, filter, pipe, switchMap, takeUntil, tap } from 'rxjs';
import { GroupService } from 'app/core/databaseModels/groups/groups.service';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

// Define the group interface


interface TreeNode {
    expandable: boolean;
    groupName: string;
    level: number;
    id: string;
}

@Component({
    selector: 'tools-groups',
    templateUrl: './groups.component.html',
    styleUrls: ['./groups.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [FormsModule,
        MatProgressSpinnerModule,
        MatSelectModule,
        MatCheckboxModule,
        MatTooltipModule,
        MatButtonModule,
        MatTreeModule,
        MatMenuModule,
        MatDividerModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        TextFieldModule,
        MatSelectModule,
        MatOptionModule,
        MatButtonModule]
})
export class GroupsComponent implements OnInit, OnDestroy {



    private _unsubscribeAll: Subject<any> = new Subject<any>();

    myGroup: Group[] = [];
    selectedGroup: Group;
    parentGroupName: string = "";
    editMode: boolean = false;
    isLoading: boolean = false;
    isSaving: boolean = false;
    isDeleting: boolean = false; // For deleting
    isParentGroupEnabled: boolean = false;

    detailsForm: UntypedFormGroup;

    /// Setup TreeView /////

    private transformer = (node: Group, level: number) => {
        return {
            expandable: !!this.dataMap.get(node.$id) && this.dataMap.get(node.$id).length > 0,
            groupName: node.groupName,
            level: level,
            id: node.$id
        };
    }
    treeControl = new FlatTreeControl<TreeNode>(
        node => node.level, node => node.expandable
    );
    treeFlattener = new MatTreeFlattener(
        this.transformer, node => node.level, node => node.expandable, node => this.dataMap.get(node.$id)
    );
    dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);
    dataMap = new Map<string, Group[]>();
    hasChild = (_: number, node: TreeNode) => node.expandable;
    /// end Setup TreeView /////

    constructor(
        private _formBuilder: UntypedFormBuilder,
        private _fuseMediaWatcherService: FuseMediaWatcherService,
        private _changeDetectorRef: ChangeDetectorRef,
        private _matDialog: MatDialog,
        private _fuseLoadingService: FuseLoadingService,
        private _fuseConfirmationService: FuseConfirmationService,
        private _groupService: GroupService) {


    }
    ngOnDestroy(): void {
        this.editMode = false;

        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    ngOnInit(): void {



        this.detailsForm = this._formBuilder.group({
            $id: [''],
            organisationID: [''],
            parentID: [{ value: '', disabled: true }], // Initially disabled
            groupName: ['', [Validators.required]],
            description: [''],
            status: [''],
        });

        this.loadGroup();


    }

    loadGroup() {


        this.isLoading = true;
        this._fuseLoadingService.show();

        this._groupService.getGroups().pipe(
            tap((res) => {
                if (!res || res.length === 0) {
                    this.isLoading = false;
                    this._fuseLoadingService.hide();

                    //this.myGroup = [];
                    this._changeDetectorRef.markForCheck();
                }
            }),
            filter((groups) => groups.length > 0),
            switchMap(() => this._groupService.groups$.pipe(takeUntil(this._unsubscribeAll))),
            tap((groups) => {
                this.myGroup = groups;
                this.dataMap = new Map<string, Group[]>();
                this.hasChild = (_: number, node: TreeNode) => node.expandable;
                this.buildTreeMap(); // Assuming this method processes 'this.myGroup' to update 'this.dataMap'
                this.dataSource.data = this.buildTree(); // Assuming this method uses 'this.dataMap'
                if (this.treeControl.dataNodes.length > 0) {
                    this.treeControl.expand(this.treeControl.dataNodes[0]);
                }
                this.isLoading = false;
                this._fuseLoadingService.hide();
                this._changeDetectorRef.markForCheck();
            }),
            takeUntil(this._unsubscribeAll)
        ).subscribe({
            complete: () => {
                this.isLoading = false;
                this._fuseLoadingService.hide();
                this._changeDetectorRef.markForCheck();
            }
        });


        this._fuseMediaWatcherService.onMediaQueryChange$('(min-width: 1440px)')
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((state) => {



            });
    }


    showEdit(mode: string) {
        if (mode == "new") {
            this.editMode = true;
            this.selectedGroup = null;
            this.detailsForm.patchValue(null);
            this.detailsForm.reset();
            this.parentGroupName = "";
            this.isParentGroupEnabled = false; // Reset the checkbox state
            return;
        } else if (mode == "edit") {
            this.editMode = true;
            this.detailsForm.patchValue(this.selectedGroup);
            this.isParentGroupEnabled = !!this.selectedGroup.parentID; // Set the checkbox state based on the selected group
        }
    }



    toggleParentGroup(event: any) {
        this.isParentGroupEnabled = event.checked;
        if (this.isParentGroupEnabled) {
            this.detailsForm.get('parentID').enable();
            // Set the dropdown to the first item that is not the current item
            this.detailsForm.patchValue({
                parentID: this.myGroup.find(group => group.$id !== this.selectedGroup?.$id)?.$id || ''
            });
        } else {
            this.detailsForm.get('parentID').disable();
            this.detailsForm.patchValue({ parentID: null }); // Set the parent group to null
        }
    }



    buildTreeMap() {
        if (this.myGroup) {
            this.myGroup.forEach(group => {
                if (!this.dataMap.has(group.parentID)) {
                    this.dataMap.set(group.parentID, []);
                }
                this.dataMap.get(group.parentID).push(group);
            });
        }

    }

    buildTree(parentID: string = null): Group[] {
        return this.dataMap.get(parentID) || [];
    }

    cancelUpdateDetails() {
        this.editMode = false;

        this.selectedGroup = null;
        this.detailsForm.patchValue(null);
        this.detailsForm.reset();
        this.parentGroupName = "";
    }

    updateDetails() {
        const group = this.detailsForm.getRawValue();


        if (this.isParentGroupEnabled && !group.parentID) {
            const confirmation = this._fuseConfirmationService.open({
                title: 'warning!',
                message: `Please select a parent group!`,
                actions: {
                    confirm: {
                        show: false,
                    }
                },
                icon: {
                    show: true,
                    name: 'heroicons_outline:exclamation-triangle',
                },

            });
            return;
        }

        if (group.$id == null) {
            this.saveNew(group);
        } else {
            this.saveEdited(group);
        }

    }
    saveEdited(group: any) {


        if (group.$id != null && group.$id == group.parentID) {
            const confirmation = this._fuseConfirmationService.open({
                title: 'warning!',
                message: `Cannot set the parent to itself!`,
                actions: {
                    confirm: {
                        show: false,
                    }
                },

                icon: {
                    show: true,
                    name: 'heroicons_outline:exclamation-triangle',
                },
            });

            return;
        } else {
            this._fuseLoadingService.show();
            //console.log(group);

            this.isSaving = true;
            this._changeDetectorRef.markForCheck();
            this._changeDetectorRef.detectChanges();
            this._groupService.updateGroup(group.$id, group).subscribe(result => {

                this._fuseLoadingService.hide();
                this.editMode = false;

                this.selectedGroup = null;
                this.isSaving = false;
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();


            })





        }



    }
    saveNew(group: any) {


        if (group.$id != null && group.$id == group.parentID) {
            const confirmation = this._fuseConfirmationService.open({
                title: 'warning!',
                message: `Cannot set the parent to itself!`,
                actions: {
                    confirm: {
                        show: false,
                    }
                },

                icon: {
                    show: true,
                    name: 'heroicons_outline:exclamation-triangle',
                },
            });

            return;
        } else {
            this._fuseLoadingService.show();
            //console.log(group);
            this.isSaving = true;
            this._changeDetectorRef.markForCheck();
            this._changeDetectorRef.detectChanges();
            this._groupService.getOrganisationID().subscribe(organisationID => {
                group.organisationID = organisationID;
                this._groupService.createGroup(group).subscribe(result => {

                    this._fuseLoadingService.hide();
                    this.editMode = false;

                    this.selectedGroup = null;
                    this.isSaving = false;

                    if (this.myGroup.length == 0) {
                        this.loadGroup();
                    }

                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();


                })
            });



        }



    }

    deleteItem() {
        // Open the confirmation dialog
        const confirmation = this._fuseConfirmationService.open({
            title: 'Delete confirmation!',
            message: `Are you sure you want to delete <i class="text-semibold text-red-600">${this.selectedGroup.groupName}</i>? This action cannot be undone!`,
            actions: {
                confirm: {
                    label: 'Delete',
                },
            },
            icon: {
                show: true,
                name: 'heroicons_outline:trash',
            },
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            if (result === 'confirmed') {
                // Check if any child group has the selected group as parentId
                const hasChild = this.myGroup.some(group => group.parentID === this.selectedGroup.$id);

                if (hasChild) {
                    // Hide loading if it was previously shown
                    this._fuseLoadingService.hide();

                    // Reset flags
                    this.editMode = false;
                    this.isDeleting = false;

                    // Show warning dialog
                    this._fuseConfirmationService.open({
                        title: 'Warning!',
                        message: `Cannot delete the parent group, it has child groups!`,
                        actions: {
                            confirm: {
                                show: false,
                            }
                        },
                        icon: {
                            show: true,
                            name: 'heroicons_outline:exclamation-triangle',
                        },
                    });

                    // Exit the function early since deletion should be canceled
                    return;
                }

                // No child groups found; proceed with deletion
                this._fuseLoadingService.show();
                this.isDeleting = true;
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();

                // Call the deleteGroup service method
                this._groupService.deleteGroup(this.selectedGroup.$id).subscribe({
                    next: (result) => {
                        // Hide loading indicator
                        this._fuseLoadingService.hide();

                        // Reset flags and selected group
                        this.editMode = false;
                        this.isDeleting = false;
                        this.selectedGroup = null;

                        // Trigger change detection
                        this._changeDetectorRef.markForCheck();
                        this._changeDetectorRef.detectChanges();
                    },
                    error: (error) => {
                        // Handle error if needed
                        console.error('Error deleting group:', error);

                        // Hide loading indicator and reset flags
                        this._fuseLoadingService.hide();
                        this.isDeleting = false;

                        // Optionally, show an error message to the user
                        this._fuseConfirmationService.open({
                            title: 'Error!',
                            message: `An error occurred while deleting the group. Please try again later.`,
                            actions: {
                                confirm: {
                                    label: 'OK',
                                }
                            },
                            icon: {
                                show: true,
                                name: 'heroicons_outline:exclamation-circle',
                            },
                        });
                    }
                });
            } else {
                // User canceled the deletion; hide loading indicator if it was shown
                this._fuseLoadingService.hide();
            }
        });
    }


    selectNode(node: TreeNode) {
        // console.log(node);
        this.editMode = false;
        this.selectedGroup = this.myGroup.find(item => item.$id == node.id)
        this.parentGroupName = this.myGroup.find(item => item.$id == this.selectedGroup.parentID)?.groupName ?? null;
        // console.log(this.selectedGroup);
    }


}

import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDrawer, MatSidenavModule } from '@angular/material/sidenav';
import { FuseMediaWatcherService } from '@fuse/services/media-watcher';
import { Subject, takeUntil } from 'rxjs';
import { TeammemberDriverComponent } from './teammember-driver/teammember-driver.component';
import { AppTemplatesComponent } from './app-templates/app-templates.component';
import { AssetsComponent } from './assets/assets.component';
import { CustomerComponent } from './customer/customer.component';
import { GroupsComponent } from './groups/groups.component';
import { UserAccountsComponent } from './user-accounts/user-accounts.component';
import { VehiclesComponent } from './vehicles/vehicles.component';

@Component({
    selector: 'tools',
    templateUrl: './tools.component.html',
    styleUrls: ['./tools.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [MatSidenavModule, MatButtonModule, MatIconModule, NgClass, TeammemberDriverComponent, AppTemplatesComponent, AssetsComponent, CustomerComponent, GroupsComponent, UserAccountsComponent, VehiclesComponent]
})
export class ToolsComponent implements OnInit, OnDestroy {
    @ViewChild('drawer') drawer: MatDrawer;
    drawerMode: 'over' | 'side' = 'side';
    drawerOpened: boolean = true;
    panels: any[] = [];
    selectedPanel: string = 'teammember-driver';

    private _unsubscribeAll: Subject<any> = new Subject<any>();

    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseMediaWatcherService: FuseMediaWatcherService,
    ) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        // Setup available panels
        this.panels = [
            {
                id: 'teammember-driver',
                icon: 'heroicons_outline:user-group',
                title: 'Driver/Team Member',
                description: 'Manage your existing team member',
            },
            {
                id: 'vehicles',
                icon: 'mat_outline:directions_car_filled',
                title: 'Vehicles',
                description: 'Manage your vehicles and access their information and preferences',
            },
            {
                id: 'assets',
                icon: 'heroicons_outline:qr-code',
                title: 'Assets/QR/TAG',
                description: 'Manage or add your Assets, QR or TAG\'s here.',
            },
            {
                id: 'groups',
                icon: 'mat_outline:account_tree',
                title: 'Groups',
                description: 'Manage your groups information',
            },
            {
                id: 'customer',
                icon: 'mat_outline:contact_phone',
                title: 'Customer & Ratings ',
                description: 'Manage your template for Scheduler,app and emails.',
            },
            {
                id: 'user-accounts',
                icon: 'heroicons_outline:users',
                title: 'User Accounts',
                description: 'Manage your app users and their information',
            },
            {
                id: 'app-templates',
                icon: 'heroicons_outline:rectangle-group',
                title: 'App & Email Templates',
                description: 'Manage your template for Scheduler,app and emails.',
            },


        ];

        // Subscribe to media changes
        this._fuseMediaWatcherService.onMediaChange$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe(({ matchingAliases }) => {
                // Set the drawerMode and drawerOpened
                if (matchingAliases.includes('lg')) {
                    this.drawerMode = 'side';
                    this.drawerOpened = true;
                }
                else {
                    this.drawerMode = 'over';
                    this.drawerOpened = false;
                }

                // Mark for check
                this._changeDetectorRef.markForCheck();
            });
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }
    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Navigate to the panel
     *
     * @param panel
     */
    goToPanel(panel: string): void {
        this.selectedPanel = panel;

        // Close the drawer on 'over' mode
        if (this.drawerMode === 'over') {
            this.drawer.close();
        }
    }

    /**
     * Get the details of the panel
     *
     * @param id
     */
    getPanelInfo(id: string): any {
        return this.panels.find(panel => panel.id === id);
    }

    /**
     * Track by function for ngFor loops
     *
     * @param index
     * @param item
     */
    trackByFn(index: number, item: any): any {
        return item.id || index;
    }

}

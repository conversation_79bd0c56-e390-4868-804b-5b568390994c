import { Routes } from "@angular/router";
import { ToolsComponent } from "./tools.component";
import { ToolsService } from "./tools.service";
import { importProvidersFrom, inject } from "@angular/core";
import { NgxDaterangepickerMd } from "ngx-daterangepicker-material";


export default [
    {
        path: '',
        component: ToolsComponent,
        providers: [
            importProvidersFrom(
                NgxDaterangepickerMd.forRoot()
            ),
        ],
        resolve: {
            data: () => inject(ToolsService).getData(),
        }
    },
] as Routes;

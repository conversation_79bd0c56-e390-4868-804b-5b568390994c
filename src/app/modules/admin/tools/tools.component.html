<div
  class="flex flex-col w-full min-w-0 sm:absolute sm:inset-0 sm:overflow-hidden"
  >
  <mat-drawer-container class="flex-auto sm:h-full">
    <!-- Drawer -->
    <mat-drawer
      class="sm:w-96 dark:bg-gray-900"
      [autoFocus]="false"
      [mode]="drawerMode"
      [opened]="drawerOpened"
      #drawer
      >
      <!-- Header -->
      <div class="flex items-center justify-between m-8 mr-6 sm:my-10">
        <!-- Title -->
        <div
          class="text-4xl font-extrabold tracking-tight leading-none"
          >
          Admin <PERSON>ls
        </div>
        <!-- Close button -->
        <div class="lg:hidden">
          <button mat-icon-button (click)="drawer.close()">
            <mat-icon
              [svgIcon]="'heroicons_outline:x-mark'"
            ></mat-icon>
          </button>
        </div>
      </div>
      <!-- Panel links -->
      <div class="flex flex-col divide-y border-t border-b">
        @for (panel of panels; track trackByFn($index, panel)) {
          <div
            class="flex px-8 py-5 cursor-pointer"
                        [ngClass]="{
                            'hover:bg-gray-100 dark:hover:bg-hover':
                                !selectedPanel || selectedPanel !== panel.id,
                            'bg-primary-50 dark:bg-hover':
                                selectedPanel && selectedPanel === panel.id
                        }"
            (click)="goToPanel(panel.id)"
            >
            <mat-icon
                            [ngClass]="{
                                'text-hint':
                                    !selectedPanel ||
                                    selectedPanel !== panel.id,
                                'text-primary dark:text-primary-500':
                                    selectedPanel && selectedPanel === panel.id
                            }"
              [svgIcon]="panel.icon"
            ></mat-icon>
            <div class="ml-3">
              <div
                class="font-medium leading-6"
                                [ngClass]="{
                                    'text-primary dark:text-primary-500':
                                        selectedPanel &&
                                        selectedPanel === panel.id
                                }"
                >
                {{ panel.title }}
              </div>
              <div class="mt-0.5 text-secondary">
                {{ panel.description }}
              </div>
            </div>
          </div>
        }
      </div>
    </mat-drawer>

    <!-- Drawer content -->
    <mat-drawer-content class="flex flex-col">
      <!-- Main -->
      <div class="flex-auto px-6 pt-8 pb-8 md:p-8 md:pb-8 lg:p-8">
        <!-- Panel header -->
        <div class="flex items-center">
          <!-- Drawer toggle -->
          <button
            class="lg:hidden -ml-2"
            mat-icon-button
            (click)="drawer.toggle()"
            >
            <mat-icon
              [svgIcon]="'heroicons_outline:bars-3'"
            ></mat-icon>
          </button>

          <!-- Panel title -->
          <div
            class="ml-2 lg:ml-0 text-3xl font-bold tracking-tight leading-none"
            >
            {{ getPanelInfo(selectedPanel).title }}
          </div>
        </div>

        <!-- Load settings panel -->
        <div class="mt-4">
          @switch (selectedPanel) {
            <!-- Account -->
            @case ('teammember-driver') {
              <tools-teammember-driver></tools-teammember-driver>
            }
            <!-- Security -->
            @case ('vehicles') {
              <tools-vehicles></tools-vehicles>
            }
            <!-- Plan & Billing -->
            @case ('assets') {
              <tools-assets></tools-assets>
            }
            <!-- groups -->
            @case ('groups') {
              <tools-groups></tools-groups>
            }
            <!-- Customer -->
            @case ('customer') {
              <tools-customer></tools-customer>
            }
            <!-- Customer -->
            @case ('user-accounts') {
              <tools-user-accounts></tools-user-accounts>
            }
            <!-- Template -->
            @case ('app-templates') {
              <tools-app-templates></tools-app-templates>
            }
          }
        </div>
      </div>
    </mat-drawer-content>
  </mat-drawer-container>
</div>

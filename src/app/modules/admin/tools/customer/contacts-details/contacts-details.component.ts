import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';
import { TextFieldModule } from '@angular/cdk/text-field';
import { DatePipe, NgClass } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, Renderer2, TemplateRef, ViewChild, ViewContainerRef, ViewEncapsulation } from '@angular/core';
import { FormArray, FormGroup, FormsModule, ReactiveFormsModule, UntypedFormArray, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule, MatRippleModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FuseFindByKeyPipe } from '@fuse/pipes/find-by-key/find-by-key.pipe';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { Subject } from 'rxjs';

import { MatMapsAutocompleteModule } from 'app/modules/widgets/mat-maps-autocomplete/mat-maps-autocomplete.module';

import { Customer, CustomerTags } from 'app/core/databaseModels/customers/contacts.types';
import { CustomerService } from 'app/core/databaseModels/customers/customers.service';
import { FuseLoadingService } from '@fuse/services/loading';
import { add } from 'lodash';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@Component({
    selector: 'contacts-details',
    templateUrl: './contacts-details.component.html',
    styleUrls: ['./contacts-details.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    exportAs: 'contacts-details',
    imports: [
        MatProgressSpinnerModule,
        MatMapsAutocompleteModule,
        MatButtonModule,
        MatTooltipModule,
        MatIconModule,
        FormsModule,
        ReactiveFormsModule,
        MatRippleModule,
        MatFormFieldModule,
        MatInputModule,
        MatCheckboxModule,
        NgClass,
        MatSelectModule,
        MatOptionModule,
        MatDatepickerModule,
        TextFieldModule,
        FuseFindByKeyPipe,
        DatePipe
    ]
})

export class ContactsDetailsComponent implements OnInit, OnDestroy {

    @Input({ required: true }) contact: Customer | null = null;
    @Input({ required: true }) tags: CustomerTags[] | null = null;
    @Input() newItem: boolean | null = false;
    @Output() closeDrawer = new EventEmitter<string>();

    @ViewChild('avatarFileInput') private _avatarFileInput: ElementRef;
    @ViewChild('tagsPanel') private _tagsPanel: TemplateRef<any>;
    @ViewChild('tagsPanelOrigin') private _tagsPanelOrigin: ElementRef;
    private _tagsPanelOverlayRef: OverlayRef;
    private _unsubscribeAll: Subject<any> = new Subject<any>();

    editMode: boolean = false;
    tagsEditMode: boolean = false;
    filteredTags: CustomerTags[];
    isSaving: boolean = false;

    contactForm: UntypedFormGroup;

    contacts: Customer[];

    selectedImage: any;




    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _formBuilder: UntypedFormBuilder,
        private _fuseConfirmationService: FuseConfirmationService,
        private _renderer2: Renderer2,
        private _overlay: Overlay,
        private _viewContainerRef: ViewContainerRef,
        private _fuseLoadingService: FuseLoadingService,
        private _customerService: CustomerService,

    ) {
    }

    ngOnInit(): void {


        // Get the tags

        this.filteredTags = this.tags;

        // Create the contact form
        this.contactForm = this._formBuilder.group({
            $id: [this.contact?.$id],
            organisationID: [this.contact?.organisationID],
            avatar: [''],
            avatarImageId: [''],
            name: ['', [Validators.required]],
            emails: this._formBuilder.array([]),
            phoneNumbers: this._formBuilder.array([], [Validators.required]),
            company: [''],
            birthday: [null],
            addresses: this._formBuilder.array([]),
            notes: [null],
            tags: [[]],
            status: [true],
            chatPhoneNumber: [''],
        });
        if (this.newItem) {

            this.toggleEditMode(true);
            //console.log(this.contact);
        } else {


            // Clear the emails and phoneNumbers form arrays
            (this.contactForm.get('emails') as UntypedFormArray).clear();
            (this.contactForm.get('phoneNumbers') as UntypedFormArray).clear();
            (this.contactForm.get('addresses') as UntypedFormArray).clear();
            // Patch values to the form
            this.contactForm.patchValue(this.contact);

            // Setup the emails form array
            const emailFormGroups = [];

            if (this.contact.emails.length > 0) {
                // Iterate through them
                this.contact.emails.forEach((email) => {
                    // Create an email form group
                    emailFormGroups.push(
                        this._formBuilder.group({
                            email: [email.email],
                            label: [email.label],
                        }),
                    );
                });
            }
            else {
                // Create an email form group
                emailFormGroups.push(
                    this._formBuilder.group({
                        email: [''],
                        label: [''],
                    }),
                );
            }

            // Add the email form groups to the emails form array
            emailFormGroups.forEach((emailFormGroup) => {
                (this.contactForm.get('emails') as UntypedFormArray).push(emailFormGroup);
            });

            // Setup the phone numbers form array
            const phoneNumbersFormGroups = [];

            if (this.contact.phoneNumbers.length > 0) {
                // Iterate through them


                this.contact.phoneNumbers.forEach((phoneNumber) => {

                    phoneNumbersFormGroups.push(

                        this._formBuilder.group({

                            phoneNumber: [phoneNumber.phoneNumber],
                            label: [phoneNumber.label],
                        }),
                    );
                });
            }
            else {
                // Create a phone number form group
                phoneNumbersFormGroups.push(
                    this._formBuilder.group({

                        phoneNumber: [''],
                        label: [''],
                    }),
                );
            }

            // Add the phone numbers form groups to the phone numbers form array
            phoneNumbersFormGroups.forEach((phoneNumbersFormGroup) => {
                (this.contactForm.get('phoneNumbers') as UntypedFormArray).push(phoneNumbersFormGroup);
            });

            ////////

            // Setup the addresses form array
            const addressFormGroups = [];

            if (this.contact.addresses.length > 0) {
                // Iterate through them
                this.contact.addresses.forEach((address) => {
                    // Create an addresses form group
                    addressFormGroups.push(
                        this._formBuilder.group({
                            address: [address.address],
                            label: [address.label],
                            latLon: [address.latLon]
                        }),
                    );
                });
            }
            else {
                // Create an address form group
                addressFormGroups.push(
                    this._formBuilder.group({
                        address: [''],
                        label: [''],
                        latLon: ['']
                    }),
                );
            }

            // Add the email form groups to the addresses form array
            addressFormGroups.forEach((addressFormGroup) => {
                (this.contactForm.get('addresses') as UntypedFormArray).push(addressFormGroup);
            });

            ///////////

            // Toggle the edit mode off
            this.toggleEditMode(false);

            // Mark for check
            this._changeDetectorRef.markForCheck();



            // Get the tags

            this.filteredTags = this.tags;


        }
    }


    /**
    * On destroy
    */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
        this.contactForm.reset();
        // Dispose the overlays if they are still on the DOM
        if (this._tagsPanelOverlayRef) {
            this._tagsPanelOverlayRef.dispose();
        }
    }

    cancelUpdateDetails() {
        this.closeDrawer.emit('cancel');
    }
    /**
     * Toggle edit mode
     *
     * @param editMode
     */
    toggleEditMode(editMode: boolean | null = null): void {
        // console.log(this.contact);
        if (editMode === null) {
            this.editMode = !this.editMode;
        }
        else {
            this.editMode = editMode;
        }

        // Mark for check
        this._changeDetectorRef.markForCheck();
    }

    /**
 * Update the contact
 */
    updateContact(): void {
        // Get the contact object
        const contact = this.contactForm.getRawValue();

        // Go through the contact object and clear empty values
        contact.emails = contact.emails.filter(email => email.email);
        contact.phoneNumbers = contact.phoneNumbers.filter(phoneNumber => phoneNumber.phoneNumber);
        contact.addresses = contact.addresses.filter(address => address.address);

        contact.addresses = contact.addresses?.map(str =>
            typeof str === 'string' ? JSON.parse(str) : str
        );

        contact.phoneNumbers = contact.phoneNumbers?.map(str =>
            typeof str === 'string' ? JSON.parse(str) : str
        );
        contact.emails = contact.emails?.map(str =>
            typeof str === 'string' ? JSON.parse(str) : str
        );
        if (contact.$id == null && this.newItem == true) {
            this.saveNew(contact);
        } else {
            this.saveEdited(contact);
        }


    }

    saveNew(customer: Customer) {

        this._fuseLoadingService.show();
        // Go through the contact object and clear empty values
        customer.emails = customer.emails.filter(email => email.email);
        customer.phoneNumbers = customer.phoneNumbers.filter(phoneNumber => phoneNumber);


        customer.addresses = customer.addresses.filter(address => address.address);
        // check if the customer addresses has empty latLon
        for (let i = 0; i < customer.addresses.length; i++) {
            if (customer.addresses[i].latLon == null || customer.addresses[i].latLon == '') {

                // show message
                this._fuseConfirmationService.open({
                    title: 'Address formate error',
                    message: 'Please Select address from google suggestions list',
                    icon: {
                        show: true,
                        name: 'heroicons_outline:exclamation',
                        color: 'warning',
                    },
                    actions: {
                        confirm: {
                            show: false,
                            label: 'OK',
                            color: 'primary',
                        },
                        cancel: {
                            show: false,
                            label: 'Fix address',

                        },
                    },
                    dismissible: true,
                });

                return;

            }
        }

        if (customer.phoneNumbers.length > 0 && customer.emails.length > 0) {
            //TODO: check phone
        }
        this.isSaving = true;
        if (this.selectedImage != null) {

            this._customerService.uploadFile(this.selectedImage).subscribe(
                result => {
                    customer.avatar = result.fileUrl;
                    customer.avatarImageId = result.fileId;
                    //  console.log(customer);
                    this._customerService.createCustomer(customer).subscribe(result => {
                        this._fuseLoadingService.hide();
                        // Mark for check
                        // Toggle the edit mode off
                        this.contactForm.reset();
                        this.contact = result;
                        this._changeDetectorRef.markForCheck();
                        this._changeDetectorRef.detectChanges();
                        this.toggleEditMode(false);
                        this.isSaving = false;
                        this.closeDrawer.emit('newITEM');
                    });


                }, error => {
                    console.error('Error uploading file:', error);
                });
        } else {
            this._customerService.createCustomer(customer).subscribe(result => {
                this._fuseLoadingService.hide();
                // Mark for check
                // Toggle the edit mode off

                this.contactForm.reset();
                this.contact = result;
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
                this.toggleEditMode(false);
                this.isSaving = false;
                this.closeDrawer.emit('newITEM');
            });


        }


    }

    saveEdited(customer: Customer) {
        this._fuseLoadingService.show();

        customer.emails = customer.emails.filter(email => email.email);
        customer.phoneNumbers = customer.phoneNumbers.filter(phoneNumber => phoneNumber);
        customer.addresses = customer.addresses.filter(address => address.address);

        // check if the customer addresses has empty latLon

        for (let i = 0; i < customer.addresses.length; i++) {
            if (customer.addresses[i].latLon == null || customer.addresses[i].latLon == '') {

                // show message
                this._fuseConfirmationService.open({
                    title: 'Address formate error',
                    message: 'Please Select address from google suggestions list',
                    icon: {
                        show: true,
                        name: 'heroicons_outline:exclamation',
                        color: 'warning',
                    },
                    actions: {
                        confirm: {
                            show: false,
                            label: 'OK',
                            color: 'primary',
                        },
                        cancel: {
                            show: false,
                            label: 'Fix address',

                        },
                    },
                    dismissible: true,
                });

                return;

            }
        }
        this.isSaving = true;
        if (this.selectedImage) {
            let _oldImageID = customer.avatarImageId;
            this._customerService.uploadFile(this.selectedImage).subscribe(
                result => {
                    customer.avatar = result.fileUrl;
                    customer.avatarImageId = result.fileId;
                    this._customerService.updateCustomer(customer.$id, customer).subscribe(result => {
                        this._fuseLoadingService.hide();
                        // Mark for check
                        this.isSaving = false;
                        this._changeDetectorRef.markForCheck();
                        this._changeDetectorRef.detectChanges();

                        this._customerService.deleteFile(_oldImageID).subscribe(result => {
                            this.closeDrawer.emit('update');
                        });

                    })
                })
        } else {
            //console.log(customer);
            this._customerService.updateCustomer(customer.$id, customer).subscribe(result => {
                this._fuseLoadingService.hide();
                this.isSaving = false;
                // Mark for check
                this._changeDetectorRef.markForCheck();
                this._changeDetectorRef.detectChanges();
                this.closeDrawer.emit('update');
            })
        }
    }
    /**
      * Upload avatar
      *
      * @param fileList
      */

    uploadAvatar(fileList: FileList): void {
        // Return if canceled
        if (!fileList.length) {
            return;
        }

        const allowedTypes = ['image/jpeg', 'image/png'];
        const file = fileList[0];

        // Return if the file is not allowed
        if (!allowedTypes.includes(file.type)) {
            return;
        }

        // update the avatar url from file
        this.contact.avatar = URL.createObjectURL(file);

        this.selectedImage = file;
    }

    /**
     * Remove the avatar
     */
    removeAvatar(): void {
        // Get the form control for 'avatar'
        const avatarFormControl = this.contactForm.get('avatar');

        // Set the avatar as null
        avatarFormControl.setValue(null);

        // Set the file input value as null
        this._avatarFileInput.nativeElement.value = null;

        // Update the contact
        this.contact.avatar = null;
        this.selectedImage = null;


    }

    /**
     * Open tags panel
     */
    openTagsPanel(): void {
        // Create the overlay
        this._tagsPanelOverlayRef = this._overlay.create({
            backdropClass: '',
            hasBackdrop: true,
            scrollStrategy: this._overlay.scrollStrategies.block(),
            positionStrategy: this._overlay.position()
                .flexibleConnectedTo(this._tagsPanelOrigin.nativeElement)
                .withFlexibleDimensions(true)
                .withViewportMargin(64)
                .withLockedPosition(true)
                .withPositions([
                    {
                        originX: 'start',
                        originY: 'bottom',
                        overlayX: 'start',
                        overlayY: 'top',
                    },
                ]),
        });

        // Subscribe to the attachments observable
        this._tagsPanelOverlayRef.attachments().subscribe(() => {
            // Add a class to the origin
            this._renderer2.addClass(this._tagsPanelOrigin.nativeElement, 'panel-opened');

            // Focus to the search input once the overlay has been attached
            this._tagsPanelOverlayRef.overlayElement.querySelector('input').focus();
        });

        // Create a portal from the template
        const templatePortal = new TemplatePortal(this._tagsPanel, this._viewContainerRef);

        // Attach the portal to the overlay
        this._tagsPanelOverlayRef.attach(templatePortal);

        // Subscribe to the backdrop click
        this._tagsPanelOverlayRef.backdropClick().subscribe(() => {
            // Remove the class from the origin
            this._renderer2.removeClass(this._tagsPanelOrigin.nativeElement, 'panel-opened');

            // If overlay exists and attached...
            if (this._tagsPanelOverlayRef && this._tagsPanelOverlayRef.hasAttached()) {
                // Detach it
                this._tagsPanelOverlayRef.detach();

                // Reset the tag filter
                this.filteredTags = this.tags;

                // Toggle the edit mode off
                this.tagsEditMode = false;
            }

            // If template portal exists and attached...
            if (templatePortal && templatePortal.isAttached) {
                // Detach it
                templatePortal.detach();
            }
        });
    }

    /**
     * Toggle the tags edit mode
     */
    toggleTagsEditMode(): void {
        this.tagsEditMode = !this.tagsEditMode;
    }

    /**
     * Filter tags
     *
     * @param event
     */
    filterTags(event): void {
        // Get the value
        const value = event.target.value.toLowerCase();

        // Filter the tags
        this.filteredTags = this.tags.filter(tag => tag.title.toLowerCase().includes(value));
    }

    /**
     * Filter tags input key down event
     *
     * @param event
     */
    filterTagsInputKeyDown(event): void {
        // Return if the pressed key is not 'Enter'
        if (event.key !== 'Enter') {
            return;
        }

        // If there is no tag available...
        if (this.filteredTags.length === 0) {
            // Create the tag
            this.createTag(event.target.value);

            // Clear the input
            event.target.value = '';

            // Return
            return;
        }

        // If there is a tag...
        const tag = this.filteredTags[0];
        const isTagApplied = this.contact.tags.find(id => id === tag.$id);

        // If the found tag is already applied to the contact...
        if (isTagApplied) {
            // Remove the tag from the contact
            this.removeTagFromContact(tag);
        }
        else {
            // Otherwise add the tag to the contact
            this.addTagToContact(tag);
        }
    }

    /**
     * Create a new tag
     *
     * @param title
     */
    createTag(title: string): void {
        const tag = {
            organisationID: this.contact.organisationID,
            title: title,
        };

        this._customerService.createCustomerTag(tag).subscribe((response) => {
            // Add the tag to the contact
            this.addTagToContact(response);
        });
    }

    /**
     * Update the tag title
     *
     * @param tag
     * @param event
     */
    updateTagTitle(tag: CustomerTags, event): void {
        // Update the title on the tag
        tag.title = event.target.value;

        this._customerService.updateCustomerTag(tag.$id, tag).subscribe((response) => {
            // Mark for check
            this._changeDetectorRef.markForCheck();
        });
    }

    /**
     * Delete the tag
     *
     * @param tag
     */
    deleteTag(tag: CustomerTags): void {
        // Delete the tag from the server
        //  this._contactsService.deleteTag(tag.$id).subscribe();
        //TODO: check if this is used in any other place then Delete
        this._customerService.deleteCustomerTag(tag.$id).subscribe((response) => {

            // Mark for check
            this._changeDetectorRef.markForCheck();
        });


    }

    /**
     * Add tag to the contact
     *
     * @param tag
     */
    addTagToContact(tag: CustomerTags): void {
        // Add the tag
        this.contact.tags.unshift(tag.$id);

        // Update the contact form
        this.contactForm.get('tags').patchValue(this.contact.tags);

        // Mark for check
        this._changeDetectorRef.markForCheck();
    }

    /**
     * Remove tag from the contact
     *
     * @param tag
     */
    removeTagFromContact(tag: CustomerTags): void {
        // Remove the tag
        this.contact.tags.splice(this.contact.tags.findIndex(item => item === tag.$id), 1);

        // Update the contact form
        this.contactForm.get('tags').patchValue(this.contact.tags);

        // Mark for check
        this._changeDetectorRef.markForCheck();
    }

    /**
     * Toggle contact tag
     *
     * @param tag
     */
    toggleContactTag(tag: CustomerTags): void {
        if (this.contact.tags.includes(tag.$id)) {
            this.removeTagFromContact(tag);
        }
        else {
            this.addTagToContact(tag);
        }
    }

    /**
     * Should the create tag button be visible
     *
     * @param inputValue
     */
    shouldShowCreateTagButton(inputValue: string): boolean {
        return !!!(inputValue === '' || this.tags.findIndex(tag => tag.title.toLowerCase() === inputValue.toLowerCase()) > -1);
    }

    /**
     * Add the email field
     */
    addEmailField(): void {
        // Create an empty email form group
        const emailFormGroup = this._formBuilder.group({
            email: [''],
            label: [''],
        });

        // Add the email form group to the emails form array
        (this.contactForm.get('emails') as UntypedFormArray).push(emailFormGroup);

        // Mark for check
        this._changeDetectorRef.markForCheck();
    }

    /**
     * Remove the email field
     *
     * @param index
     */
    removeEmailField(index: number): void {
        // Get form array for emails
        const emailsFormArray = this.contactForm.get('emails') as UntypedFormArray;

        // Remove the email field
        emailsFormArray.removeAt(index);

        // Mark for check
        this._changeDetectorRef.markForCheck();
    }

    /**
     * Add an empty phone number field
     */
    addPhoneNumberField(): void {
        // Create an empty phone number form group
        const phoneNumberFormGroup = this._formBuilder.group({

            phoneNumber: [''],
            label: [''],
        });

        // Add the phone number form group to the phoneNumbers form array
        (this.contactForm.get('phoneNumbers') as UntypedFormArray).push(phoneNumberFormGroup);

        // Mark for check
        this._changeDetectorRef.markForCheck();
    }

    /**
     * Remove the phone number field
     *
     * @param index
     */
    removePhoneNumberField(index: number): void {
        // Get form array for phone numbers
        const phoneNumbersFormArray = this.contactForm.get('phoneNumbers') as UntypedFormArray;


        // Remove the phone number field
        phoneNumbersFormArray.removeAt(index);

        // Mark for check
        this._changeDetectorRef.markForCheck();
    }




    /**
   * Add the address field
   */
    addAddressField(): void {
        // Create an empty address form group
        const addressesFormGroup = this._formBuilder.group({
            address: [''],
            label: [''],
            latLon: [''],
        });

        // Add the address form group to the addresses form array
        (this.contactForm.get('addresses') as UntypedFormArray).push(addressesFormGroup);

        // Mark for check
        this._changeDetectorRef.markForCheck();
    }

    /**
     * Remove the address field
     *
     * @param index
     */
    removeAddressField(index: number): void {
        // Get form array for addresses
        const addressesFormArray = this.contactForm.get('addresses') as UntypedFormArray;

        // Remove the address field
        addressesFormArray.removeAt(index);

        // Mark for check
        this._changeDetectorRef.markForCheck();
    }

    /**
     * Track by function for ngFor loops
     *
     * @param index
     * @param item
     */
    trackByFn(index: number, item: any): any {
        return item.id || index;
    }


    onAutocompleteSelected(location: any, index: number) {
        // Assuming 'addresses' is a FormArray inside your form
        const addressesArray = this.contactForm.get('addresses') as FormArray;

        // Find the address FormGroup at the specified index
        const addressGroup = addressesArray.at(index) as FormGroup;

        // Update the 'address' FormControl within the address FormGroup
        addressGroup.get('address').setValue(location.address);
        addressGroup.get('latLon').setValue(location.latLon);
    }



}


import { TextFieldModule } from '@angular/cdk/text-field';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDrawer, MatSidenavModule } from '@angular/material/sidenav';
import { FuseMediaWatcherService } from '@fuse/services/media-watcher';
import { Subject, takeUntil } from 'rxjs';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatDialog } from '@angular/material/dialog';
import { FuseLoadingService } from '@fuse/services/loading';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatMenuModule } from '@angular/material/menu';

import { ContactsDetailsComponent } from './contacts-details/contacts-details.component';
import { Customer, CustomerTags } from 'app/core/databaseModels/customers/contacts.types';
import { CustomerService } from 'app/core/databaseModels/customers/customers.service';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@Component({
    selector: 'tools-customer',
    templateUrl: './customer.component.html',
    styleUrls: ['./customer.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [MatMenuModule,
        MatPaginatorModule,
        MatSortModule,
        MatTableModule,
        MatSidenavModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        TextFieldModule,
        MatSelectModule,
        MatOptionModule,
        MatButtonModule,
        ContactsDetailsComponent,
        MatProgressSpinnerModule]
})
export class CustomerComponent implements OnInit, OnDestroy {
    @ViewChild(MatPaginator) paginator: MatPaginator;
    @ViewChild(MatSort) sort: MatSort;
    @ViewChild('matDrawer', { static: true }) matDrawer: MatDrawer;

    private _unsubscribeAll: Subject<any> = new Subject<any>();
    displayedColumns: string[] = ['avatar', 'name', 'email', 'phone', 'actions'];
    dataSource: MatTableDataSource<Customer>;
    drawerMode: 'side' | 'over';

    isLoading: boolean = false; // Initially, data is loading
    isDrawerOpen: boolean = false;
    drawerOpened: boolean;


    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseMediaWatcherService: FuseMediaWatcherService,
        private _matDialog: MatDialog,
        private _fuseLoadingService: FuseLoadingService,
        private _fuseConfirmationService: FuseConfirmationService,
        private _customerService: CustomerService,

    ) {

        this.drawerOpened = false;

    }

    //TODO:Load phone number and email inside table row and update the avatar name in two word!

    customers: Customer[];
    tags: CustomerTags[]
    selectedCustomer: Customer;
    isNewItem = false;

    ngOnInit(): void {

        this.isLoading = true;
        this._fuseLoadingService.show();




        this._customerService.getCustomers().subscribe(customers => {
            this._customerService.customers$
                .pipe(takeUntil(this._unsubscribeAll))
                .subscribe(customers => {
                    this.customers = customers;
                    //  console.log(this.customers);
                    this.dataSource = new MatTableDataSource(this.customers);
                    if (this.customers.length > 0) {
                        this.dataSource.paginator = this.paginator;
                        this.dataSource.sort = this.sort;
                    }


                    this.isLoading = false;
                    this._fuseLoadingService.hide();

                    // Mark for check
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();
                });
        });


        this._customerService.getCustomerTags().subscribe(tags => {
            this._customerService.tags$
                .pipe(takeUntil(this._unsubscribeAll))
                .subscribe(tags => {
                    this.tags = tags;
                })
        })

        this.drawerMode = 'over';
        this._fuseMediaWatcherService.onMediaQueryChange$('(min-width: 1440px)')
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((state) => {
                // Calculate the drawer mode
                this.drawerMode = 'over';


            });
    }
    onBackdropClicked() {
        this.matDrawer.close();

    }

    applyFilter(event: Event) {
        const filterValue = (event.target as HTMLInputElement).value;
        this.dataSource.filter = filterValue.trim().toLowerCase();

        if (this.dataSource.paginator) {
            this.dataSource.paginator.firstPage();
        }
    }
    /**
     * Drawer opened changed
     *
     * @param opened
     */
    drawerOpenedChanged(opened: boolean): void {
        this.drawerOpened = opened;
    }

    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }


    addNewItem() {

        this.selectedCustomer = this.initializeToNull<Customer>();
        this.selectedCustomer.$id = null;
        this._customerService.getOrganisationID().subscribe(organisationID => {
            this.selectedCustomer.organisationID = organisationID;
            this.selectedCustomer.tags = [];
            this.isNewItem = true;
            // console.log(this.selectedCustomer);
            this.matDrawer.open();
        });


    }


    viewDetails(rowData: any) {
        // Mark for check
        this.isNewItem = false;
        //    console.log(rowData.addresses);



        if (rowData.addresses) {
            rowData.addresses = rowData.addresses.map(str => (typeof str === 'string' ? JSON.parse(str) : str));
        }
        if (rowData.phoneNumbers) {
            rowData.phoneNumbers = rowData.phoneNumbers.map(str => (typeof str === 'string' ? JSON.parse(str) : str));
        }
        if (rowData.emails) {
            rowData.emails = rowData.emails.map(str => (typeof str === 'string' ? JSON.parse(str) : str));
        }


        this.selectedCustomer = rowData;
        //  console.log(this.selectedCustomer)
        this._changeDetectorRef.markForCheck();
        this.matDrawer.toggle();

    }

    //  the delete function
    deleteItem(rowData: any) {
        // Open the confirmation dialog
        const confirmation = this._fuseConfirmationService.open({
            title: 'Delete confirmation!',
            message: `Are you sure you want to delete <i class="text-semibold text-red-600">${rowData.name}</i>? This action cannot be undone!`,
            actions: {
                confirm: {
                    label: 'Delete',
                },
            },
            icon: {
                show: true,
                name: 'heroicons_outline:trash',
            },
        });
        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            this._fuseLoadingService.show();
            // If the confirm button pressed...
            if (result === 'confirmed') {

                this._customerService.deleteCustomer(rowData.$id).subscribe(result => {

                    this._customerService.deleteFile(rowData.avatarImageId).subscribe(result => {
                        this._fuseLoadingService.hide();

                        // Mark for check
                        this._changeDetectorRef.markForCheck();
                        this._changeDetectorRef.detectChanges();
                        //  console.log('Deleting item:', rowData);

                    });

                })


            } else { this._fuseLoadingService.hide(); }

        });

    }



    //Export table

    exportTable(exportType: string) {
        console.log(exportType);
    }


    onDrawerOpened() {
        this.isDrawerOpen = true;
    }

    onDrawerClosed() {
        this.isDrawerOpen = false;
    }
    closeDrawer(mode: string) {
        //  console.log(mode);
        this.matDrawer.close();
        this._changeDetectorRef.markForCheck();



    }

    //initializeToNull
    initializeToNull<T>(): T {
        const proxy = new Proxy({}, {
            get: () => null
        });
        // Create a new object with null values
        const obj = Object.assign({}, proxy);
        return obj as T;
    }


}

<div class="-m-6 flex max-h-screen max-w-240 flex-col md:min-w-160">
    <!-- Header -->
    <div
        class="flex h-16 flex-0 items-center justify-between bg-primary pl-6 pr-3 text-on-primary sm:pl-8 sm:pr-5"
    >
        <div class="text-lg font-medium">Add New Organisation</div>
        <button mat-icon-button (click)="discard()" [tabIndex]="-1">
            <mat-icon
                class="text-current"
                [svgIcon]="'heroicons_outline:x-mark'"
            ></mat-icon>
        </button>
    </div>

    <!-- Organisations form -->

    <form
        class="flex flex-auto flex-col overflow-y-auto p-6 sm:p-8"
        [formGroup]="organisationsForm"
    >
        <div class="grid grid-cols-2 grid-rows-1 gap-4">
            <div>
                <!-- Section -->
                <div class="flex w-full">
                    <div class="w-full">
                        <div class="text-xl">Company Profile</div>
                        <div class="text-secondary">
                            Following information is publicly show on the User
                            platform.
                        </div>
                    </div>
                    <!-- Avatar -->
                    <div class="flex flex-auto items-end -mt-16 ml-20">
                        <div
                            class="relative flex items-center justify-center w-32 h-32 rounded-full overflow-hidden ring-4 ring-bg-card mt-10"
                        >
                            <!-- Upload / Remove avatar -->
                            <div
                                class="absolute inset-0 bg-black bg-opacity-50 z-10"
                            ></div>
                            <div
                                class="absolute inset-0 flex items-center justify-center z-20"
                            >
                                <div>
                                    <input
                                        id="avatar-file-input"
                                        class="absolute h-0 w-0 opacity-0 invisible pointer-events-none"
                                        type="file"
                                        [multiple]="false"
                                        [accept]="'image/jpeg, image/png'"
                                        (change)="
                                            uploadAvatar(avatarFileInput.files)
                                        "
                                        #avatarFileInput
                                    />
                                    <label
                                        class="flex items-center justify-center w-10 h-10 rounded-full cursor-pointer hover:bg-hover"
                                        for="avatar-file-input"
                                        matRipple
                                    >
                                        <mat-icon
                                            class="text-white"
                                            [svgIcon]="
                                                'heroicons_outline:camera'
                                            "
                                        ></mat-icon>
                                    </label>
                                </div>
                                <div>
                                    <button
                                        mat-icon-button
                                        (click)="removeAvatar()"
                                    >
                                        <mat-icon
                                            class="text-white"
                                            [svgIcon]="
                                                'heroicons_outline:trash'
                                            "
                                        ></mat-icon>
                                    </button>
                                </div>
                            </div>
                            <!-- Image/Letter -->

                            @if (newOrganisation) {
                                @if (newOrganisation.avatar) {
                                    <img
                                        class="object-cover w-full h-full"
                                        [src]="newOrganisation.avatar"
                                    />
                                }
                                @if (
                                    !newOrganisation.avatar &&
                                    newOrganisation.organisationName
                                ) {
                                    <div
                                        class="flex items-center justify-center w-full h-full rounded overflow-hidden uppercase text-8xl font-bold leading-none bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                    >
                                        {{
                                            newOrganisation.organisationName.charAt(
                                                0
                                            )
                                        }}
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
                <div class="grid sm:grid-cols-4 gap-6 w-full mt-8">
                    <!-- Name -->
                    <div class="sm:col-span-4">
                        <mat-form-field
                            class="w-full"
                            appearance="fill"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Company Name</mat-label>
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_solid:building-office-2'"
                                matPrefix
                            ></mat-icon>
                            <input
                                [formControlName]="'organisationName'"
                                matInput
                            />
                            @if (
                                organisationsForm
                                    .get("organisationName")
                                    .hasError("required")
                            ) {
                                <mat-error>
                                    Company Name is required
                                </mat-error>
                            }
                        </mat-form-field>
                    </div>
                    <!-- ABN -->
                    <div class="sm:col-span-4">
                        <mat-form-field
                            class="w-full"
                            appearance="fill"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>ABN Number:</mat-label>
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_solid:check-badge'"
                                matPrefix
                            ></mat-icon>
                            <input [formControlName]="'abn'" matInput />
                        </mat-form-field>
                    </div>

                    <!-- Address -->
                    <div class="sm:col-span-4">
                        <mat-form-field
                            class="w-full"
                            appearance="fill"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Address</mat-label>
                            <div class="text-secondary" matPrefix>
                                <i class="fa-solid fa-location-dot"></i>
                            </div>
                            <input
                                class="ml-2"
                                matInput
                                [formControlName]="'address'"
                                [spellcheck]="false"
                                matMapsAutocomplete
                                [country]="'au'"
                                [placeholder]="'Enter your home address'"
                                (onAutocompleteSelected)="
                                    onAutocompleteSelected($event)
                                "
                            />
                        </mat-form-field>
                    </div>

                    <!-- Description -->
                    <div class="sm:col-span-4">
                        <mat-form-field
                            class="w-full"
                            appearance="fill"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>About</mat-label>
                            <textarea
                                matInput
                                [formControlName]="'description'"
                                cdkTextareaAutosize
                                [cdkAutosizeMinRows]="5"
                            ></textarea>
                        </mat-form-field>
                        <div class="mt-1 text-md text-hint">
                            Brief description for your client Company.
                        </div>
                    </div>
                    <!---Start Subscription Date-->
                    <div class="sm:col-span-2">
                        <mat-form-field
                            class="w-full"
                            appearance="fill"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Start Subscription Date</mat-label>
                            <input
                                [formControlName]="'subscriptionStartDate'"
                                matInput
                                [matDatepicker]="picker"
                            />
                            <mat-datepicker-toggle
                                [for]="picker"
                                matSuffix
                            ></mat-datepicker-toggle>
                            <mat-datepicker #picker></mat-datepicker>
                            @if (
                                organisationsForm
                                    .get("subscriptionStartDate")
                                    .hasError("required")
                            ) {
                                <mat-error>
                                    Start Subscription Date is required
                                </mat-error>
                            }
                        </mat-form-field>
                    </div>
                    <!---End Subscription Date-->
                    <div class="sm:col-span-2">
                        <mat-form-field
                            class="w-full"
                            appearance="fill"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>End Subscription Date</mat-label>
                            <input
                                [formControlName]="'subscriptionEndDate'"
                                matInput
                                [matDatepicker]="picker2"
                            />
                            <mat-datepicker-toggle
                                [for]="picker2"
                                matSuffix
                            ></mat-datepicker-toggle>
                            <mat-datepicker #picker2></mat-datepicker>
                        </mat-form-field>
                    </div>
                </div>
            </div>
            <div>
                <!-- Divider -->
                <div class="mt-20 mb-10 border-t"></div>
                <!-- Section -->
                <div class="w-full">
                    <div class="text-xl">Contact Information</div>
                    <div class="text-secondary">
                        Communication details in case we want to connect with
                        you. These will be kept private.
                    </div>
                </div>
                <!-- Emails -->
                <div class="sm:col-span-2">
                    <div class="space-y-4">
                        <div class="space-y-4">
                            @for (
                                email of organisationsForm.get("emails")[
                                    "controls"
                                ];
                                track trackByFn(i, email);
                                let i = $index;
                                let first = $first;
                                let last = $last
                            ) {
                                <div class="flex">
                                    <mat-form-field
                                        class="flex-auto"
                                        appearance="fill"
                                        [subscriptSizing]="'dynamic'"
                                    >
                                        @if (first) {
                                            <mat-label>Email</mat-label>
                                        }
                                        <mat-icon
                                            matPrefix
                                            class="hidden icon-size-5 sm:flex"
                                            [svgIcon]="
                                                'heroicons_solid:envelope'
                                            "
                                        ></mat-icon>
                                        <input
                                            matInput
                                            [formControl]="email.get('email')"
                                            [placeholder]="'Email address'"
                                            [spellcheck]="false"
                                        />
                                        @if (
                                            email.get("email").hasError("email")
                                        ) {
                                            <mat-error>
                                                Please enter a valid email
                                                address
                                            </mat-error>
                                        }
                                    </mat-form-field>
                                    <mat-form-field
                                        class="ml-2 w-full max-w-24 flex-auto sm:ml-4 sm:max-w-40"
                                        [subscriptSizing]="'dynamic'"
                                    >
                                        @if (first) {
                                            <mat-label>Label</mat-label>
                                        }
                                        <mat-icon
                                            matPrefix
                                            class="hidden icon-size-5 sm:flex"
                                            [svgIcon]="'heroicons_solid:tag'"
                                        ></mat-icon>
                                        <input
                                            matInput
                                            [formControl]="email.get('label')"
                                            [placeholder]="'Label'"
                                        />
                                    </mat-form-field>
                                    <!-- Remove email -->
                                    @if (!(first && last)) {
                                        <div
                                            class="flex w-10 items-center pl-2"
                                            [ngClass]="{ 'mt-6': first }"
                                        >
                                            <button
                                                class="h-8 min-h-8 w-8"
                                                mat-icon-button
                                                (click)="removeEmailField(i)"
                                                matTooltip="Remove"
                                            >
                                                <mat-icon
                                                    class="icon-size-5"
                                                    [svgIcon]="
                                                        'heroicons_solid:trash'
                                                    "
                                                ></mat-icon>
                                            </button>
                                        </div>
                                    }
                                </div>
                            }
                        </div>
                        <div
                            class="group -ml-4 mt-2 inline-flex cursor-pointer items-center rounded px-4 py-2"
                            (click)="addEmailField()"
                        >
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_solid:plus-circle'"
                            ></mat-icon>
                            <span
                                class="text-secondary ml-2 font-medium group-hover:underline"
                                >Add an email address</span
                            >
                        </div>
                    </div>
                </div>
                <!-- Phone numbers -->
                <div class="sm:col-span-2">
                    <div class="space-y-4">
                        @for (
                            phoneNumber of organisationsForm.get(
                                "phoneNumbers"
                            )["controls"];
                            track trackByFn(i, phoneNumber);
                            let i = $index;
                            let first = $first;
                            let last = $last
                        ) {
                            <div class="flex">
                                <mat-form-field
                                    class="flex-auto"
                                    appearance="fill"
                                    [subscriptSizing]="'dynamic'"
                                >
                                    @if (first) {
                                        <mat-label>Phone</mat-label>
                                    }
                                    <mat-icon
                                        matPrefix
                                        class="hidden icon-size-5 sm:flex"
                                        [svgIcon]="'heroicons_solid:phone'"
                                    ></mat-icon>
                                    <input
                                        matInput
                                        [formControl]="
                                            phoneNumber.get('phoneNumber')
                                        "
                                        [placeholder]="'Phone'"
                                    />
                                </mat-form-field>
                                <mat-form-field
                                    class="ml-2 w-full max-w-24 flex-auto sm:ml-4 sm:max-w-40"
                                    [subscriptSizing]="'dynamic'"
                                >
                                    @if (first) {
                                        <mat-label>Label</mat-label>
                                    }
                                    <mat-icon
                                        matPrefix
                                        class="hidden icon-size-5 sm:flex"
                                        [svgIcon]="'heroicons_solid:tag'"
                                    ></mat-icon>
                                    <input
                                        matInput
                                        [formControl]="phoneNumber.get('label')"
                                        [placeholder]="'Label'"
                                    />
                                </mat-form-field>
                                <!-- Remove phone number -->
                                @if (!(first && last)) {
                                    <div
                                        class="flex w-10 items-center pl-2"
                                        [ngClass]="{ 'mt-6': first }"
                                    >
                                        <button
                                            class="h-8 min-h-8 w-8"
                                            mat-icon-button
                                            (click)="removePhoneNumberField(i)"
                                            matTooltip="Remove"
                                        >
                                            <mat-icon
                                                class="icon-size-5"
                                                [svgIcon]="
                                                    'heroicons_solid:trash'
                                                "
                                            ></mat-icon>
                                        </button>
                                    </div>
                                }
                            </div>
                        }
                    </div>
                    <div
                        class="group -ml-4 mt-4 inline-flex cursor-pointer items-center rounded px-4 py-2"
                        (click)="addPhoneNumberField()"
                    >
                        <mat-icon
                            class="icon-size-5"
                            [svgIcon]="'heroicons_solid:plus-circle'"
                        ></mat-icon>
                        <span
                            class="text-secondary ml-2 font-medium group-hover:underline"
                            >Add a phone number</span
                        >
                    </div>
                </div>
                <div class="grid sm:grid-cols-4 gap-6 w-full mt-8">
                    <!-- Country -->
                    <div class="sm:col-span-2">
                        <mat-form-field
                            class="w-full"
                            appearance="fill"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Country</mat-label>
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_solid:map-pin'"
                                matPrefix
                            ></mat-icon>
                            <mat-select [formControlName]="'country'">
                                <mat-option [value]="'australia'"
                                    >Australia</mat-option
                                >
                                <mat-option [value]="'usa'"
                                    >United States</mat-option
                                >
                                <mat-option [value]="'canada'"
                                    >Canada</mat-option
                                >
                                <mat-option [value]="'mexico'"
                                    >Mexico</mat-option
                                >
                                <mat-option [value]="'france'"
                                    >France</mat-option
                                >
                                <mat-option [value]="'germany'"
                                    >Germany</mat-option
                                >
                                <mat-option [value]="'italy'">Italy</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <!-- Timezone -->
                    <div class="sm:col-span-2">
                        <mat-form-field
                            class="w-full"
                            appearance="fill"
                            [subscriptSizing]="'dynamic'"
                        >
                            <mat-label>Timezone</mat-label>
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_solid:globe-alt'"
                                matPrefix
                            ></mat-icon>
                            <mat-select
                                [formControlName]="'timezone'"
                                (selectionChange)="onTimeZoneChange($event)"
                            >
                                @for (timezone of timeZones; track timezone) {
                                    <mat-option [value]="timezone"
                                        >{{ timezone.name }} /
                                        {{ timezone.offset }}</mat-option
                                    >
                                }
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div class="col-span-4">
                        <div class="grid w-full gap-6 sm:grid-cols-2">
                            <!-- Plan -->
                            <div class="sm:col-span-3">
                                <fuse-alert
                                    [appearance]="'outline'"
                                    [type]="'info'"
                                >
                                    Changing the plan will take effect
                                    immediately. You will be charged for the
                                    rest of the current month.
                                </fuse-alert>
                            </div>

                            <mat-radio-group
                                class="pointer-events-none invisible absolute h-0 w-0"
                                [formControlName]="'planId'"
                                #planRadioGroup="matRadioGroup"
                            >
                                @for (
                                    plan of subscriptionPlans;
                                    track trackByFn($index, plan)
                                ) {
                                    <mat-radio-button
                                        [value]="plan.$id"
                                        [disabled]="plan.disabled"
                                    ></mat-radio-button>
                                }
                            </mat-radio-group>
                            @for (
                                plan of subscriptionPlans;
                                track trackByFn($index, plan)
                            ) {
                                <div
                                    class="bg-card relative flex flex-col items-start justify-start rounded-md p-6 shadow"
                                    [ngClass]="{
                                        'cursor-pointer': !plan.disabled,
                                        'ring ring-inset ring-primary':
                                            planRadioGroup.value === plan.$id
                                    }"
                                    (click)="
                                        !plan.disabled &&
                                            (planRadioGroup.value = plan.$id) &&
                                            setPlanId(plan.$id)
                                    "
                                >
                                    @if (planRadioGroup.value === plan.$id) {
                                        <mat-icon
                                            class="absolute right-0 top-0 mr-3 mt-3 text-primary icon-size-7"
                                            [svgIcon]="
                                                'heroicons_solid:check-circle'
                                            "
                                        ></mat-icon>
                                    }
                                    <div class="font-semibold">
                                        {{ plan.subscriptionName }}
                                    </div>
                                    <div
                                        class="text-secondary mt-1 whitespace-normal"
                                    >
                                        {{ plan.details }}
                                    </div>
                                    <div class="flex-auto"></div>
                                    <div class="mt-8 text-lg">
                                        <span>{{
                                            plan.price
                                                | currency
                                                    : plan.currency
                                                    : "symbol"
                                                    : "1.0"
                                        }}</span>
                                        <span class="text-secondary">
                                            / per team member per day
                                        </span>
                                    </div>
                                </div>
                            } @empty {
                                @if (isLoading) {
                                    <div class="flex justify-center p-10">
                                        <mat-spinner
                                            class="spinnr"
                                            [diameter]="24"
                                        >
                                        </mat-spinner>
                                        <div class="ml-5 text-center">
                                            Loading...
                                        </div>
                                    </div>
                                } @else {
                                    <div class="flex justify-center p-10">
                                        <span class="text-center"
                                            >there are no plans</span
                                        >
                                    </div>
                                }
                            }
                        </div>
                    </div>
                    <!-- Divider -->
                    <div class="mt-5 mb-5 border-t"></div>
                    <!-- Approved status -->
                    <div
                        class="flex items-center justify-between sm:col-span-4"
                    >
                        <div
                            class="flex-auto cursor-pointer"
                            (click)="approvalToggle.toggle()"
                        >
                            <div class="font-medium leading-6">
                                Organisation Approved Status
                            </div>
                            @if (organisationsForm.get("status").value) {
                                <div class="text-secondary text-md">
                                    The Organisation is approved. If you want to
                                    disapprove the access, check the toggle.
                                </div>
                            } @else {
                                <div class="text-secondary text-md">
                                    By default, the Organisation is not
                                    approved. If you want to approve the
                                    Organisation, check the toggle.
                                </div>
                            }
                        </div>
                        <mat-slide-toggle
                            class="ml-4"
                            [color]="'primary'"
                            [formControlName]="'status'"
                            #approvalToggle
                        >
                        </mat-slide-toggle>
                    </div>
                </div>
            </div>
        </div>

        <!-- Divider -->
        <div class="mt-11 mb-10 border-t"></div>

        <!-- Actions -->
        <div
            class="mt-4 flex flex-col justify-between sm:mt-6 sm:flex-row sm:items-center"
        >
            <div class="-ml-2"></div>

            <div class="mt-4 flex items-center sm:mt-0">
                <!-- Discard -->
                <button
                    class="ml-auto sm:ml-0"
                    mat-stroked-button
                    (click)="discard()"
                >
                    Discard
                </button>

                <!-- Save -->
                <button
                    class="order-first sm:order-last ml-5"
                    mat-flat-button
                    [disabled]="organisationsForm.invalid || isSaving"
                    [color]="'primary'"
                    (click)="save()"
                >
                    <div class="flex items-center">
                        @if (isSaving) {
                            <mat-spinner diameter="20"></mat-spinner>
                            <span class="ml-2"> Saving...</span>
                        } @else {
                            Save
                        }
                    </div>
                </button>
            </div>
        </div>
    </form>
</div>

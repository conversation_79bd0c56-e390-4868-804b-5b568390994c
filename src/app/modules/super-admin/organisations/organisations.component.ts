import { TextFieldModule } from '@angular/cdk/text-field';
import { <PERSON><PERSON><PERSON>cy<PERSON><PERSON><PERSON>, NgClass } from '@angular/common';
import { ChangeDetectorRef, Component, ElementRef, OnDestroy, OnInit, Signal, ViewEncapsulation, inject, viewChild } from '@angular/core';
import {
    FormArray,
    FormsModule,
    ReactiveFormsModule,
    UntypedFormArray,
    UntypedFormBuilder,
    UntypedFormGroup,
    Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { FuseAlertComponent } from '@fuse/components/alert';
import { FuseLoadingService } from '@fuse/services/loading';
import { OrganisationsService } from 'app/core/databaseModels/organisations/organisations.service';
import { Organisation } from 'app/core/databaseModels/organisations/organisations.types';
import { SubscriptionPlansService } from 'app/core/databaseModels/subscriptionPlans/subscriptionPlans.service';
import { SubscriptionPlans } from 'app/core/databaseModels/subscriptionPlans/subscriptionPlans.types';
import { MatMapsAutocompleteModule } from 'app/modules/widgets/mat-maps-autocomplete/mat-maps-autocomplete.module';
import { assign } from 'lodash';
import { provideNgxMask } from 'ngx-mask';
import { Subject, takeUntil } from 'rxjs';


@Component({
    selector: 'app-organisations',
    encapsulation: ViewEncapsulation.None,
    imports: [
        NgClass,
        MatButtonModule,
        MatIconModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatSlideToggleModule,
        MatDatepickerModule,
        MatSelectModule,
        MatOptionModule,
        TextFieldModule,
        MatProgressSpinnerModule,
        CurrencyPipe,
        MatRadioModule,
        FuseAlertComponent,
        MatMapsAutocompleteModule,
    ],
    providers: [provideNgxMask()],
    templateUrl: './organisations.component.html',
    styleUrl: './organisations.component.scss'
})
export class OrganisationsComponent implements OnInit, OnDestroy {
    private organisationData: { organisation: Organisation } = inject(MAT_DIALOG_DATA)
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    private _avatarFileInput = viewChild<ElementRef<HTMLInputElement>>('avatarFileInput');

    isSaving: boolean = false;
    organisationsForm: UntypedFormGroup;
    newOrganisation = {} as Organisation;
    subscriptionPlans: SubscriptionPlans[] = [];
    selectedImage: any;
    isLoading: boolean = false;
    isUploading: boolean = false;

    timeZones = [
        { name: 'UTC', offset: '+00:00' },
        { name: 'America/New_York', offset: '-05:00' },
        { name: 'America/Los_Angeles', offset: '-08:00' },
        { name: 'Europe/London', offset: '+00:00' },
        { name: 'Europe/Paris', offset: '+01:00' },
        { name: 'Asia/Tokyo', offset: '+09:00' },
        { name: 'Australia/Sydney', offset: '+10:00' },
        { name: 'Australia/Melbourne', offset: '+10:00' },
        // Add more time zones as needed
    ];

    constructor(
        public matDialogRef: MatDialogRef<OrganisationsComponent>,
        private _formBuilder: UntypedFormBuilder,
        private _fuseLoadingService: FuseLoadingService,
        private _organisationsService: OrganisationsService,
        private _changeDetectorRef: ChangeDetectorRef,
        private _subscriptionPlansService: SubscriptionPlansService,
    ) {


    }
    ngOnInit(): void {
        this.organisationsForm = this._formBuilder.group({
            organisationName: ['', Validators.required],
            subscriptionStartDate: ['', Validators.required],
            subscriptionEndDate: [''],
            abn: [''],
            description: [''],
            avatar: [''],
            avatarImageId: [''],
            teamMemberCredits: [''],
            timezone: [''],
            timezoneOffset: [''],
            country: [''],
            phoneNumbers: this._formBuilder.array([]),
            emails: this._formBuilder.array([]),
            address: [''],
            planId: ['', Validators.required],
            status: [''],
            addressLatLon: [''],


        });


        this.loadSubscriptionPlans();
    }
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    discard(): void {

        this.matDialogRef.close();
    }
    loadSubscriptionPlans() {
        this.isLoading = true;

        this._subscriptionPlansService.getSubscriptionPlans().pipe(
            takeUntil(this._unsubscribeAll)
        ).subscribe((plans) => {
            this.subscriptionPlans = plans;
            this.isLoading = false;
            // Patch value from data
            this.patchOrganisation();

            this._changeDetectorRef.markForCheck();

        })
    }

    patchOrganisation() {
        if (this.organisationData && this.organisationData.organisation) {

            this.newOrganisation = this.organisationData.organisation;
            console.log(this.newOrganisation)
            this.organisationsForm.patchValue(this.newOrganisation);
            this.isUploading = true;
            // Find the timezone object that matches the stored timezone and offset
            const selectedTimezone = this.timeZones.find(tz =>
                tz.name === this.newOrganisation.timezone && tz.offset === this.newOrganisation.timezoneOffset);
            this.organisationsForm.get('timezone').setValue(selectedTimezone);

            (this.organisationsForm.get('emails') as UntypedFormArray).clear();
            (
                this.organisationsForm.get('phoneNumbers') as UntypedFormArray
            ).clear();
            // Clear the emails and phoneNumbers form arrays
            this.organisationsForm.patchValue(this.newOrganisation);
            // Setup the emails form array
            const emailFormGroups = [];
            if (this.newOrganisation.emails.length > 0) {
                // Iterate through them
                this.newOrganisation.emails.forEach((email) => {
                    // Create an email form group
                    if (typeof email === 'string') {
                        email = JSON.parse(email);
                    }
                    emailFormGroups.push(
                        this._formBuilder.group({
                            email: [email.email],
                            label: [email.label],
                        })
                    );
                });
            } else {
                // Create an email form group
                emailFormGroups.push(
                    this._formBuilder.group({
                        email: [''],
                        label: [''],
                    })
                );
            }

            // Add the email form groups to the emails form array
            emailFormGroups.forEach((emailFormGroup) => {
                (this.organisationsForm.get('emails') as UntypedFormArray).push(
                    emailFormGroup
                );
            });

            // Setup the phone numbers form array
            const phoneNumbersFormGroups = [];
            if (this.newOrganisation.phoneNumbers.length > 0) {
                // Iterate through them
                this.newOrganisation.phoneNumbers.forEach((phoneNumber) => {
                    // Create an phone number form group
                    if (typeof phoneNumber === 'string') {
                        phoneNumber = JSON.parse(phoneNumber);
                    }
                    phoneNumbersFormGroups.push(
                        this._formBuilder.group({
                            phoneNumber: [phoneNumber.phoneNumber],
                            label: [phoneNumber.label],
                        })
                    );
                });
            } else {
                // Create an phone number form group
                phoneNumbersFormGroups.push(
                    this._formBuilder.group({
                        phoneNumber: [''],
                        label: [''],
                    })
                );
            }

            // Add the phone number form groups to the phoneNumbers form array
            phoneNumbersFormGroups.forEach((phoneNumberFormGroup) => {
                (this.organisationsForm.get('phoneNumbers') as UntypedFormArray).push(
                    phoneNumberFormGroup
                );
            });


        } else {
            this.addEmailField();
            this.addPhoneNumberField();
        }
    }

    onAutocompleteSelected(location: any) {
        this.organisationsForm.controls.address.setValue(location.address);
        this.organisationsForm.controls.addressLatLon.setValue(location.latLon);
    }
    get emailsFormArray(): FormArray {
        return this.organisationsForm.get('emails') as FormArray;
    }

    get phoneNumbersFormArray(): FormArray {
        return this.organisationsForm.get('phoneNumbers') as FormArray;
    }

    trackByFn(index: number, item: any): any {
        return item.id || index;
    }

    uploadAvatar(fileList: FileList): void {
        // Return if canceled
        if (!fileList.length) {
            return;
        }

        const allowedTypes = ['image/jpeg', 'image/png'];
        const file = fileList[0];

        // Return if the file is not allowed
        if (!allowedTypes.includes(file.type)) {
            return;
        }

        // update the avatar url from file
        this.newOrganisation.avatar = URL.createObjectURL(file);

        this.selectedImage = file;
    }
    removeAvatar(): void {
        // Get the form control for 'avatar'
        const avatarFormControl = this.organisationsForm.get('avatar');

        // Set the avatar as null
        avatarFormControl.setValue(null);

        // Set the file input value as null
        this._avatarFileInput().nativeElement.value = null;

        // Update the contact
        this.newOrganisation.avatar = null;
        this.selectedImage = null;


    }

    setPlanId(id: string) {
        this.newOrganisation.planId = id;
        this.organisationsForm.get('planId').setValue(id);
        this.organisationsForm.updateValueAndValidity();
    }

    addPhoneNumberField(): void {
        // Create an empty phone number form group
        const phoneNumberFormGroup = this._formBuilder.group({

            phoneNumber: [''],
            label: [''],
        });

        // Add the phone number form group to the phoneNumbers form array
        (this.organisationsForm.get('phoneNumbers') as UntypedFormArray).push(phoneNumberFormGroup);
        this.organisationsForm.updateValueAndValidity();

        // Mark for check
        this._changeDetectorRef.markForCheck();
    }

    removePhoneNumberField(index: number): void {
        // Get form array for phone numbers
        const phoneNumbersFormArray = this.organisationsForm.get('phoneNumbers') as UntypedFormArray;


        // Remove the phone number field
        phoneNumbersFormArray.removeAt(index);
        // Mark for check
        this._changeDetectorRef.markForCheck();
    }
    addEmailField(): void {
        // Create an empty email form group
        const emailFormGroup = this._formBuilder.group({
            email: [''],
            label: [''],
        });

        // Add the email form group to the emails form array
        (this.organisationsForm.get('emails') as UntypedFormArray).push(
            emailFormGroup
        );
        // Trigger validation on the parent form
        this.organisationsForm.updateValueAndValidity();
        // Mark for check
        this._changeDetectorRef.markForCheck();
    }

    removeEmailField(index: number): void {
        // Get form array for emails
        const emailsFormArray = this.organisationsForm.get(
            'emails'
        ) as UntypedFormArray;

        // Remove the email field
        emailsFormArray.removeAt(index);


        // Mark for check
        this._changeDetectorRef.markForCheck();
    }

    onTimeZoneChange(event: any): void {
        console.log(event.value);
        // Pars the time zone

        this.organisationsForm.controls.timezone.setValue(event.name);
        this.organisationsForm.controls.timezoneOffset.setValue(event.offset);

    }
    save(): void {


        if (this.organisationsForm.valid) {
            try {
                this.isSaving = true;
                this._fuseLoadingService.show();
                if (this.isUploading) {
                    this.newOrganisation = assign(this.newOrganisation, this.organisationsForm.value);
                } else {
                    this.newOrganisation = assign({}, this.organisationsForm.value);
                }

                if (!this.newOrganisation.teamMemberCredits) {
                    this.newOrganisation.teamMemberCredits = 0
                }

                this.newOrganisation.phoneNumbers =
                    this.newOrganisation.phoneNumbers?.map(str =>
                        typeof str === 'string' ? JSON.parse(str) : str
                    );

                this.newOrganisation.emails =
                    this.newOrganisation.emails?.map(str =>
                        typeof str === 'string' ? JSON.parse(str) : str
                    );
                if (this.isUploading) {
                    // Update Data
                    this.updateOrganisations(this.newOrganisation);

                } else {
                    //New Data
                    if (this.selectedImage) {
                        this._organisationsService.uploadFile(this.selectedImage).subscribe(
                            (result) => {
                                this.newOrganisation.avatar = result.fileUrl;
                                this.newOrganisation.avatarImageId = result.fileId;
                                this.createOrganisation();

                            },
                            (error) => {
                                console.error('Error uploading file:', error);
                                this.isSaving = false;
                                this._fuseLoadingService.hide();
                            });

                    } else {
                        this.createOrganisation();
                    }
                }
            } catch (error) {
                console.error('Error saving user:', error.message);
                this.isSaving = false;

            }
        }

    }

    createOrganisation(): void {
        this.newOrganisation.emails = this.newOrganisation.emails.filter(email => email.email);
        this.newOrganisation.phoneNumbers = this.newOrganisation.phoneNumbers.filter(phoneNumber => phoneNumber.phoneNumber);



        this._organisationsService
            .createOrganisation(this.newOrganisation)
            .subscribe(
                () => {
                    this.isSaving = false;
                    this._fuseLoadingService.hide();
                    this.matDialogRef.close();
                },
                (error) => {
                    console.error('Error saving organisation:', error);
                    this.isSaving = false;
                    this._fuseLoadingService.hide();
                }
            );
    }

    updateOrganisations(newOrganisation: Organisation) {
        newOrganisation.emails = newOrganisation.emails.filter(email => email.email);
        newOrganisation.phoneNumbers = newOrganisation.phoneNumbers.filter(phoneNumber => phoneNumber.phoneNumber);

        try {
            this._fuseLoadingService.show();

            if (this.selectedImage) {
                // Upload new image
                let _oldImageID = newOrganisation.avatarImageId;
                this._organisationsService.uploadFile(this.selectedImage).subscribe(
                    result => {
                        newOrganisation.avatar = result.fileUrl;
                        newOrganisation.avatarImageId = result.fileId;
                        this._organisationsService.updateOrganisation(newOrganisation.$id, newOrganisation).subscribe(result => {
                            this._fuseLoadingService.hide();
                            // Mark for check
                            this._changeDetectorRef.markForCheck();
                            this._changeDetectorRef.detectChanges();
                            if (_oldImageID) {
                                this._organisationsService.deleteFile(_oldImageID).subscribe(result => {
                                    // console.log(result);
                                    this.isSaving = false;
                                    this._changeDetectorRef.markForCheck();
                                    this._changeDetectorRef.detectChanges();
                                    this.matDialogRef.close();
                                });
                            } else {
                                this.isSaving = false;
                                this._changeDetectorRef.markForCheck();
                                this._changeDetectorRef.detectChanges();
                                this.matDialogRef.close();
                            }
                        })
                    }
                );
            } else if (newOrganisation.avatar === null && newOrganisation.avatarImageId) {
                // Delete existing image
                this._organisationsService.deleteFile(newOrganisation.avatarImageId).subscribe(result => {
                    newOrganisation.avatarImageId = null; // Clear the ID after deletion
                    this._organisationsService.updateOrganisation(newOrganisation.$id, newOrganisation).subscribe(result => {
                        this._fuseLoadingService.hide();
                        this.isSaving = false;
                        this._changeDetectorRef.markForCheck();
                        this._changeDetectorRef.detectChanges();
                        this.matDialogRef.close();
                    });
                });
            } else {
                // No image changes, just update the organisation
                this._organisationsService.updateOrganisation(newOrganisation.$id, newOrganisation).subscribe(result => {
                    this._fuseLoadingService.hide();
                    this.isSaving = false;
                    this._changeDetectorRef.markForCheck();
                    this._changeDetectorRef.detectChanges();
                    this.matDialogRef.close();
                });
            }
        } catch (error) {
            console.error('Error saving user:', error.message);
            this.isSaving = false;
        }
    }



}

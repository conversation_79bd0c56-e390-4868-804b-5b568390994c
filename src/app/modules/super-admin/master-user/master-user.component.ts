
import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, Signal, ViewEncapsulation, inject, input } from '@angular/core';
import {
    FormsModule,
    ReactiveFormsModule,
    UntypedFormBuilder,
    UntypedFormGroup,
    Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { FuseLoadingService } from '@fuse/services/loading';
import { OrganisationsService } from 'app/core/databaseModels/organisations/organisations.service';
import { Organisation } from 'app/core/databaseModels/organisations/organisations.types';
import { StripeService } from 'app/core/databaseModels/stripe.service';
import { SubscriptionPlansService } from 'app/core/databaseModels/subscriptionPlans/subscriptionPlans.service';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { User } from 'app/core/databaseModels/user/user.types';
import { OrganisationSelectComponent } from 'app/modules/widgets/organisation-select/organisation-select.component';
import { assign, result } from 'lodash';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { Subject } from 'rxjs';



@Component({
    selector: 'app-master-user',
    encapsulation: ViewEncapsulation.None,
    imports: [
        MatButtonModule,
        MatIconModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatSlideToggleModule,
        MatDatepickerModule,
        MatSelectModule,
        NgxMaskDirective,
        MatProgressSpinnerModule,
        OrganisationSelectComponent,
    ],
    providers: [provideNgxMask()],
    templateUrl: './master-user.component.html',
    styleUrl: './master-user.component.scss'
})
export class MasterUserComponent implements OnInit, OnDestroy {
    private organisationInData: { organisations: Organisation[] } = inject(MAT_DIALOG_DATA);
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    isSaving: boolean = false;
    isChecking: boolean = false;
    isFound: boolean = false;
    masterUserForm: UntypedFormGroup;
    user = {} as User;
    stripeCustomerId;
    stripePriceId = '';
    selectedUser: User;
    newItem = input<boolean>(false);
    isUploading: boolean = false;
    selectedOrganisation: Organisation;
    organisationsData: Organisation[] = [];
    generateReportDialog;

    constructor(
        public matDialogRef: MatDialogRef<MasterUserComponent>,
        private _userService: UserService,
        private _changeDetectorRef: ChangeDetectorRef,
        private _formBuilder: UntypedFormBuilder,
        private _fuseLoadingService: FuseLoadingService,
        private _stripeService: StripeService,
        private _organisationsService: OrganisationsService,
        private _fuseConfirmationService: FuseConfirmationService,

    ) { }
    ngOnInit(): void {
        this.masterUserForm = this._formBuilder.group({
            organisationID: [''],
            name: ['', [Validators.required]],
            email: ['', [Validators.required, Validators.email]],
            phone: ['', [Validators.required]],
            defaultUser: [true],
            status: [true],
            verifiedPhone: [false],
            verifiedEmail: [false],

        });

        if (this.organisationInData && this.organisationInData.organisations) {
            this.organisationsData = this.organisationInData.organisations;

        }


        // Add listeners for email and phone changes
        this.masterUserForm.get('email').valueChanges.subscribe(value => {
            if (this.user && this.user.email !== value) {
                this.checkExistEmail(value);
            } else {
                this.masterUserForm.get('email').setErrors(null);
            }
        });

        this.masterUserForm.get('phone').valueChanges.subscribe(value => {
            if (this.user && this.user.phone !== value) {
                this.checkExistPhone(value);
            } else {
                this.masterUserForm.get('phone').setErrors(null);
            }
        });
    }

    ngOnDestroy(): void {


        this.masterUserForm.reset();
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    discard(): void {
        this.matDialogRef.close();
    }

    onOrganisationSelected(organisation: Organisation): void {

        if (!organisation) {
            this.selectedOrganisation = null;
            this.selectedUser = null;

            return;
        }
        // Set the selected customer
        if (Array.isArray(organisation.emails) && organisation.emails.length > 0) {
            organisation.emails = organisation.emails.map(email => {
                // Parse stringified JSON objects
                if (typeof email === 'string') {
                    return JSON.parse(email);
                }
                // Return the email if it's already an object
                return email;
            });
        }
        // Ensure phoneNumbers exist and is an array
        if (Array.isArray(organisation.phoneNumbers) && organisation.phoneNumbers.length > 0) {
            organisation.phoneNumbers = organisation.phoneNumbers.map(phone => {
                // Parse stringified JSON objects
                if (typeof phone === 'string') {
                    return JSON.parse(phone);
                }
                // Return the phone if it's already an object
                return phone;
            });
        }
        this.selectedOrganisation = organisation;

        // Find if Organisation is already have any Master User
        this.checkForMasterUser()

    }
    checkForMasterUser() {
        this.isChecking = true;
        this._userService.getMasterUser(this.selectedOrganisation.$id).subscribe(result => {
            if (result) {
                this.isChecking = false;
                this.selectedUser = result;
                this.isFound = true;
                //  this.masterUserForm.patchValue(result);
            } else {
                this.isChecking = false;
                this.isFound = false;
                this.masterUserForm.reset();
            }
        })
    }
    checkExistEmail(email: string) {
        if (email == null) {
            return false;
        }
        this._userService.checkExistingEmail(email.trim()).subscribe(result => {
            if (result) {
                this.masterUserForm.get('email').setErrors({ emailExists: true });
            } else {
                this.masterUserForm.get('email').setErrors(null);
            }
        });
    }

    checkExistPhone(phone: string) {
        if (phone == null) {
            return false;
        }
        this._userService.checkExistingPhone(`+${phone.replace(/\D/g, '')}`).subscribe(result => {
            if (result) {
                this.masterUserForm.get('phone').setErrors({ phoneExists: true });
            } else {
                this.masterUserForm.get('phone').setErrors(null);
            }
        });
    }
    save() {
        if (this.isUploading) {
            // UPDATE Default User
            this.user = assign(this.user, this.masterUserForm.value);
        } else {
            this.user = assign(this.user, this.masterUserForm.value);
            this.saveNew(this.user);

        }
    }
    saveNew(newUserData: User) {
        if (this.masterUserForm.invalid) {
            return;
        }
        // get stripeCustomerId from organisation PlanId

        this.generateResponseDialogWithMessage(`Please wait while we Creating your Master User`);


        this._fuseLoadingService.show();
        this.isSaving = true;
        newUserData.phone = `+${newUserData.phone.replace(/\D/g, '')}`;
        newUserData.notes = 'This is a default user and cannot be deleted. Your Stripe account linked to this email address cannot be deleted either.';
        newUserData.defaultUser = true;
        newUserData.status = true;
        newUserData.verifiedEmail = false;
        newUserData.verifiedPhone = false;
        // console.log('newUserData:', newUserData);
        this.generateReportDialog.close();
        this.generateResponseDialogWithMessage(`Generating Started!`);
        try {
            newUserData.organisationID = this.selectedOrganisation.$id;

            this._userService.create(newUserData).subscribe((user: User) => {

                //console.log('newUser:', user);
                this.generateReportDialog.close();

                this.generateResponseDialogWithMessage(`New Default User Created!`);
                // Create Stripe Customer
                const data = {
                    customerEmail: user.email,
                    customerName: user.name,
                };

                // this._stripeService.createStripeCustomer(data).subscribe((stripeUser) => {
                //     if (stripeUser) {
                //         //Update Organisation StripeCustomerId
                //         //  console.log('stripeUser:', stripeUser);
                //     }
                //     this.generateReportDialog.close();

                //     this.generateResponseDialogWithMessage(`Stripe Customer Created!`);

                // });

                this._organisationsService.updateOrganisationStripe(this.selectedOrganisation.$id, user.email).subscribe(org => {
                    // console.log('org:', org);
                    this.generateReportDialog.close();

                    this.generateResponseDialogWithMessage(`Organisation Updated for Stripe!`);
                    // Mark for check
                    this._userService.createRecovery(newUserData.email).subscribe(recovery => {
                        //   console.log('recovery:', recovery);
                        this.generateReportDialog.close();

                        this.generateResponseDialogWithMessage(`Recovery Email sent to: ${user.email}!`);
                        this._fuseLoadingService.hide();
                        this.masterUserForm.reset();
                        this.isSaving = false;
                        this._changeDetectorRef.markForCheck();
                        this._changeDetectorRef.detectChanges();
                        // Close delay
                        setTimeout(() => {
                            this.generateReportDialog.close();
                            this.matDialogRef.close();
                        }, 2000);

                    })
                })


            })


        } catch (error) {
            console.error('Error saving user:', error);
            this.isSaving = false;

        }
    }


    generateResponseDialogWithMessage(message: string) {

        this.generateReportDialog = this._fuseConfirmationService.open({
            title: `Creating Master User...`,
            message: message,
            icon: { show: true, name: "heroicons_outline:check", color: "success" },
            actions: { confirm: { show: false }, cancel: { show: false } },
            dismissible: false,
        });

    }


    callRecovery(email: string) {
        this._userService.createRecovery(email).subscribe(result => {
            console.log('recovery:', result);
            this._fuseLoadingService.hide();
            // Mark for check
            this._changeDetectorRef.markForCheck();
            this._changeDetectorRef.detectChanges();
        })
    }



}

<div class="-m-6 flex max-h-screen max-w-240 flex-col md:min-w-160">
    <!-- Header -->
    <div
        class="flex h-16 flex-0 items-center justify-between bg-primary pl-6 pr-3 text-on-primary sm:pl-8 sm:pr-5"
    >
        <div class="text-lg font-medium">Add Master User To Organisation</div>
        <button mat-icon-button (click)="discard()" [tabIndex]="-1">
            <mat-icon
                class="text-current"
                [svgIcon]="'heroicons_outline:x-mark'"
            ></mat-icon>
        </button>
    </div>

    <!-- Master user form -->

    <form
        class="flex flex-auto flex-col overflow-y-auto p-6 sm:p-8"
        [formGroup]="masterUserForm"
    >
        <div>
            <!-- Section Organisation Selector-->
            <div>
                <div
                    class="flex flex-col flex-auto p-6 bg-card rounded-2xl shadow min-w-160"
                >
                    <div class="mt-6">Selected Organisation details.</div>
                    <hr class="w-full border-t my-6" />
                    <div class="flex flex-col">
                        <div class="flex items-center">
                            <organisation-select
                                class="w-full"
                                [organisations]="organisationsData"
                                (organisationSelected)="
                                    onOrganisationSelected($event)
                                "
                            />
                        </div>
                        @if (!selectedOrganisation) {
                            <div
                                class="flex flex-auto flex-col items-center justify-center bg-gray-100 dark:bg-transparent"
                            >
                                <div class="text-secondary">
                                    Please select an organisation.
                                </div>
                            </div>
                        } @else {
                            <div>
                                <div class="flex items-center mt-4">
                                    <i
                                        class="icon-size-5 mr-3 fa-duotone fa-building"
                                    ></i>
                                    <span class="leading-none">{{
                                        selectedOrganisation?.organisationName
                                    }}</span>
                                </div>
                                <!-- Emails -->
                                @if (selectedOrganisation?.emails.length) {
                                    <div class="flex items-center mt-4">
                                        <i
                                            class="icon-size-5 mr-3 fa-duotone fa-envelope"
                                        ></i>
                                        @for (
                                            email of selectedOrganisation.emails;
                                            track email
                                        ) {
                                            <div
                                                class="flex items-center leading-6"
                                            >
                                                <span
                                                    class="ml-2.5 font-mono"
                                                    >{{ email.email }}</span
                                                >
                                                @if (email.label) {
                                                    <div
                                                        class="text-md truncate text-secondary"
                                                    >
                                                        <span class="mx-2"
                                                            >&bull;</span
                                                        >
                                                        <span
                                                            class="font-medium"
                                                            >{{
                                                                email.label
                                                            }}</span
                                                        >
                                                    </div>
                                                }
                                            </div>
                                        }
                                    </div>
                                }

                                <!-- Phone -->
                                @if (
                                    selectedOrganisation?.phoneNumbers.length
                                ) {
                                    <div class="flex items-center mt-4">
                                        <i
                                            class="icon-size-5 mr-3 fa-duotone fa-phone"
                                        ></i>
                                        @for (
                                            phoneNumber of selectedOrganisation.phoneNumbers;
                                            track phoneNumber
                                        ) {
                                            <div
                                                class="flex items-center leading-6"
                                            >
                                                <div class="ml-2.5 font-mono">
                                                    {{
                                                        phoneNumber.phoneNumber
                                                    }}
                                                </div>
                                                @if (phoneNumber.label) {
                                                    <div
                                                        class="text-md truncate text-secondary"
                                                    >
                                                        <span class="mx-2"
                                                            >&bull;</span
                                                        >
                                                        <span
                                                            class="font-medium"
                                                            >{{
                                                                phoneNumber.label
                                                            }}</span
                                                        >
                                                    </div>
                                                }
                                            </div>
                                        }
                                    </div>
                                }
                                <!--Organisation address-->
                                @if (selectedOrganisation.address) {
                                    <div class="flex items-center mt-4">
                                        <i
                                            class="icon-size-5 mr-3 fa-duotone fa-location-dot"
                                        ></i>
                                        <span class="leading-none">{{
                                            selectedOrganisation?.address
                                        }}</span>
                                    </div>
                                }
                            </div>
                        }
                    </div>
                </div>
            </div>
            <!-- Section User-->
            @if (selectedOrganisation) {
                @if (isChecking) {
                    <div
                        class="flex flex-auto flex-col items-center justify-center bg-gray-100 dark:bg-transparent"
                    >
                        <mat-spinner
                            color="primary"
                            mode="indeterminate"
                            [diameter]="40"
                        ></mat-spinner>
                        <div class="text-secondary">
                            Checking for master user...
                        </div>
                    </div>
                } @else {
                    @if (isFound) {
                        <div
                            class="flex p-10 flex-auto flex-col items-center justify-center bg-gray-100 dark:bg-transparent"
                        >
                            <mat-icon
                                class="text-yellow-600 icon-size-7"
                                [svgIcon]="
                                    'heroicons_solid:exclamation-triangle'
                                "
                            ></mat-icon>
                            <div class="text-secondary">
                                Master user already exists.
                            </div>
                            <!-- Divider -->
                            <div class="mt-11 mb-10 border-t"></div>

                            <!-- Details -->
                            <div
                                class="flex flex-col items-center lg:ml-8 lg:mt-0 lg:items-start"
                            >
                                <div class="text-lg font-bold leading-none">
                                    {{ selectedUser.name }}
                                </div>
                                <div class="text-secondary">
                                    <span class="mr-2 text-black">email:</span>
                                    {{ selectedUser.email }}
                                </div>
                            </div>

                            <div class="mt-8">
                                <button
                                    mat-stroked-button
                                    color="primary"
                                    (click)="callRecovery(selectedUser.email)"
                                >
                                    Resend Recover Email
                                </button>
                            </div>
                        </div>
                    } @else {
                        <div>
                            <div class="flex w-full"></div>
                            <!-- Name -->
                            <div class="mt-8">
                                <mat-form-field
                                    class="w-full"
                                    [subscriptSizing]="'dynamic'"
                                >
                                    <mat-label>Full Name</mat-label>
                                    <mat-icon
                                        matPrefix
                                        class="hidden sm:flex icon-size-5"
                                        [svgIcon]="
                                            'heroicons_solid:user-circle'
                                        "
                                    ></mat-icon>
                                    <input
                                        matInput
                                        [formControlName]="'name'"
                                        [placeholder]="'Name'"
                                        [spellcheck]="false"
                                    />
                                </mat-form-field>
                            </div>
                            <!-- Phone numbers -->

                            <div class="mt-8">
                                <mat-form-field
                                    class="w-full"
                                    [subscriptSizing]="'dynamic'"
                                >
                                    <mat-label
                                        >Phone [Must start with country code ex:
                                        +61]</mat-label
                                    >
                                    <mat-icon
                                        matPrefix
                                        class="hidden sm:flex icon-size-5"
                                        [svgIcon]="'heroicons_solid:phone'"
                                    ></mat-icon>
                                    <input
                                        type="tel"
                                        mask="+00000 000 000"
                                        matInput
                                        [formControlName]="'phone'"
                                        [placeholder]="'phone'"
                                    />
                                    @if (
                                        masterUserForm
                                            .get("phone")
                                            .hasError("phoneExists")
                                    ) {
                                        <mat-error>
                                            Phone number already exists.
                                        </mat-error>
                                    }
                                </mat-form-field>
                            </div>

                            <!-- Emails -->
                            <div class="mt-8">
                                <mat-form-field
                                    class="w-full"
                                    [subscriptSizing]="'dynamic'"
                                >
                                    <mat-label>Email</mat-label>
                                    <mat-icon
                                        matPrefix
                                        class="hidden sm:flex icon-size-5"
                                        [svgIcon]="'heroicons_solid:envelope'"
                                    ></mat-icon>
                                    <input
                                        type="email"
                                        matInput
                                        [formControlName]="'email'"
                                        [placeholder]="'email'"
                                        [spellcheck]="false"
                                    />
                                    @if (
                                        masterUserForm
                                            .get("email")
                                            .hasError("emailExists")
                                    ) {
                                        <mat-error>
                                            Email already exists.
                                        </mat-error>
                                    }
                                </mat-form-field>
                            </div>
                        </div>
                    }
                }
            }
        </div>
        <!-- Divider -->
        <div class="mt-10 mb-10 border-t"></div>
        <!-- Actions -->
        <div
            class="mt-4 flex flex-col justify-between sm:mt-6 sm:flex-row sm:items-center"
        >
            <div class="-ml-2"></div>
            <div class="mt-4 flex items-center sm:mt-0">
                <!-- Discard -->
                <button
                    class="ml-auto sm:ml-0"
                    mat-stroked-button
                    (click)="discard()"
                >
                    Discard
                </button>

                <!-- Save -->
                <button
                    class="order-first sm:order-last ml-5"
                    mat-flat-button
                    [disabled]="masterUserForm.invalid || isSaving"
                    [color]="'primary'"
                    (click)="save()"
                >
                    <div class="flex items-center">
                        @if (isSaving) {
                            <mat-spinner diameter="20"></mat-spinner>
                            <span class="ml-2"> Saving...</span>
                        } @else {
                            Save
                        }
                    </div>
                </button>
            </div>
        </div>
    </form>
</div>

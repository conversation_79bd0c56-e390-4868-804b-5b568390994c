import { <PERSON><PERSON><PERSON><PERSON>, NgClass } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild, ViewEncapsulation, inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { OrganisationsService } from 'app/core/databaseModels/organisations/organisations.service';
import { Organisation } from 'app/core/databaseModels/organisations/organisations.types';
import { NgApexchartsModule } from 'ng-apexcharts';
import { Subject } from 'rxjs';
import { OrganisationsComponent } from './organisations/organisations.component';
import { MasterUserComponent } from './master-user/master-user.component';

@Component({
    selector: 'super-admin',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [MatButtonModule, MatIconModule,
        MatMenuModule, MatDividerModule, NgApexchartsModule,
        MatTableModule, MatSortModule, NgClass, MatProgressBarModule,
        DatePipe, MatInputModule, MatDialogModule,
        FormsModule, ReactiveFormsModule, MatFormFieldModule, MatProgressSpinnerModule],
    templateUrl: './super-admin.component.html',
    styleUrl: './super-admin.component.scss'
})
export class SuperAdminComponent implements OnInit, AfterViewInit, OnDestroy {

    @ViewChild(' organisationsTable', { read: MatSort }) organisationsTableMatSort: MatSort;

    private _unsubscribeAll: Subject<any> = new Subject<any>();
    organisationsDataSource: MatTableDataSource<any> = new MatTableDataSource();
    organisationsTableColumns: string[] = ['organisationName', 'subscriptionStartDate', 'subscriptionEndDate', 'paymentStatus', 'status', 'action'];
    isLoading: boolean = false;

    readonly addDialog = inject(MatDialog);


    // Private
    organisations: Organisation[] = [];


    constructor(
        private _organisationsService: OrganisationsService,
        private _changeDetectorRef: ChangeDetectorRef,
    ) {

    }

    ngOnInit(): void {
        this.loadExistingOrganisations();
    }
    ngAfterViewInit(): void {

    }
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }


    trackByFn(index: number, item: any): any {
        return item.id || index;
    }


    loadExistingOrganisations(limit: number = 10): void {
        try {
            this.isLoading = true;
            this._organisationsService
                .getOrganisations(limit)
                .subscribe((organisations) => {

                    this.organisations = organisations;
                    this.organisationsDataSource.data = organisations;
                    this.organisationsDataSource.sort = this.organisationsTableMatSort;
                    this._changeDetectorRef.markForCheck();
                    this.isLoading = false;
                });
        }
        catch (error) {
            console.error(error);
            this.isLoading = false;
            this._changeDetectorRef.markForCheck();
        }
    }

    getOrganisationsLength(status: String): number {
        switch (status) {
            case "Active":
                return this.organisations.filter(organisation => organisation.status == true).length

            case "Inactive":
                return this.organisations.filter(organisation => organisation.status == false).length

            case "Unpaid":
                return this.organisations.filter(organisation => organisation.paymentStatus === "Unpaid" || organisation.paymentStatus === null).length

            case "Expired":
                const today = new Date();
                today.setHours(0, 0, 0, 0); // Normalize to start of the day

                return this.organisations.filter(organisation => {
                    const { subscriptionEndDate } = organisation;

                    if (!subscriptionEndDate) {
                        // Optionally, handle organisations without a subscription end date
                        return false;
                    }

                    // Parse the subscriptionEndDate if it's a string
                    const endDate = typeof subscriptionEndDate === 'string'
                        ? new Date(subscriptionEndDate)
                        : subscriptionEndDate;

                    if (isNaN(endDate.getTime())) {
                        // Log invalid dates for debugging
                        console.warn(`Invalid subscriptionEndDate for organisation ID: ${organisation.$id}`);
                        return false;
                    }

                    // Compare the endDate with today's date
                    return endDate < today;
                }).length;
            default:
                return this.organisations.length
        }


    }

    paymentStatus(payment: string): number {

        if (payment === "Paid") {
            return this.organisations.filter(organisation => organisation.paymentStatus === "Paid").length
        } else {
            return this.organisations.filter(organisation => organisation.paymentStatus === "Unpaid").length
        }
    }

    applyFilter(event: Event) {
        const filterValue = (event.target as HTMLInputElement).value;
        this.organisationsDataSource.filter = filterValue.trim().toLowerCase();

        if (this.organisationsDataSource.paginator) {
            this.organisationsDataSource.paginator.firstPage();
        }
    }


    openOrganisationDialog(organisation?: Organisation) {

        const dialogRef = this.addDialog.open(OrganisationsComponent, {
            data: {
                organisation
            }
        })

        dialogRef.afterClosed().subscribe(result => {
            this.loadExistingOrganisations();
            console.log(`Dialog result: ${result}`);
        });
    }
    openMasterUserDialog() {
        const dialogRef = this.addDialog.open(MasterUserComponent, {
            data: {
                organisations: this.organisations
            }
        });

        dialogRef.afterClosed().subscribe(result => {
            console.log(`Dialog result: ${result}`);
        });
    }

}

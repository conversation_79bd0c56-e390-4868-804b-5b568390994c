<div class="flex flex-col flex-auto w-full p-5">
    <div class="flex flex-wrap w-full max-w-screen-xl mx-auto p-6 md:p-8">
        <!-- Title and action buttons -->
        <div class="flex items-center justify-between w-full">
            <div>
                <h2 class="text-3xl font-semibold tracking-tight leading-8">
                    Super admin dashboard
                </h2>
                <div class="font-medium tracking-tight text-secondary">
                    Keep track of your platform status
                </div>
            </div>
            <div class="flex items-center ml-6">
                <button class="hidden sm:inline-flex" mat-stroked-button>
                    <mat-icon
                        class="icon-size-5"
                        [svgIcon]="'heroicons_solid:document-chart-bar'"
                    ></mat-icon>
                    <span class="ml-2">Reports</span>
                </button>
                <button
                    class="hidden bg-accent sm:inline-flex ml-3 text-white"
                    mat-flat-button
                    (click)="openMasterUserDialog()"
                >
                    <i
                        class="icon-size-5 fa-duotone fa-solid fa-person-circle-plus"
                    ></i>

                    <span class="ml-2">Add Master User</span>
                </button>
                <button
                    class="hidden sm:inline-flex ml-3"
                    mat-flat-button
                    [color]="'primary'"
                    (click)="openOrganisationDialog()"
                >
                    <i
                        class="icon-size-5 fa-duotone fa-solid fa-rectangles-mixed"
                    ></i>
                    <span class="ml-2">Add Organization</span>
                </button>

                <!-- Actions menu (visible on xs) -->
                <div class="sm:hidden">
                    <button [matMenuTriggerFor]="actionsMenu" mat-icon-button>
                        <mat-icon
                            [svgIcon]="'heroicons_mini:ellipsis-vertical'"
                        ></mat-icon>
                    </button>
                    <mat-menu #actionsMenu="matMenu">
                        <button mat-menu-item>Add Organization</button>
                        <button mat-menu-item>Add User</button>
                        <button mat-menu-item>Settings</button>
                    </mat-menu>
                </div>
            </div>
        </div>
    </div>
    <div
        class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 w-full min-w-0"
    >
        <!-- Summary -->
        <div
            class="flex flex-col flex-auto p-6 bg-card shadow rounded-2xl overflow-hidden"
        >
            <div class="flex items-start justify-between">
                <div
                    class="text-lg font-medium tracking-tight leading-6 truncate"
                >
                    Summary
                </div>
                <div class="ml-2 -mt-2 -mr-3">
                    <button mat-icon-button [matMenuTriggerFor]="summaryMenu">
                        <mat-icon
                            class="icon-size-5"
                            [svgIcon]="'heroicons_mini:ellipsis-vertical'"
                        ></mat-icon>
                    </button>
                    <mat-menu #summaryMenu="matMenu">
                        <button mat-menu-item>Yesterday</button>
                        <button mat-menu-item>2 days ago</button>
                        <button mat-menu-item>3 days ago</button>
                    </mat-menu>
                </div>
            </div>
            <div class="flex flex-col items-center mt-2">
                <div
                    class="text-7xl sm:text-8xl font-bold tracking-tight leading-none text-blue-500"
                >
                    <!-- Length  Of organisations status true-->
                    {{ getOrganisationsLength("Active") }}
                </div>
                <div
                    class="text-lg font-medium text-blue-600 dark:text-blue-500"
                >
                    Active Organisations
                </div>
                <div
                    class="flex items-baseline justify-center w-full mt-5 text-secondary"
                >
                    <div class="text-md font-medium truncate">Total:</div>
                    <div class="ml-1.5 text-lg font-semibold">
                        {{ getOrganisationsLength("Total") }}
                    </div>
                </div>
            </div>
        </div>
        <!-- Overdue -->
        <div
            class="flex flex-col flex-auto p-6 bg-card shadow rounded-2xl overflow-hidden"
        >
            <div class="flex items-start justify-between">
                <div
                    class="text-lg font-medium tracking-tight leading-6 truncate"
                >
                    Overdue
                </div>
                <div class="ml-2 -mt-2 -mr-3">
                    <button mat-icon-button [matMenuTriggerFor]="overdueMenu">
                        <mat-icon
                            class="icon-size-5"
                            [svgIcon]="'heroicons_mini:ellipsis-vertical'"
                        ></mat-icon>
                    </button>
                    <mat-menu #overdueMenu="matMenu">
                        <button mat-menu-item>Yesterday</button>
                        <button mat-menu-item>2 days ago</button>
                        <button mat-menu-item>3 days ago</button>
                    </mat-menu>
                </div>
            </div>
            <div class="flex flex-col items-center mt-2">
                <div
                    class="text-7xl sm:text-8xl font-bold tracking-tight leading-none text-red-500"
                >
                    {{ getOrganisationsLength("Unpaid") }}
                </div>
                <div class="text-lg font-medium text-red-600 dark:text-red-500">
                    Overdue Payments
                </div>
                <div
                    class="flex items-baseline justify-center w-full mt-5 text-secondary"
                >
                    <div class="text-md font-medium truncate">
                        Expired Subscriptions:
                    </div>
                    <div class="ml-1.5 text-lg font-semibold">
                        {{ getOrganisationsLength("Expired") }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="grid grid-cols-1 xl:grid-cols-3 gap-8 w-full mt-8">
        <!-- Recent organisations table -->
        <div
            class="xl:col-span-2 flex flex-col flex-auto bg-card shadow rounded-2xl overflow-hidden"
        >
            <div class="flex p-6">
                <div>
                    <div
                        class="mr-4 text-lg font-medium tracking-tight leading-6 truncate"
                    >
                        Existing Organisations
                    </div>
                    <div class="text-secondary font-medium">
                        {{ paymentStatus("Paid") }} paid,
                        {{ paymentStatus("Unpaid") }} unpaid
                    </div>
                </div>

                <!-- Actions -->
                <div
                    class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4 ml-auto"
                >
                    <!-- Search -->
                    <mat-form-field
                        class="fuse-mat-dense fuse-mat-rounded min-w-60"
                        [subscriptSizing]="'dynamic'"
                    >
                        <mat-icon
                            class="icon-size-5"
                            matPrefix
                            [svgIcon]="'heroicons_solid:magnifying-glass'"
                        ></mat-icon>
                        <input
                            matInput
                            (keyup)="applyFilter($event)"
                            [autocomplete]="'off'"
                            [placeholder]="'Search ...'"
                            #input
                        />
                    </mat-form-field>
                </div>
            </div>
            <div class="overflow-x-auto mx-6">
                @if (isLoading) {
                    <div class="flex justify-center p-10">
                        <mat-spinner class="spinnr" [diameter]="24">
                        </mat-spinner>
                        <div class="ml-5 text-center">Loading...</div>
                    </div>
                } @else if (organisations.length === 0) {
                    <div
                        class="flex flex-col items-center justify-center h-full"
                    >
                        <div class="text-secondary text-center">
                            No organisations found
                        </div>
                    </div>
                } @else {
                    <table
                        class="w-full bg-transparent"
                        mat-table
                        matSort
                        [dataSource]="organisationsDataSource"
                        [trackBy]="trackByFn"
                        #organisationsTable
                    >
                        <!-- organisationName -->
                        <ng-container matColumnDef="organisationName">
                            <th
                                mat-header-cell
                                mat-sort-header
                                *matHeaderCellDef
                            >
                                Organisation Name
                            </th>
                            <td mat-cell *matCellDef="let organisation">
                                <span
                                    class="pr-6 font-medium text-xl text-secondary whitespace-nowrap"
                                >
                                    {{ organisation.organisationName }}
                                </span>
                            </td>
                        </ng-container>

                        <!-- subscriptionStartDate -->
                        <ng-container matColumnDef="subscriptionStartDate">
                            <th
                                mat-header-cell
                                mat-sort-header
                                *matHeaderCellDef
                            >
                                Subscription Start Date
                            </th>
                            <td mat-cell *matCellDef="let organisation">
                                <span class="pr-6 whitespace-nowrap">
                                    {{
                                        organisation.subscriptionStartDate
                                            | date: "MMM dd, y"
                                    }}
                                </span>
                            </td>
                        </ng-container>

                        <!-- subscriptionEndDate -->
                        <ng-container matColumnDef="subscriptionEndDate">
                            <th
                                mat-header-cell
                                mat-sort-header
                                *matHeaderCellDef
                            >
                                Subscription End Date
                            </th>
                            <td mat-cell *matCellDef="let organisation">
                                <span class="pr-6 whitespace-nowrap">
                                    @if (organisation.subscriptionEndDate) {
                                        <i>{{
                                            organisation.subscriptionEndDate
                                                | date: "MMM dd, y"
                                        }}</i>
                                    } @else {
                                        <i class="fa-solid fa-infinity"></i>
                                    }
                                </span>
                            </td>
                        </ng-container>

                        <!-- paymentStatus -->
                        <ng-container matColumnDef="paymentStatus">
                            <th
                                mat-header-cell
                                mat-sort-header
                                *matHeaderCellDef
                            >
                                Payment Status
                            </th>
                            <td mat-cell *matCellDef="let organisation">
                                <span
                                    class="inline-flex items-center font-bold text-xs px-2.5 py-0.5 rounded-full tracking-wide uppercase"
                                    [ngClass]="
                                        organisation.paymentStatus &&
                                        organisation.paymentStatus.toLowerCase() ===
                                            'paid'
                                            ? 'bg-green-200 text-green-800 dark:bg-green-600 dark:text-green-50'
                                            : 'bg-red-200 text-red-800 dark:bg-red-600 dark:text-red-50'
                                    "
                                >
                                    <span
                                        class="pr-2 pl-2 font-medium whitespace-nowrap text-center"
                                    >
                                        {{
                                            organisation.paymentStatus &&
                                            organisation.paymentStatus.toLowerCase() ===
                                                "paid"
                                                ? "Paid"
                                                : "Unpaid"
                                        }}
                                    </span>
                                </span>
                            </td>
                        </ng-container>

                        <!-- Status -->
                        <ng-container matColumnDef="status">
                            <th
                                mat-header-cell
                                mat-sort-header
                                *matHeaderCellDef
                            >
                                Status
                            </th>
                            <td mat-cell *matCellDef="let organisation">
                                <span
                                    class="inline-flex items-center font-bold text-xs px-2.5 py-0.5 rounded-full tracking-wide uppercase"
                                    [ngClass]="{
                                        'bg-red-200 text-red-800 dark:bg-red-600 dark:text-red-50':
                                            !organisation.status,
                                        'bg-green-200 text-green-800 dark:bg-green-600 dark:text-green-50':
                                            organisation.status
                                    }"
                                >
                                    <span
                                        class="leading-relaxed whitespace-nowrap"
                                        >{{
                                            organisation.status == true
                                                ? "Active"
                                                : "Inactive"
                                        }}</span
                                    >
                                </span>
                            </td>
                        </ng-container>
                        <!-- Action -->
                        <ng-container matColumnDef="action">
                            <th
                                mat-header-cell
                                mat-sort-header
                                *matHeaderCellDef
                            >
                                Action
                            </th>
                            <td mat-cell *matCellDef="let organisation">
                                <button
                                    (click)="
                                        openOrganisationDialog(organisation)
                                    "
                                    mat-icon-button
                                >
                                    <mat-icon
                                        class="text-secondary"
                                        [svgIcon]="'heroicons_outline:pencil'"
                                    ></mat-icon>
                                </button>
                            </td>
                        </ng-container>

                        <!-- Footer -->
                        <ng-container matColumnDef="recentOrdersTableFooter">
                            <td
                                class="py-6 px-0 border-0"
                                mat-footer-cell
                                *matFooterCellDef
                                colspan="6"
                            >
                                <button
                                    class="min-w-56"
                                    mat-stroked-button
                                    (click)="loadExistingOrganisations(5000)"
                                >
                                    @if (isLoading) {
                                        <mat-spinner
                                            [diameter]="20"
                                            [mode]="'indeterminate'"
                                        ></mat-spinner>
                                    } @else {
                                        See all Organisation
                                    }
                                </button>
                            </td>
                        </ng-container>

                        <tr
                            mat-header-row
                            *matHeaderRowDef="organisationsTableColumns"
                        ></tr>
                        <tr
                            class="order-row h-16"
                            mat-row
                            *matRowDef="
                                let row;
                                columns: organisationsTableColumns
                            "
                        ></tr>
                        <tr
                            class="h-16 border-0"
                            mat-footer-row
                            *matFooterRowDef="['recentOrdersTableFooter']"
                        ></tr>
                    </table>
                }
            </div>
        </div>

        <!-- Budget -->
        <div class="flex flex-col flex-auto p-6 bg-card rounded-2xl shadow">
            <div class="flex items-center">
                <div class="flex flex-col">
                    <div
                        class="mr-4 text-lg font-medium tracking-tight leading-6 truncate"
                    >
                        Budget
                    </div>
                    <div class="text-secondary font-medium">
                        Monthly budget summary
                    </div>
                </div>
                <div class="ml-auto -mt-2 -mr-2">
                    <button mat-icon-button [matMenuTriggerFor]="budgetMenu">
                        <mat-icon
                            class="icon-size-5"
                            [svgIcon]="'heroicons_mini:ellipsis-vertical'"
                        ></mat-icon>
                    </button>
                    <mat-menu #budgetMenu="matMenu">
                        <button mat-menu-item>Expenses breakdown</button>
                        <button mat-menu-item>Savings breakdown</button>
                        <button mat-menu-item>Bills breakdown</button>
                        <mat-divider class="my-2"></mat-divider>
                        <button mat-menu-item>
                            <span class="flex items-center">
                                <mat-icon
                                    class="icon-size-5 mr-3"
                                    [svgIcon]="'heroicons_solid:printer'"
                                ></mat-icon>
                                <span>Print budget summary</span>
                            </span>
                        </button>
                        <button mat-menu-item>
                            <span class="flex items-center">
                                <mat-icon
                                    class="icon-size-5 mr-3"
                                    [svgIcon]="'heroicons_solid:envelope'"
                                ></mat-icon>
                                <span>Email budget summary</span>
                            </span>
                        </button>
                    </mat-menu>
                </div>
            </div>
            <div class="mt-6">
                Last month; you had <strong>223</strong> expense organisations,
                <strong>12</strong> savings entries and
                <strong>4</strong> bills.
            </div>
            <div class="my-8 space-y-8">
                <div class="flex flex-col">
                    <div class="flex items-center">
                        <div
                            class="flex items-center justify-center w-14 h-14 rounded bg-red-100 text-red-800 dark:bg-red-600 dark:text-red-50"
                        >
                            <mat-icon
                                class="text-current"
                                [svgIcon]="'heroicons_outline:credit-card'"
                            ></mat-icon>
                        </div>
                        <div class="flex-auto ml-4 leading-none">
                            <div class="text-sm font-medium text-secondary">
                                Expenses
                            </div>
                            <div class="mt-2 font-medium text-2xl">0.00</div>
                            <mat-progress-bar
                                class="mt-3 rounded-full"
                                [color]="'warn'"
                                [mode]="'determinate'"
                                [value]="73"
                            ></mat-progress-bar>
                        </div>
                        <div
                            class="flex items-end justify-end min-w-18 mt-auto ml-6"
                        >
                            <div class="text-lg leading-none">2.6%</div>
                            <mat-icon
                                class="text-green-600 icon-size-4 ml-1"
                                [svgIcon]="'heroicons_mini:arrow-long-down'"
                            ></mat-icon>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col">
                    <div class="flex items-center">
                        <div
                            class="flex items-center justify-center w-14 h-14 rounded bg-indigo-100 text-indigo-800 dark:bg-indigo-600 dark:text-indigo-50"
                        >
                            <mat-icon
                                class="text-current"
                                [svgIcon]="'heroicons_outline:banknotes'"
                            ></mat-icon>
                        </div>
                        <div class="flex-auto ml-4 leading-none">
                            <div class="text-sm font-medium text-secondary">
                                Savings
                            </div>
                            <div class="mt-2 font-medium text-2xl">0.00</div>
                            <mat-progress-bar
                                class="mt-3 rounded-full"
                                [mode]="'determinate'"
                                [value]="25"
                            ></mat-progress-bar>
                        </div>
                        <div
                            class="flex items-end justify-end min-w-18 mt-auto ml-6"
                        >
                            <div class="text-lg leading-none">12.7%</div>
                            <mat-icon
                                class="text-red-600 icon-size-4 ml-1"
                                [svgIcon]="'heroicons_mini:arrow-long-up'"
                            ></mat-icon>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col">
                    <div class="flex items-center">
                        <div
                            class="flex items-center justify-center w-14 h-14 rounded bg-teal-100 text-teal-800 dark:bg-teal-600 dark:text-teal-50"
                        >
                            <mat-icon
                                class="text-current"
                                [svgIcon]="'heroicons_outline:light-bulb'"
                            ></mat-icon>
                        </div>
                        <div class="flex-auto ml-4 leading-none">
                            <div class="text-sm font-medium text-secondary">
                                Bills
                            </div>
                            <div class="mt-2 font-medium text-2xl">0.00</div>
                            <mat-progress-bar
                                class="mt-3 rounded-full"
                                [mode]="'determinate'"
                                [value]="15"
                            ></mat-progress-bar>
                        </div>
                        <div
                            class="flex items-end justify-end min-w-18 mt-auto ml-6"
                        >
                            <div class="text-lg leading-none">105.7%</div>
                            <mat-icon
                                class="text-red-600 icon-size-4 ml-1"
                                [svgIcon]="'heroicons_mini:arrow-long-up'"
                            ></mat-icon>
                        </div>
                    </div>
                    <div class="mt-3 text-md text-secondary">
                        Exceeded your personal limit! Be careful next month.
                    </div>
                </div>
            </div>
            <div class="flex items-center mt-auto">
                <button class="mt-2" mat-stroked-button>
                    Download Summary
                </button>
            </div>
        </div>
    </div>
</div>

import { AfterViewInit, Component, inject, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import {
    MAT_DIALOG_DATA,
    MatDialog,
    MatDialogActions,
    MatDialogClose,
    MatDialogContent,
    MatDialogRef,
    MatDialogTitle,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { ScheduledJob } from 'app/core/databaseModels/scheduledJobs/scheduledJob.types';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';
import { Customer } from 'app/core/databaseModels/customers/contacts.types';
import { ExportTableService } from 'app/services/export-table.service';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { ScheduledJobsService } from 'app/core/databaseModels/scheduledJobs/scheduledJob.service';
import { Subject, takeUntil } from 'rxjs';
import { DatePipe, NgClass } from '@angular/common';
import { TeamMembersService } from 'app/core/databaseModels/teammembers/teammembers.service';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';


@Component({
    selector: 'app-order-history',
    imports: [DatePipe, NgClass, MatIconModule, MatTableModule, MatPaginatorModule, MatMenuModule, MatFormFieldModule, MatInputModule, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose, MatButtonModule, MatProgressSpinnerModule],
    templateUrl: './order-history.component.html',
    styleUrl: './order-history.component.scss'
})
export class OrderHistoryComponent implements OnInit, OnDestroy, OnDestroy, AfterViewInit {
    @ViewChild(MatPaginator) paginator: MatPaginator;
    private _unsubscribeAll: Subject<any> = new Subject<any>();

    data = inject(MAT_DIALOG_DATA);
    isLoading: boolean = false;
    customerId: string;
    scheduledJobList: ScheduledJob[] = [];
    teamMembers: TeamMember[];

    displayedColumns: string[] = ['avatar', 'jobTitle', 'dueDate', 'actions'];
    dataSource: MatTableDataSource<ScheduledJob> = new MatTableDataSource<ScheduledJob>();

    // Status Lookup objects
    dispatchStatusLookup = {
        '': { color: 'gray', icon: 'fa-solid fa-mobile-signal-out' },
        'null': { color: 'gray', icon: 'fa-solid fa-mobile-signal-out' },
        'dispatched': { color: 'blue', icon: 'fa-solid fa-check' },
        'received': { color: 'blue', icon: 'fa-solid fa-check-double' },
        'onMyWay': { color: 'yellow', icon: 'fa-solid fa-arrows-turn-right' },
        'finished': { color: 'green', icon: 'fa-solid fa-circle-check' },
        'started': { color: '#8FBC8F', icon: 'fa-solid fa-box-circle-check' },
    };

    jobStatusLookup = {
        'pending': { color: 'gray', icon: 'fa-solid fa-clock' },
        'added': { color: 'gray', icon: 'fa-solid fa-circle-dot' },
        'approved': { color: 'blue' },
        'started': { color: '#8FBC8F' },
        'onMyWay': { color: 'yellow' },
        'finished': { color: 'green' }
    };

    constructor(
        private dialogRef: MatDialogRef<OrderHistoryComponent>,
        private _scheduledJobsService: ScheduledJobsService,
        private _teamMembersService: TeamMembersService,


    ) {
        this.customerId = this.data.customerId;
    }

    ngOnInit(): void {

        this.loadJobList(this.customerId);
    }

    ngAfterViewInit() {
        this.dataSource.paginator = this.paginator;
    }
    ngOnDestroy(): void {
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    getApprovedStatus(approved: string): number {
        return this.scheduledJobList.filter(item => item.jobStatus === approved).length;
    }

    loadSelectedEvents(): void {

    }
    loadJobList(clientID) {
        this.isLoading = true;

        this._teamMembersService.getTeamMembers().subscribe((teamMembers) => {
            this.teamMembers = teamMembers;
            this._scheduledJobsService.getScheduledJobs(null, null, clientID, null, false)
                .pipe(takeUntil(this._unsubscribeAll))
                .subscribe(scheduledJobs => {
                    this.scheduledJobList = scheduledJobs.sort((a, b) => {
                        return new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime();
                    });
                    this.dataSource.data = this.scheduledJobList;
                    this.isLoading = false;
                });
        });
    }




    applyFilter(event: Event, filter?: string) {

        const filterValue = filter ? filter : (event.target as HTMLInputElement).value;
        this.dataSource.filter = filterValue.trim().toLowerCase();

        if (this.dataSource.paginator) {
            this.dataSource.paginator.firstPage();
        }
    }





    getTeamMemberInfo(id: string): { name: string, avatar: string | null, fullName: string } {
        try {
            const teamMember = this.teamMembers.find(item => item.$id === id);
            if (teamMember) {
                let name = '';
                if (teamMember.avatar) {
                    name = teamMember.avatar;
                } else {
                    // Handle a single name without spaces
                    const nameParts = teamMember.name.split(' ');
                    let initials = nameParts[0][0]; // Take the first letter of the first part
                    if (nameParts.length > 1) {
                        initials += nameParts[nameParts.length - 1][0]; // Add the first letter of the last part if it exists
                    }
                    name = initials.toUpperCase();
                }
                return {
                    name: name,
                    avatar: teamMember.avatar || null,
                    fullName: teamMember.name
                };
            }
            return { name: '', avatar: null, fullName: '' }; // return an empty object if no team member is found
        } catch (error) {

        }
    }


    getEventAttributes(item) {
        let eventColor = 'gray';
        let icon = 'fa-solid fa-clock';

        const dispatchAttributes = this.dispatchStatusLookup[item.dispatchStatus || 'null'];
        const jobAttributes = this.jobStatusLookup[item.jobStatus];

        if (dispatchAttributes) {
            eventColor = dispatchAttributes.color;
            icon = dispatchAttributes.icon;
        }

        if (jobAttributes) {
            eventColor = jobAttributes.color;
            if (jobAttributes.icon) {
                icon = jobAttributes.icon;
            }
        }

        return { eventColor, icon };
    }

    loadSelectedSchedule(scheduledId) {

        this.dialogRef.close(scheduledId);
    }

}

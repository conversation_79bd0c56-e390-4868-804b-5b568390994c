

import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs'; 
import { NotificationService, Notification } from 'app/services/notification.service';

@Component({
    selector: 'app-notification',
    imports: [],
    templateUrl: './notification.component.html'
})
export class NotificationComponent implements OnInit, OnDestroy {
    notifications: Notification[] = [];
    private subscription!: Subscription;

    constructor(private notificationService: NotificationService) { }

    ngOnInit(): void {
        this.subscription = this.notificationService.notifications$.subscribe(notifications => {
            this.notifications = notifications;
            // Set up automatic dismissal for notifications with a duration
            notifications.forEach(notification => {
                if (notification.duration) {
                    setTimeout(() => {
                        this.removeNotification(notification);
                    }, notification.duration);
                }
            });
        });
    }

    handleAction(notification: Notification): void {
        if (notification.action && notification.action.callback) {
            notification.action.callback(() => this.removeNotification(notification));
        }
    }

    removeNotification(notification: Notification): void {
        this.notificationService.remove(notification);
    }

    ngOnDestroy(): void {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }
}

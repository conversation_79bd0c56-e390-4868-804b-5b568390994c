
import { Component, Inject, Query<PERSON>ist, <PERSON><PERSON><PERSON><PERSON><PERSON>, ElementRef, EventEmitter, Output } from '@angular/core';
import {
    MatDialog,
    MatDialogActions,
    MatDialogClose,
    MatDialogContent,
    MatDialogRef,
    MatDialogTitle, MAT_DIALOG_DATA
} from '@angular/material/dialog';

import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@Component({
    selector: 'app-success-dialog',
    imports: [FormsModule, MatButtonModule, MatDialogActions, MatDialogClose, MatDialogTitle, MatDialogContent, MatProgressSpinnerModule],
    template: `
    <h2 mat-dialog-title>Success</h2>
    <mat-dialog-content>
      <p>{{ data.message }}</p>
    </mat-dialog-content>
    <mat-dialog-actions>
      <button mat-button mat-dialog-close>Close</button>
    </mat-dialog-actions>
  `,
    styles: [`
    mat-dialog-content {
      font-size: 1.2em;
    }
  `]
})
export class SuccessDialogComponent {
    constructor(@Inject(MAT_DIALOG_DATA) public data: { message: string }) { }
}

<div class="fixed bottom-0 left-0 p-4 z-90">
    @for (notification of notifications; track notification) {
        <div
            class="w-full max-w-xs mb-2 p-4 text-gray-500 bg-white rounded-lg shadow dark:bg-gray-800 dark:text-gray-400"
            role="alert"
        >
            <div class="flex">
                <div
                    class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 rounded-lg"
                    [ngClass]="
                        notification.iconColor ||
                        'text-blue-500 bg-blue-100 dark:text-blue-300 dark:bg-blue-900'
                    "
                >
                    <i
                        [class]="notification.icon || 'fa-solid fa-info-circle'"
                    ></i>
                    <span class="sr-only">Icon</span>
                </div>
                <div class="ms-3 text-sm font-normal">
                    <span
                        class="mb-1 text-sm font-semibold"
                        [ngClass]="
                            notification.messageColor ||
                            'text-gray-900 dark:text-white'
                        "
                    >
                        {{ notification.message }}
                    </span>
                    <div class="mb-2 text-sm font-normal">
                        {{ notification.details }}
                    </div>
                    @if (notification.action) {
                        <div class="grid grid-cols-2 gap-2">
                            <button
                                (click)="handleAction(notification)"
                                class="flex-1 inline-flex justify-center items-center px-2 py-1.5 text-xs font-medium text-center text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-500 dark:hover:bg-blue-600 dark:focus:ring-blue-800"
                            >
                                {{ notification.action.label }}
                            </button>
                            <button
                                (click)="removeNotification(notification)"
                                class="flex-1 inline-flex justify-center items-center px-2 py-1.5 text-xs font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 dark:bg-gray-600 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-700 dark:focus:ring-gray-700"
                            >
                                Not now
                            </button>
                        </div>
                    }
                </div>
                <button
                    type="button"
                    class="ms-auto -mx-1.5 -my-1.5 bg-white items-center justify-center flex-shrink-0 text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
                    (click)="removeNotification(notification)"
                    aria-label="Close"
                >
                    <span class="sr-only">Close</span>
                    <i class="fa-solid fa-times"></i>
                </button>
            </div>
        </div>
    }
</div>

import { Component, input, OnD<PERSON>roy, OnInit, output, signal } from '@angular/core';

import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { Customer } from 'app/core/databaseModels/customers/contacts.types';
import { MatOptionModule } from '@angular/material/core';
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'customer-select',
    templateUrl: './customer-select.component.html',
    styleUrls: ['./customer-select.component.scss'],
    imports: [
        ReactiveFormsModule,
        MatAutocompleteModule,
        MatInputModule,
        MatFormFieldModule,
        MatIconModule,
        MatOptionModule
    ]
})
export class CustomerSelectComponent implements OnInit, OnDestroy {
    private destroy$ = new Subject<void>();
    customers = input.required<Customer[]>();  // Customers array from parent
    customerSelected = output<Customer>();  // Emits selected customer to parent
    // selectedCustomer? = input<Customer>();
    onCustomerCleared = output<void>();  // Emits when customer is cleared

    customerInputCtrl = new FormControl();  // Reactive form control for input
    filteredCustomers = signal<Customer[]>([]);  // Signal for filtered customer list

    ngOnInit(): void {
        // if (this.selectedCustomer) {
        //     this.customerInputCtrl.setValue(this.selectedCustomer);
        //     this.filteredCustomers.set(this.customers());
        // }
        // Subscribe to value changes with takeUntil for cleanup
        this.customerInputCtrl.valueChanges
            .pipe(takeUntil(this.destroy$))
            .subscribe(value => {
                this.filteredCustomers.set(
                    this._filterCustomers(typeof value === 'string' ? value : value?.name || '')
                );
            });
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    private _filterCustomers(value: string): Customer[] {
        const filterValue = value?.toLowerCase().trim(); // Ensure value is string and safely apply trim()

        return this.customers().filter(customer => {
            // Match by customer name
            const nameMatch = customer.name?.toLowerCase().trim().includes(filterValue);

            // Match by phone number (parsing JSON)
            const phoneNumberMatch = customer.phoneNumbers?.some(phoneNumberString => {
                let phoneNum;
                if (typeof phoneNumberString === 'string') {
                    try {
                        // Parse the JSON string to an object
                        phoneNum = JSON.parse(phoneNumberString);
                        // Check if the phoneNumber includes the filterValue
                        return phoneNum.phoneNumber.includes(filterValue);
                    } catch (error) {
                        console.error('Error parsing phone number JSON:', error);
                        // Handle invalid numbers by returning false
                        return false;
                    }
                }
                return false; // Return false if phone number is not in string format
            });

            return nameMatch || phoneNumberMatch; // Return if either name or phone number matches
        });
    }



    onCustomerSelected(event: MatAutocompleteSelectedEvent): void {
        const selectedCustomer = event.option.value as Customer;
        this.customerSelected.emit(selectedCustomer);  // Emit the selected customer
    }

    displayFn(customer: Customer): string {
        return customer ? customer.name : '';
    }

    clearCustomerInput(): void {
        this.customerInputCtrl.setValue(null, { emitEvent: false }); // Prevent unnecessary emission
        this.customerInputCtrl.reset(null, { emitEvent: false });
        this.customerSelected.emit(null);
        this.onCustomerCleared.emit();
        this.filteredCustomers.set(this.customers().slice());
    }
    formatPhoneNumbers(phoneNumbers: string[]): string {
        if (!phoneNumbers) {
            return '';
        }
        return phoneNumbers.map(phoneNumberString => {
            let phoneNumberObj;

            // Check if phoneNumberString is a JSON string and try to parse it
            if (typeof phoneNumberString === 'string' && phoneNumberString.trim().startsWith('{')) {
                try {
                    phoneNumberObj = JSON.parse(phoneNumberString);
                } catch (error) {
                    console.error('Error parsing phone number JSON', error);
                    return 'Invalid Number';
                }
            } else {
                return 'Invalid Number Format';
            }

            // Return formatted phone number and label
            return `${phoneNumberObj.phoneNumber} (${phoneNumberObj.label})`;
        }).join(', ');
    }

}

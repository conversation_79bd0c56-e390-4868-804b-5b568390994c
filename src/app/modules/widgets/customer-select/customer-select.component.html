<mat-form-field class="w-full">
    <mat-label>Customer:</mat-label>
    <input
        type="text"
        placeholder="Pick a customer"
        matInput
        [formControl]="customerInputCtrl"
        [matAutocomplete]="auto"
    />

    <mat-autocomplete
        #auto="matAutocomplete"
        (optionSelected)="onCustomerSelected($event)"
        [displayWith]="displayFn"
    >
        @for (customer of filteredCustomers(); track customer) {
            <mat-option [value]="customer">
                <div class="flex flex-col">
                    <span class="font-semibold">{{ customer.name }}</span>
                    <span class="text-sm text-secondary">
                        {{ formatPhoneNumbers(customer.phoneNumbers) }}</span
                    >
                </div>
            </mat-option>
        }
    </mat-autocomplete>

    <mat-icon matPrefix>account_circle</mat-icon>
    @if (customerInputCtrl.value) {
        <button mat-icon-button matSuffix (click)="clearCustomerInput()">
            <i class="fa-duotone fa-circle-xmark"></i>
        </button>
    }
</mat-form-field>

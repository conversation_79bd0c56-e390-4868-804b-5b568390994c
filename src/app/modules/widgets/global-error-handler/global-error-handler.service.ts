import { ErrorHandler, Injectable } from '@angular/core';

@Injectable()
export class GlobalErrorHandler implements ErrorHandler {

    handleError(error: any): void {
        // Log the error
        console.error('Global Error Handler:', error);

        // Display the error
        window.alert('An unexpected error occurred');

        // In a real application, you might use a more advanced method for displaying the error,
        // like an Angular Material dialog, a toast notification system, etc.
    }
}

import { Component, input, output, signal } from '@angular/core';

import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatOptionModule } from '@angular/material/core';
import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';

@Component({
    selector: 'team-member-select',
    templateUrl: './team-member-select.component.html',
    styleUrls: ['./team-member-select.component.scss'],
    imports: [
    ReactiveFormsModule,
    MatAutocompleteModule,
    MatInputModule,
    MatFormFieldModule,
    MatIconModule,
    MatOptionModule
]
})
export class TeamMemberSelectComponent {
    teamMembers = input.required<TeamMember[]>();  // Input for the list of team members
    teamMemberSelected = output<TeamMember>();  // Output to emit the selected team member

    teamMemberInputCtrl = new FormControl();  // Reactive form control for input
    filteredTeamMembers = signal<TeamMember[]>([]);  // Signal for filtered team members

    ngOnInit(): void {
        this.teamMemberInputCtrl.valueChanges.subscribe(value => {
            // Handle both string and object cases
            this.filteredTeamMembers.set(this._filterTeamMembers(typeof value === 'string' ? value : value?.name || ''));
        });
    }

    private _filterTeamMembers(value: string): TeamMember[] {
        const filterValue = value?.toLowerCase().trim();  // Ensure value is string and safely apply trim()

        return this.teamMembers().filter(teamMember => {
            // Match by team member name
            const nameMatch = teamMember.name?.toLowerCase().trim().includes(filterValue);

            // Match by phone number or employee number
            const phoneOrEmployeeMatch = teamMember.phone?.includes(filterValue) ||
                teamMember.employeeNumber?.toLowerCase().includes(filterValue);

            return nameMatch || phoneOrEmployeeMatch;
        });
    }

    onTeamMemberSelected(event: MatAutocompleteSelectedEvent): void {
        const selectedTeamMember = event.option.value as TeamMember;
        this.teamMemberSelected.emit(selectedTeamMember);  // Emit the selected team member
    }

    displayFn(teamMember: TeamMember): string {
        return teamMember ? teamMember.name : '';
    }

    clearTeamMemberInput(): void {
        this.teamMemberInputCtrl.setValue(null);  // Clear the input value
        this.teamMemberInputCtrl.reset();  // Reset the form control to ensure it clears the selected state
        this.teamMemberSelected.emit(null);  // Emit a null to indicate no team member is selected

        // Clear the selected team member and update filtered team members
        this.filteredTeamMembers.set(this.teamMembers().slice());  // Reset filtered list
    }

    formatAssignmentHistory(assignmentHistory: string[]): string {
        if (!assignmentHistory) {
            return '';
        }
        return assignmentHistory.map(assignmentString => {
            let assignmentObj;

            // Check if assignmentString is a JSON string and try to parse it
            if (typeof assignmentString === 'string' && assignmentString.trim().startsWith('{')) {
                try {
                    assignmentObj = JSON.parse(assignmentString);
                } catch (error) {
                    console.error('Error parsing assignment JSON', error);
                    return 'Invalid Assignment';
                }
            } else {
                return 'Invalid Assignment Format';
            }

            // Return formatted assignment details
            return `Vehicle: ${assignmentObj.assignedVehicle}, Start: ${assignmentObj.assignedVehicleStartDate}, End: ${assignmentObj.assignedVehicleEndDate}`;
        }).join(', ');
    }
}

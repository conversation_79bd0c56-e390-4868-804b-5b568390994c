<mat-form-field class="w-full">
    <mat-label>Select Team Member</mat-label>
    <input
        type="text"
        placeholder="Pick one team member"
        aria-label="Team Member"
        matInput
        [formControl]="teamMemberInputCtrl"
        [matAutocomplete]="auto"
    />
    <mat-autocomplete
        #auto="matAutocomplete"
        (optionSelected)="onTeamMemberSelected($event)"
        [displayWith]="displayFn"
    >
        @for (teamMember of filteredTeamMembers(); track teamMember) {
            <mat-option [value]="teamMember">
                <div class="flex flex-col">
                    <span class="font-semibold">{{ teamMember.name }}</span>
                    <span class="text-sm text-secondary"
                        >Employee #: {{ teamMember.employeeNumber }}</span
                    >
                    <span class="text-sm text-secondary"
                        >Phone: {{ teamMember.phone }}</span
                    >
                </div>
            </mat-option>
        }
    </mat-autocomplete>

    <mat-icon class="icon-size-5"
        matPrefix
        [svgIcon]="'heroicons_solid:user-circle'"
     />

    @if (teamMemberInputCtrl.value) {
        <button mat-icon-button matSuffix (click)="clearTeamMemberInput()">
            <i class="fa-duotone fa-circle-xmark"></i>
        </button>
    }
</mat-form-field>

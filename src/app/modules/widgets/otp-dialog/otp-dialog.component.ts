import { Compo<PERSON>, Query<PERSON>ist, <PERSON><PERSON>hildren, ElementRef, EventEmitter, Output } from '@angular/core';

import {
    MatD<PERSON>og,

    MatDialogContent,
    MatDialogRef,
    MatDialogTitle,
} from '@angular/material/dialog';


import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@Component({
    selector: 'app-otp-dialog',
    imports: [FormsModule, MatButtonModule, MatDialogTitle, MatDialogContent, MatProgressSpinnerModule],
    templateUrl: './otp-dialog.component.html',
    styleUrls: ['./otp-dialog.component.scss']
})
export class OtpDialogComponent {
    otp: string[] = ['', '', '', '', '', ''];
    isConfirming: boolean = false;

    @ViewChildren('otpInput') otpInputs!: QueryList<ElementRef>;
    @Output() otpSubmit = new EventEmitter<string>();

    constructor(private dialogRef: MatDialogRef<OtpDialogComponent>) { }

    handleInput(event: Event, index: number) {
        const target = event.target as HTMLInputElement;
        const value = target.value;

        // Allow only a single digit
        if (/^\d$/.test(value)) {
            this.otp[index] = value;

            // Move focus to the next input if not the last
            if (index < this.otp.length - 1 && value) {
                this.focusNextInput(index + 1);
            }
        } else {
            // Clear input if invalid
            target.value = '';
        }
    }

    handleKeyDown(event: KeyboardEvent, index: number) {
        if (
            !/^\d$/.test(event.key) &&
            event.key !== 'Backspace' &&
            event.key !== 'Delete' &&
            event.key !== 'Tab' &&
            event.key !== 'ArrowLeft' &&
            event.key !== 'ArrowRight'
        ) {
            event.preventDefault();
        }

        if (event.key === 'Backspace' || event.key === 'Delete') {
            event.preventDefault();

            if (this.otp[index] !== '') {
                // Clear current input if not empty
                this.otp[index] = '';
                (event.target as HTMLInputElement).value = '';
            } else if (index > 0) {
                // Move to the previous input
                this.focusPreviousInput(index - 1);
            }
        } else if (event.key === 'ArrowLeft' && index > 0) {
            // Navigate left
            this.focusPreviousInput(index - 1);
        } else if (event.key === 'ArrowRight' && index < this.otp.length - 1) {
            // Navigate right
            this.focusNextInput(index + 1);
        }
    }

    handleFocus(event: FocusEvent) {
        (event.target as HTMLInputElement).select();
    }

    handlePaste(event: ClipboardEvent) {
        event.preventDefault();
        const text = event.clipboardData?.getData('text') || '';
        if (!new RegExp(`^[0-9]{${this.otp.length}}$`).test(text)) {
            return;
        }
        const digits = text.split('');
        digits.forEach((digit, index) => {
            if (index < this.otp.length) {
                this.otp[index] = digit;
                const inputElement = this.otpInputs.get(index)?.nativeElement as HTMLInputElement;
                if (inputElement) {
                    inputElement.value = digit;
                }
            }
        });
        const submitButton = document.querySelector('button[type=submit]') as HTMLButtonElement;
        if (submitButton) {
            submitButton.focus();
        }
    }

    onSubmit() {
        const otpCode = this.otp.join('');
        if (otpCode.length !== 6) {
            return;
        }
        this.isConfirming = true;
        //  this.dialogRef.close(otpCode);
        this.otpSubmit.emit(otpCode);
    }

    close() {
        this.dialogRef.close();
    }

    private focusNextInput(index: number) {
        const nextInput = this.otpInputs.get(index);
        if (nextInput) {
            nextInput.nativeElement.focus();
        }
    }

    private focusPreviousInput(index: number) {
        const prevInput = this.otpInputs.get(index);
        if (prevInput) {
            prevInput.nativeElement.focus();
        }
    }
}

<!-- <div class="flex flex-col items-center justify-center min-h-screen bg-slate-50">
    <div
        class="max-w-md mx-auto text-center bg-white px-4 sm:px-8 py-10 rounded-xl shadow"
    >
        <header class="mb-8">
            <h1 class="text-2xl font-bold mb-1">Mobile Phone Verification</h1>
            <p class="text-[15px] text-slate-500">
                Enter the 6-digit verification code that was sent to your phone
                number.
            </p>
        </header>
        <form (ngSubmit)="onSubmit()" #otpForm="ngForm">
            <div class="flex items-center justify-center gap-3">
                <input
                    #otpInput
                    [(ngModel)]="otp[0]"
                    name="otp0"
                    type="text"
                    class="w-14 h-14 text-center text-2xl font-extrabold text-slate-900 bg-slate-100 border border-transparent hover:border-slate-200 appearance-none rounded p-4 outline-none focus:bg-white focus:border-indigo-400 focus:ring-2 focus:ring-indigo-100"
                    maxlength="1"
                    (input)="handleInput($event, 0)"
                    (keydown)="handleKeyDown($event, 0)"
                    (focus)="handleFocus($event)"
                    (paste)="handlePaste($event)"
                    autocomplete="off"
                />
                <input
                    #otpInput
                    [(ngModel)]="otp[1]"
                    name="otp1"
                    type="text"
                    class="w-14 h-14 text-center text-2xl font-extrabold text-slate-900 bg-slate-100 border border-transparent hover:border-slate-200 appearance-none rounded p-4 outline-none focus:bg-white focus:border-indigo-400 focus:ring-2 focus:ring-indigo-100"
                    maxlength="1"
                    (input)="handleInput($event, 1)"
                    (keydown)="handleKeyDown($event, 1)"
                    (focus)="handleFocus($event)"
                    (paste)="handlePaste($event)"
                    autocomplete="off"
                />
                <input
                    #otpInput
                    [(ngModel)]="otp[2]"
                    name="otp2"
                    type="text"
                    class="w-14 h-14 text-center text-2xl font-extrabold text-slate-900 bg-slate-100 border border-transparent hover:border-slate-200 appearance-none rounded p-4 outline-none focus:bg-white focus:border-indigo-400 focus:ring-2 focus:ring-indigo-100"
                    maxlength="1"
                    (input)="handleInput($event, 2)"
                    (keydown)="handleKeyDown($event, 2)"
                    (focus)="handleFocus($event)"
                    (paste)="handlePaste($event)"
                    autocomplete="off"
                />
                <input
                    #otpInput
                    [(ngModel)]="otp[3]"
                    name="otp3"
                    type="text"
                    class="w-14 h-14 text-center text-2xl font-extrabold text-slate-900 bg-slate-100 border border-transparent hover:border-slate-200 appearance-none rounded p-4 outline-none focus:bg-white focus:border-indigo-400 focus:ring-2 focus:ring-indigo-100"
                    maxlength="1"
                    (input)="handleInput($event, 3)"
                    (keydown)="handleKeyDown($event, 3)"
                    (focus)="handleFocus($event)"
                    (paste)="handlePaste($event)"
                    autocomplete="off"
                />
                <input
                    #otpInput
                    [(ngModel)]="otp[4]"
                    name="otp4"
                    type="text"
                    class="w-14 h-14 text-center text-2xl font-extrabold text-slate-900 bg-slate-100 border border-transparent hover:border-slate-200 appearance-none rounded p-4 outline-none focus:bg-white focus:border-indigo-400 focus:ring-2 focus:ring-indigo-100"
                    maxlength="1"
                    (input)="handleInput($event, 4)"
                    (keydown)="handleKeyDown($event, 4)"
                    (focus)="handleFocus($event)"
                    (paste)="handlePaste($event)"
                    autocomplete="off"
                />
                <input
                    #otpInput
                    [(ngModel)]="otp[5]"
                    name="otp5"
                    type="text"
                    class="w-14 h-14 text-center text-2xl font-extrabold text-slate-900 bg-slate-100 border border-transparent hover:border-slate-200 appearance-none rounded p-4 outline-none focus:bg-white focus:border-indigo-400 focus:ring-2 focus:ring-indigo-100"
                    maxlength="1"
                    (input)="handleInput($event, 5)"
                    (keydown)="handleKeyDown($event, 5)"
                    (focus)="handleFocus($event)"
                    (paste)="handlePaste($event)"
                    autocomplete="off"
                />
            </div>
            <div class="max-w-[260px] mx-auto mt-4">
                <button
                    type="submit"
                    class="w-full inline-flex justify-center whitespace-nowrap rounded-lg bg-indigo-500 px-3.5 py-2.5 text-sm font-medium text-white shadow-sm shadow-indigo-950/10 hover:bg-indigo-600 focus:outline-none focus:ring focus:ring-indigo-300 focus-visible:outline-none focus-visible:ring focus-visible:ring-indigo-300 transition-colors duration-150"
                >
                    Verify Account
                </button>
            </div>
        </form>
        <div class="text-sm text-slate-500 mt-4">
            Didn't receive code?
            <a
                class="font-medium text-indigo-500 hover:text-indigo-600"
                href=""
                >Resend</a
            >
        </div>
    </div>
</div> -->
<h2 mat-dialog-title class="text-2xl font-bold mb-1">
    Mobile Phone Verification
</h2>
<mat-dialog-content>
    <p class="text-[15px] text-slate-500">
        Enter the 6-digit verification code that was sent to your phone number.
    </p>

    <form class="mt-10" (ngSubmit)="onSubmit()" #otpForm="ngForm">
        <div class="flex items-center justify-center gap-3">
            <input
                #otpInput
                [(ngModel)]="otp[0]"
                name="otp0"
                type="text"
                class="w-14 h-14 text-center text-2xl font-extrabold text-slate-900 bg-slate-100 border border-transparent hover:border-slate-200 appearance-none rounded p-4 outline-none focus:bg-white focus:border-indigo-400 focus:ring-2 focus:ring-indigo-100"
                maxlength="1"
                (input)="handleInput($event, 0)"
                (keydown)="handleKeyDown($event, 0)"
                (focus)="handleFocus($event)"
                (paste)="handlePaste($event)"
                autocomplete="off"
            />
            <input
                #otpInput
                [(ngModel)]="otp[1]"
                name="otp1"
                type="text"
                class="w-14 h-14 text-center text-2xl font-extrabold text-slate-900 bg-slate-100 border border-transparent hover:border-slate-200 appearance-none rounded p-4 outline-none focus:bg-white focus:border-indigo-400 focus:ring-2 focus:ring-indigo-100"
                maxlength="1"
                (input)="handleInput($event, 1)"
                (keydown)="handleKeyDown($event, 1)"
                (focus)="handleFocus($event)"
                (paste)="handlePaste($event)"
                autocomplete="off"
            />
            <input
                #otpInput
                [(ngModel)]="otp[2]"
                name="otp2"
                type="text"
                class="w-14 h-14 text-center text-2xl font-extrabold text-slate-900 bg-slate-100 border border-transparent hover:border-slate-200 appearance-none rounded p-4 outline-none focus:bg-white focus:border-indigo-400 focus:ring-2 focus:ring-indigo-100"
                maxlength="1"
                (input)="handleInput($event, 2)"
                (keydown)="handleKeyDown($event, 2)"
                (focus)="handleFocus($event)"
                (paste)="handlePaste($event)"
                autocomplete="off"
            />
            <input
                #otpInput
                [(ngModel)]="otp[3]"
                name="otp3"
                type="text"
                class="w-14 h-14 text-center text-2xl font-extrabold text-slate-900 bg-slate-100 border border-transparent hover:border-slate-200 appearance-none rounded p-4 outline-none focus:bg-white focus:border-indigo-400 focus:ring-2 focus:ring-indigo-100"
                maxlength="1"
                (input)="handleInput($event, 3)"
                (keydown)="handleKeyDown($event, 3)"
                (focus)="handleFocus($event)"
                (paste)="handlePaste($event)"
                autocomplete="off"
            />
            <input
                #otpInput
                [(ngModel)]="otp[4]"
                name="otp4"
                type="text"
                class="w-14 h-14 text-center text-2xl font-extrabold text-slate-900 bg-slate-100 border border-transparent hover:border-slate-200 appearance-none rounded p-4 outline-none focus:bg-white focus:border-indigo-400 focus:ring-2 focus:ring-indigo-100"
                maxlength="1"
                (input)="handleInput($event, 4)"
                (keydown)="handleKeyDown($event, 4)"
                (focus)="handleFocus($event)"
                (paste)="handlePaste($event)"
                autocomplete="off"
            />
            <input
                #otpInput
                [(ngModel)]="otp[5]"
                name="otp5"
                type="text"
                class="w-14 h-14 text-center text-2xl font-extrabold text-slate-900 bg-slate-100 border border-transparent hover:border-slate-200 appearance-none rounded p-4 outline-none focus:bg-white focus:border-indigo-400 focus:ring-2 focus:ring-indigo-100"
                maxlength="1"
                (input)="handleInput($event, 5)"
                (keydown)="handleKeyDown($event, 5)"
                (focus)="handleFocus($event)"
                (paste)="handlePaste($event)"
                autocomplete="off"
            />
        </div>
        <div class="max-w-[260px] mx-auto mt-10">
            <button
                type="submit"
                class="w-full inline-flex justify-center whitespace-nowrap rounded-lg bg-indigo-500 px-3.5 py-2.5 text-sm font-medium text-white shadow-sm shadow-indigo-950/10 hover:bg-indigo-600 focus:outline-none focus:ring focus:ring-indigo-300 focus-visible:outline-none focus-visible:ring focus-visible:ring-indigo-300 transition-colors duration-150"
            >
                @if (isConfirming) {
                    <div class="flex justify-center">
                        <mat-spinner class="spinnr" [diameter]="24">
                        </mat-spinner>
                        <div class="ml-5 text-center">Verifying...</div>
                    </div>
                } @else {
                    Verify SMS code
                }
            </button>
        </div>
    </form>
    <div class="text-sm text-slate-500 mt-4">
        Didn't receive code?
        <a class="font-medium text-indigo-500 hover:text-indigo-600" href=""
            >Resend</a
        >
    </div>
</mat-dialog-content>

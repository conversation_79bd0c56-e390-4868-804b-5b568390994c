import { Component, input, output, signal } from '@angular/core';

import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatOptionModule } from '@angular/material/core';
import { Organisation } from 'app/core/databaseModels/organisations/organisations.types';
@Component({
    selector: 'organisation-select',
    templateUrl: './organisation-select.component.html',
    styleUrls: ['./organisation-select.component.scss'],
    imports: [
    ReactiveFormsModule,
    MatAutocompleteModule,
    MatInputModule,
    MatFormFieldModule,
    MatIconModule,
    MatOptionModule
]
})
export class OrganisationSelectComponent {
    organisations = input.required<Organisation[]>();  // organisations array from parent
    organisationSelected = output<Organisation>();  // Emits selected organisation to parent

    organisationInputCtrl = new FormControl();  // Reactive form control for input
    filteredOrganisations = signal<Organisation[]>([]);  // Signal for filtered organisation list

    ngOnInit(): void {
        this.organisationInputCtrl.valueChanges.subscribe(value => {
            // Handle both string and object cases
            this.filteredOrganisations.set(this._filterOrganisations(typeof value === 'string' ? value : value?.name || ''));
        });
    }

    private _filterOrganisations(value: string): Organisation[] {
        const filterValue = value?.toLowerCase().trim(); // Ensure value is string and safely apply trim()

        return this.organisations().filter(organisation => {
            // Match by organisation name
            const nameMatch = organisation.organisationName?.toLowerCase().trim().includes(filterValue);

            // Match by phone number (parsing JSON)
            const phoneNumberMatch = organisation.phoneNumbers?.some(phoneNumberString => {
                let phoneNum;
                if (typeof phoneNumberString === 'string') {
                    try {
                        // Parse the JSON string to an object
                        phoneNum = JSON.parse(phoneNumberString);
                        // Check if the phoneNumber includes the filterValue
                        return phoneNum.phoneNumber.includes(filterValue);
                    } catch (error) {
                        console.error('Error parsing phone number JSON:', error);
                        // Handle invalid numbers by returning false
                        return false;
                    }
                }
                return false; // Return false if phone number is not in string format
            });

            return nameMatch || phoneNumberMatch; // Return if either name or phone number matches
        });
    }



    onOrganisationSelected(event: MatAutocompleteSelectedEvent): void {
        const selectedOrganisation = event.option.value as Organisation;
        this.organisationSelected.emit(selectedOrganisation);  // Emit the selected organisation
    }

    displayFn(organisation: Organisation): string {
        return organisation ? organisation.organisationName : '';
    }

    clearOrganisationInput(): void {
        this.organisationInputCtrl.setValue(null);  // Clear the input value
        this.organisationInputCtrl.reset();  // Reset the form control to ensure it clears selected state
        this.organisationSelected.emit(null);  // Emit a null to indicate no organisation is selected

        // Clear the selected organisation and update filtered organisations
        this.filteredOrganisations.set(this.organisations().slice());  // Reset filtered list
    }
    formatPhoneNumbers(phoneNumbers: string[]): string {
        if (!phoneNumbers) {
            return '';
        }
        return phoneNumbers.map(phoneNumberString => {
            let phoneNumberObj;

            // Check if phoneNumberString is a JSON string and try to parse it
            if (typeof phoneNumberString === 'string' && phoneNumberString.trim().startsWith('{')) {
                try {
                    phoneNumberObj = JSON.parse(phoneNumberString);
                } catch (error) {
                    console.error('Error parsing phone number JSON', error);
                    return 'Invalid Number';
                }
            } else {
                return 'Invalid Number Format';
            }

            // Return formatted phone number and label
            return `${phoneNumberObj.phoneNumber} (${phoneNumberObj.label})`;
        }).join(', ');
    }

}

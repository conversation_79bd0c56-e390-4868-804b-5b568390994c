<mat-form-field class="w-full">
    <mat-label>Organisations:</mat-label>
    <input
        type="text"
        placeholder="Pick a organisation"
        matInput
        [formControl]="organisationInputCtrl"
        [matAutocomplete]="auto"
    />

    <mat-autocomplete
        #auto="matAutocomplete"
        (optionSelected)="onOrganisationSelected($event)"
        [displayWith]="displayFn"
    >
        @for (organisation of filteredOrganisations(); track organisation) {
            <mat-option [value]="organisation">
                <div class="flex flex-col">
                    <span class="font-semibold">{{
                        organisation.organisationName
                    }}</span>
                    <span class="text-sm text-secondary">
                        {{
                            formatPhoneNumbers(organisation.phoneNumbers)
                        }}</span
                    >
                </div>
            </mat-option>
        }
    </mat-autocomplete>
    <i matPrefix class="fa-duotone fa-solid fa-rectangles-mixed mr-2"></i>

    @if (organisationInputCtrl.value) {
        <button mat-icon-button matSuffix (click)="clearOrganisationInput()">
            <i class="fa-duotone fa-circle-xmark"></i>
        </button>
    }
</mat-form-field>

import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { FlatTreeControl } from '@angular/cdk/tree';
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree';
import { SelectionModel } from '@angular/cdk/collections';

export interface TreeNode {
    id: string;
    name: string;
    parentID?: string;
    children?: TreeNode[];
}

export interface FlatTreeNode {
    item: string;
    level: number;
    expandable: boolean;
    id: string;
}

@Component({
    selector: 'app-select-check-all-tree',
    templateUrl: './select-check-all-tree.component.html',
    styleUrls: ['./select-check-all-tree.component.css'],
    standalone: false
})
export class SelectCheckAllTreeComponent implements OnInit, OnChanges {
    @Input() data: TreeNode[] = [];
    @Input() selectedIds: string[] = [];
    @Output() modelChange = new EventEmitter<string[]>();

    treeControl: FlatTreeControl<FlatTreeNode>;
    treeFlattener: MatTreeFlattener<TreeNode, FlatTreeNode>;
    dataSource: MatTreeFlatDataSource<TreeNode, FlatTreeNode>;

    checklistSelection = new SelectionModel<FlatTreeNode>(true);

    constructor() {
        // Move transformer definition inside treeFlattener initialization
        this.treeFlattener = new MatTreeFlattener(
            (node, level) => this.transformer(node, level), // Use arrow function to access class context
            node => node.level,
            node => node.expandable,
            node => node.children
        );

        this.treeControl = new FlatTreeControl<FlatTreeNode>(
            node => node.level,
            node => node.expandable
        );

        this.dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);
    }

    ngOnInit() {
        this.initialize();
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes.data) {
            this.initialize();
        }
    }

    initialize() {
        this.dataSource.data = this.data;
        this.treeControl.expandAll();
        this.updateSelection();
    }

    updateSelection() {
        this.checklistSelection.clear();
        const nodes = this.treeControl.dataNodes.filter(node => this.selectedIds.includes(node.id));
        nodes.forEach(node => this.checklistSelection.select(node));
    }

    // Define transformer as a method to avoid initialization issues
    transformer(node: TreeNode, level: number): FlatTreeNode {
        return {
            id: node.id,
            item: node.name,
            level: level,
            expandable: !!node.children && node.children.length > 0,
        };
    }

    todoItemSelectionToggle(node: FlatTreeNode): void {
        this.checklistSelection.toggle(node);
        const descendants = this.treeControl.getDescendants(node);
        this.checklistSelection.isSelected(node)
            ? this.checklistSelection.select(...descendants)
            : this.checklistSelection.deselect(...descendants);

        // Force update for the parent nodes
        this.checkAllParentsSelection(node);
        this.emitModelChange();
    }

    checkAllParentsSelection(node: FlatTreeNode) {
        let parent: FlatTreeNode | null = this.getParentNode(node);
        while (parent) {
            this.checkRootNodeSelection(parent);
            parent = this.getParentNode(parent);
        }
    }
    getParentNode(node: FlatTreeNode): FlatTreeNode | null {
        const currentLevel = this.treeControl.getLevel(node);

        if (currentLevel < 1) {
            return null;
        }

        const startIndex = this.treeControl.dataNodes.indexOf(node) - 1;

        for (let i = startIndex; i >= 0; i--) {
            const currentNode = this.treeControl.dataNodes[i];

            if (this.treeControl.getLevel(currentNode) < currentLevel) {
                return currentNode;
            }
        }
        return null;
    }
    checkRootNodeSelection(node: FlatTreeNode) {
        const nodeSelected = this.checklistSelection.isSelected(node);
        const descendants = this.treeControl.getDescendants(node);
        const descAllSelected = descendants.length > 0 && descendants.every(child =>
            this.checklistSelection.isSelected(child)
        );

        if (nodeSelected && !descAllSelected) {
            this.checklistSelection.deselect(node);
        } else if (!nodeSelected && descAllSelected) {
            this.checklistSelection.select(node);
        }
    }


    emitModelChange() {
        const selectedIds = this.checklistSelection.selected.map(node => node.id);
        this.modelChange.emit(selectedIds);
    }



    descendantsPartiallySelected(node: FlatTreeNode): boolean {
        const descendants = this.treeControl.getDescendants(node);
        const result = descendants.some(child => this.checklistSelection.isSelected(child));
        return result && !descendants.every(child => this.checklistSelection.isSelected(child));
    }

    hasChild = (_: number, nodeData: FlatTreeNode) => nodeData.expandable;
}

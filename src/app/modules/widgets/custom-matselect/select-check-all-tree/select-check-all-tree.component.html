@if(dataSource.data.length === 0){
<div class="flex justify-center">
    <div class="text-secondary">There is no group setup yet.</div>
</div>
} @else {
<mat-tree [dataSource]="dataSource" [treeControl]="treeControl">
    <mat-tree-node
        *matTreeNodeDef="let node; when: hasChild"
        matTreeNodePadding
    >
        <button mat-icon-button matTreeNodeToggle>
            <i
                class="fa"
                [class]="
                    treeControl.isExpanded(node)
                        ? 'fa-chevron-down'
                        : 'fa-chevron-right'
                "
            ></i>
        </button>
        <mat-checkbox
            [checked]="checklistSelection.isSelected(node)"
            [indeterminate]="descendantsPartiallySelected(node)"
            (change)="todoItemSelectionToggle(node)"
        >
            {{ node.item }}
        </mat-checkbox>
    </mat-tree-node>

    <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding>
        <button mat-icon-button disabled></button>
        <mat-checkbox
            [checked]="checklistSelection.isSelected(node)"
            (change)="todoItemSelectionToggle(node)"
        >
            {{ node.item }}
        </mat-checkbox>
    </mat-tree-node>
</mat-tree>
}

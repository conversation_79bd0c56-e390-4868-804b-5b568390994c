// file-input-dropzone.component.ts
import { Component, Output, EventEmitter, HostListener, Input, OnChanges, SimpleChanges, Optional } from '@angular/core';
import { MatBottomSheetRef } from '@angular/material/bottom-sheet';

@Component({
    selector: 'file-input-dropzone',
    templateUrl: './file-input-dropzone.component.html',
    styleUrls: ['./file-input-dropzone.component.css'],
    standalone: false
})
export class FileInputDropzoneComponent implements OnChanges {

    @Input() multiple: boolean = true;
    @Input() preloadedFiles: Array<{ filename: string, fileUrl: string, fileId: string }> = [];

    @Output() selectedFiles = new EventEmitter<File[]>();
    @Output() removePreloadedFile = new EventEmitter<string>();
    public fileList: Array<{ filename: string, file: File, url: string }> = [];

    constructor(@Optional() private bottomSheetRef: MatBottomSheetRef<FileInputDropzoneComponent>) { }


    onAttachFilesInput(event: Event): void {
        const files: FileList = (event.target as HTMLInputElement).files;
        this.processFiles(files);
    }

    ngOnChanges(changes: SimpleChanges): void {
        //   console.log(this.preloadedFiles)
    }


    processFiles(files: FileList): void {
        const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/gif'];
        Array.from(files).forEach(file => {
            if (allowedTypes.includes(file.type)) {
                if (file.size <= 6 * 1024 * 1024) {  // Check if file size is within 2MB limit
                    const url = URL.createObjectURL(file);
                    this.fileList.push({ filename: file.name, file: file, url: url });
                } else {
                    alert('File size exceeds 6MB: ' + file.name);
                }
            } else {
                alert('Invalid file type: ' + file.name);
            }
        });
        this.selectedFiles.emit(this.fileList.map(fileItem => fileItem.file));
        // Close the bottom sheet and pass the selected files if opened as a bottom sheet
        if (this.bottomSheetRef) {
            this.bottomSheetRef.dismiss(this.fileList.map(fileItem => fileItem.file));
        }
    }

    @HostListener('dragover', ['$event'])
    onDragOver(event: Event): void {
        event.preventDefault();
        event.stopPropagation();
    }

    @HostListener('dragleave', ['$event'])
    onDragLeave(event: Event): void {
        event.preventDefault();
        event.stopPropagation();
    }

    @HostListener('drop', ['$event'])
    onDrop(event: Event): void {
        event.preventDefault();
        event.stopPropagation();

        const files = (event as DragEvent).dataTransfer.files;
        if (this.multiple || files.length === 1) {
            this.processFiles(files);
        } else {
            alert('Only one file can be selected');
        }
    }

    removeFile(index: number): void {
        URL.revokeObjectURL(this.fileList[index].url);
        this.fileList.splice(index, 1);
        this.selectedFiles.emit(this.fileList.map(fileItem => fileItem.file));
    }


    public reset(): void {
        this.fileList.forEach(fileItem => URL.revokeObjectURL(fileItem.url));
        this.fileList = [];
        this.preloadedFiles = [];
        this.selectedFiles.emit([]);
    }

    removePreLoadedFile(index: number): void {
        // console.log(this.preloadedFiles[index]);
        const fileToRemove = this.preloadedFiles[index];
        if (fileToRemove) {
            // Display a confirmation dialog
            const confirmDeletion = confirm(`Are you sure you want to delete the file "${fileToRemove.filename}"?`);

            if (confirmDeletion) {
                // If confirmed, emit the fileId to the parent component for server-side deletion
                this.removePreloadedFile.emit(fileToRemove.fileId);

                // Remove the file from the preloadedFiles list
                this.preloadedFiles.splice(index, 1);
            }
        }
    }




}

<div>
  <div class="flex items-center justify-center w-full mt-5">
    <label
      for="dropzone-file"
      class="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-bray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600"
      >
      <div class="flex flex-col items-center justify-center pt-5 pb-6">
        <i
          class="icon-size-10 fa-duotone fa-cloud-arrow-up w-8 h-8 mb-4 text-gray-500 dark:text-gray-400"
        ></i>
        <p class="mb-2 text-sm text-gray-500 dark:text-gray-400">
          <span class="font-semibold">Click to upload</span> or drag
          and drop
        </p>
        <p class="text-xs text-gray-500 dark:text-gray-400">
          PDF, PNG, JPG or GIF (MAX 6MB each file)
        </p>
      </div>
      <input
        id="dropzone-file"
        type="file"
        class="hidden"
        [multiple]="multiple"
        (change)="onAttachFilesInput($event)"
        accept="application/pdf, application/msword, application/vnd.ms-excel, image/jpeg, image/png, image/gif"
        />
    </label>
  </div>
  <ul class="mt-4">
    @for (file of fileList; track file; let i = $index) {
      <li
        class="flex justify-between items-center p-2 border-b"
        >
        <a href="#">{{ file.filename }}</a>
        @if (!file.file) {
          <span>(already uploaded)</span>
        }
        <i
          class="fa-duotone fa-trash-can cursor-pointer"
          (click)="removeFile(i)"
        ></i>
      </li>
    }

    @for (pfile of preloadedFiles; track pfile; let i = $index) {
      <li
        class="flex justify-between items-center p-2 border-b"
        >
        <a href="{{ pfile.fileUrl }}" target="_blank">{{
          pfile.filename
        }}</a>
        <span>(already uploaded)</span>
        <i
          class="fa-duotone fa-trash-can cursor-pointer"
          (click)="removePreLoadedFile(i)"
        ></i>
      </li>
    }
  </ul>
</div>

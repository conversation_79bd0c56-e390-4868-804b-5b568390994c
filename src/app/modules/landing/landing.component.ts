import { Component, ViewEncapsulation, Renderer2, Inject } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';

import { NavBarComponent } from './nav-bar/nav-bar.component';
import { LandingFooterComponent } from './landing-footer/landing-footer.component';



@Component({
    selector: 'landing',
    templateUrl: './landing.component.html',
    styleUrls: ['./landing.component.scss'],
    encapsulation: ViewEncapsulation.None,
    imports: [RouterModule, MatButtonModule, MatIconModule, NavBarComponent, LandingFooterComponent]
})
export class LandingComponent {
    constructor(
        private renderer: Renderer2,
        @Inject(DOCUMENT) private document: Document
    ) {
        // Add Google Ads gtag.js script
        const script = this.renderer.createElement('script');
        script.async = true;
        script.src = 'https://www.googletagmanager.com/gtag/js?id=AW-17253575375';
        this.renderer.appendChild(this.document.head, script);

        // Add gtag config script
        const script2 = this.renderer.createElement('script');
        script2.text = `
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', 'AW-17253575375');
        `;
        this.renderer.appendChild(this.document.head, script2);
    }
}

// contact.component.ts
import { Component, OnInit, ViewEncapsulation, ElementRef, ViewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { fuseAnimations } from '@fuse/animations';
import { FuseAlertComponent, FuseAlertType } from '@fuse/components/alert';

import { EmailService } from 'app/core/databaseModels/email.service';
import { environment } from 'environments/environment';

declare global {
    interface Window { grecaptcha: any; }
}

@Component({
    selector: 'app-contact',
    encapsulation: ViewEncapsulation.None,
    animations: fuseAnimations,
    imports: [
        FormsModule,
        FuseAlertComponent,
        ReactiveFormsModule,
        MatProgressSpinnerModule,
    ],
    templateUrl: './contact.component.html',
    styleUrls: ['./contact.component.scss']
})
export class ContactComponent implements OnInit {
    @ViewChild('captchaRef') captchaRef: ElementRef;

    alert: { type: FuseAlertType; message: string } = {
        type: 'success',
        message: '',
    };
    isSubmitted = false;
    isSending = false;
    contactUsForm: UntypedFormGroup;
    siteKey: string = environment.recaptcha.siteKey; // Replace with your actual site key
    captchaPassed: boolean = false;
    captchaResponse: string | null = null;
    captchaError: boolean = false;
    captchaWidgetId: number | null = null;
    inquiryType = 'inquiry';

    constructor(
        private _formBuilder: UntypedFormBuilder,
        private _emailService: EmailService,
    ) { }

    ngOnInit(): void {
        this.contactUsForm = this._formBuilder.group({
            firstName: ['', Validators.required],
            lastName: ['', Validators.required],
            email: ['', [Validators.required, Validators.email]],
            phone: ['', [Validators.required, Validators.pattern(/^\+?[0-9\s]{10,15}$/)]],
            message: ['', Validators.required],
            inquiryType: ['inquiry'],
        });

        this.loadRecaptchaScript();
    }

    loadRecaptchaScript() {
        const scriptId = 'recaptcha-script';
        if (document.getElementById(scriptId)) {
            this.renderReCaptcha();
            return;
        }
        const script = document.createElement('script');
        script.id = scriptId;
        script.src = 'https://www.google.com/recaptcha/api.js?render=explicit';
        script.async = true;
        script.defer = true;
        script.onload = () => {
            this.renderReCaptcha();
        };
        document.body.appendChild(script);
    }

    renderReCaptcha() {
        if (window.grecaptcha && window.grecaptcha.render) {
            this.captchaWidgetId = window.grecaptcha.render(this.captchaRef.nativeElement, {
                'sitekey': this.siteKey,
                'callback': (response: string) => this.handleCaptchaResponse(response),
                'expired-callback': () => this.handleCaptchaExpired(),
            });
        } else {
            // If grecaptcha is not yet ready, try again after a delay
            setTimeout(() => this.renderReCaptcha(), 500);
        }
    }

    public setInquiryType(type: string): void {
        this.contactUsForm.patchValue({ inquiryType: type });
        this.inquiryType = type;
    }

    public onSubmit(): void {
        if (this.contactUsForm.invalid || !this.captchaPassed) {
            if (!this.captchaPassed) {
                this.captchaError = true;
            }
            return;
        }
        this.contactUsForm.disable();
        this.isSending = true;

        const payload = {
            name: `${this.contactUsForm.value.firstName} ${this.contactUsForm.value.lastName}`,
            email: this.contactUsForm.value.email,
            phone: this.contactUsForm.value.phone,
            message: this.contactUsForm.value.message,
            inquiryType: this.contactUsForm.value.inquiryType,
            captchaResponse: this.captchaResponse,
        };

        console.log(payload);

        // Send the payload to your server for processing and CAPTCHA verification
        this._emailService.sendContactEmail(payload).subscribe((response) => {
            console.log(response);
            this.isSubmitted = true;
            this.isSending = false;
            this.contactUsForm.reset();
            this.contactUsForm.enable();
            this.resetCaptcha();
            this.alert = {
                type: 'success',
                message: 'Your message has been sent. Thank you. We will get back to you as soon as possible.',
            };
        })
        // Simulate sending email

    }

    handleCaptchaResponse(response: string): void {
        this.captchaPassed = true;
        this.captchaResponse = response;
        this.captchaError = false;
    }

    handleCaptchaExpired(): void {
        this.captchaPassed = false;
        this.captchaResponse = null;
    }

    resetCaptcha(): void {
        this.captchaPassed = false;
        this.captchaResponse = null;
        if (this.captchaWidgetId !== null && window.grecaptcha) {
            window.grecaptcha.reset(this.captchaWidgetId);
        }
    }
}

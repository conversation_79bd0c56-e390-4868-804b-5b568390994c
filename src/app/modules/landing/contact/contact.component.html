<div class="w-full mt-20">
    <div
        class="relative items-center w-full py-12 pb-12 mx-auto mt-12 max-w-7xl"
    >
        <svg
            class="absolute -mt-24 blur-3xl"
            fill="none"
            viewBox="0 0 400 400"
            height="100%"
            width="100%"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clip-path="url(#clip0_10_20)">
                <g filter="url(#filter0_f_10_20)">
                    <path
                        d="M128.6 0H0V322.2L106.2 134.75L128.6 0Z"
                        fill="#03FFE0"
                    ></path>
                    <path
                        d="M0 322.2V400H240H320L106.2 134.75L0 322.2Z"
                        fill="#7C87F8"
                    ></path>
                    <path
                        d="M320 400H400V78.75L106.2 134.75L320 400Z"
                        fill="#4C65E4"
                    ></path>
                    <path
                        d="M400 0H128.6L106.2 134.75L400 78.75V0Z"
                        fill="#043AFF"
                    ></path>
                </g>
            </g>
            <defs>
                <filter
                    color-interpolation-filters="sRGB"
                    filterUnits="userSpaceOnUse"
                    height="720.666"
                    id="filter0_f_10_20"
                    width="720.666"
                    x="-160.333"
                    y="-160.333"
                >
                    <feFlood
                        flood-opacity="0"
                        result="BackgroundImageFix"
                    ></feFlood>
                    <feBlend
                        in="SourceGraphic"
                        in2="BackgroundImageFix"
                        mode="normal"
                        result="shape"
                    ></feBlend>
                    <feGaussianBlur
                        result="effect1_foregroundBlur_10_20"
                        stdDeviation="80.1666"
                    ></feGaussianBlur>
                </filter>
            </defs>
        </svg>
        <section
            class="relative object-cover w-full border shadow-2xl px-8 py-8 lg:py-16 bg-slate-100 rounded-2xl"
        >
            <div class="container mx-auto text-center">
                <h5
                    class="block antialiased tracking-normal font-sans font-semibold leading-snug text-blue-gray-900 mb-4 !text-base lg:!text-2xl"
                >
                    Customer Care
                </h5>
                <h1
                    class="block antialiased tracking-normal font-sans font-semibold leading-tight text-blue-gray-900 mb-4 !text-3xl lg:!text-5xl"
                >
                    We're Here to Help
                </h1>
                <p
                    class="block text-black antialiased font-sans leading-relaxed text-inherit mb-10 font-normal !text-lg lg:mb-20 mx-auto max-w-3xl !text-white-500"
                >
                    Whether it's a question about our services, a request for
                    technical assistance, or suggestions for improvement, our
                    team is eager to hear from you.
                </p>
            </div>
        </section>
    </div>

    <section class="body-font relative text-gray-600 p-2">
        <div class="container mx-auto flex flex-wrap px-5 py-24 sm:flex-nowrap">
            <div
                class="relative flex items-end justify-start overflow-hidden rounded-lg bg-gray-300 p-10 sm:mr-10 md:w-1/2 lg:w-2/3"
            >
                <iframe
                    width="100%"
                    height="100%"
                    class="absolute inset-0"
                    frameborder="0"
                    title="map"
                    marginheight="0"
                    marginwidth="0"
                    scrolling="no"
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3145.8481856513663!2d144.95283927643015!3d-37.81777477197472!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6ad65d4e769d03b3%3A0xf673772e1dc216e4!2s123%2F585%20Little%20Collins%20St%2C%20Melbourne%20VIC%203000!5e0!3m2!1sen!2sau!4v1727512710790!5m2!1sen!2sau"
                    style="filter: grayscale(1) contrast(1.2) opacity(0.4)"
                ></iframe>
                <div
                    class="relative flex flex-wrap rounded bg-white py-6 shadow-md"
                >
                    <div class="px-6 lg:w-1/2">
                        <h2
                            class="title-font text-xs font-semibold tracking-widest text-gray-900"
                        >
                            ADDRESS
                        </h2>
                        <p class="mt-1">
                            Suite 123 585 Little Collins Street Melbourne Vic
                            3000, Australia
                        </p>
                    </div>
                    <div class="mt-4 px-6 lg:mt-0 lg:w-1/2">
                        <h2
                            class="title-font text-xs font-semibold tracking-widest text-gray-900"
                        >
                            INQUIRY EMAIL
                        </h2>
                        <a
                            class="leading-relaxed text-indigo-500"
                            href="mailto: inquiry&#64;streamlinerapps.com"
                            >inquiry&#64;streamlinerapps.com</a
                        >
                        <h2
                            class="title-font text-xs font-semibold tracking-widest text-gray-900"
                        >
                            SUPPORT EMAIL
                        </h2>
                        <a
                            class="leading-relaxed text-indigo-500"
                            href="mailto: support&#64;streamlinerapps.com"
                            >support&#64;streamlinerapps.com</a
                        >
                        <h2
                            class="title-font mt-4 text-xs font-semibold tracking-widest text-gray-900"
                        >
                            PHONE
                        </h2>
                        <p class="leading-relaxed">
                            <a class="text-indigo-500" href="tel:1800 013 888"
                                >1800 013 888</a
                            >
                        </p>
                    </div>
                </div>
            </div>
            <div
                class="mt-8 flex w-full items-center flex-col bg-white md:ml-auto md:mt-0 md:w-1/2 md:py-8 lg:w-1/3 p-2 rounded-lg"
            >
                @if (isSubmitted) {
                    <fuse-alert
                        class="mt-8 mb-10"
                        [appearance]="'outline'"
                        [showIcon]="false"
                        [type]="alert.type"
                        [@shake]="alert.type === 'error'"
                    >
                        {{ alert.message }}
                    </fuse-alert>
                }
                @if (isSending) {
                    <div class="flex justify-center p-10">
                        <mat-spinner class="spinnr" [diameter]="24">
                        </mat-spinner>
                        <div class="ml-5 text-center">Sending...</div>
                    </div>
                } @else {
                    <form
                        [formGroup]="contactUsForm"
                        (ngSubmit)="onSubmit()"
                        class="flex flex-col gap-2 lg:max-w-sm"
                    >
                        <p
                            class="block antialiased font-sans text-sm leading-normal text-inherit text-left !font-semibold !text-gray-600"
                        >
                            Select Options for Business Engagement
                        </p>
                        <div class="flex gap-4">
                            <button
                                class="align-middle select-none font-sans font-bold text-center uppercase transition-all disabled:opacity-50 disabled:shadow-none disabled:pointer-events-none text-xs py-3 px-6 rounded-lg border border-gray-900 text-gray-900 hover:opacity-75 focus:ring focus:ring-gray-300 active:opacity-[0.85] max-w-fit"
                                type="button"
                                data-ripple-dark="true"
                                (click)="setInquiryType('inquiry')"
                            >
                                @if (inquiryType === "inquiry") {
                                    <i
                                        class="ml-3 icon-size-5 fa-duotone fa-check text-green-600"
                                    ></i>
                                }
                                General inquiry</button
                            ><button
                                class="align-middle select-none font-sans font-bold text-center uppercase transition-all disabled:opacity-50 disabled:shadow-none disabled:pointer-events-none text-xs py-3 px-6 rounded-lg border border-gray-900 text-gray-900 hover:opacity-75 focus:ring focus:ring-gray-300 active:opacity-[0.85] max-w-fit"
                                type="button"
                                data-ripple-dark="true"
                                (click)="setInquiryType('support')"
                            >
                                Product Support
                                @if (inquiryType === "support") {
                                    <i
                                        class="ml-3 icon-size-5 fa-duotone fa-check text-green-600"
                                    ></i>
                                }
                            </button>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p
                                    class="block antialiased font-sans text-sm leading-normal text-inherit mb-2 text-left font-medium !text-gray-900"
                                >
                                    First Name
                                </p>
                                <div class="relative w-full h-11 !min-w-full">
                                    <input
                                        placeholder="First Name"
                                        [formControlName]="'firstName'"
                                        name="first-name"
                                        class="peer w-full h-full bg-transparent font-sans font-normal outline outline-0 focus:outline-0 disabled:bg-blue-gray-50 disabled:border-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 border-t-transparent focus:border-t-transparent placeholder:opacity-0 focus:placeholder:opacity-100 text-sm px-3 py-3 rounded-md !border !border-gray-300 text-gray-900 placeholder:!text-gray-500 focus:!border-gray-900 focus:!border-2 focus:border-t-gray-900"
                                    /><label
                                        class="flex w-full h-full select-none pointer-events-none absolute left-0 font-normal !overflow-visible truncate peer-placeholder-shown:text-blue-gray-500 leading-tight peer-focus:leading-tight peer-disabled:text-transparent peer-disabled:peer-placeholder-shown:text-blue-gray-500 transition-all -top-1.5 peer-placeholder-shown:text-sm text-[11px] peer-focus:text-[11px] before:content[' '] before:block before:box-border before:w-2.5 before:h-1.5 before:mt-[6.5px] before:mr-1 peer-placeholder-shown:before:border-transparent before:rounded-tl-md before:border-t peer-focus:before:border-t-2 before:border-l peer-focus:before:border-l-2 before:pointer-events-none before:transition-all peer-disabled:before:border-transparent after:content[' '] after:block after:flex-grow after:box-border after:w-2.5 after:h-1.5 after:mt-[6.5px] after:ml-1 peer-placeholder-shown:after:border-transparent after:rounded-tr-md after:border-t peer-focus:after:border-t-2 after:border-r peer-focus:after:border-r-2 after:pointer-events-none after:transition-all peer-disabled:after:border-transparent peer-placeholder-shown:leading-[4.1] text-gray-500 peer-focus:text-gray-900 before:border-blue-gray-200 peer-focus:before:!border-gray-900 after:border-blue-gray-200 peer-focus:after:!border-gray-900 hidden"
                                    >
                                    </label>
                                    @if (
                                        contactUsForm.get("firstName")
                                            .invalid &&
                                        contactUsForm.get("firstName").touched
                                    ) {
                                        <div class="text-red-500 text-sm mb-5">
                                            First name is required.
                                        </div>
                                    }
                                </div>
                            </div>
                            <div class="">
                                <p
                                    class="block antialiased font-sans text-sm leading-normal text-inherit mb-2 text-left font-medium !text-gray-900"
                                >
                                    Last Name
                                </p>
                                <div class="relative w-full h-11 !min-w-full">
                                    <input
                                        placeholder="Last Name"
                                        [formControlName]="'lastName'"
                                        name="last-name"
                                        class="peer w-full h-full bg-transparent font-sans font-normal outline outline-0 focus:outline-0 disabled:bg-blue-gray-50 disabled:border-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 border-t-transparent focus:border-t-transparent placeholder:opacity-0 focus:placeholder:opacity-100 text-sm px-3 py-3 rounded-md !border !border-gray-300 text-gray-900 placeholder:!text-gray-500 focus:!border-gray-900 focus:!border-2 focus:border-t-gray-900"
                                    /><label
                                        class="flex w-full h-full select-none pointer-events-none absolute left-0 font-normal !overflow-visible truncate peer-placeholder-shown:text-blue-gray-500 leading-tight peer-focus:leading-tight peer-disabled:text-transparent peer-disabled:peer-placeholder-shown:text-blue-gray-500 transition-all -top-1.5 peer-placeholder-shown:text-sm text-[11px] peer-focus:text-[11px] before:content[' '] before:block before:box-border before:w-2.5 before:h-1.5 before:mt-[6.5px] before:mr-1 peer-placeholder-shown:before:border-transparent before:rounded-tl-md before:border-t peer-focus:before:border-t-2 before:border-l peer-focus:before:border-l-2 before:pointer-events-none before:transition-all peer-disabled:before:border-transparent after:content[' '] after:block after:flex-grow after:box-border after:w-2.5 after:h-1.5 after:mt-[6.5px] after:ml-1 peer-placeholder-shown:after:border-transparent after:rounded-tr-md after:border-t peer-focus:after:border-t-2 after:border-r peer-focus:after:border-r-2 after:pointer-events-none after:transition-all peer-disabled:after:border-transparent peer-placeholder-shown:leading-[4.1] text-gray-500 peer-focus:text-gray-900 before:border-blue-gray-200 peer-focus:before:!border-gray-900 after:border-blue-gray-200 peer-focus:after:!border-gray-900 hidden"
                                    >
                                    </label>
                                    @if (
                                        contactUsForm.get("lastName").invalid &&
                                        contactUsForm.get("lastName").touched
                                    ) {
                                        <div class="text-red-500 text-sm mb-5">
                                            Last name is required.
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="mt-2">
                            <p
                                class="block antialiased font-sans text-sm leading-normal text-inherit mb-2 text-left font-medium !text-gray-900"
                            >
                                Your Email
                            </p>
                            <div class="relative w-full h-11 !min-w-full">
                                <input
                                    placeholder="<EMAIL>"
                                    [formControlName]="'email'"
                                    name="email"
                                    class="peer w-full h-full bg-transparent font-sans font-normal outline outline-0 focus:outline-0 disabled:bg-blue-gray-50 disabled:border-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 border-t-transparent focus:border-t-transparent placeholder:opacity-0 focus:placeholder:opacity-100 text-sm px-3 py-3 rounded-md !border !border-gray-300 text-gray-900 placeholder:!text-gray-500 focus:!border-gray-900 focus:!border-2 focus:border-t-gray-900"
                                /><label
                                    class="flex w-full h-full select-none pointer-events-none absolute left-0 font-normal !overflow-visible truncate peer-placeholder-shown:text-blue-gray-500 leading-tight peer-focus:leading-tight peer-disabled:text-transparent peer-disabled:peer-placeholder-shown:text-blue-gray-500 transition-all -top-1.5 peer-placeholder-shown:text-sm text-[11px] peer-focus:text-[11px] before:content[' '] before:block before:box-border before:w-2.5 before:h-1.5 before:mt-[6.5px] before:mr-1 peer-placeholder-shown:before:border-transparent before:rounded-tl-md before:border-t peer-focus:before:border-t-2 before:border-l peer-focus:before:border-l-2 before:pointer-events-none before:transition-all peer-disabled:before:border-transparent after:content[' '] after:block after:flex-grow after:box-border after:w-2.5 after:h-1.5 after:mt-[6.5px] after:ml-1 peer-placeholder-shown:after:border-transparent after:rounded-tr-md after:border-t peer-focus:after:border-t-2 after:border-r peer-focus:after:border-r-2 after:pointer-events-none after:transition-all peer-disabled:after:border-transparent peer-placeholder-shown:leading-[4.1] text-gray-500 peer-focus:text-gray-900 before:border-blue-gray-200 peer-focus:before:!border-gray-900 after:border-blue-gray-200 peer-focus:after:!border-gray-900 hidden"
                                >
                                </label>
                                @if (
                                    contactUsForm.get("email").invalid &&
                                    contactUsForm.get("email").touched
                                ) {
                                    <div class="text-red-500 text-sm mb-5">
                                        @if (
                                            contactUsForm.get("email").errors
                                                ?.required
                                        ) {
                                            Email is required.
                                        }
                                        @if (
                                            contactUsForm.get("email").errors
                                                ?.email
                                        ) {
                                            Enter a valid email address.
                                        }
                                    </div>
                                }
                            </div>
                        </div>
                        <div class="mt-2">
                            <p
                                class="block antialiased font-sans text-sm leading-normal text-inherit mb-2 text-left font-medium !text-gray-900"
                            >
                                Your Phone
                            </p>
                            <div class="relative w-full h-11 !min-w-full">
                                <input
                                    placeholder="+61 000 000 000"
                                    [formControlName]="'phone'"
                                    name="phone"
                                    class="peer w-full h-full bg-transparent font-sans font-normal outline outline-0 focus:outline-0 disabled:bg-blue-gray-50 disabled:border-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 border-t-transparent focus:border-t-transparent placeholder:opacity-0 focus:placeholder:opacity-100 text-sm px-3 py-3 rounded-md !border !border-gray-300 text-gray-900 placeholder:!text-gray-500 focus:!border-gray-900 focus:!border-2 focus:border-t-gray-900"
                                /><label
                                    class="flex w-full h-full select-none pointer-events-none absolute left-0 font-normal !overflow-visible truncate peer-placeholder-shown:text-blue-gray-500 leading-tight peer-focus:leading-tight peer-disabled:text-transparent peer-disabled:peer-placeholder-shown:text-blue-gray-500 transition-all -top-1.5 peer-placeholder-shown:text-sm text-[11px] peer-focus:text-[11px] before:content[' '] before:block before:box-border before:w-2.5 before:h-1.5 before:mt-[6.5px] before:mr-1 peer-placeholder-shown:before:border-transparent before:rounded-tl-md before:border-t peer-focus:before:border-t-2 before:border-l peer-focus:before:border-l-2 before:pointer-events-none before:transition-all peer-disabled:before:border-transparent after:content[' '] after:block after:flex-grow after:box-border after:w-2.5 after:h-1.5 after:mt-[6.5px] after:ml-1 peer-placeholder-shown:after:border-transparent after:rounded-tr-md after:border-t peer-focus:after:border-t-2 after:border-r peer-focus:after:border-r-2 after:pointer-events-none after:transition-all peer-disabled:after:border-transparent peer-placeholder-shown:leading-[4.1] text-gray-500 peer-focus:text-gray-900 before:border-blue-gray-200 peer-focus:before:!border-gray-900 after:border-blue-gray-200 peer-focus:after:!border-gray-900 hidden"
                                >
                                </label>
                                @if (
                                    contactUsForm.get("phone").invalid &&
                                    contactUsForm.get("phone").touched
                                ) {
                                    <div class="text-red-500 text-sm mb-5">
                                        @if (
                                            contactUsForm.get("phone").errors
                                                ?.required
                                        ) {
                                            Phone number is required.
                                        }
                                        @if (
                                            contactUsForm.get("phone").errors
                                                ?.pattern
                                        ) {
                                            Enter a valid phone number.
                                        }
                                    </div>
                                }
                            </div>
                        </div>
                        <div class="mt-2">
                            <p
                                class="block antialiased font-sans text-sm leading-normal text-inherit mb-2 text-left font-medium !text-gray-900"
                            >
                                Your Message
                            </p>
                            <div class="relative w-full !min-w-full">
                                <textarea
                                    rows="6"
                                    placeholder="Message"
                                    [formControlName]="'message'"
                                    name="message"
                                    class="peer w-full h-full min-h-[100px] bg-transparent font-sans font-normal outline outline-0 focus:outline-0 resize-y disabled:bg-blue-gray-50 disabled:border-0 disabled:resize-none transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 border-t-transparent focus:border-t-transparent text-sm px-3 py-2.5 rounded-[7px] !resize-none !border !border-gray-300 text-gray-900 placeholder:!text-gray-500 focus:!border-gray-900 focus:!border-2 focus:border-t-gray-900"
                                    spellcheck="false"
                                ></textarea
                                ><label
                                    class="flex w-full h-full select-none pointer-events-none absolute left-0 font-normal peer-placeholder-shown:text-blue-gray-500 leading-tight peer-focus:leading-tight peer-disabled:text-transparent peer-disabled:peer-placeholder-shown:text-blue-gray-500 transition-all -top-1.5 peer-placeholder-shown:text-sm text-[11px] peer-focus:text-[11px] before:content[' '] before:block before:box-border before:w-2.5 before:h-1.5 before:mt-[6.5px] before:mr-1 peer-placeholder-shown:before:border-transparent before:rounded-tl-md before:border-t peer-focus:before:border-t-2 before:border-l peer-focus:before:border-l-2 before:pointer-events-none before:transition-all peer-disabled:before:border-transparent after:content[' '] after:block after:flex-grow after:box-border after:w-2.5 after:h-1.5 after:mt-[6.5px] after:ml-1 peer-placeholder-shown:after:border-transparent after:rounded-tr-md after:border-t peer-focus:after:border-t-2 after:border-r peer-focus:after:border-r-2 after:pointer-events-none after:transition-all peer-disabled:after:border-transparent peer-placeholder-shown:leading-[3.75] text-blue-gray-400 peer-focus:text-gray-900 before:border-blue-gray-200 peer-focus:before:!border-gray-900 after:border-blue-gray-200 peer-focus:after:!border-gray-900 hidden"
                                ></label>
                                @if (
                                    contactUsForm.get("message").invalid &&
                                    contactUsForm.get("message").touched
                                ) {
                                    <div class="text-red-500 text-sm">
                                        Message is required.
                                    </div>
                                }
                            </div>
                        </div>
                        <div class="mt-4">
                            <div #captchaRef class="flex justify-center"></div>
                            @if (captchaError) {
                                <div class="text-red-500 text-sm">
                                    Please complete the reCAPTCHA.
                                </div>
                            }
                        </div>
                        <button
                            class="mt-5 align-middle select-none font-sans font-bold text-center uppercase transition-all disabled:opacity-50 disabled:shadow-none disabled:pointer-events-none text-xs py-3 px-6 rounded-lg bg-gray-900 text-white shadow-md shadow-gray-900/10 hover:shadow-lg hover:shadow-gray-900/20 focus:opacity-[0.85] focus:shadow-none active:opacity-[0.85] active:shadow-none bg-gray-900 w-full"
                            type="button"
                            data-ripple-light="true"
                            [disabled]="!contactUsForm.valid || !captchaPassed"
                            (click)="onSubmit()"
                        >
                            Send message
                        </button>
                    </form>
                }
                <p class="mt-3 text-xs text-gray-500">
                    We will get back to you as soon as possible.
                </p>
            </div>
        </div>
    </section>
</div>

<div class="w-full mt-10">
    <section class="relative flex items-center justify-center">
        <div
            class="relative items-center w-full px-5 py-12 mx-auto max-w-7xl lg:px-16 lg:py-32 md:px-12"
        >
            <div>
                <div class="text-center">
                    <img
                        class="left-10 h-60"
                        src="images/logo/logo-text.svg"
                        alt="Logo image"
                    />
                </div>
                <p
                    class="mt-6 text-4xl font-extrabold tracking-tight text-black md:text-6xl"
                >
                    Just like if Jobs &amp;
                    <span class="md:block">Scheduler had a baby.</span>
                </p>
                <p class="max-w-2xl mt-4 text-base text-slate-600">
                    will be the easiest way to build a scheduled jobs.
                </p>
            </div>
            <div
                class="relative items-center w-full py-12 pb-12 mx-auto mt-12 max-w-7xl"
            >
                <svg
                    class="absolute -mt-24 blur-3xl"
                    fill="none"
                    viewBox="0 0 400 400"
                    height="100%"
                    width="100%"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <g clip-path="url(#clip0_10_20)">
                        <g filter="url(#filter0_f_10_20)">
                            <path
                                d="M128.6 0H0V322.2L106.2 134.75L128.6 0Z"
                                fill="#03FFE0"
                            ></path>
                            <path
                                d="M0 322.2V400H240H320L106.2 134.75L0 322.2Z"
                                fill="#7C87F8"
                            ></path>
                            <path
                                d="M320 400H400V78.75L106.2 134.75L320 400Z"
                                fill="#4C65E4"
                            ></path>
                            <path
                                d="M400 0H128.6L106.2 134.75L400 78.75V0Z"
                                fill="#043AFF"
                            ></path>
                        </g>
                    </g>
                    <defs>
                        <filter
                            color-interpolation-filters="sRGB"
                            filterUnits="userSpaceOnUse"
                            height="720.666"
                            id="filter0_f_10_20"
                            width="720.666"
                            x="-160.333"
                            y="-160.333"
                        >
                            <feFlood
                                flood-opacity="0"
                                result="BackgroundImageFix"
                            ></feFlood>
                            <feBlend
                                in="SourceGraphic"
                                in2="BackgroundImageFix"
                                mode="normal"
                                result="shape"
                            ></feBlend>
                            <feGaussianBlur
                                result="effect1_foregroundBlur_10_20"
                                stdDeviation="80.1666"
                            ></feGaussianBlur>
                        </filter>
                    </defs>
                </svg>
                <img
                    alt=""
                    class="relative object-cover w-full border rounded shadow-2xl lg:rounded-2xl"
                    src="images/landing/solutions/dashboard_scheduler.jpg"
                />
            </div>
        </div>
    </section>

    <section>
        <div class="px-8 py-12 mx-auto space-y-24 md:px-12 lg:px-32 max-w-7xl">
            <div
                class="grid items-center grid-cols-1 gap-4 mt-6 list-none lg:grid-cols-2 lg:gap-24"
            >
                <div>
                    <span
                        class="text-xs font-bold tracking-wide text-gray-500 uppercase"
                        >mobile app</span
                    >
                    <p
                        class="mt-8 text-4xl font-semibold tracking-tight text-gray-900"
                    >
                        Get live updates on your jobs and never miss a beat.
                        <span class="lg:block lg:text-gray-600"
                            >Track the progress of your jobs in real time
                        </span>
                    </p>
                    <p class="mt-4 text-base text-gray-500">
                        Experience the power of real-time tracking. Stay
                        connected to your jobs and make informed decisions with
                        instant updates.
                    </p>
                </div>
                <div class="p-2 border bg-gray-50 rounded-3xl">
                    <div
                        class="h-full overflow-hidden bg-white border shadow-lg rounded-3xl"
                    >
                        <img
                            alt="Lexingtøn thumbnail"
                            class="relative w-full rounded-2xl drop-shadow-2xl"
                            src="images/landing/solutions/phone-01.jpg"
                        />
                    </div>
                </div>
            </div>
            <div
                class="grid items-center grid-cols-1 gap-4 mt-6 list-none lg:grid-cols-2 lg:gap-24"
            >
                <div>
                    <span
                        class="text-xs font-bold tracking-wide text-gray-500 uppercase"
                        >mobile app</span
                    >
                    <p
                        class="mt-8 text-4xl font-semibold tracking-tight text-gray-900"
                    >
                        Tracking Job Progress
                        <span class="lg:block lg:text-gray-600"
                            >Essential details such as the job name,
                            description, and estimated completion time.</span
                        >
                    </p>

                    <ul class="mt-4 text-base text-gray-500">
                        <li>
                            <strong>View Past Jobs:</strong>Access a
                            comprehensive list of completed and ongoing jobs.
                        </li>
                        <li>
                            <strong>Track Job Duration:</strong>Make direct and
                            accurate estimates of job durations.
                        </li>
                        <li>
                            <strong>Review Job Details:</strong>Get detailed
                            information about past jobs, including start and
                            stop times, descriptions, and notes.
                        </li>
                    </ul>
                </div>
                <div class="p-2 border bg-gray-50 rounded-3xl lg:order-first">
                    <div
                        class="h-full overflow-hidden bg-white border shadow-lg rounded-3xl"
                    >
                        <img
                            alt="Lexingtøn thumbnail"
                            class="relative w-full rounded-2xl drop-shadow-2xl"
                            src="images/landing/solutions/phone-02.jpg"
                        />
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Platform solution -->
    <section class="text-gray-600 body-font">
        <div class="container px-5 py-24 mx-auto">
            <div class="flex flex-col text-center w-full mb-20">
                <h2
                    class="text-xs text-indigo-500 tracking-widest font-medium title-font mb-1"
                >
                    AND MANY MORE
                </h2>
                <h1
                    class="sm:text-3xl text-2xl font-medium title-font text-gray-900"
                >
                    Our platform solutions
                </h1>
            </div>
            <div class="flex flex-wrap -m-4">
                <div class="p-4 md:w-1/3">
                    <div
                        class="flex rounded-lg h-full bg-gray-100 p-8 flex-col"
                    >
                        <div class="flex items-center mb-3">
                            <div
                                class="w-8 h-8 mr-3 inline-flex items-center justify-center rounded-full bg-indigo-500 text-white flex-shrink-0"
                            >
                                <i class="fa-duotone fa-clock text-white"></i>
                            </div>
                            <h2
                                class="text-gray-900 text-lg title-font font-medium"
                            >
                                Clock-in clock-out
                            </h2>
                        </div>
                        <div class="flex-grow">
                            <p class="leading-relaxed text-base">
                                Your employees can clock in and out from mobile
                                device and you can Track time for jobs,
                                projects, clients, equipment, mileage, and more
                                Always know where your employees are using GPS.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="p-4 md:w-1/3">
                    <div
                        class="flex rounded-lg h-full bg-gray-100 p-8 flex-col"
                    >
                        <div class="flex items-center mb-3">
                            <div
                                class="w-8 h-8 mr-3 inline-flex items-center justify-center rounded-full bg-indigo-500 text-white flex-shrink-0"
                            >
                                <i
                                    class="fa-duotone fa-location text-white"
                                ></i>
                            </div>
                            <h2
                                class="text-gray-900 text-lg title-font font-medium"
                            >
                                Real-Time Updates
                            </h2>
                        </div>
                        <div class="flex-grow">
                            <p class="leading-relaxed text-base">
                                Experience real-time updates without page
                                reloads. See job statuses and team member
                                locations on the map as they change in real
                                time.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="p-4 md:w-1/3">
                    <div
                        class="flex rounded-lg h-full bg-gray-100 p-8 flex-col"
                    >
                        <div class="flex items-center mb-3">
                            <div
                                class="w-8 h-8 mr-3 inline-flex items-center justify-center rounded-full bg-indigo-500 text-white flex-shrink-0"
                            >
                                <i
                                    class="fa-duotone fa-chart-pie text-white"
                                ></i>
                            </div>
                            <h2
                                class="text-gray-900 text-lg title-font font-medium"
                            >
                                Simple Reports
                            </h2>
                        </div>
                        <div class="flex-grow">
                            <p class="leading-relaxed text-base">
                                Generate simple, readable reports with no
                                unnecessary data flow, ensuring efficient data
                                consumption.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section>
        <div class="px-8 py-12 mx-auto space-y-24 md:px-12 lg:px-32 max-w-7xl">
            <div
                class="grid items-center grid-cols-1 gap-4 mt-6 list-none lg:grid-cols-2 lg:gap-24"
            >
                <div>
                    <span
                        class="text-xs font-bold tracking-wide text-gray-500 uppercase"
                        >Mobile App</span
                    >
                    <p
                        class="mt-8 text-4xl font-semibold tracking-tight text-gray-900"
                    >
                        Visualize Your Work
                        <span class="lg:block lg:text-gray-600"
                            >View all captured images associated with a specific
                            job.</span
                        >
                    </p>
                    <ul class="mt-4 text-base text-gray-500">
                        <li>
                            <strong>Document Progress: </strong>Take photos of
                            your jobs at key stages to visually track progress
                            and document important details.
                        </li>
                        <li>
                            <strong>Share Images: </strong>Share captured images
                            with clients or team members for collaboration and
                            feedback.
                        </li>
                    </ul>
                </div>
                <div class="p-2 border bg-gray-50 rounded-3xl">
                    <div
                        class="h-full overflow-hidden bg-white border shadow-lg rounded-3xl"
                    >
                        <img
                            alt="Lexingtøn thumbnail"
                            class="relative w-full rounded-2xl drop-shadow-2xl"
                            src="images/landing/solutions/phone-03.jpg"
                        />
                    </div>
                </div>
            </div>
            <div
                class="grid items-center grid-cols-1 gap-4 mt-6 list-none lg:grid-cols-2 lg:gap-24"
            >
                <div>
                    <span
                        class="text-xs font-bold tracking-wide text-gray-500 uppercase"
                        >Mobile app</span
                    >
                    <p
                        class="mt-8 text-4xl font-semibold tracking-tight text-gray-900"
                    >
                        Manage Your Schedule
                        <span class="lg:block lg:text-gray-600"
                            >See upcoming jobs directly on calendar</span
                        >
                    </p>
                    <p class="mt-4 text-base text-gray-500">
                        Choose which jobs you want to focus on for a specific
                        day.
                    </p>
                </div>
                <div class="p-2 border bg-gray-50 rounded-3xl lg:order-first">
                    <div
                        class="h-full overflow-hidden bg-white border shadow-lg rounded-3xl"
                    >
                        <img
                            alt="Lexingtøn thumbnail"
                            class="relative w-full rounded-2xl drop-shadow-2xl"
                            src="images/landing/solutions/phone-04.jpg"
                        />
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section>
        <div class="px-8 py-12 mx-auto md:px-12 lg:px-32 max-w-7xl">
            <div>
                <p
                    class="mt-12 text-4xl font-semibold tracking-tighter text-gray-900 lg:text-5xl"
                >
                    Our customers pretend
                    <span class="text-gray-500 md:block"
                        >they love us and our app</span
                    >
                </p>
            </div>
            <ul
                role="list"
                class="grid max-w-2xl grid-cols-1 gap-6 mx-auto mt-12 sm:gap-4 lg:max-w-none lg:grid-cols-3"
            >
                <li class="p-2 border bg-gray-50 rounded-3xl">
                    <figure
                        class="relative flex flex-col justify-between h-full p-6 bg-white border shadow-lg rounded-2xl"
                    >
                        <blockquote class="relative">
                            <p class="text-base text-gray-500">
                                Being in the recycling industry, we were always
                                looking for ways to enhance our jobs' security
                                and efficiency.
                            </p>
                        </blockquote>
                        <figcaption
                            class="relative flex items-center justify-between pt-6 mt-6"
                        >
                            <div>
                                <div class="font-medium text-gray-900">
                                    Michael Doe
                                </div>
                                <div class="mt-1 text-sm text-gray-500">
                                    CEO of Lexingtøn
                                </div>
                            </div>
                            <div
                                class="overflow-hidden rounded-full bg-gray-50"
                            >
                                <img
                                    alt=""
                                    src="/images/avatars/male-02.jpg"
                                    width="56"
                                    height="56"
                                    decoding="async"
                                    data-nimg="future"
                                    class="object-cover h-14 w-14 grayscale"
                                    loading="lazy"
                                    style="color: transparent"
                                />
                            </div>
                        </figcaption>
                    </figure>
                </li>
                <li class="p-2 border bg-gray-50 rounded-3xl">
                    <figure
                        class="relative flex flex-col justify-between h-full p-6 bg-white border shadow-lg rounded-2xl"
                    >
                        <blockquote class="relative">
                            <p class="text-base text-gray-500">
                                Implementing Semplice's scheduling technology
                                has been a game-changer for our supply chain
                                management.
                            </p>
                        </blockquote>
                        <figcaption
                            class="relative flex items-center justify-between pt-6 mt-6"
                        >
                            <div>
                                <div class="font-medium text-gray-900">
                                    Sam Johnson
                                </div>
                                <div class="mt-1 text-sm text-gray-500">
                                    Director of Engineering
                                </div>
                            </div>
                            <div
                                class="overflow-hidden rounded-full bg-gray-50"
                            >
                                <img
                                    alt=""
                                    src="/images/avatars/male-04.jpg"
                                    width="56"
                                    height="56"
                                    decoding="async"
                                    data-nimg="future"
                                    class="object-cover h-14 w-14 grayscale"
                                    loading="lazy"
                                    style="color: transparent"
                                />
                            </div>
                        </figcaption>
                    </figure>
                </li>
                <li class="p-2 border bg-gray-50 rounded-3xl">
                    <figure
                        class="relative flex flex-col justify-between h-full p-6 bg-white border shadow-lg rounded-2xl"
                    >
                        <blockquote class="relative">
                            <p class="text-base text-gray-500">
                                We were initially hesitant about integrating Job
                                Tracking into our existing systems, fearing the
                                complexity of the process, but we had an idea
                                Streamliner APP.
                            </p>
                        </blockquote>
                        <figcaption
                            class="relative flex items-center justify-between pt-6 mt-6"
                        >
                            <div>
                                <div class="font-medium text-gray-900">
                                    Jenson Button
                                </div>
                                <div class="mt-1 text-sm text-gray-500">
                                    Founder of Benji and Tom
                                </div>
                            </div>
                            <div
                                class="overflow-hidden rounded-full bg-gray-50"
                            >
                                <img
                                    alt=""
                                    src="/images/avatars/male-08.jpg"
                                    width="56"
                                    height="56"
                                    decoding="async"
                                    data-nimg="future"
                                    class="object-cover h-14 w-14 grayscale"
                                    loading="lazy"
                                    style="color: transparent"
                                />
                            </div>
                        </figcaption>
                    </figure>
                </li>
            </ul>
        </div>
    </section>
    <section>
        <div class="px-8 py-12 mx-auto md:px-12 lg:px-32 max-w-7xl">
            <div class="p-2 border bg-gray-50 rounded-3xl">
                <div
                    class="p-10 text-center bg-white border shadow-lg md:p-20 rounded-3xl dark:bg-neutral-900"
                >
                    <p
                        class="text-4xl font-semibold tracking-tighter text-black"
                    >
                        Download our app today
                    </p>
                    <div
                        class="mt-8 flex flex-col items-center justify-center gap-4 md:flex-row"
                    >
                        <div class="flex flex-col items-center">
                            <div
                                class="flex items-center w-24 h-24 rounded-xl bg-black shadow-2xl"
                            >
                                <img
                                    class="h-25"
                                    src="images/logo/logo.svg"
                                    alt="Logo image"
                                />
                            </div>
                            <div class="flex text-2xl font-bold mt-2 ml-5">
                                Streamliner Admin
                            </div>
                        </div>
                        <div class="flex flex-col items-center">
                            <div
                                class="flex items-center w-24 h-24 rounded-xl bg-white shadow-2xl"
                            >
                                <img
                                    class="h-25"
                                    src="images/logo/logo.svg"
                                    alt="Logo image"
                                />
                            </div>
                            <div class="flex text-2xl font-bold mt-2 ml-5">
                                Streamliner Team
                            </div>
                        </div>
                        <div class="flex flex-col items-center">
                            <div
                                class="flex items-center w-24 h-24 rounded-xl bg-violet-400 shadow-2xl"
                            >
                                <img
                                    class="h-25"
                                    src="images/logo/logo.svg"
                                    alt="Logo image"
                                />
                            </div>
                            <div class="flex text-2xl font-bold mt-2 ml-5">
                                Streamliner Client
                            </div>
                        </div>
                    </div>
                    <p class="mt-4 text-base text-gray-500">
                        The fastest method for working together
                        <span class="md:block">
                            on staging and temporary environments.</span
                        >
                    </p>
                    <div
                        class="flex flex-col items-center justify-center gap-2 mx-auto mt-8 md:flex-row"
                    >
                        <a
                            href="https://play.google.com/store/apps/developer?id=Streamliner+Apps+Pty+Ltd"
                            target="_blank"
                            class="inline-flex items-center justify-center w-full h-12 gap-3 px-5 py-3 bg-gray-100 md:w-auto rounded-xl hover:bg-gray-200 focus:outline-none"
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="currentColor"
                                class="size-4"
                                viewBox="0 0 512 512"
                            >
                                <path
                                    d="M99.617 8.057a50.191 50.191 0 00-38.815-6.713l230.932 230.933 74.846-74.846L99.617 8.057zM32.139 20.116c-6.441 8.563-10.148 19.077-10.148 30.199v411.358c0 11.123 3.708 21.636 10.148 30.199l235.877-235.877L32.139 20.116zM464.261 212.087l-67.266-37.637-81.544 81.544 81.548 81.548 67.273-37.64c16.117-9.03 25.738-25.442 25.738-43.908s-9.621-34.877-25.749-43.907zM291.733 279.711L60.815 510.629c3.786.891 7.639 1.371 11.492 1.371a50.275 50.275 0 0027.31-8.07l266.965-149.372-74.849-74.847z"
                                ></path>
                            </svg>
                            <span class="text-xs text-gray-600"
                                >Download on Google Play</span
                            ></a
                        ><a
                            href="https://apps.apple.com/us/developer/streamliner-apps-pty-ltd/id1721271667"
                            target="_blank"
                            class="inline-flex items-center justify-center w-full h-12 gap-3 px-5 py-3 bg-gray-100 md:w-auto rounded-xl hover:bg-gray-200 focus:outline-none"
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="currentColor"
                                class="size-4"
                                viewBox="0 0 305 305"
                            >
                                <path
                                    d="M40.74 112.12c-25.79 44.74-9.4 112.65 19.12 153.82C74.09 286.52 88.5 305 108.24 305c.37 0 .74 0 1.13-.02 9.27-.37 15.97-3.23 22.45-5.99 7.27-3.1 14.8-6.3 26.6-6.3 11.22 0 18.39 3.1 25.31 6.1 6.83 2.95 13.87 6 24.26 5.81 22.23-.41 35.88-20.35 47.92-37.94a168.18 168.18 0 0021-43l.09-.28a2.5 2.5 0 00-1.33-3.06l-.18-.08c-3.92-1.6-38.26-16.84-38.62-58.36-.34-33.74 25.76-51.6 31-54.84l.24-.15a2.5 2.5 0 00.7-3.51c-18-26.37-45.62-30.34-56.73-30.82a50.04 50.04 0 00-4.95-.24c-13.06 0-25.56 4.93-35.61 8.9-6.94 2.73-12.93 5.09-17.06 5.09-4.64 0-10.67-2.4-17.65-5.16-9.33-3.7-19.9-7.9-31.1-7.9l-.79.01c-26.03.38-50.62 15.27-64.18 38.86z"
                                ></path>
                                <path
                                    d="M212.1 0c-15.76.64-34.67 10.35-45.97 23.58-9.6 11.13-19 29.68-16.52 48.38a2.5 2.5 0 002.29 2.17c1.06.08 2.15.12 3.23.12 15.41 0 32.04-8.52 43.4-22.25 11.94-14.5 17.99-33.1 16.16-49.77A2.52 2.52 0 00212.1 0z"
                                ></path>
                            </svg>
                            <span class="text-xs text-gray-600"
                                >Download on App Store</span
                            >
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

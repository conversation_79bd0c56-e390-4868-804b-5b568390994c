
import { Component, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { RouterLink } from '@angular/router';
import { FuseCardComponent } from '@fuse/components/card';


@Component({
    selector: 'landing-home',
    templateUrl: './home.component.html',
    styleUrls: ['./home.component.scss'],
    encapsulation: ViewEncapsulation.None,
    imports: [FuseCardComponent, MatButtonModule, RouterLink, MatIconModule]
})
export class LandingHomeComponent {

    isVideoModalOpen = false;
    videoUrl = 'https://www.youtube.com/embed/mHs4oRU6FlQ';
    safeVideoUrl: SafeResourceUrl;

    /**
     * Constructor
     */
    constructor(
        private _sanitizer: DomSanitizer,

    ) {
        this.safeVideoUrl = this._sanitizer.bypassSecurityTrustResourceUrl(this.videoUrl);
    }

    openVideoModal(): void {
        this.isVideoModalOpen = true;
        // Prevent body scrolling when modal is open
        document.body.style.overflow = 'hidden';
    }

    closeVideoModal(): void {
        this.isVideoModalOpen = false;
        // Re-enable body scrolling
        document.body.style.overflow = 'auto';
    }
}

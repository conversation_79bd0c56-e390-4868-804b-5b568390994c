<div class="relative flex flex-col flex-auto min-w-0 overflow-hidden">
    <header
        class="ezy__header34 light pt-16 md:pt-28 bg-white dark:bg-[#0b1727] text-zinc-900 dark:text-white relative overflow-hidden z-10"
    >
        <div
            class="flex items-center sm:max-w-auto relative px-10 sm:mx-auto sm:rounded-lg sm:px-20"
        >
            <div class="mx-auto">
                <h1
                    class="flex flex-col gap-2 text-center text-8xl font-black md:flex-row lg:tracking-tight xl:text-9xl"
                >
                    <span
                        class="before:absolute before:-z-10 before:text-black before:content-[attr(data-text)]"
                        data-text="Schedule."
                        ><span
                            class="animate-gradient-1 bg-gradient-to-r from-indigo-500 to-blue-500 bg-clip-text text-transparent"
                            >Schedule.
                        </span>
                    </span>

                    <span
                        class="before:absolute before:-z-10 before:text-black before:content-[attr(data-text)]"
                        data-text="Allocate."
                        ><span
                            class="animate-gradient-2 bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent"
                        >
                            Allocate.
                        </span>
                    </span>

                    <span
                        class="before:absolute before:-z-10 before:text-black before:content-[attr(data-text)]"
                        data-text="Track."
                        ><span
                            class="animate-gradient-3 bg-gradient-to-r from-fuchsia-600 to-purple-600 bg-clip-text text-transparent"
                            >Track.</span
                        >
                    </span>
                </h1>
            </div>
        </div>

        <!-- shape one -->
        <svg
            class="absolute top-0 left-0 -z-10"
            width="62"
            height="62"
            viewBox="0 0 62 62"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M0 62V0H62C62.0281 34.238 34.2662 62 0 62Z"
                fill="#FF6A35"
                fill-opacity="0.26"
            />
        </svg>

        <!-- shape two -->
        <svg
            class="absolute top-[20%] left-[5%] -z-10"
            width="49"
            height="40"
            viewBox="0 0 49 40"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M39.7007 19.7167C64.5265 24.2635 33.2736 43.5256 19.8503 39.4334C6.42703 35.3413 0 30.6059 0 19.7167C0 8.82747 8.8873 0 19.8503 0C30.8134 0 14.8749 15.1699 39.7007 19.7167Z"
                fill="#1DC9FF"
                fill-opacity="0.6"
            />
        </svg>

        <!-- shape three -->
        <svg
            class="absolute bottom-0 left-0 -z-10"
            width="100"
            height="301"
            viewBox="0 0 100 301"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M100 7.37581C100 3.30288 96.795 0 92.8428 0C88.8892 0 85.6856 3.30288 85.6856 7.37581H100ZM22.4316 235.974C18.4794 235.974 15.2744 239.275 15.2744 243.349C15.2744 247.422 18.4794 250.725 22.4316 250.725V235.974ZM-78.8428 320.248C-82.795 320.248 -86 323.551 -86 327.624C-86 331.699 -82.795 335 -78.8428 335V320.248ZM6.44099 264.17C6.44099 260.097 3.23743 256.794 -0.716209 256.794C-4.66842 256.794 -7.87341 260.097 -7.87341 264.17H6.44099ZM85.6856 7.37581V97.6368H100V7.37581H85.6856ZM85.6856 97.6368C85.6856 107.708 77.6867 115.966 67.6953 115.966V130.717C85.4809 130.717 100 115.969 100 97.6368H85.6856ZM67.6953 115.966C49.9067 115.966 35.3919 130.728 35.3919 149.058H49.7063C49.7063 138.981 57.7081 130.717 67.6953 130.717V115.966ZM35.3919 149.058V154.391H49.7063V149.058H35.3919ZM35.3919 154.391C35.3919 172.72 49.9067 187.483 67.6953 187.483V172.732C57.7081 172.732 49.7063 164.466 49.7063 154.391H35.3919ZM67.6953 187.483C77.6838 187.483 85.6856 195.747 85.6856 205.823H100C100 187.494 85.4852 172.732 67.6953 172.732V187.483ZM85.6856 205.823V211.728H100V205.823H85.6856ZM85.6856 211.728C85.6856 225.065 75.0972 235.974 61.9208 235.974V250.725C82.9 250.725 100 233.318 100 211.728H85.6856ZM61.9208 235.974H45.9016V250.725H61.9208V235.974ZM45.9016 235.974H22.4316V250.725H45.9016V235.974ZM-78.8428 335H-62.3655V320.248H-78.8428V335ZM-62.3655 335C-24.3722 335 6.44099 303.297 6.44099 264.17H-7.87341C-7.87341 295.134 -32.2623 320.248 -62.3655 320.248V335Z"
                fill="#FDB314"
                fill-opacity="0.32"
            />
        </svg>

        <!-- shape four -->
        <svg
            class="absolute bottom-0 left-1/2 -z-10"
            width="90"
            height="81"
            viewBox="0 0 90 81"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M85.8436 9.44612L39.2469 77.0032L4.08537 2.92747L85.8436 9.44612Z"
                stroke="#4175DF"
                stroke-opacity="0.78"
                stroke-width="4"
            />
        </svg>

        <!-- shape five -->
        <svg
            class="absolute bottom-0 right-0 -z-10"
            width="134"
            height="133"
            viewBox="0 0 134 133"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M66.9999 133C104.003 133 134 103.227 134 66.5C134 29.773 104.003 0 66.9999 0C29.9968 0 0 29.773 0 66.5C0 103.227 29.9968 133 66.9999 133Z"
                fill="#FF9100"
                fill-opacity="0.59"
            />
        </svg>

        <!-- shape six -->
        <svg
            class="absolute top-[10%] right-0 -z-10"
            width="149"
            height="255"
            viewBox="0 0 149 255"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g opacity="0.2">
                <path
                    d="M29.9138 200.627C16.1542 200.627 5 211.61 5 225.159C5 238.709 16.1542 249.692 29.9138 249.692C43.6732 249.692 54.8275 238.709 54.8275 225.159V124.964"
                    stroke="#4484AB"
                    stroke-width="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                />
                <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M82.5575 92.916C97.6331 92.916 109.854 80.6999 109.854 65.6317C109.854 50.5624 97.6331 38.3473 82.5575 38.3473C67.4819 38.3473 55.2607 50.5624 55.2607 65.6317C55.2607 80.6999 67.4819 92.916 82.5575 92.916Z"
                    stroke="#34C69F"
                    stroke-width="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                />
                <path
                    d="M82.7746 124.964C116.395 124.964 143.651 98.1094 143.651 64.9819C143.651 31.8548 116.395 5 82.7746 5C49.1538 5 21.8984 31.8548 21.8984 64.9819"
                    stroke="#FDB314"
                    stroke-width="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                />
            </g>
        </svg>

        <div class="container px-4 mx-auto">
            <div class="grid grid-cols-12 gap-6">
                <div class="col-span-12 md:col-span-5 text-center">
                    <div class="flex flex-col justify-center h-full">
                        <section class="flex mt-30">
                            <div
                                class="text-black dark:text-white relative left-1/2 top-1/1 p-10 transform -translate-x-1/2 -translate-y-1/2"
                            >
                                <h1
                                    class="wrapper-text m-0 text-8xl font-bold uppercase text-center"
                                >
                                    <div
                                        class="line overflow-hidden whitespace-nowrap h-16 leading-tight"
                                    >
                                        <p class="animate-moveWords">#1 JOB</p>
                                        <p class="animate-moveWords">
                                            MANAGEMENT
                                        </p>
                                        <p class="animate-moveWords">
                                            PLATFORM
                                        </p>
                                    </div>
                                    <div
                                        class="line overflow-hidden whitespace-nowrap h-16 leading-tight ml-20"
                                    >
                                        <p class="animate-moveWords">
                                            MANAGEMENT
                                        </p>
                                        <p class="animate-moveWords">
                                            PLATFORM
                                        </p>
                                        <p class="animate-moveWords">#1 JOB</p>
                                    </div>
                                    <div
                                        class="line overflow-hidden whitespace-nowrap h-16 leading-tight ml-40"
                                    >
                                        <p class="animate-moveWords">
                                            PLATFORM
                                        </p>
                                        <p class="animate-moveWords">#1 JOB</p>
                                        <p class="animate-moveWords">
                                            MANAGEMENT
                                        </p>
                                    </div>
                                </h1>
                            </div>
                        </section>
                        <div>
                            <h2
                                class="dark:text-white text-3xl md:text-5xl font-bold mb-2 uppercase"
                            >
                                help move your business forward
                            </h2>
                            <p
                                class="text-sm md:text-base text-black-50 dark:text-gray-50 mb-4"
                            >
                                Enhance your business efficiency and make
                                smarter decisions with an all-in-one platform
                                solution. Streamline job templates, scheduling,
                                team member tracking, and workflow management
                                seamlessly.
                            </p>
                            <div class="relative inline-flex mt-10 group">
                                <div
                                    class="absolute transitiona-all duration-1000 opacity-70 -inset-px bg-gradient-to-r from-[#44BCFF] via-[#FF44EC] to-[#FF675E] rounded-xl blur-lg group-hover:opacity-100 group-hover:-inset-1 group-hover:duration-200 animate-tilt"
                                ></div>
                                <a
                                    routerLink="/home/<USER>"
                                    title=""
                                    class="relative inline-flex items-center justify-center px-8 py-4 text-lg font-bold text-white transition-all duration-200 bg-gray-900 font-pj rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-900"
                                    role="button"
                                >
                                    Explore all features
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-span-12 lg:col-span-5 relative text-center">
                    <div>
                        <img
                            src="images/landing/hero.png"
                            alt=""
                            class="max-w-full h-auto mx-auto"
                        />
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Header & Cards -->
    <div
        class="relative pt-8 pb-12 sm:pt-20 sm:pb-24 px-6 sm:px-16 overflow-hidden"
    >
        <!-- Background - @formatter:off -->
        <svg
            class="-z-1 absolute inset-0"
            viewBox="0 0 960 540"
            width="100%"
            height="100%"
            preserveAspectRatio="xMidYMax slice"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g
                class="opacity-40 text-gray-200 dark:text-gray-800"
                fill="none"
                stroke="currentColor"
                stroke-width="100"
            >
                <circle r="234" cx="196" cy="23"></circle>
                <circle r="234" cx="790" cy="491"></circle>
            </g>
        </svg>

        <section class="pt-12 sm:pt-16 bg-gray-300 bg-opacity-25 rounded-2xl">
            <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div class="max-w-2xl mx-auto text-center">
                    <h1 class="px-6 text-lg text-gray-600 font-inter">
                        Smart job management tracking, made for Business
                    </h1>
                    <p
                        class="mt-5 text-4xl font-bold leading-tight text-gray-900 sm:leading-tight sm:text-5xl lg:text-6xl lg:leading-tight font-pj"
                    >
                        Turn your jobs into profitable
                        <span class="relative inline-flex sm:inline">
                            <span
                                class="bg-gradient-to-r from-[#44BCFF] via-[#FF44EC] to-[#FF675E] blur-lg filter opacity-30 w-full h-full absolute inset-0"
                            ></span>
                            <span class="relative"> business </span>
                        </span>
                    </p>

                    <div
                        class="px-8 sm:items-center sm:justify-center sm:px-0 sm:space-x-5 sm:flex mt-9"
                    >
                        <div class="relative inline-flex mt-10 group">
                            <div
                                class="absolute transitiona-all duration-1000 opacity-70 -inset-px bg-gradient-to-r from-[#44BCFF] via-[#FF44EC] to-[#FF675E] rounded-xl blur-lg group-hover:opacity-100 group-hover:-inset-1 group-hover:duration-200 animate-tilt"
                            ></div>
                            <a
                                routerLink="/sign-up"
                                title=""
                                class="relative inline-flex items-center justify-center px-8 py-4 text-lg font-bold text-white transition-all duration-200 bg-gray-900 font-pj rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-900"
                                role="button"
                            >
                                Get your account now
                            </a>
                        </div>
                        <div class="relative inline-flex mt-10 group">
                            <a
                                (click)="openVideoModal()"
                                title=""
                                class="inline-flex items-center justify-center w-full px-6 py-3 mt-4 text-lg font-bold text-gray-900 transition-all duration-200 border-2 border-gray-400 sm:w-auto sm:mt-0 rounded-xl font-pj focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-900 hover:bg-gray-900 focus:bg-gray-900 hover:text-white focus:text-white hover:border-gray-900 focus:border-gray-900"
                                role="button"
                            >
                                <svg
                                    class="w-5 h-5 mr-2"
                                    viewBox="0 0 18 18"
                                    fill="none"
                                    stroke="currentColor"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M8.18003 13.4261C6.8586 14.3918 5 13.448 5 11.8113V5.43865C5 3.80198 6.8586 2.85821 8.18003 3.82387L12.5403 7.01022C13.6336 7.80916 13.6336 9.44084 12.5403 10.2398L8.18003 13.4261Z"
                                        stroke-width="2"
                                        stroke-miterlimit="10"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                    />
                                </svg>
                                Watch free demo
                            </a>
                        </div>
                    </div>

                    <p
                        class="mt-8 text-base text-gray-500 font-inter dark:text-white"
                    >
                        $0.99/c per day. Hassle free and free lifetime access.
                    </p>
                </div>
            </div>

            <div class="pb-12 rounded-2xl">
                <div class="relative">
                    <div class="absolute inset-0 h-2/3"></div>
                    <div class="relative mx-auto">
                        <div
                            class="lg:max-w-6xl lg:mx-auto relative group cursor-pointer"
                            (click)="openVideoModal()"
                        >
                            <img
                                class="transform scale-110"
                                src="images/landing/illustration.png"
                                alt=""
                            />
                            <!-- Play button overlay -->
                            <div
                                class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                                <div
                                    class="bg-black bg-opacity-50 rounded-full p-4"
                                >
                                    <svg
                                        class="w-12 h-12 text-white"
                                        fill="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path d="M8 5v14l11-7z" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section>
            <div class="max-w-[85rem] mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Grid -->
                <div
                    class="grid md:grid-cols-2 gap-4 md:gap-8 xl:gap-20 md:items-center"
                >
                    <div>
                        <h1
                            class="block text-3xl font-bold text-gray-800 sm:text-4xl lg:text-6xl lg:leading-tight dark:text-white"
                        >
                            Start your journey with
                            <span class="text-blue-600">Streamliner</span>
                        </h1>
                        <p
                            class="mt-3 text-lg text-gray-800 dark:text-neutral-400"
                        >
                            Optimise job management and track your team's live
                            locations effortlessly with our all-in-one platform.
                        </p>

                        <!-- Buttons -->
                        <div class="mt-7 grid gap-3 w-full sm:inline-flex">
                            <a
                                class="py-3 px-4 inline-flex justify-center items-center gap-x-2 text-sm font-semibold rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
                                routerLink="/sign-up"
                            >
                                Get started
                                <svg
                                    class="flex-shrink-0 size-4"
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                >
                                    <path d="m9 18 6-6-6-6" />
                                </svg>
                            </a>
                            <a
                                class="py-3 px-4 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-800"
                                routerLink="/home/<USER>"
                            >
                                Contact sales team
                            </a>
                        </div>
                        <!-- End Buttons -->

                        <!-- Review -->
                        <div class="mt-6 lg:mt-10 grid grid-cols-2 gap-x-5">
                            <!-- Review -->
                            <div class="py-5">
                                <div class="flex space-x-1">
                                    <svg
                                        class="size-4 text-gray-800 dark:text-neutral-200"
                                        width="51"
                                        height="51"
                                        viewBox="0 0 51 51"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M27.0352 1.6307L33.9181 16.3633C34.2173 16.6768 34.5166 16.9903 34.8158 16.9903L50.0779 19.1845C50.9757 19.1845 51.275 20.4383 50.6764 21.0652L39.604 32.3498C39.3047 32.6632 39.3047 32.9767 39.3047 33.2901L41.998 49.2766C42.2973 50.217 41.1002 50.8439 40.5017 50.5304L26.4367 43.3208C26.1375 43.3208 25.8382 43.3208 25.539 43.3208L11.7732 50.8439C10.8754 51.1573 9.97763 50.5304 10.2769 49.59L12.9702 33.6036C12.9702 33.2901 12.9702 32.9767 12.671 32.6632L1.29923 21.0652C0.700724 20.4383 0.999979 19.4979 1.89775 19.4979L17.1598 17.3037C17.459 17.3037 17.7583 16.9903 18.0575 16.6768L24.9404 1.6307C25.539 0.69032 26.736 0.69032 27.0352 1.6307Z"
                                            fill="currentColor"
                                        />
                                    </svg>
                                    <svg
                                        class="size-4 text-gray-800 dark:text-neutral-200"
                                        width="51"
                                        height="51"
                                        viewBox="0 0 51 51"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M27.0352 1.6307L33.9181 16.3633C34.2173 16.6768 34.5166 16.9903 34.8158 16.9903L50.0779 19.1845C50.9757 19.1845 51.275 20.4383 50.6764 21.0652L39.604 32.3498C39.3047 32.6632 39.3047 32.9767 39.3047 33.2901L41.998 49.2766C42.2973 50.217 41.1002 50.8439 40.5017 50.5304L26.4367 43.3208C26.1375 43.3208 25.8382 43.3208 25.539 43.3208L11.7732 50.8439C10.8754 51.1573 9.97763 50.5304 10.2769 49.59L12.9702 33.6036C12.9702 33.2901 12.9702 32.9767 12.671 32.6632L1.29923 21.0652C0.700724 20.4383 0.999979 19.4979 1.89775 19.4979L17.1598 17.3037C17.459 17.3037 17.7583 16.9903 18.0575 16.6768L24.9404 1.6307C25.539 0.69032 26.736 0.69032 27.0352 1.6307Z"
                                            fill="currentColor"
                                        />
                                    </svg>
                                    <svg
                                        class="size-4 text-gray-800 dark:text-neutral-200"
                                        width="51"
                                        height="51"
                                        viewBox="0 0 51 51"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M27.0352 1.6307L33.9181 16.3633C34.2173 16.6768 34.5166 16.9903 34.8158 16.9903L50.0779 19.1845C50.9757 19.1845 51.275 20.4383 50.6764 21.0652L39.604 32.3498C39.3047 32.6632 39.3047 32.9767 39.3047 33.2901L41.998 49.2766C42.2973 50.217 41.1002 50.8439 40.5017 50.5304L26.4367 43.3208C26.1375 43.3208 25.8382 43.3208 25.539 43.3208L11.7732 50.8439C10.8754 51.1573 9.97763 50.5304 10.2769 49.59L12.9702 33.6036C12.9702 33.2901 12.9702 32.9767 12.671 32.6632L1.29923 21.0652C0.700724 20.4383 0.999979 19.4979 1.89775 19.4979L17.1598 17.3037C17.459 17.3037 17.7583 16.9903 18.0575 16.6768L24.9404 1.6307C25.539 0.69032 26.736 0.69032 27.0352 1.6307Z"
                                            fill="currentColor"
                                        />
                                    </svg>
                                    <svg
                                        class="size-4 text-gray-800 dark:text-neutral-200"
                                        width="51"
                                        height="51"
                                        viewBox="0 0 51 51"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M27.0352 1.6307L33.9181 16.3633C34.2173 16.6768 34.5166 16.9903 34.8158 16.9903L50.0779 19.1845C50.9757 19.1845 51.275 20.4383 50.6764 21.0652L39.604 32.3498C39.3047 32.6632 39.3047 32.9767 39.3047 33.2901L41.998 49.2766C42.2973 50.217 41.1002 50.8439 40.5017 50.5304L26.4367 43.3208C26.1375 43.3208 25.8382 43.3208 25.539 43.3208L11.7732 50.8439C10.8754 51.1573 9.97763 50.5304 10.2769 49.59L12.9702 33.6036C12.9702 33.2901 12.9702 32.9767 12.671 32.6632L1.29923 21.0652C0.700724 20.4383 0.999979 19.4979 1.89775 19.4979L17.1598 17.3037C17.459 17.3037 17.7583 16.9903 18.0575 16.6768L24.9404 1.6307C25.539 0.69032 26.736 0.69032 27.0352 1.6307Z"
                                            fill="currentColor"
                                        />
                                    </svg>
                                    <svg
                                        class="size-4 text-gray-800 dark:text-neutral-200"
                                        width="51"
                                        height="51"
                                        viewBox="0 0 51 51"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M27.0352 1.6307L33.9181 16.3633C34.2173 16.6768 34.5166 16.9903 34.8158 16.9903L50.0779 19.1845C50.9757 19.1845 51.275 20.4383 50.6764 21.0652L39.604 32.3498C39.3047 32.6632 39.3047 32.9767 39.3047 33.2901L41.998 49.2766C42.2973 50.217 41.1002 50.8439 40.5017 50.5304L26.4367 43.3208C26.1375 43.3208 25.8382 43.3208 25.539 43.3208L11.7732 50.8439C10.8754 51.1573 9.97763 50.5304 10.2769 49.59L12.9702 33.6036C12.9702 33.2901 12.9702 32.9767 12.671 32.6632L1.29923 21.0652C0.700724 20.4383 0.999979 19.4979 1.89775 19.4979L17.1598 17.3037C17.459 17.3037 17.7583 16.9903 18.0575 16.6768L24.9404 1.6307C25.539 0.69032 26.736 0.69032 27.0352 1.6307Z"
                                            fill="currentColor"
                                        />
                                    </svg>
                                </div>

                                <p
                                    class="mt-3 text-sm text-gray-800 dark:text-neutral-200"
                                >
                                    <span class="font-bold">4.9</span> /5 - from
                                    reviews
                                </p>

                                <div class="mt-5">
                                    <!-- Star -->
                                    <svg
                                        class="h-auto w-16 text-gray-800 dark:text-white"
                                        width="80"
                                        height="27"
                                        viewBox="0 0 80 27"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M20.558 9.74046H11.576V12.3752H17.9632C17.6438 16.0878 14.5301 17.7245 11.6159 17.7245C7.86341 17.7245 4.58995 14.7704 4.58995 10.6586C4.58995 6.62669 7.70373 3.51291 11.6159 3.51291C14.6498 3.51291 16.4063 5.42908 16.4063 5.42908L18.2426 3.51291C18.2426 3.51291 15.8474 0.878184 11.4961 0.878184C5.94724 0.838264 1.67578 5.50892 1.67578 10.5788C1.67578 15.5289 5.70772 20.3592 11.6558 20.3592C16.8854 20.3592 20.7177 16.8063 20.7177 11.4969C20.7177 10.3792 20.558 9.74046 20.558 9.74046Z"
                                            fill="currentColor"
                                        />
                                        <path
                                            d="M27.8621 7.78442C24.1894 7.78442 21.5547 10.6587 21.5547 14.012C21.5547 17.4451 24.1096 20.3593 27.9419 20.3593C31.415 20.3593 34.2094 17.7645 34.2094 14.0918C34.1695 9.94011 30.896 7.78442 27.8621 7.78442ZM27.902 10.2994C29.6984 10.2994 31.415 11.7764 31.415 14.0918C31.415 16.4072 29.7383 17.8842 27.902 17.8842C25.906 17.8842 24.3491 16.2874 24.3491 14.0519C24.3092 11.8962 25.8661 10.2994 27.902 10.2994Z"
                                            fill="currentColor"
                                        />
                                        <path
                                            d="M41.5964 7.78442C37.9238 7.78442 35.2891 10.6587 35.2891 14.012C35.2891 17.4451 37.844 20.3593 41.6763 20.3593C45.1493 20.3593 47.9438 17.7645 47.9438 14.0918C47.9038 9.94011 44.6304 7.78442 41.5964 7.78442ZM41.6364 10.2994C43.4328 10.2994 45.1493 11.7764 45.1493 14.0918C45.1493 16.4072 43.4727 17.8842 41.6364 17.8842C39.6404 17.8842 38.0835 16.2874 38.0835 14.0519C38.0436 11.8962 39.6004 10.2994 41.6364 10.2994Z"
                                            fill="currentColor"
                                        />
                                        <path
                                            d="M55.0475 7.82434C51.6543 7.82434 49.0195 10.7784 49.0195 14.0918C49.0195 17.8443 52.0934 20.3992 54.9676 20.3992C56.764 20.3992 57.6822 19.7205 58.4407 18.8822V20.1198C58.4407 22.2754 57.1233 23.5928 55.1273 23.5928C53.2111 23.5928 52.2531 22.1557 51.8938 21.3573L49.4587 22.3553C50.297 24.1517 52.0135 26.0279 55.0874 26.0279C58.4407 26.0279 60.9956 23.9122 60.9956 19.481V8.18362H58.3608V9.26147C57.6423 8.38322 56.5245 7.82434 55.0475 7.82434ZM55.287 10.2994C56.9237 10.2994 58.6403 11.7365 58.6403 14.1317C58.6403 16.6068 56.9636 17.9241 55.2471 17.9241C53.4507 17.9241 51.774 16.4471 51.774 14.1716C51.8139 11.6966 53.5305 10.2994 55.287 10.2994Z"
                                            fill="currentColor"
                                        />
                                        <path
                                            d="M72.8136 7.78442C69.62 7.78442 66.9453 10.2994 66.9453 14.0519C66.9453 18.004 69.9393 20.3593 73.093 20.3593C75.7278 20.3593 77.4044 18.8822 78.3625 17.6048L76.1669 16.1277C75.608 17.006 74.6499 17.8443 73.093 17.8443C71.3365 17.8443 70.5381 16.8862 70.0192 15.9281L78.4423 12.4152L78.0032 11.3772C77.1649 9.46107 75.2886 7.78442 72.8136 7.78442ZM72.8934 10.2196C74.0511 10.2196 74.8495 10.8184 75.2487 11.5768L69.6599 13.9321C69.3405 12.0958 71.097 10.2196 72.8934 10.2196Z"
                                            fill="currentColor"
                                        />
                                        <path
                                            d="M62.9531 19.9999H65.7076V1.47693H62.9531V19.9999Z"
                                            fill="currentColor"
                                        />
                                    </svg>
                                    <!-- End Star -->
                                </div>
                            </div>
                            <!-- End Review -->
                        </div>
                        <!-- End Review -->
                    </div>
                    <!-- End Col -->

                    <div class="relative ms-4">
                        <img
                            class="w-full rounded-md"
                            src="images/landing/bg-01.jpg"
                            alt="Image Description"
                        />
                        <div
                            class="absolute inset-0 -z-[1] bg-gradient-to-tr from-gray-200 via-white/0 to-white/0 size-full rounded-md mt-4 -mb-4 me-4 -ms-4 lg:mt-6 lg:-mb-6 lg:me-6 lg:-ms-6 dark:from-neutral-800 dark:via-neutral-900/0 dark:to-neutral-900/0"
                        ></div>

                        <!-- SVG-->
                        <div class="absolute bottom-0 start-0">
                            <svg
                                class="w-2/3 ms-auto h-auto text-white dark:text-neutral-900"
                                width="630"
                                height="451"
                                viewBox="0 0 630 451"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <rect
                                    x="531"
                                    y="352"
                                    width="99"
                                    height="99"
                                    fill="currentColor"
                                />
                                <rect
                                    x="140"
                                    y="352"
                                    width="106"
                                    height="99"
                                    fill="currentColor"
                                />
                                <rect
                                    x="482"
                                    y="402"
                                    width="64"
                                    height="49"
                                    fill="currentColor"
                                />
                                <rect
                                    x="433"
                                    y="402"
                                    width="63"
                                    height="49"
                                    fill="currentColor"
                                />
                                <rect
                                    x="384"
                                    y="352"
                                    width="49"
                                    height="50"
                                    fill="currentColor"
                                />
                                <rect
                                    x="531"
                                    y="328"
                                    width="50"
                                    height="50"
                                    fill="currentColor"
                                />
                                <rect
                                    x="99"
                                    y="303"
                                    width="49"
                                    height="58"
                                    fill="currentColor"
                                />
                                <rect
                                    x="99"
                                    y="352"
                                    width="49"
                                    height="50"
                                    fill="currentColor"
                                />
                                <rect
                                    x="99"
                                    y="392"
                                    width="49"
                                    height="59"
                                    fill="currentColor"
                                />
                                <rect
                                    x="44"
                                    y="402"
                                    width="66"
                                    height="49"
                                    fill="currentColor"
                                />
                                <rect
                                    x="234"
                                    y="402"
                                    width="62"
                                    height="49"
                                    fill="currentColor"
                                />
                                <rect
                                    x="334"
                                    y="303"
                                    width="50"
                                    height="49"
                                    fill="currentColor"
                                />
                                <rect
                                    x="581"
                                    width="49"
                                    height="49"
                                    fill="currentColor"
                                />
                                <rect
                                    x="581"
                                    width="49"
                                    height="64"
                                    fill="currentColor"
                                />
                                <rect
                                    x="482"
                                    y="123"
                                    width="49"
                                    height="49"
                                    fill="currentColor"
                                />
                                <rect
                                    x="507"
                                    y="124"
                                    width="49"
                                    height="24"
                                    fill="currentColor"
                                />
                                <rect
                                    x="531"
                                    y="49"
                                    width="99"
                                    height="99"
                                    fill="currentColor"
                                />
                            </svg>
                        </div>
                        <!-- End SVG-->
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->
            </div>
        </section>
        <!-- spacer div -->
        <div class="h-[5rem]"></div>
        <section>
            <div class="flex flex-col items-center justify-center">
                <div
                    class="relative items-center w-full px-5 pt-12 mx-auto max-w-7xl lg:pt-36 lg:px-16 md:px-12"
                >
                    <div class="max-w-6xl mx-auto text-center">
                        <p
                            class="text-4xl font-extrabold tracking-tight bg-gradient-to-r from-green-300 via-blue-500 to-purple-600 bg-clip-text text-transparent md:text-8xl"
                        >
                            Manage your Job schedule
                            <span class="md:block">has never been easier</span>
                        </p>
                        <p
                            class="max-w-xl mx-auto mt-8 text-base lg:text-xl text-slate-600"
                        >
                            Streamliner is a platform that allows you to
                            schedule and deploy jobs with a single click, from
                            anywhere in the world.
                        </p>
                    </div>
                    <div
                        class="flex flex-col justify-center gap-3 mt-10 sm:flex-row"
                    >
                        <a
                            routerLink="/sign-up"
                            class="items-center inline-flex focus:outline-none justify-center text-white bg-[#5b03e3] duration-200 focus-visible:outline-black focus-visible:ring-black font-medium hover:bg-transparent hover:border-white hover:text-white lg:w-auto px-6 py-3 rounded-full text-center w-full"
                            >Get started</a
                        >
                    </div>
                </div>
                <div
                    class="relative items-center w-full py-12 pb-12 mx-auto mt-12 max-w-7xl"
                >
                    <img
                        alt=""
                        class="relative object-cover w-full rounded lg:rounded-2xl"
                        src="images/landing/home_appwindow_02.png"
                    />
                </div>
            </div>
        </section>

        <section class="relative py-12 sm:py-16 lg:pt-20 xl:pb-0">
            <div class="mt-16 md:mt-20">
                <img
                    class="object-cover object-top w-full h-auto mx-auto scale-150 2xl:max-w-screen-2xl xl:scale-100"
                    src="images/landing/banner.png"
                    alt=""
                />
            </div>
        </section>
        <!-- spacer div -->
        <div class="h-[5rem]"></div>

        <div class="flex flex-col items-center">
            <h2 class="text-xl font-semibold">PRICING</h2>
            <div
                class="mt-1 text-4xl sm:text-7xl font-extrabold tracking-tight leading-tight text-center"
            >
                Take control of your business.
            </div>
            <div
                class="mt-3 sm:text-2xl text-center tracking-tight text-secondary"
            >
                <div class="">
                    Start with a small team and upgrade as you grow.
                </div>
                <div class="">
                    Take control of every aspect of your operations.
                </div>
            </div>
        </div>
        <!-- Card -->
        <div class="flex justify-center mt-10 sm:mt-20">
            <fuse-card class="flex flex-col lg:flex-row max-w-sm lg:max-w-240">
                <div class="p-6 sm:p-8 lg:p-10">
                    <div class="text-3xl font-bold">Free Lifetime Access</div>
                    <div class="mt-2 leading-relaxed text-secondary">
                        Join a community of like-minded individuals dedicated to
                        building successful web-based businesses.free lifetime
                        access to this supportive network.
                    </div>
                    <div class="flex items-center mt-10">
                        <div class="font-medium text-secondary">
                            INCLUDED FEATURES
                        </div>
                        <div class="flex-auto ml-2 border-b-2"></div>
                    </div>
                    <!-- Features -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-y-4 mt-6">
                        <div class="flex items-center">
                            <mat-icon
                                class="icon-size-5 text-primary"
                                [svgIcon]="'heroicons_solid:check-circle'"
                            ></mat-icon>
                            <div class="ml-2">Powerful scheduling calendar</div>
                        </div>
                        <div class="flex items-center">
                            <mat-icon
                                class="icon-size-5 text-primary"
                                [svgIcon]="'heroicons_solid:check-circle'"
                            ></mat-icon>
                            <div class="ml-2">Live map asset tracking</div>
                        </div>
                        <div class="flex items-center">
                            <mat-icon
                                class="icon-size-5 text-primary"
                                [svgIcon]="'heroicons_solid:check-circle'"
                            ></mat-icon>
                            <div class="ml-2">
                                Real-time team member tracking on the map
                            </div>
                        </div>
                        <div class="flex items-center">
                            <mat-icon
                                class="icon-size-5 text-primary"
                                [svgIcon]="'heroicons_solid:check-circle'"
                            ></mat-icon>
                            <div class="ml-2">
                                Fleet vehicle maintenance schedule tracking
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Price -->
                <div
                    class="flex flex-col items-center p-8 lg:px-10 lg:py-12 lg:min-w-80 bg-gray-100 dark:bg-black dark:bg-opacity-10"
                >
                    <div class="flex items-center whitespace-nowrap">
                        <div class="text-8xl font-extrabold tracking-tight">
                            $0.99
                        </div>
                        <div class="ml-2 text-2xl font-semibold text-secondary">
                            AUD
                        </div>
                    </div>
                    <div class="font-medium text-center text-secondary">
                        <div>monthly subscription for premium,</div>
                        <div>Only $0.99 AUD per team member per day.</div>
                    </div>
                    <a
                        routerLink="/sign-up"
                        class="w-full mt-8 lg:mt-auto bg-primary dark:bg-primaryDark text-white font-semibold text-center py-4 px-6 rounded-lg"
                    >
                        Get Started
                    </a>
                </div>
            </fuse-card>
        </div>
    </div>

    <!-- Features -->
    <div
        class="flex flex-col items-center px-6 py-10 sm:px-16 sm:pt-18 sm:pb-20 bg-white dark:bg-gray-800"
    >
        <div class="w-full max-w-7xl">
            <div>
                <div
                    class="text-4xl font-extrabold tracking-tight leading-tight"
                >
                    Everything you need to build efficiently
                </div>
                <div class="max-w-xl mt-2 text-xl text-secondary">
                    Start growing your business with our tools. Be efficient,
                    spend less time on details, and focus more on your core
                    operations.
                </div>
            </div>
            <!-- Features grid -->
            <div
                class="grid grid-cols-1 gap-x-6 gap-y-12 sm:grid-cols-2 lg:grid-cols-3 lg:gap-16 w-full mt-12 sm:mt-16"
            >
                <div>
                    <span
                        class="flex items-center justify-center w-12 h-12 rounded bg-primary"
                    >
                        <mat-icon
                            class="text-white"
                            [svgIcon]="'heroicons_outline:pencil-square'"
                        ></mat-icon>
                    </span>
                    <div class="mt-4 text-xl font-medium">
                        Create and Edit Jobs
                    </div>
                    <div class="mt-2 leading-6 text-secondary">
                        Easily create and edit jobs on the schedule calendar.
                        Upload images via drag-and-drop, add categories, custom
                        fields, and create recursive job templates, and much
                        more.
                    </div>
                </div>
                <div>
                    <span
                        class="flex items-center justify-center w-12 h-12 rounded bg-primary"
                    >
                        <mat-icon
                            class="text-white"
                            [svgIcon]="'heroicons_outline:funnel'"
                        ></mat-icon>
                    </span>
                    <div class="mt-4 text-xl font-medium">
                        Search and Filter
                    </div>
                    <div class="mt-2 leading-6 text-secondary">
                        Quickly search, filter, and track scheduled jobs,
                        categories, and custom tags. Access real-time job
                        details and status effortlessly.
                    </div>
                </div>
                <div>
                    <span
                        class="flex items-center justify-center w-12 h-12 rounded bg-primary"
                    >
                        <mat-icon
                            class="text-white"
                            [svgIcon]="'heroicons_outline:arrow-path'"
                        ></mat-icon>
                    </span>
                    <div class="mt-4 text-xl font-medium">
                        Real-Time Updates
                    </div>
                    <div class="mt-2 leading-6 text-secondary">
                        Experience real-time updates without page reloads. See
                        job statuses and team member locations on the map as
                        they change in real time.
                    </div>
                </div>
                <div>
                    <span
                        class="flex items-center justify-center w-12 h-12 rounded bg-primary"
                    >
                        <mat-icon
                            class="text-white"
                            [svgIcon]="'heroicons_outline:clock'"
                        ></mat-icon>
                    </span>
                    <div class="mt-4 text-xl font-medium">
                        Clock-in clock-out
                    </div>
                    <div class="mt-2 leading-6 text-secondary">
                        Your employees can clock in and out from mobile device
                        and you can Track time for jobs, projects, clients,
                        equipment, mileage, and more Always know where your
                        employees are using GPS.
                    </div>
                </div>
                <div>
                    <span
                        class="flex items-center justify-center w-12 h-12 rounded bg-primary"
                    >
                        <mat-icon
                            class="text-white"
                            [svgIcon]="'heroicons_outline:document-text'"
                        ></mat-icon>
                    </span>
                    <div class="mt-4 text-xl font-medium">
                        Powerful Invoicing Dashboard
                    </div>
                    <div class="mt-2 leading-6 text-secondary">
                        Enjoy invoicing features that allow you to create
                        invoices, manage payment, and track invoice status.
                    </div>
                </div>
                <div>
                    <span
                        class="flex items-center justify-center w-12 h-12 rounded bg-primary"
                    >
                        <mat-icon
                            class="text-white"
                            [svgIcon]="'heroicons_outline:chart-bar-square'"
                        ></mat-icon>
                    </span>
                    <div class="mt-4 text-xl font-medium">Simple Reports</div>
                    <div class="mt-2 leading-6 text-secondary">
                        Generate simple, readable reports with no unnecessary
                        data flow, ensuring efficient data consumption.
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Mobile App Hero -->
    <section class="flex items-center justify-center">
        <div
            class="flex-row px-4 py-12 mx-auto max-w-7xl sm:px-6 md:px-12 lg:px-24 lg:py-24 items-center"
        >
            <div class="flex flex-wrap mx-auto max-w-7xl justify-center">
                <div class="basis-1/2 w-full lg:max-w-lg lg:w-1/2 rounded-xl">
                    <div>
                        <div class="relative w-full max-w-lg">
                            <div
                                class="absolute top-0 rounded-full bg-violet-300 -left-4 w-72 h-72 mix-blend-multiply filter blur-xl opacity-70 animate-blob"
                            ></div>

                            <div
                                class="absolute rounded-full bg-fuchsia-300 -bottom-24 right-20 w-72 h-72 mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"
                            ></div>
                            <div class="relative">
                                <img
                                    class="object-cover object-center mx-auto"
                                    alt="hero"
                                    src="images/landing/app_right_hand.png"
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="basis-1/2 w-full lg:max-w-lg lg:w-1/2 rounded-xl">
                    <span
                        class="mb-8 text-xs font-bold tracking-widest text-blue-600 uppercase"
                    >
                        Streamliner Mobile App
                    </span>
                    <h1
                        class="mb-8 text-4xl font-bold leading-none tracking-tighter text-neutral-600 md:text-7xl lg:text-5xl dark:text-white"
                    >
                        Download our mobile app.
                    </h1>
                    <div class="flex items-center w-full m-5">
                        <div
                            class="flex-col items-center justify-center w-full"
                        >
                            <div
                                class="flex items-center-center w-15 h-15 rounded-xl bg-black shadow-2xl"
                            >
                                <img
                                    class="h-15"
                                    src="images/logo/logo.svg"
                                    alt="Logo image"
                                />
                            </div>
                            <div class="flex text-md font-bold mt-2">
                                Streamliner Admin
                            </div>
                        </div>
                        <div
                            class="flex-col items-center justify-center w-full"
                        >
                            <div
                                class="flex items-center-center w-15 h-15 rounded-xl bg-white shadow-2xl"
                            >
                                <img
                                    class="h-15"
                                    src="images/logo/logo.svg"
                                    alt="Logo image"
                                />
                            </div>
                            <div class="flex text-md font-bold mt-2">
                                Streamliner Team
                            </div>
                        </div>
                        <div
                            class="flex-col items-center justify-center w-full"
                        >
                            <div
                                class="flex items-center-center w-15 h-15 rounded-xl bg-violet-400 shadow-2xl"
                            >
                                <img
                                    class="h-15"
                                    src="images/logo/logo.svg"
                                    alt="Logo image"
                                />
                            </div>
                            <div class="flex text-md font-bold mt-2">
                                Streamliner Client
                            </div>
                        </div>
                    </div>

                    <p
                        class="mb-8 text-base leading-relaxed text-left text-gray-500"
                    >
                        A free app to help your team members manage scheduled
                        jobs effortlessly.
                    </p>
                    <div class="mt-0 lg:mt-6 max-w-7xl sm:flex">
                        <div class="flex flex-row justify-center">
                            <div class="flex gap-3">
                                <!-- https://developer.apple.com/app-store/marketing/guidelines/#section-badges -->
                                <a
                                    href="https://apps.apple.com/us/developer/streamliner-apps-pty-ltd/id1721271667"
                                    target="_blank"
                                >
                                    <div
                                        class="mt-3 flex h-14 w-48 items-center justify-center rounded-xl bg-black text-white"
                                    >
                                        <div class="mr-3">
                                            <svg
                                                viewBox="0 0 384 512"
                                                width="30"
                                            >
                                                <path
                                                    fill="currentColor"
                                                    d="M318.7 268.7c-.2-36.7 16.4-64.4 50-84.8-18.8-26.9-47.2-41.7-84.7-44.6-35.5-2.8-74.3 20.7-88.5 20.7-15 0-49.4-19.7-76.4-19.7C63.3 141.2 4 184.8 4 273.5q0 39.3 14.4 81.2c12.8 36.7 59 126.7 107.2 125.2 25.2-.6 43-17.9 75.8-17.9 31.8 0 48.3 17.9 76.4 17.9 48.6-.7 90.4-82.5 102.6-119.3-65.2-30.7-61.7-90-61.7-91.9zm-56.6-164.2c27.3-32.4 24.8-61.9 24-72.5-24.1 1.4-52 16.4-67.9 34.9-17.5 19.8-27.8 44.3-25.6 71.9 26.1 2 49.9-11.4 69.5-34.3z"
                                                />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="text-xs">
                                                Download on the
                                            </div>
                                            <div
                                                class="-mt-1 font-sans text-2xl font-semibold"
                                            >
                                                App Store
                                            </div>
                                        </div>
                                    </div>
                                </a>

                                <a
                                    href="https://play.google.com/store/apps/developer?id=Streamliner+Apps+Pty+Ltd"
                                    target="_blank"
                                >
                                    <!-- https://play.google.com/intl/en_us/badges/ -->
                                    <div
                                        class="mt-3 flex h-14 w-48 items-center justify-center rounded-lg bg-black text-white"
                                    >
                                        <div class="mr-3">
                                            <svg
                                                viewBox="30 336.7 120.9 129.2"
                                                width="30"
                                            >
                                                <path
                                                    fill="#FFD400"
                                                    d="M119.2,421.2c15.3-8.4,27-14.8,28-15.3c3.2-1.7,6.5-6.2,0-9.7  c-2.1-1.1-13.4-7.3-28-15.3l-20.1,20.2L119.2,421.2z"
                                                />
                                                <path
                                                    fill="#FF3333"
                                                    d="M99.1,401.1l-64.2,64.7c1.5,0.2,3.2-0.2,5.2-1.3  c4.2-2.3,48.8-26.7,79.1-43.3L99.1,401.1L99.1,401.1z"
                                                />
                                                <path
                                                    fill="#48FF48"
                                                    d="M99.1,401.1l20.1-20.2c0,0-74.6-40.7-79.1-43.1  c-1.7-1-3.6-1.3-5.3-1L99.1,401.1z"
                                                />
                                                <path
                                                    fill="#3BCCFF"
                                                    d="M99.1,401.1l-64.3-64.3c-2.6,0.6-4.8,2.9-4.8,7.6  c0,7.5,0,107.5,0,113.8c0,4.3,1.7,7.4,4.9,7.7L99.1,401.1z"
                                                />
                                            </svg>
                                        </div>

                                        <div>
                                            <div class="text-xs">GET IT ON</div>
                                            <div
                                                class="-mt-1 font-sans text-xl font-semibold"
                                            >
                                                Google Play
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA -->
    <div
        class="px-6 py-10 sm:py-12 sm:px-16 bg-primary-600 text-on-primary-600"
    >
        <div
            class="flex flex-col items-center w-full max-w-7xl mx-auto text-center"
        >
            <div
                class="text-3xl sm:text-4xl sm:text-5xl font-extrabold leading-6 sm:leading-10"
            >
                Boost your productivity.
            </div>
            <div
                class="mt-2 text-3xl sm:text-4xl sm:text-5xl font-extrabold leading-6 sm:leading-10 text-black text-opacity-70"
            >
                Start using Streamliner today.
            </div>
        </div>
    </div>
    <section>
        <div class="px-8 py-24 mx-auto md:px-12 lg:px-32 max-w-7xl">
            <div class="text-center">
                <h1
                    class="text-4xl font-semibold tracking-tighter text-gray-900 lg:text-5xl text-balance"
                >
                    You'r in good company
                    <span class="text-gray-600">wherever and anywhere</span>
                </h1>
                <p
                    class="w-1/2 mx-auto mt-4 text-base font-medium text-gray-500 text-balance"
                >
                    Collaborate Efficiently on Job Management and Ongoing
                    Platform Environments Trusted by Companies of All Sizes
                </p>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-5 mt-24 gap-4">
                <div
                    class="flex justify-center col-span-1 px-8 bg-card rounded-lg shadow-xl overflow-hidden"
                >
                    <a href="https://envirobins.com.au/" target="_blank">
                        <img
                            class="object-cover h-full p-2"
                            src="images/landing/companies_logos/logo.png"
                            alt="logo"
                    /></a>
                </div>
                <div
                    class="flex justify-center col-span-1 px-8 bg-card rounded-lg shadow-xl overflow-hidden"
                >
                    <a href="https://www.chilcorp.com/" target="_blank">
                        <img
                            class="object-cover h-full p-2 max-h-14"
                            src="images/landing/companies_logos/banner_logo_chilcorp.png"
                            alt="logo"
                        />
                    </a>
                </div>
                <div
                    class="flex justify-center col-span-1 px-8 bg-card rounded-lg shadow-xl overflow-hidden"
                >
                    <a href="https://eco1wrc.com.au/" target="_blank">
                        <img
                            class="object-cover h-full p-2"
                            src="images/landing/companies_logos/logo-3.jpg"
                            alt="logo"
                    /></a>
                </div>
                <div
                    class="flex justify-center col-span-1 px-8 bg-card rounded-lg shadow-xl overflow-hidden"
                >
                    <a href="#" target="_blank">
                        <img
                            class="object-cover h-full p-2"
                            src="images/landing/companies_logos/logo.png"
                            alt="logo"
                    /></a>
                </div>
                <div
                    class="flex justify-center col-span-1 px-8 bg-card rounded-lg shadow-xl overflow-hidden"
                >
                    <a
                        href="https://broadwaytransportco.com.au/"
                        target="_blank"
                    >
                        <img
                            class="object-cover h-full p-2"
                            src="images/landing/companies_logos/logo-2.jpg"
                            alt="logo"
                    /></a>
                </div>
            </div>
        </div>
    </section>
    <section>
        <div
            class="flex flex-col flex-auto items-center sm:justify-center min-w-0 md:p-8"
        >
            <div
                class="flex md:w-full md:max-w-6xl sm:rounded-2xl sm:shadow overflow-hidden sm:bg-card"
            >
                <div
                    class="relative hidden md:flex flex-auto items-center justify-center h-full p-16 lg:px-28 overflow-hidden bg-gray-800 dark:border-r"
                >
                    <!-- Background - @formatter:off -->
                    <!-- Rings -->
                    <svg
                        class="absolute inset-0 pointer-events-none"
                        viewBox="0 0 960 540"
                        width="100%"
                        height="100%"
                        preserveAspectRatio="xMidYMax slice"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <g
                            class="text-gray-700 opacity-25"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="100"
                        >
                            <circle r="234" cx="196" cy="23"></circle>
                            <circle r="234" cx="790" cy="491"></circle>
                        </g>
                    </svg>
                    <!-- Dots -->
                    <svg
                        class="absolute -top-16 -right-16 text-gray-700"
                        viewBox="0 0 220 192"
                        width="220"
                        height="192"
                        fill="none"
                    >
                        <defs>
                            <pattern
                                id="837c3e70-6c3a-44e6-8854-cc48c737b659"
                                x="0"
                                y="0"
                                width="20"
                                height="20"
                                patternUnits="userSpaceOnUse"
                            >
                                <rect
                                    x="0"
                                    y="0"
                                    width="4"
                                    height="4"
                                    fill="currentColor"
                                ></rect>
                            </pattern>
                        </defs>
                        <rect
                            width="220"
                            height="192"
                            fill="url(#837c3e70-6c3a-44e6-8854-cc48c737b659)"
                        ></rect>
                    </svg>
                    <!-- @formatter:on -->
                    <!-- Content -->
                    <div class="z-10 relative w-full max-w-2xl">
                        <div
                            class="text-7xl font-bold leading-none text-gray-100"
                        >
                            <div>Welcome to</div>
                            <div>our community</div>
                        </div>
                        <div
                            class="mt-6 text-lg tracking-tight leading-6 text-gray-400"
                        >
                            Streamliner helps you schedule and build jobs with
                            well-coded dashboards full of beautiful and rich
                            modules. Join us and make starting your business
                            much easier today.
                        </div>
                        <div class="flex items-center mt-8">
                            <div class="flex flex-0 items-center -space-x-1.5">
                                <img
                                    class="flex-0 w-10 h-10 rounded-full ring-4 ring-offset-1 ring-gray-800 ring-offset-gray-800 object-cover"
                                    src="images/avatars/female-18.jpg"
                                />
                                <img
                                    class="flex-0 w-10 h-10 rounded-full ring-4 ring-offset-1 ring-gray-800 ring-offset-gray-800 object-cover"
                                    src="images/avatars/female-11.jpg"
                                />
                                <img
                                    class="flex-0 w-10 h-10 rounded-full ring-4 ring-offset-1 ring-gray-800 ring-offset-gray-800 object-cover"
                                    src="images/avatars/male-09.jpg"
                                />
                                <img
                                    class="flex-0 w-10 h-10 rounded-full ring-4 ring-offset-1 ring-gray-800 ring-offset-gray-800 object-cover"
                                    src="images/avatars/male-16.jpg"
                                />
                            </div>
                            <div
                                class="ml-4 font-medium tracking-tight text-gray-400"
                            >
                                More than 17k people joined us, it's your turn
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-full sm:w-auto py-8 px-4 sm:p-12 md:p-16">
                    <div class="w-full max-w-80 sm:w-80 mx-auto sm:mx-0">
                        <!-- Logo -->
                        <div class="w-12">
                            <img src="images/logo/logo.svg" />
                        </div>

                        <!-- Title -->
                        <div
                            class="mt-8 text-4xl font-extrabold tracking-tight leading-tight"
                        >
                            Almost there!
                        </div>
                        <div class="mt-0.5">
                            Unlock the full potential of your team and
                            streamline your operations with Streamliner.
                        </div>
                        <div class="mt-8">
                            <a
                                routerLink="/sign-up"
                                class="w-full mt-8 bg-primary dark:bg-primaryDark text-white font-semibold text-center py-4 px-6 rounded-lg"
                            >
                                Get Started
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Frequently asked questions -->
    <div
        class="flex flex-col items-center pt-12 sm:pt-18 pb-8 sm:pb-20 px-6 sm:px-16 bg-gray-50 dark:bg-gray-800"
    >
        <div class="w-full max-w-7xl">
            <div>
                <div
                    class="text-4xl font-extrabold tracking-tight leading-tight"
                >
                    Frequently asked questions
                </div>
                <div class="max-w-xl mt-2 text-xl text-secondary">
                    Here are the most frequently asked questions you may check
                    before getting started
                </div>
            </div>
            <!-- FAQs grid -->
            <div
                class="grid grid-cols-1 gap-x-6 gap-y-12 sm:grid-cols-2 lg:gap-x-16 w-full mt-12 sm:mt-16"
            >
                <div>
                    <div class="text-xl font-semibold">
                        What is the duration of the access?
                    </div>
                    <div class="mt-2 leading-6 text-secondary">
                        <p>
                            As long as you maintain your subscription, your data
                            will be accessible for a lifetime as long as you are
                            with us. If you choose to cancel your subscription,
                            your account and data will remain accessible for
                            three months. After this period, your account will
                            be deleted, and your data will no longer be
                            accessible.
                        </p>
                    </div>
                </div>
                <div>
                    <div class="text-xl font-semibold">
                        What happens if I’m not satisfied?
                    </div>
                    <div class="mt-2 leading-6 text-secondary">
                        <p>
                            If you have already paid for your first month, we
                            offer a 30-day money-back guarantee with no
                            questions asked. After the first month, you can
                            still cancel your account at any time. We will
                            calculate the amount corresponding to the days you
                            have used our app for that month and refund only the
                            remaining amount.
                        </p>
                    </div>
                </div>
                <div>
                    <div class="text-xl font-semibold">
                        What features are included in the subscription?
                    </div>
                    <div class="mt-2 leading-6 text-secondary">
                        <p>
                            Your subscription includes a comprehensive set of
                            tools to streamline your business operations. This
                            includes access to our powerful scheduling calendar,
                            live map asset tracking, real-time team member
                            tracking, and fleet vehicle maintenance schedule
                            tracking.
                        </p>
                        <p class="mt-2">
                            Additionally, you will have access to our robust
                            reporting and analytics features, enabling you to
                            make data-driven decisions and optimize your
                            workflows.
                        </p>
                    </div>
                </div>
                <div>
                    <div class="text-xl font-semibold">
                        Can I upgrade or downgrade my plan?
                    </div>
                    <div class="mt-2 leading-6 text-secondary">
                        <p>
                            Yes, you can upgrade or downgrade your plan at any
                            time to suit your business needs. Whether you need
                            more features, additional users, or different
                            functionality, simply contact our support team, and
                            they will assist you with the process.
                        </p>
                        <p class="mt-2">
                            We offer flexible plans to ensure that your
                            subscription aligns with your evolving business
                            requirements.
                        </p>
                    </div>
                </div>
                <div>
                    <div class="text-xl font-semibold">
                        Is there a limit to the number of users?
                    </div>
                    <div class="mt-2 leading-6 text-secondary">
                        <p>
                            No, there is no limit to the number of users you can
                            add to your account. You can add as many team
                            members as you need and only pay for the ones
                            actively using the platform.
                        </p>
                        <p class="mt-2">
                            Our scalable solution grows with your business,
                            providing you with the flexibility to expand your
                            team without any restrictions.
                        </p>
                    </div>
                </div>
                <div>
                    <div class="text-xl font-semibold">
                        Can I access Streamliner from multiple devices?
                    </div>
                    <div class="mt-2 leading-6 text-secondary">
                        <p>
                            Yes, you can access Streamliner from any device with
                            an internet connection, making it easy to manage
                            your business on the go.
                        </p>
                        <p class="mt-2">
                            Whether you are using a desktop, laptop, tablet, or
                            smartphone, our platform is designed to be fully
                            responsive and accessible across various devices.
                            This ensures that you and your team can stay
                            connected and productive from anywhere.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Video Modal -->
@if (isVideoModalOpen) {
    <div
        class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 backdrop-blur-sm"
        (click)="closeVideoModal()"
    >
        <div
            class="relative w-full max-w-4xl mx-4 aspect-video"
            (click)="$event.stopPropagation()"
        >
            <button
                class="absolute -top-10 right-0 text-white hover:text-gray-300"
                (click)="closeVideoModal()"
            >
                <mat-icon>close</mat-icon>
            </button>
            <iframe
                [src]="safeVideoUrl"
                class="w-full h-full rounded-lg"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen
            >
            </iframe>
        </div>
    </div>
}

@keyframes moveWords {
    0% {
      transform: translateY(0%);
    }
    50% {
      transform: translateY(-100%);
    }
    100% {
      transform: translateY(0%);
    }
  }

  .animate-moveWords {
    animation: moveWords 6s infinite ease;
  }

  .line:nth-child(odd) {
    transform: skew(60deg, -30deg) scaleY(0.66667);
  }

  .line:nth-child(even) {
    transform: skew(0deg, -30deg) scaleY(1.33333);
  }

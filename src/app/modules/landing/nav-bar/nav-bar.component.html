<nav
    class="flex fixed top-0 left-0 z-20 drop-shadow-md w-full px-4 py-4 justify-between items-center backdrop-blur-md bg-white/95 dark:bg-gray-800"
>
    <!-- Logo Section -->
    <a routerLink="/home/" class="flex flex-shrink-0 items-center">
        <img class="h-10 w-auto" src="images/logo/logo.svg" alt="Logo image" />
        <span class="text-black-600 ml-2 text-3xl font-bold">Streamliner</span>
    </a>

    <!-- Mobile Menu Button -->
    <div class="lg:hidden">
        <button
            (click)="toggleMobileMenu()"
            class="navbar-burger flex items-center text-blue-600 p-3"
        >
            <mat-icon>{{ mobileMenuOpen ? "close" : "menu" }}</mat-icon>
        </button>
    </div>

    <!-- Desktop Navigation -->
    <ul
        class="hidden absolute top-1/2 left-1/2 transform -translate-y-1/2 -translate-x-1/2 lg:flex lg:mx-auto lg:items-center lg:w-auto lg:space-x-6"
    >
        <li>
            <a
                routerLink="/home/"
                routerLinkActive="active-link"
                class="text-sm font-bold hover:text-gray-500"
                >Home</a
            >
        </li>
        <li class="text-gray-300">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                stroke="currentColor"
                class="w-4 h-4 current-fill"
                viewBox="0 0 24 24"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 5v0m0 7v0m0 7v0m0-13a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                />
            </svg>
        </li>
        <li>
            <a
                routerLink="/home/<USER>"
                routerLinkActive="active-link"
                class="text-sm font-bold hover:text-gray-500"
                >About Us</a
            >
        </li>
        <li class="text-gray-300">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                stroke="currentColor"
                class="w-4 h-4 current-fill"
                viewBox="0 0 24 24"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 5v0m0 7v0m0 7v0m0-13a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                />
            </svg>
        </li>
        <li>
            <a
                routerLink="/home/<USER>"
                routerLinkActive="active-link"
                class="text-sm font-bold hover:text-gray-500"
                >Solutions</a
            >
        </li>
        <li class="text-gray-300">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                stroke="currentColor"
                class="w-4 h-4 current-fill"
                viewBox="0 0 24 24"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 5v0m0 7v0m0 7v0m0-13a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                />
            </svg>
        </li>
        <li>
            <a
                routerLink="/home/<USER>"
                routerLinkActive="active-link"
                class="text-sm font-bold hover:text-gray-500"
                >Pricing</a
            >
        </li>
        <li class="text-gray-300">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                stroke="currentColor"
                class="w-4 h-4 current-fill"
                viewBox="0 0 24 24"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 5v0m0 7v0m0 7v0m0-13a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                />
            </svg>
        </li>
        <li>
            <a
                routerLink="/home/<USER>"
                routerLinkActive="active-link"
                class="text-sm font-bold hover:text-gray-500"
                >Shop</a
            >
        </li>
        <li class="text-gray-300">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                stroke="currentColor"
                class="w-4 h-4 current-fill"
                viewBox="0 0 24 24"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 5v0m0 7v0m0 7v0m0-13a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                />
            </svg>
        </li>
        <li>
            <a
                routerLink="/home/<USER>"
                routerLinkActive="active-link"
                class="text-sm font-bold hover:text-gray-500"
                >Contact</a
            >
        </li>
    </ul>

    <!-- Desktop CTA Buttons -->
    <div class="hidden lg:flex items-center space-x-4">
        <a
            [routerLink]="'/dashboard'"
            (click)="loadingDashboard = true"
            class="hidden lg:inline-block lg:ml-auto lg:mr-3 py-2 px-6 bg-primary hover:bg-primary-700 text-sm text-white font-bold rounded-xl transition duration-200"
        >
            @if (loadingDashboard) {
                <mat-icon class="animate-spin mr-2">refresh</mat-icon>
            }
            Dashboard
        </a>
        <a
            routerLink="/sign-up"
            class="hidden lg:inline-block py-2 px-6 bg-primary-50 hover:bg-primary-100 text-sm text-gray-600 font-bold rounded-xl transition duration-200"
        >
            Sign up
        </a>
    </div>
</nav>

<!-- Mobile Menu Overlay -->
<div [class.hidden]="!mobileMenuOpen" class="navbar-menu relative z-50">
    <div class="navbar-backdrop fixed inset-0 bg-gray-800 opacity-25"></div>
    <nav
        class="fixed top-0 left-0 bottom-0 flex flex-col w-5/6 max-w-sm py-6 px-6 bg-white border-r overflow-y-auto"
    >
        <div class="flex items-center mb-8">
            <a
                routerLink="/home/"
                class="mr-auto text-3xl font-bold leading-none"
                (click)="toggleMobileMenu()"
            >
                <img class="h-10" src="images/logo/logo.svg" alt="Logo image" />
                <span class="text-black-600 mt-2">Streamliner</span>
            </a>
            <button class="navbar-close" (click)="toggleMobileMenu()">
                <mat-icon
                    class="h-6 w-6 text-gray-400 cursor-pointer hover:text-gray-500"
                    >close</mat-icon
                >
            </button>
        </div>
        <div>
            <ul>
                <li class="mb-1">
                    <a
                        routerLink="/home/"
                        routerLinkActive="active-link"
                        class="block p-4 text-sm font-semibold text-gray-400 hover:bg-blue-50 hover:text-blue-600 rounded"
                        (click)="toggleMobileMenu()"
                        >Home</a
                    >
                </li>
                <li class="mb-1">
                    <a
                        routerLink="/home/<USER>"
                        routerLinkActive="active-link"
                        class="block p-4 text-sm font-semibold text-gray-400 hover:bg-blue-50 hover:text-blue-600 rounded"
                        (click)="toggleMobileMenu()"
                        >About Us</a
                    >
                </li>
                <li class="mb-1">
                    <a
                        routerLink="/home/<USER>"
                        routerLinkActive="active-link"
                        class="block p-4 text-sm font-semibold text-gray-400 hover:bg-blue-50 hover:text-blue-600 rounded"
                        (click)="toggleMobileMenu()"
                        >Solutions</a
                    >
                </li>
                <li class="mb-1">
                    <a
                        routerLink="/home/<USER>"
                        routerLinkActive="active-link"
                        class="block p-4 text-sm font-semibold text-gray-400 hover:bg-blue-50 hover:text-blue-600 rounded"
                        (click)="toggleMobileMenu()"
                        >Pricing</a
                    >
                </li>
                <li class="mb-1">
                    <a
                        routerLink="/home/<USER>"
                        routerLinkActive="active-link"
                        class="block p-4 text-sm font-semibold text-gray-400 hover:bg-blue-50 hover:text-blue-600 rounded"
                        (click)="toggleMobileMenu()"
                        >Shop</a
                    >
                </li>
                <li class="mb-1">
                    <a
                        routerLink="/home/<USER>"
                        routerLinkActive="active-link"
                        class="block p-4 text-sm font-semibold text-gray-400 hover:bg-blue-50 hover:text-blue-600 rounded"
                        (click)="toggleMobileMenu()"
                        >Contact</a
                    >
                </li>
            </ul>
        </div>
        <div class="mt-auto">
            <div class="pt-6">
                <a
                    [routerLink]="'/dashboard'"
                    class="block px-4 py-3 mb-3 leading-loose text-xs text-center font-semibold bg-gray-50 hover:bg-gray-100 rounded-xl"
                    (click)="loadingDashboard = true; toggleMobileMenu()"
                >
                    @if (loadingDashboard) {
                        <mat-icon class="animate-spin mr-2">refresh</mat-icon>
                    }
                    Dashboard
                </a>
                <a
                    routerLink="/sign-up"
                    class="block px-4 py-3 mb-2 leading-loose text-xs text-center text-white font-semibold bg-blue-600 hover:bg-blue-700 rounded-xl"
                    (click)="toggleMobileMenu()"
                    >Sign Up</a
                >
            </div>
        </div>
    </nav>
</div>

<div class="w-full mt-20">
    <!-- SHOP Navbar Container -->
    <div class="bg-white text-gray-800">
        <!-- Top Navbar -->
        <nav class="py-3 m-5">
            <div class="container mx-auto">
                <div class="flex items-center justify-between">
                    <!-- Left Side - Account and Cart -->

                    <div class="flex items-center space-x-4">
                        <!-- All Category Button -->
                        <button
                            class="flex items-center border border-gray-300 text-gray-800 rounded-md px-3 py-2 hover:bg-gray-100"
                        >
                            <i class="fas fa-stream mr-2"></i> All Category
                        </button>
                        <!-- Search Form -->
                        <form>
                            <div class="flex">
                                <input
                                    type="search"
                                    placeholder="product name, brand, etc."
                                    class="border border-gray-300 rounded-l-md px-3 py-2 focus:outline-none focus:border-blue-500"
                                />
                                <button
                                    type="submit"
                                    class="bg-blue-500 text-white px-4 rounded-r-md hover:bg-blue-600 focus:outline-none"
                                >
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Right Side - Mobile Menu Button -->
                    <button
                        class="block lg:hidden text-gray-800 focus:outline-none"
                        aria-label="Toggle navigation"
                    >
                        <!-- Hamburger Icon -->
                        <i class="fa-duotone fa-bars"></i>
                    </button>
                    <!-- Center - Category and Search -->

                    <div class="flex items-center space-x-2">
                        <button
                            class="border border-blue-500 text-blue-500 rounded-md px-4 py-2 hover:bg-blue-500 hover:text-white focus:outline-none"
                        >
                            Account
                        </button>
                        <button
                            class="border border-blue-500 text-blue-500 rounded-md px-4 py-2 hover:bg-blue-500 hover:text-white focus:outline-none"
                        >
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                    </div>
                </div>
            </div>
        </nav>
    </div>

    <section class="px-8 py-8 lg:py-16">
        <div class="container mx-auto text-center">
            <h5
                class="block antialiased tracking-normal font-sans font-semibold leading-snug text-blue-gray-900 mb-4 !text-base lg:!text-2xl"
            >
                Shop Coming Soon!
            </h5>
            <h1
                class="block antialiased tracking-normal font-sans font-semibold leading-tight text-blue-gray-900 mb-4 !text-3xl lg:!text-5xl"
            >
                <i class="fa-duotone fa-bag-shopping"></i>
                Stay Tuned
            </h1>
            <p
                class="block antialiased font-sans leading-relaxed text-inherit mb-10 font-normal !text-lg lg:mb-20 mx-auto max-w-3xl !text-gray-500"
            >
                We're working hard on this. Please check back soon!
            </p>
            <i class="fa-duotone fa-solid fa-shop-lock icon-size-20"></i>
        </div>
    </section>
    <!-- Product category -->
    <div class="container mx-auto p-10">
        <div class="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-6 gap-6">
            <!-- Repeat this card component as many times as needed -->
            @for (category of categories; track category) {
                <div class="text-center">
                    <div class="bg-card rounded-lg shadow-xl overflow-hidden">
                        <div class="relative">
                            <div
                                class="absolute right-0 top-0 flex flex-col p-3"
                            >
                                <button mat-icon-button>
                                    <i
                                        class="fa-duotone fa-heart text-white text-opacity-50 icon-size-5"
                                    ></i>
                                </button>
                                <button mat-icon-button>
                                    <i
                                        class="fa-duotone fa-arrow-rotate-right text-white text-opacity-50 icon-size-5"
                                    ></i>
                                </button>
                            </div>
                            <img
                                class="object-cover w-full h-32"
                                src="images/landing/shop/svg/no-image-placeholder.svg"
                                alt="Card cover image"
                            />
                        </div>
                        <div class="p-4">
                            <div class="flex flex-col">
                                <div class="text-xl leading-tight">
                                    Coming Soon
                                </div>
                                <div class="mt-2 text-2xl font-semibold">
                                    $0.00
                                </div>
                            </div>
                            <hr class="my-4 border-b" />
                            <div class="flex items-center text-sm">
                                <span
                                    class="text-secondary mr-3 whitespace-nowrap"
                                >
                                    Options:
                                </span>
                                <span class="mr-2">1</span>
                                <span class="mr-2">2</span>
                                <span class="mr-2">3</span>
                                <span class="mr-2 font-bold text-primary"
                                    >4</span
                                >
                                <span class="mr-2">5</span>
                            </div>
                            <div class="mt-4">
                                <button
                                    class="inline-flex overflow-hidden text-white bg-gray-900 rounded group w-full"
                                >
                                    <span
                                        class="px-3 py-2 text-white bg-purple-500 group-hover:bg-purple-600 flex items-center justify-center"
                                    >
                                        <i
                                            class="fa-duotone fa-bag-shopping"
                                        ></i>
                                    </span>
                                    <span class="pl-4 pr-5 py-2">
                                        Coming Soon
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
            <!-- Repeat ends -->
            <!-- Add more card components here -->
        </div>
    </div>
</div>

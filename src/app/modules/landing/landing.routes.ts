import { Routes } from '@angular/router';
import { LandingHomeComponent } from 'app/modules/landing/home/<USER>';
import { LandingComponent } from './landing.component';
import { AboutUsComponent } from './about-us/about-us.component';
import { ContactComponent } from './contact/contact.component';
import { SolutionsComponent } from './solutions/solutions.component';
import { PricingComponent } from './pricing/pricing.component';
import { SignupComponent } from './signup/signup.component';
import { ShopComponent } from './shop/shop.component';
import { TermsComponent } from './terms/terms.component';
import { PrivacyPolicyComponent } from './privacy-policy/privacy-policy.component';
import { UserTermsComponent } from './user-terms/user-terms.component';

export default [
    {
        path: '',
        component: LandingComponent,
        children: [
            { path: '', pathMatch: 'full', component: LandingHomeComponent },
            { path: 'home', component: LandingHomeComponent },
            { path: 'about', component: AboutUsComponent },
            { path: 'solutions', component: SolutionsComponent },
            { path: 'pricing', component: PricingComponent },
            { path: 'contact', component: ContactComponent },
            { path: 'signup', component: SignupComponent },
            { path: 'shop', component: ShopComponent },
            { path: 'privacy-policy', component: PrivacyPolicyComponent },
            { path: 'terms', component: TermsComponent },
            { path: 'user-terms', component: UserTermsComponent },
            { path: '**', redirectTo: 'home' }
        ]
    }
] as Routes;

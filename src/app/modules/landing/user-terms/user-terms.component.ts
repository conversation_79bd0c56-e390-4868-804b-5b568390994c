// user-terms.component.ts
import { Component, OnInit, inject } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { SafeHtml, DomSanitizer } from '@angular/platform-browser';
import { AppwriteService } from 'app/core/databaseModels/appwrite.service';
import { ExecutionMethod } from 'appwrite';


@Component({
    selector: 'app-user-terms',
    standalone: true,
    imports: [],
    templateUrl: './user-terms.component.html',
    styleUrls: ['./user-terms.component.scss']
})
export class UserTermsComponent implements OnInit {


    private route = inject(ActivatedRoute);
    private sanitizer = inject(DomSanitizer);

    loading = true;
    error: string | null = null;
    organizationName: string = '';
    sanitizedTerms: SafeHtml = '';
    currentDate = new Date();


    constructor(
        private readonly aws: AppwriteService) {
    }
    ngOnInit() {


        const organisationId = this.route.snapshot.queryParams['organisationId'];

        if (!organisationId) {
            this.error = 'Valid Organisation is required';
            this.loading = false;
            return;
        }

        this.fetchTerms(organisationId);
    }

    private fetchTerms(organisationId: string) {
        const endpoint = `/user-terms?organisationId=${organisationId}`;
        const payload = null; // No payload for GET request
        this.aws.executeUserFunction<any>(
            endpoint,
            payload,
            ExecutionMethod.GET
        ).subscribe({
            next: (response) => {
                this.organizationName = response.organizationName;
                // Sanitize the HTML content
                this.sanitizedTerms = this.sanitizer.bypassSecurityTrustHtml(response.terms);
                this.loading = false;
            },
            error: (err) => {
                this.error = 'Failed to load terms. Please try again later.';
                this.loading = false;
                console.error('Error fetching terms:', err);
            },
        });
    }

}

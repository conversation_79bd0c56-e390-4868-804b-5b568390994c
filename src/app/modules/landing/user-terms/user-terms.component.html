<div
    class="flex w-full min-h-screen bg-gradient-to-b mt-40 from-gray-50 to-white py-8 px-4 sm:px-6 lg:px-8"
>
    <!-- Loading State -->
    @if (loading) {
        <div class="flex justify-center items-center min-h-[50vh]">
            <div
                class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"
            ></div>
        </div>
    }

    <!-- Error State -->
    @if (error) {
        <div class="max-w-3xl mx-auto text-center">
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <h3 class="text-lg font-medium text-red-800">
                    Error Loading Terms
                </h3>
                <p class="mt-2 text-sm text-red-700">{{ error }}</p>
            </div>
        </div>
    }

    <!-- Content -->
    @if (!loading && !error) {
        <div class="max-w-4xl mx-auto">
            <!-- Organization Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                    {{ organizationName }}
                </h1>
                <p class="mt-3 text-xl text-gray-500">Terms and Conditions</p>
                <div class="mt-4 flex justify-center">
                    <div class="h-1 w-20 bg-blue-500 rounded"></div>
                </div>
            </div>

            <!-- Terms Content -->
            <div class="bg-white shadow-xl rounded-lg overflow-hidden">
                <div
                    class="prose prose-blue max-w-none p-8 prose-headings:text-gray-900 prose-p:text-gray-600 prose-a:text-blue-600 prose-strong:text-gray-900"
                    [innerHTML]="sanitizedTerms"
                ></div>
            </div>

            <!-- Footer -->
            <div class="mt-8 text-center text-sm text-gray-500">
                Last updated: {{ currentDate | date: "longDate" }}
            </div>
        </div>
    }
</div>

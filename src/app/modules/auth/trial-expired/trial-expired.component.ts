import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { Router, RouterLink } from '@angular/router';
import { fuseAnimations } from '@fuse/animations';
import { FuseAlertComponent, FuseAlertType } from '@fuse/components/alert';
import { SubscriptionInfoService } from 'app/core/auth/subscription-info.service';
import { SubscriptionPlansService } from 'app/core/databaseModels/subscriptionPlans/subscriptionPlans.service';
import { SubscriptionPlans } from 'app/core/databaseModels/subscriptionPlans/subscriptionPlans.types';
import { ManageCreditComponent } from 'app/modules/admin/settings/plan-billing/manage-credit/manage-credit.component';

@Component({
    selector: 'auth-trial-expired',
    templateUrl: './trial-expired.component.html',
    encapsulation: ViewEncapsulation.None,
    animations: fuseAnimations,
    imports: [RouterLink,
        FuseAlertComponent,
        MatIconModule,
        MatButtonModule,
    ]
})
export class AuthTrialExpiredComponent implements OnInit {
    subscriptionInfo: any;
    paymentEmail: string = null;
    stripeCustomerId: string = null;
    organisationID: string = null;
    organisationName: string = null;
    teamMemberCredits: number = 0;
    selectedPlan: SubscriptionPlans;
    planId: string
    alert: { type: FuseAlertType; message: string } = {
        type: 'error',
        message: '',
    };
    showAlert: boolean = true;

    constructor(
        private subscriptionInfoService: SubscriptionInfoService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _subscriptionPlansService: SubscriptionPlansService,
    ) { }

    ngOnInit(): void {
        this.subscriptionInfo = this.subscriptionInfoService.subscriptionInfo;


        if (this.subscriptionInfo) {
            // Handle the case where subscriptionInfo is not available
            // Maybe redirect to sign-in or fetch the data

            const trialEndDate = this.subscriptionInfo.trialEndDate ? new Date(this.subscriptionInfo.trialEndDate) : null;
            this.stripeCustomerId = this.subscriptionInfo.stripeCustomerId;
            this.paymentEmail = this.subscriptionInfo.paymentEmail;
            this.planId = this.subscriptionInfo.planId;
            this.organisationName = this.subscriptionInfo.organisationName;
            this.teamMemberCredits = this.subscriptionInfo.teamMemberCredits;
            this.organisationID = this.subscriptionInfo.organisationId;
            if (this.planId) {
                this._subscriptionPlansService.getSubscriptionPlan(this.planId).subscribe((plan: SubscriptionPlans) => {
                    this.selectedPlan = plan;
                });
            }
            console.log(`Trial End Date:${trialEndDate} Stripe Customer ID:${this.stripeCustomerId} Payment Email:${this.paymentEmail} Organization ID:${this.organisationID} Plan ID:${this.planId} Organisation Name:${this.organisationName} Team Member Credits:${this.teamMemberCredits}`);
            console.log('Subscription Info:', this.subscriptionInfo);
            this.alert = {
                type: 'error',
                message: 'Your trial period has expired on ' + trialEndDate.toISOString().split('T')[0] + '. Please renew your subscription.',
            }
            this.showAlert = true;
        }

        console.log('Subscription Info:', this.subscriptionInfo);
    }

    subscribeCreditDialog(): void {
        const dialogRef = this._matDialog.open(ManageCreditComponent, {
            data: {
                organisationID: this.organisationID,
                currentCredit: 0,
                stripeCustomerId: this.stripeCustomerId,
                stripePriceId: this.selectedPlan.stripePriceId,
                customerEmail: this.paymentEmail,
                customerName: this.organisationName,
                selectedPlanName: this.selectedPlan.subscriptionName,
                price: this.selectedPlan.price,
                currency: this.selectedPlan.currency,
                trialMode: false,
                teamMemberCredits: this.teamMemberCredits
            }
        });

        dialogRef.afterClosed().subscribe(result => {
            // console.log('Compose dialog was closed!');
            // Navigate to login
            this._router.navigateByUrl('/sign-in');
        });
    }
}

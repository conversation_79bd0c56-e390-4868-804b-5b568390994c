import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import {
    FormsModule,
    ReactiveFormsModule,
    UntypedFormBuilder,
    UntypedFormGroup,
    Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatStepperModule } from '@angular/material/stepper';
import { Router, RouterLink } from '@angular/router';
import { fuseAnimations } from '@fuse/animations';
import { FuseAlertComponent, FuseAlertType } from '@fuse/components/alert';
import { AppwriteAuthService } from 'app/core/auth/appwrite.auth.service';
import { FuseCardComponent } from '@fuse/components/card';
import { debounceTime, distinctUntilChanged, filter, Observable, of, Subscription, switchMap } from 'rxjs';
import { NgxMaterialIntlTelInputComponent } from 'ngx-material-intl-tel-input';


@Component({
    selector: 'auth-sign-up',
    templateUrl: './sign-up.component.html',
    encapsulation: ViewEncapsulation.None,
    animations: fuseAnimations,
    imports: [
        RouterLink,
        FuseAlertComponent,
        MatOptionModule,
        FormsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatButtonModule,
        MatIconModule,
        MatCheckboxModule,
        MatRadioModule,
        MatSelectModule,
        MatStepperModule,
        FuseCardComponent,
        MatProgressSpinnerModule,
        NgxMaterialIntlTelInputComponent,
    ]
})
export class AuthSignUpComponent implements OnInit {


    alert: { type: FuseAlertType; message: string } = {
        type: 'success',
        message: '',
    };
    signUpForm: UntypedFormGroup;
    showAlert: boolean = false;
    private subscriptions: Subscription = new Subscription();
    phoneControl;

    /**
     * Constructor
     */
    constructor(
        private _authService: AppwriteAuthService,
        private _formBuilder: UntypedFormBuilder,
        private _router: Router
    ) { }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        // Create the form
        this.signUpForm = this._formBuilder.group({
            step1: this._formBuilder.group({
                name: ['', Validators.required],
                email: ['', [Validators.required, Validators.email]],
                phone: ['', [Validators.required]],
            }),
            step2: this._formBuilder.group({
                company: ['', Validators.required],
                country: ['', Validators.required],
                language: ['', Validators.required],
            }),
            step3: this._formBuilder.group({
                planName: ['', Validators.required],
            }),
            step4: this._formBuilder.group({
                agreements: [false, Validators.requiredTrue],
            }),
        });
        // Add listeners for email and phone changes
        // Get references to the controls
        const emailControl = this.signUpForm.get('step1.email');
        this.phoneControl = this.signUpForm.get('step1.phone');



        // Subscribe to email value changes with debounce and validation
        this.subscriptions.add(
            emailControl.valueChanges.pipe(
                debounceTime(500),
                distinctUntilChanged(),
                filter(() => emailControl.valid && emailControl.value),
                switchMap(value => this.checkExistEmail(value))
            ).subscribe(result => {
                if (result) {
                    emailControl.setErrors({ emailExists: true });
                } else {
                    const errors = emailControl.errors;
                    if (errors) {
                        delete errors.emailExists;
                        if (!Object.keys(errors).length) {
                            emailControl.setErrors(null);
                        } else {
                            emailControl.setErrors(errors);
                        }
                    }
                }
            })
        );

        // Subscribe to phone value changes with debounce and validation
        this.subscriptions.add(
            this.phoneControl.valueChanges.pipe(
                debounceTime(500),
                distinctUntilChanged(),
                filter(() => this.phoneControl.valid && this.phoneControl.value),
                switchMap(value => this.checkExistPhone(this.phoneControl.value.replace(/\s+/g, '')))
            ).subscribe(result => {
                if (result) {
                    this.phoneControl.setErrors({ phoneExists: true });
                } else {
                    const errors = this.phoneControl.errors;
                    if (errors) {
                        delete errors.phoneExists;
                        if (!Object.keys(errors).length) {
                            this.phoneControl.setErrors(null);
                        } else {
                            this.phoneControl.setErrors(errors);
                        }
                    }
                }
            })
        );
    }

    ngOnDestroy(): void {
        this.subscriptions.unsubscribe();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------


    checkExistEmail(email: string): Observable<boolean> {
        if (!email) {
            return of(false);
        }
        const formattedEmail = email.trim().toLowerCase();
        return this._authService.checkExistEmail(formattedEmail);
    }

    checkExistPhone(phone: string): Observable<boolean> {
        if (!phone) {
            return of(false);
        }

        return this._authService.checkExistPhone(phone);
    }




    /**
     * Sign up
     */
    signUp(): void {
        // Do nothing if the form is invalid
        if (this.signUpForm.invalid) {
            return;
        }

        // Disable the form


        // Hide the alert
        this.showAlert = false;

        const formData = this.signUpForm.value;
        this.signUpForm.disable();

        // Sign up
        formData.step1.phone = formData.step1.phone.replace(/\s+/g, '');
        // console.log('form Object:', formData);
        this._authService.signUp(formData).subscribe(
            (response) => {
                // Navigate to the confirmation required page
                if (response === 'Success') {
                    this._router.navigateByUrl('/confirmation-required');
                } else {
                    // Re-enable the form
                    this.signUpForm.enable();

                    // Set the alert
                    this.alert = {
                        type: 'error',
                        message: `Something went wrong, please try again. Error: ${response}`,
                    };

                    // Show the alert
                    this.showAlert = true;
                }
            },
            (error) => {
                // Handle any unexpected errors
                this.signUpForm.enable();

                // Set the alert
                this.alert = {
                    type: 'error',
                    message: `An unexpected error occurred: ${error}`,
                };

                // Show the alert
                this.showAlert = true;
            }
        );
    }

}

<div
    class="flex min-w-0 flex-auto flex-col items-center sm:flex-row sm:justify-center md:items-start md:justify-start"
>
    <div
        class="w-full px-4 py-8 sm:bg-card sm:w-auto sm:rounded-2xl sm:p-12 sm:shadow md:flex md:h-full md:w-1/2 md:items-center md:justify-end md:rounded-none md:p-16 md:shadow-none"
    >
        <div class="mx-auto w-full max-w-120 sm:mx-0 sm:w-120">
            <!-- Logo -->
            <div class="w-12">
                <img src="images/logo/logo.svg" />
            </div>

            <!-- Title -->
            <div
                class="mt-8 text-4xl font-extrabold leading-tight tracking-tight"
            >
                Sign up
            </div>
            <div class="mt-0.5 flex items-baseline font-medium">
                <div>Already have an account?</div>
                <a
                    class="ml-1 text-primary-500 hover:underline"
                    [routerLink]="['/sign-in']"
                    >Sign in
                </a>
            </div>

            <!-- Alert -->
            @if (showAlert) {
                <fuse-alert
                    class="mt-8"
                    [appearance]="'outline'"
                    [showIcon]="false"
                    [type]="alert.type"
                    [@shake]="alert.type === 'error'"
                >
                    {{ alert.message }}
                </fuse-alert>
            }

            <!-- Sign Up form -->
            <form class="mt-8 w-full" [formGroup]="signUpForm">
                <mat-vertical-stepper [linear]="true" #verticalStepper>
                    <mat-step
                        [formGroupName]="'step1'"
                        [stepControl]="signUpForm.get('step1')"
                        #verticalStepperStep1
                    >
                        <ng-template matStepLabel>Your information</ng-template>
                        <p class="my-6 font-medium">
                            Fill in your basic information to let us know your
                            preferences
                        </p>
                        <div class="flex">
                            <mat-form-field class="flex-auto">
                                <input
                                    matInput
                                    [formControlName]="'name'"
                                    [placeholder]="'Your full name'"
                                    required
                                />
                                <mat-icon
                                    class="icon-size-5"
                                    matPrefix
                                    [svgIcon]="'heroicons_solid:user-circle'"
                                ></mat-icon>
                            </mat-form-field>
                        </div>
                        <div class="flex">
                            <mat-form-field class="flex-auto">
                                <input
                                    matInput
                                    [formControlName]="'email'"
                                    [placeholder]="'Email Address'"
                                    required
                                />
                                <mat-icon
                                    class="icon-size-5"
                                    matPrefix
                                    [svgIcon]="'heroicons_solid:envelope'"
                                ></mat-icon>
                                @if (
                                    signUpForm.get("step1").get("email")?.errors
                                        ?.email
                                ) {
                                    <mat-error>
                                        Please enter a valid email address.
                                    </mat-error>
                                }
                                @if (
                                    signUpForm.get("step1").get("email")?.errors
                                        ?.emailExists
                                ) {
                                    <mat-error>
                                        Email already exists.
                                    </mat-error>
                                }
                            </mat-form-field>
                        </div>
                        <div class="flex">
                            <ngx-material-intl-tel-input
                                fieldControlName="phone"
                                [required]="true"
                                [autoIpLookup]="true"
                                includeDialCode="true"
                                textLabels="false"
                                numberValidation="true"
                            >
                            </ngx-material-intl-tel-input>
                        </div>
                        @if (
                            signUpForm.get("step1.phone")?.errors?.phoneExists
                        ) {
                            <mat-error>Phone number already exists.</mat-error>
                        }
                        <div class="flex justify-end mt-5">
                            <button
                                class="px-8"
                                mat-flat-button
                                [color]="'primary'"
                                [disabled]="
                                    verticalStepperStep1.stepControl.pristine ||
                                    verticalStepperStep1.stepControl.invalid
                                "
                                type="button"
                                matStepperNext
                            >
                                Next
                            </button>
                        </div>
                    </mat-step>

                    <mat-step
                        [formGroupName]="'step2'"
                        [stepControl]="signUpForm.get('step2')"
                        #verticalStepperStep2
                    >
                        <ng-template matStepLabel>Company</ng-template>
                        <p class="my-6 font-medium">
                            Your organisation here will get to know you with
                            this information
                        </p>

                        <div class="flex">
                            <mat-form-field class="flex-auto">
                                <input
                                    matInput
                                    [formControlName]="'company'"
                                    [placeholder]="'Company Name'"
                                    required
                                />
                            </mat-form-field>
                        </div>
                        <div class="gt-xs:flex-row flex flex-col">
                            <mat-form-field class="gt-xs:pr-3 flex-auto">
                                <mat-select
                                    [formControlName]="'country'"
                                    [placeholder]="'Country / Region'"
                                    required
                                >
                                    <mat-option [value]="'australia'"
                                        >Australia</mat-option
                                    >
                                    <mat-option [value]="'usa'"
                                        >United States</mat-option
                                    >
                                    <mat-option [value]="'canada'"
                                        >Canada</mat-option
                                    >
                                    <mat-option [value]="'united-kingdom'"
                                        >United Kingdom</mat-option
                                    >
                                    <mat-option [value]="'mexico'"
                                        >Mexico</mat-option
                                    >
                                    <mat-option [value]="'france'"
                                        >France</mat-option
                                    >
                                    <mat-option [value]="'germany'"
                                        >Germany</mat-option
                                    >
                                    <mat-option [value]="'italy'"
                                        >Italy</mat-option
                                    >
                                </mat-select>
                                <mat-icon
                                    class="icon-size-5"
                                    matPrefix
                                    [svgIcon]="'heroicons_solid:map-pin'"
                                ></mat-icon>
                            </mat-form-field>
                            <mat-form-field class="gt-xs:pl-3 flex-auto">
                                <mat-select
                                    [formControlName]="'language'"
                                    [placeholder]="'Language'"
                                    required
                                >
                                    <mat-option [value]="'english'"
                                        >English</mat-option
                                    >
                                    <mat-option [value]="'french'"
                                        >French</mat-option
                                    >
                                    <mat-option [value]="'spanish'"
                                        >Spanish</mat-option
                                    >
                                    <mat-option [value]="'german'"
                                        >German</mat-option
                                    >
                                    <mat-option [value]="'italian'"
                                        >Italian</mat-option
                                    >
                                </mat-select>
                                <mat-icon
                                    class="icon-size-5"
                                    matPrefix
                                    [svgIcon]="'heroicons_solid:language'"
                                ></mat-icon>
                            </mat-form-field>
                        </div>
                        <div class="flex justify-end">
                            <button
                                class="mr-2 px-8"
                                mat-flat-button
                                [color]="'accent'"
                                type="button"
                                matStepperPrevious
                            >
                                Back
                            </button>
                            <button
                                class="px-8"
                                mat-flat-button
                                [color]="'primary'"
                                [disabled]="
                                    verticalStepperStep2.stepControl.pristine ||
                                    verticalStepperStep2.stepControl.invalid
                                "
                                type="button"
                                matStepperNext
                            >
                                Next
                            </button>
                        </div>
                    </mat-step>

                    <mat-step
                        [formGroupName]="'step3'"
                        [stepControl]="signUpForm.get('step3')"
                    >
                        <ng-template matStepLabel>Subscription</ng-template>
                        <p class="my-6 font-medium">
                            Select your subscription plan,<span
                                class="text-lg font-semibold leading-tight"
                            >
                                30 days free trial</span
                            >
                            and more features to come.
                        </p>
                        <div class="gt-sm:flex-row flex flex-col">
                            <div
                                class="gt-sm:mt-0 gt-sm:ml-16 mt-8 flex flex-col"
                            >
                                <fuse-card
                                    class="filter-pricing w-full max-w-80 flex-col items-center p-8 text-center sm:px-10 sm:py-12 lg:rounded-none lg:rounded-l"
                                >
                                    <div
                                        class="text-4xl font-bold leading-tight tracking-tight"
                                    >
                                        Premium
                                    </div>
                                    <!-- Price -->
                                    <div
                                        class="mt-8 flex items-baseline whitespace-nowrap"
                                    >
                                        <div
                                            class="text-6xl font-semibold leading-tight tracking-tight"
                                        >
                                            $0.99c
                                        </div>
                                        <div
                                            class="text-secondary ml-2 text-2xl"
                                        >
                                            / day
                                        </div>
                                    </div>
                                    <!-- Price details -->
                                    <div
                                        class="text-secondary mt-2 flex flex-col"
                                    >
                                        <div>billed monthly</div>
                                        <div>
                                            <b>Per team member</b>
                                        </div>
                                    </div>
                                    <!-- Features -->
                                    <div class="mt-8 space-y-2">
                                        <div><b>100</b> Team members</div>
                                        <div><b>50GB</b> storage</div>
                                        <div>Analytics Reports</div>
                                        <div>Free mobile app</div>
                                        <div>24/7 email support</div>
                                    </div>
                                    <!-- CTA -->
                                    <mat-radio-group
                                        class="flex flex-col mt-10"
                                        [color]="'primary'"
                                        [formControlName]="'planName'"
                                    >
                                        <mat-radio-button
                                            class="mb-2"
                                            [value]="'Premium Plan'"
                                        >
                                            Start your trial
                                        </mat-radio-button>
                                    </mat-radio-group>
                                </fuse-card>
                            </div>
                        </div>
                        <div class="mt-8 flex justify-end">
                            <button
                                class="mr-2 px-8"
                                mat-flat-button
                                [color]="'accent'"
                                type="button"
                                matStepperPrevious
                            >
                                Back
                            </button>
                            <button
                                class="px-8"
                                mat-flat-button
                                [color]="'primary'"
                                type="button"
                                matStepperNext
                            >
                                Next
                            </button>
                        </div>
                    </mat-step>

                    <mat-step [formGroupName]="'step4'">
                        <ng-template matStepLabel>Done</ng-template>
                        <div class="mt-1.5 inline-flex w-full items-end">
                            <mat-checkbox
                                class="-ml-2"
                                [color]="'primary'"
                                [formControlName]="'agreements'"
                            >
                                <span>I agree with</span>
                                <a
                                    class="ml-1 text-primary-500 hover:underline"
                                    [routerLink]="['/home/<USER>']"
                                    >Terms
                                </a>
                                <span>and</span>
                                <a
                                    class="ml-1 text-primary-500 hover:underline"
                                    [routerLink]="['/home/<USER>']"
                                    >Privacy Policy
                                </a>
                            </mat-checkbox>
                        </div>
                        <div class="mt-8 flex justify-end">
                            <button
                                class="mr-2 px-8"
                                mat-flat-button
                                [color]="'accent'"
                                type="button"
                                matStepperPrevious
                            >
                                Back
                            </button>
                            <button
                                class="px-8"
                                mat-flat-button
                                [color]="'warn'"
                                type="reset"
                                (click)="verticalStepper.reset()"
                            >
                                Reset
                            </button>
                            <!-- Submit button -->
                            <button
                                class="fuse-mat-button-large ml-6 w-full"
                                mat-flat-button
                                [color]="'primary'"
                                [disabled]="
                                    !signUpForm.valid || signUpForm.disabled
                                "
                                (click)="signUp()"
                            >
                                @if (!signUpForm.disabled) {
                                    <span> Create your free account </span>
                                }
                                @if (signUpForm.disabled) {
                                    <mat-progress-spinner
                                        [diameter]="24"
                                        [mode]="'indeterminate'"
                                    ></mat-progress-spinner>
                                }
                            </button>
                        </div>
                    </mat-step>
                </mat-vertical-stepper>
            </form>
        </div>
    </div>
    <div
        class="relative hidden h-full w-1/2 flex-auto items-center justify-center overflow-hidden bg-gray-800 p-16 dark:border-l md:flex lg:px-28"
    >
        <!-- Background -->
        <!-- Rings -->
        <!-- prettier-ignore -->
        <svg class="absolute inset-0 pointer-events-none"
             viewBox="0 0 960 540" width="100%" height="100%" preserveAspectRatio="xMidYMax slice" xmlns="http://www.w3.org/2000/svg">
            <g class="text-gray-700 opacity-25" fill="none" stroke="currentColor" stroke-width="100">
                <circle r="234" cx="196" cy="23"></circle>
                <circle r="234" cx="790" cy="491"></circle>
            </g>
        </svg>
        <!-- Dots -->
        <!-- prettier-ignore -->
        <svg class="absolute -top-16 -right-16 text-gray-700"
             viewBox="0 0 220 192" width="220" height="192" fill="none">
            <defs>
                <pattern id="837c3e70-6c3a-44e6-8854-cc48c737b659" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                    <rect x="0" y="0" width="4" height="4" fill="currentColor"></rect>
                </pattern>
            </defs>
            <rect width="220" height="192" fill="url(#837c3e70-6c3a-44e6-8854-cc48c737b659)"></rect>
        </svg>
        <!-- Content -->
        <div class="relative z-10 w-full max-w-2xl">
            <div class="text-7xl font-bold leading-none text-gray-100">
                <div>Welcome to</div>
                <div>our community</div>
            </div>
            <div class="mt-6 text-lg leading-6 tracking-tight text-gray-400">
                Streamliner Apps empowers businesses to create meticulously
                organised jobs. Join us today and embark on the journey of
                efficiently scheduling and managing your work tasks.
            </div>
            <div class="mt-8 flex items-center">
                <div class="flex flex-0 items-center -space-x-1.5">
                    <img
                        class="h-10 w-10 flex-0 rounded-full object-cover ring-4 ring-gray-800 ring-offset-1 ring-offset-gray-800"
                        src="images/avatars/female-18.jpg"
                    />
                    <img
                        class="h-10 w-10 flex-0 rounded-full object-cover ring-4 ring-gray-800 ring-offset-1 ring-offset-gray-800"
                        src="images/avatars/female-11.jpg"
                    />
                    <img
                        class="h-10 w-10 flex-0 rounded-full object-cover ring-4 ring-gray-800 ring-offset-1 ring-offset-gray-800"
                        src="images/avatars/male-09.jpg"
                    />
                    <img
                        class="h-10 w-10 flex-0 rounded-full object-cover ring-4 ring-gray-800 ring-offset-1 ring-offset-gray-800"
                        src="images/avatars/male-16.jpg"
                    />
                </div>
                <div class="ml-4 font-medium tracking-tight text-gray-400">
                    More than 17k people joined us, it's your turn
                </div>
            </div>
        </div>
    </div>
</div>

import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RouterLink } from '@angular/router';
import { fuseAnimations } from '@fuse/animations';
import { FuseAlertComponent, FuseAlertType } from '@fuse/components/alert';
import { SubscriptionInfoService } from 'app/core/auth/subscription-info.service';
import { StripeService } from 'app/core/databaseModels/stripe.service';

@Component({
    selector: 'auth-subscription-expired',
    templateUrl: './subscription-expired.component.html',
    encapsulation: ViewEncapsulation.None,
    animations: fuseAnimations,
    imports: [RouterLink,
        FuseAlertComponent,
        FuseAlertComponent,
        MatIconModule,
        MatButtonModule,
        MatProgressSpinnerModule,
    ]
})
export class AuthSubscriptionExpiredComponent implements OnInit {
    subscriptionInfo: any;
    paymentEmail: string = null;
    alert: { type: FuseAlertType; message: string } = {
        type: 'error',
        message: '',
    };
    showAlert: boolean = false;
    moveToDashboard = false;
    stripeCustomerId: string = null;

    constructor(
        private subscriptionInfoService: SubscriptionInfoService,
        private stripeService: StripeService) { }

    ngOnInit(): void {
        this.subscriptionInfo = this.subscriptionInfoService.subscriptionInfo;

        if (this.subscriptionInfo) {
            // Handle the case where subscriptionInfo is not available
            // Maybe redirect to sign-in or fetch the data
            console.log('Subscription Info:', this.subscriptionInfo);
            const subscriptionEndDate = this.subscriptionInfo.subscriptionEndDate ? new Date(this.subscriptionInfo.subscriptionEndDate) : null;
            this.stripeCustomerId = this.subscriptionInfo.stripeCustomerId;
            this.alert = {
                type: 'error',
                message: 'Your subscription has expired on ' + subscriptionEndDate.toISOString().split('T')[0] + '. Please renew your subscription.',
            }
            this.showAlert = true;
        }

        console.log('Subscription Info:', this.subscriptionInfo);
    }


    openManageSubscription(): void {
        this.moveToDashboard = true;
        if (this.stripeCustomerId) {
            this.stripeService.navigateToDashboard(this.stripeCustomerId).subscribe((url) => {
                if (url && url.includes('stripe.com')) {
                    // console.log('Navigating to Stripe URL:', url);

                    window.location.href = url;  // Navigate to the Stripe URL
                } else {
                    //   console.log('URL does not contain stripe.com or is invalid:', url);
                    this.moveToDashboard = false;

                }
            });
        }

    }
}


import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormsModule, NgForm, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { fuseAnimations } from '@fuse/animations';
import { FuseAlertComponent, FuseAlertType } from '@fuse/components/alert';
// import { AuthService } from 'app/core/auth/auth.service';
import { AppwriteAuthService } from 'app/core/auth/appwrite.auth.service';
import { SubscriptionInfoService } from 'app/core/auth/subscription-info.service';
@Component({
    selector: 'auth-sign-in',
    templateUrl: './sign-in.component.html',
    encapsulation: ViewEncapsulation.None,
    animations: fuseAnimations,
    imports: [RouterLink, FuseAlertComponent, FormsModule, ReactiveFormsModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatCheckboxModule, MatProgressSpinnerModule]
})
export class AuthSignInComponent implements OnInit {
    @ViewChild('signInNgForm') signInNgForm: NgForm;

    alert: { type: FuseAlertType; message: string } = {
        type: 'success',
        message: '',
    };
    signInForm: UntypedFormGroup;
    showAlert: boolean = false;

    /**
     * Constructor
     */
    constructor(
        private _activatedRoute: ActivatedRoute,
        private _authService: AppwriteAuthService,
        private _formBuilder: UntypedFormBuilder,
        private _router: Router,
        private subscriptionInfoService: SubscriptionInfoService,
    ) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {

        this.signInForm = this._formBuilder.group({
            email: ['', [Validators.required, Validators.email]],
            password: ['', Validators.required],
            rememberMe: [''],
        });
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    //   // Handle successful sign-in
    //   const redirectURL = this._activatedRoute.snapshot.queryParamMap.get('redirectURL') || '/signed-in-redirect';

    //   // Navigate to the redirect url
    //   this._router.navigateByUrl(redirectURL);
    /**
     * Sign in
     */signIn(): void {
        // Return if the form is invalid
        if (this.signInForm.invalid) {
            return;
        }

        // Disable the form
        this.signInForm.disable();

        // Hide the alert
        this.showAlert = false;

        // Sign in
        // Sign in
        this._authService.signIn(this.signInForm.value)
            .subscribe(
                (result) => {
                    if (result.status === 'success') {
                        const subscriptionInfo = result.subscriptionInfo;

                        if (subscriptionInfo.trialMode) {
                            if (!subscriptionInfo.trialExpired) {
                                // Trial is active
                                const redirectURL = this._activatedRoute.snapshot.queryParamMap.get('redirectURL') || '/signed-in-redirect';
                                this._router.navigateByUrl(redirectURL);
                            } else {
                                // Trial has expired
                                this._authService.signOut();
                                // Store subscriptionInfo
                                this.subscriptionInfoService.subscriptionInfo = subscriptionInfo;
                                this._router.navigateByUrl('/trial-expired');
                            }
                        } else {
                            if (!subscriptionInfo.subscriptionExpired) {
                                // Subscription is active
                                const redirectURL = this._activatedRoute.snapshot.queryParamMap.get('redirectURL') || '/signed-in-redirect';
                                this._router.navigateByUrl(redirectURL);
                            } else {
                                // Subscription has expired
                                this._authService.signOut();
                                // Store subscriptionInfo
                                this.subscriptionInfoService.subscriptionInfo = subscriptionInfo;
                                this._router.navigateByUrl('/subscription-expired');
                            }
                        }
                    } else if (result.status === 'suspended') {
                        this._authService.signOut();
                        // Handle suspended user
                        this.alert = {
                            type: 'error',
                            message: 'Your Account is Suspended, Please Contact Admin!',
                        };
                        this.showAlert = true;
                        this.signInForm.enable();
                    } else if (result.status === 'no_user') {
                        // Handle no user found
                        this.alert = {
                            type: 'error',
                            message: 'No user found with the given credentials.',
                        };
                        this.showAlert = true;
                        this.signInForm.enable();
                    }
                },
                (error) => {
                    console.error('SignIn Error:', error);
                    this.signInForm.enable();
                    this.signInNgForm.resetForm();
                    this.alert = {
                        type: 'error',
                        message: 'Wrong email or password',
                    };
                    this.showAlert = true;
                }
            );
    }
}

<div
    class="flex flex-col sm:flex-row items-center md:items-start sm:justify-center md:justify-start flex-auto min-w-0"
>
    <div
        class="md:flex md:items-center md:justify-end w-full sm:w-auto md:h-full md:w-1/2 py-8 px-4 sm:p-12 md:p-16 sm:rounded-2xl md:rounded-none sm:shadow md:shadow-none sm:bg-card"
    >
        <div class="w-full max-w-80 sm:w-80 mx-auto sm:mx-0">
            <!-- Logo -->
            <div class="w-80">
                <a [routerLink]="['/']">
                    <img src="images/logo/logo-stacked.svg" />
                </a>
            </div>

            <!-- Title -->
            <div
                class="mt-2 text-4xl font-extrabold tracking-tight leading-tight"
            >
                Sign in
            </div>
            <div class="flex items-baseline mt-0.5 font-medium">
                <div>Don't have an account?</div>
                <a
                    class="ml-1 text-primary-500 hover:underline"
                    [routerLink]="['/sign-up']"
                    >Sign up
                </a>
            </div>

            <!-- Alert -->
            <fuse-alert
                class="mt-8"
                [appearance]="'outline'"
                [showIcon]="false"
                [type]="'info'"
            >
                Welcome to <strong>Streamliner</strong>. enter your credentials.
                Click on the "Sign in" button to access.
            </fuse-alert>

            <!-- Alert -->
            @if (showAlert) {
                <fuse-alert
                    class="mt-8"
                    [appearance]="'outline'"
                    z
                    [showIcon]="false"
                    [type]="alert.type"
                    [@shake]="alert.type === 'error'"
                >
                    {{ alert.message }}
                </fuse-alert>
            }

            <!-- Sign in form -->
            <form class="mt-8" [formGroup]="signInForm" #signInNgForm="ngForm">
                <!-- Email field -->
                <mat-form-field class="w-full">
                    <mat-label>Email address</mat-label>
                    <input id="email" matInput [formControlName]="'email'" />
                    @if (signInForm.get("email").hasError("required")) {
                        <mat-error> Email address is required </mat-error>
                    }
                    @if (signInForm.get("email").hasError("email")) {
                        <mat-error>
                            Please enter a valid email address
                        </mat-error>
                    }
                </mat-form-field>

                <!-- Password field -->
                <mat-form-field class="w-full">
                    <mat-label>Password</mat-label>
                    <input
                        id="password"
                        matInput
                        type="password"
                        [formControlName]="'password'"
                        #passwordField
                    />
                    <button
                        mat-icon-button
                        type="button"
                        (click)="
                            passwordField.type === 'password'
                                ? (passwordField.type = 'text')
                                : (passwordField.type = 'password')
                        "
                        matSuffix
                    >
                        @if (passwordField.type === "password") {
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_solid:eye'"
                            ></mat-icon>
                        }
                        @if (passwordField.type === "text") {
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_solid:eye-slash'"
                            ></mat-icon>
                        }
                    </button>
                    <mat-error> Password is required </mat-error>
                </mat-form-field>

                <!-- Actions -->
                <div
                    class="inline-flex items-center justify-between w-full mt-1.5"
                >
                    <mat-checkbox
                        class="-ml-2"
                        [color]="'primary'"
                        [formControlName]="'rememberMe'"
                    >
                        Remember me
                    </mat-checkbox>
                    <a
                        class="text-md font-medium text-primary-500 hover:underline"
                        [routerLink]="['/forgot-password']"
                        >Forgot password?
                    </a>
                </div>

                <!-- Submit button -->
                <button
                    class="fuse-mat-button-large w-full mt-6"
                    mat-flat-button
                    [color]="'primary'"
                    [disabled]="signInForm.disabled"
                    (click)="signIn()"
                >
                    @if (!signInForm.disabled) {
                        <span> Sign in </span>
                    }
                    @if (signInForm.disabled) {
                        <mat-progress-spinner
                            [diameter]="24"
                            [mode]="'indeterminate'"
                        ></mat-progress-spinner>
                    }
                </button>
            </form>
        </div>
    </div>
    <div
        class="relative hidden md:flex flex-auto items-center justify-center w-1/2 h-full p-16 lg:px-28 overflow-hidden bg-gray-800 dark:border-l"
    >
        <!-- Background - @formatter:off -->
        <!-- Rings -->
        <svg
            class="absolute inset-0 pointer-events-none"
            viewBox="0 0 960 540"
            width="100%"
            height="100%"
            preserveAspectRatio="xMidYMax slice"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g
                class="text-gray-700 opacity-25"
                fill="none"
                stroke="currentColor"
                stroke-width="100"
            >
                <circle r="234" cx="196" cy="23"></circle>
                <circle r="234" cx="790" cy="491"></circle>
            </g>
        </svg>
        <!-- Dots -->
        <svg
            class="absolute -top-16 -right-16 text-gray-700"
            viewBox="0 0 220 192"
            width="220"
            height="192"
            fill="none"
        >
            <defs>
                <pattern
                    id="837c3e70-6c3a-44e6-8854-cc48c737b659"
                    x="0"
                    y="0"
                    width="20"
                    height="20"
                    patternUnits="userSpaceOnUse"
                >
                    <rect
                        x="0"
                        y="0"
                        width="4"
                        height="4"
                        fill="currentColor"
                    ></rect>
                </pattern>
            </defs>
            <rect
                width="220"
                height="192"
                fill="url(#837c3e70-6c3a-44e6-8854-cc48c737b659)"
            ></rect>
        </svg>
        <!-- @formatter:on -->
        <!-- Content -->
        <div class="z-10 relative w-full max-w-2xl">
            <div class="text-7xl font-bold leading-none text-gray-100">
                <div>Welcome to</div>
                <div>Streamliner</div>
            </div>
            <div class="mt-6 text-lg tracking-tight leading-6 text-gray-400">
                Streamliner Apps empowers businesses to create meticulously
                organised jobs. Join us today and embark on the journey of
                efficiently scheduling and managing your work tasks.
            </div>
            <div class="flex items-center mt-8">
                <div class="flex flex-0 items-center -space-x-1.5">
                    <img
                        class="flex-0 w-10 h-10 rounded-full ring-4 ring-offset-1 ring-gray-800 ring-offset-gray-800 object-cover"
                        src="images/avatars/female-18.jpg"
                    />
                    <img
                        class="flex-0 w-10 h-10 rounded-full ring-4 ring-offset-1 ring-gray-800 ring-offset-gray-800 object-cover"
                        src="images/avatars/female-11.jpg"
                    />
                    <img
                        class="flex-0 w-10 h-10 rounded-full ring-4 ring-offset-1 ring-gray-800 ring-offset-gray-800 object-cover"
                        src="images/avatars/male-09.jpg"
                    />
                    <img
                        class="flex-0 w-10 h-10 rounded-full ring-4 ring-offset-1 ring-gray-800 ring-offset-gray-800 object-cover"
                        src="images/avatars/male-16.jpg"
                    />
                </div>
                <div class="ml-4 font-medium tracking-tight text-gray-400">
                    More than 8k people joined us, it's your turn
                </div>
            </div>
        </div>
    </div>
</div>

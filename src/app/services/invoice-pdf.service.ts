import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { firstValueFrom } from 'rxjs';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { Invoicing, InvoicingItem } from 'app/core/databaseModels/invoicing/invoicing.type';
import { Customer } from 'app/core/databaseModels/customers/contacts.types';
import { Organisation } from 'app/core/databaseModels/organisations/organisations.types';

@Injectable({
    providedIn: 'root'
})
export class InvoicePdfService {
    constructor(private http: HttpClient) { }

    async generateInvoicePdf(
        invoice: Invoicing,
        customer: Customer,
        organisation: Organisation,
        invoicePrefix: string,
        dueDays: number,
        getFile: boolean = false
    ): Promise<Blob | void> {
        const doc = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
        });

        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;
        const margin = 20; // standard margin

        // Track total pages for page numbering
        let totalPages = 1;

        // Add organization logo if available (top right)
        if (organisation?.avatar) {
            const logoData = await this.loadImage(organisation.avatar);
            if (logoData) {
                const maxLogoWidth = 40;
                const maxLogoHeight = 40;
                const { width, height } = this.calculateImageDimensions(
                    logoData.width,
                    logoData.height,
                    maxLogoWidth,
                    maxLogoHeight
                );

                // Center the logo within the allocated space
                const xOffset = pageWidth - maxLogoWidth - margin;
                const yOffset = margin;
                const xCenter = xOffset + (maxLogoWidth - width) / 2;
                const yCenter = yOffset + (maxLogoHeight - height) / 2;

                doc.addImage(logoData.dataUrl, 'PNG', xCenter, yCenter, width, height);
            }
        }

        // Add invoice number and organization details (top left)
        doc.setFontSize(24);
        doc.setTextColor(0, 0, 0);
        doc.text('INVOICE', margin, 30);
        doc.setFontSize(16);
        doc.text(`#${invoicePrefix}${invoice.invoiceNumber}`, margin, 40);

        // Organization details (below invoice number)
        doc.setFontSize(12);
        const orgDetails = [
            organisation?.organisationName || '',
            organisation?.address || '',
            `ABN: ${organisation?.abn || ''}`,
            organisation?.website || ''
        ];
        doc.text(orgDetails, margin, 55);

        // Add invoice details and bill to info in two columns
        const leftColX = margin;
        const rightColX = pageWidth / 2 + 10;
        const detailsY = 90;

        // Left column - Invoice Details
        doc.setFontSize(12);
        doc.setFont(undefined, 'bold');
        doc.text('Invoice Details', leftColX, detailsY);
        doc.setFont(undefined, 'normal');
        doc.setFontSize(10);
        doc.text([
            `Invoice Date: ${new Date(invoice.invoiceDate).toLocaleDateString()}`,
            `Due Date: ${new Date(invoice.dueDate).toLocaleDateString()}`
        ], leftColX, detailsY + 10);

        // Right column - Bill To
        doc.setFontSize(12);
        doc.setFont(undefined, 'bold');
        doc.text('Bill To', rightColX, detailsY);
        doc.setFont(undefined, 'normal');
        doc.setFontSize(10);
        doc.text([
            customer?.name || '',
            customer?.addresses?.[0]?.address || '',
            this.parseContactInfo(customer?.emails?.[0]) || ''
        ], rightColX, detailsY + 10);

        // Add black divider line
        const dividerY = detailsY + 25;
        doc.setLineWidth(0.5);
        doc.line(margin, dividerY, pageWidth - margin, dividerY);

        // Parse and prepare items
        const parsedItems = this.parseInvoiceItems(invoice.items);

        // Add items table with pagination support
        const tableData = parsedItems.map(item => [
            item.description || '',
            this.formatCurrency(item.price),
            item.quantity?.toString() || '0',
            this.formatCurrency(item.discount),
            this.formatCurrency(item.total)
        ]);

        autoTable(doc, {
            startY: dividerY + 5,
            head: [['Description', 'Rate', 'Qty', 'Discount', 'Total']],
            body: tableData,
            styles: {
                fontSize: 8,
                cellPadding: 2
            },
            headStyles: {
                fillColor: [0, 0, 0],
                textColor: [255, 255, 255],
                fontStyle: 'bold'
            },
            columnStyles: {
                0: { cellWidth: 'auto' },
                1: { cellWidth: 30, halign: 'right' },
                2: { cellWidth: 20, halign: 'center' },
                3: { cellWidth: 30, halign: 'right' },
                4: { cellWidth: 30, halign: 'right' }
            },
            didParseCell: function (data) {
                if (data.row.section === 'head') {
                    if (data.column.index === 1) data.cell.styles.halign = 'right';
                    if (data.column.index === 2) data.cell.styles.halign = 'center';
                    if (data.column.index === 3) data.cell.styles.halign = 'right';
                    if (data.column.index === 4) data.cell.styles.halign = 'right';
                }
            },
            didDrawPage: function (data) {
                totalPages = doc.getNumberOfPages();
            },
            margin: {
                top: 10,
                right: margin,
                bottom: 45, // Add extra bottom margin for page numbers and totals
                left: margin
            }
        });

        // Get the final Y position after the table
        const finalY = (doc as any).lastAutoTable.finalY + 20;
        const currentPage = doc.getNumberOfPages();

        // Add totals to the last page
        doc.setPage(currentPage);

        // Check if we have enough space for totals and payment instructions
        const requiredSpace = 95; // approximate space needed for totals and payment instructions

        if (finalY + requiredSpace > pageHeight - margin) {
            // Not enough space, add a new page
            doc.addPage();
            totalPages++;
        }

        // Add a light gray background for totals section
        doc.setFillColor(245, 245, 245);
        const totalsY = (finalY + requiredSpace > pageHeight - margin) ? margin + 20 : finalY;
        this.addRoundedRect(doc, pageWidth - 90, totalsY - 10, 70, 40, 5);

        doc.setFontSize(14);
        doc.setFont(undefined, 'normal');
        doc.text('Subtotal:', pageWidth - 85, totalsY);
        doc.text('Tax:', pageWidth - 85, totalsY + 10);

        doc.setFont(undefined, 'bold');
        doc.text('TOTAL DUE:', pageWidth - 85, totalsY + 20);

        // Align the amounts to the right
        doc.setFont(undefined, 'normal');
        doc.text(this.formatCurrency(invoice.total), pageWidth - 25, totalsY, { align: 'right' });
        doc.text(this.formatCurrency(invoice.tax), pageWidth - 25, totalsY + 10, { align: 'right' });
        doc.setFont(undefined, 'bold');
        doc.text(this.formatCurrency(invoice.netTotal), pageWidth - 25, totalsY + 20, { align: 'right' });

        // Add payment instructions with a light gray background
        let instructionsY = totalsY + 40;

        // Check if we have enough space for payment instructions
        if (instructionsY + 60 > pageHeight - margin) {
            // Not enough space, add a new page
            doc.addPage();
            totalPages++;
            // Reset Y position for the new page
            instructionsY = margin + 20;
        }

        doc.setFillColor(245, 245, 245);
        this.addRoundedRect(doc, margin, instructionsY, pageWidth - (margin * 2), 50, 5);

        doc.setFont(undefined, 'bold');
        doc.setFontSize(12);
        doc.text('Payment Terms & Instructions:', margin + 5, instructionsY + 8);

        // Render HTML content from invoice.description
        doc.setFont(undefined, 'normal');
        doc.setFontSize(11);

        const parsedHtml = this.parseHtmlForPdf(invoice.description || '');
        let currentY = instructionsY + 15;

        // Render parsed HTML content
        parsedHtml.forEach(item => {
            // Check if we need a new page
            if (currentY > pageHeight - margin) {
                doc.addPage();
                totalPages++;
                currentY = margin + 10;
            }

            if (item.isBold) {
                doc.setFont(undefined, 'bold');
            } else {
                doc.setFont(undefined, 'normal');
            }

            doc.text(item.text, margin + 5, currentY);
            currentY += 5; // Move to next line with 5mm spacing
        });

        // Add standard payment text after the HTML content
        currentY += 2; // Add a little extra space

        // Check if we need a new page for standard text
        if (currentY + 15 > pageHeight - margin) {
            doc.addPage();
            totalPages++;
            currentY = margin + 10;
        }

        doc.setFont(undefined, 'normal');
        const paymentText = [
            `Please pay within ${dueDays} days of invoice date.`,
            'For any queries regarding this invoice, please contact our accounts department.',
            'Thank you for your business.'
        ];

        doc.text(paymentText, margin + 5, currentY);

        // Add page numbers if we have multiple pages
        if (totalPages > 1) {
            // Add page numbers to all pages
            for (let i = 1; i <= totalPages; i++) {
                doc.setPage(i);
                this.addPageNumber(doc, i, totalPages);
            }
        }

        // Return file as Blob if requested
        if (getFile) {
            return doc.output('blob');
        }

        // Open PDF in new window
        window.open(URL.createObjectURL(doc.output('blob')));
    }

    /**
     * Add page number in circular format to bottom right of page
     */
    private addPageNumber(doc: jsPDF, currentPage: number, totalPages: number): void {
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;

        // Draw circle at bottom right
        const circleRadius = 4;
        const circleX = pageWidth - 10;
        const circleY = pageHeight - 10;

        doc.setFillColor(245, 245, 245); // Light gray
        doc.setDrawColor(200, 200, 200); // Border color
        doc.circle(circleX, circleY, circleRadius, 'FD'); // Fill and draw

        // Add page numbers text
        doc.setTextColor(50, 50, 50);
        doc.setFontSize(10);
        doc.setFont(undefined, 'bold');

        // Center the text in the circle
        const text = `${currentPage}/${totalPages}`;
        const textWidth = doc.getStringUnitWidth(text) * 8 / doc.internal.scaleFactor;

        doc.text(
            text,
            circleX - (textWidth / 2),
            circleY + 1  // Slight adjustment to center vertically
        );
    }

    /**
     * Parse HTML content for PDF rendering
     * @param html HTML string to parse
     * @returns Array of text segments with formatting info
     */
    private parseHtmlForPdf(html: string): Array<{ text: string; isBold: boolean }> {
        // Create a temporary DOM element to parse the HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;

        const result: Array<{ text: string; isBold: boolean }> = [];

        // Process each paragraph
        const paragraphs = tempDiv.querySelectorAll('p');
        paragraphs.forEach(paragraph => {
            let paragraphText = '';
            let isBold = false;

            // Check if the entire paragraph is bold
            if (paragraph.querySelector('strong') &&
                paragraph.textContent === paragraph.querySelector('strong')?.textContent) {
                isBold = true;
                paragraphText = paragraph.textContent || '';
            } else {
                // Process mixed content
                Array.from(paragraph.childNodes).forEach(node => {
                    if (node.nodeType === Node.TEXT_NODE) {
                        // Text node
                        paragraphText += node.textContent || '';
                    } else if (node.nodeName === 'STRONG' || node.nodeName === 'B') {
                        // Bold text
                        const boldText = {
                            text: node.textContent || '',
                            isBold: true
                        };

                        // If we have accumulated text, add it first
                        if (paragraphText) {
                            result.push({ text: paragraphText, isBold: false });
                            paragraphText = '';
                        }

                        result.push(boldText);
                    }
                });

                // Add any remaining text
                if (paragraphText) {
                    result.push({ text: paragraphText, isBold: false });
                }

                // Skip adding the paragraph again if we processed mixed content
                return;
            }

            // Convert HTML entities like &nbsp; to actual spaces
            paragraphText = this.decodeHtmlEntities(paragraphText);

            // Add the paragraph
            result.push({ text: paragraphText, isBold });
        });

        return result;
    }

    /**
     * Convert HTML entities to their corresponding characters
     */
    private decodeHtmlEntities(text: string): string {
        const textArea = document.createElement('textarea');
        textArea.innerHTML = text;
        return textArea.value;
    }

    private parseInvoiceItems(items: any[]): InvoicingItem[] {
        return items.map(item => {
            if (typeof item === 'string') {
                try {
                    return JSON.parse(item) as InvoicingItem;
                } catch {
                    return this.createEmptyItem();
                }
            }
            return item as InvoicingItem;
        });
    }

    private createEmptyItem(): InvoicingItem {
        return {
            description: '',
            price: 0,
            quantity: 0,
            discount: 0,
            total: 0
        };
    }

    private addRoundedRect(doc: jsPDF, x: number, y: number, width: number, height: number, radius: number): void {
        doc.roundedRect(x, y, width, height, radius, radius, 'F');
    }

    private formatCurrency(value: number | undefined): string {
        return `$${(value || 0).toFixed(2)}`;
    }

    private parseContactInfo(contact: any): string {
        if (!contact) return '';
        if (typeof contact === 'string') {
            try {
                const parsed = JSON.parse(contact);
                return parsed.email || parsed.phoneNumber || '';
            } catch {
                return contact;
            }
        }
        return contact.email || contact.phoneNumber || '';
    }

    private calculateImageDimensions(
        originalWidth: number,
        originalHeight: number,
        maxWidth: number,
        maxHeight: number
    ): { width: number; height: number } {
        const aspectRatio = originalWidth / originalHeight;

        let newWidth = maxWidth;
        let newHeight = maxHeight;

        if (aspectRatio > 1) {
            // Image is wider than tall
            newWidth = maxWidth;
            newHeight = newWidth / aspectRatio;

            if (newHeight > maxHeight) {
                newHeight = maxHeight;
                newWidth = newHeight * aspectRatio;
            }
        } else {
            // Image is taller than wide
            newHeight = maxHeight;
            newWidth = newHeight * aspectRatio;

            if (newWidth > maxWidth) {
                newWidth = maxWidth;
                newHeight = newWidth / aspectRatio;
            }
        }

        return { width: newWidth, height: newHeight };
    }

    private async loadImage(path: string): Promise<{ dataUrl: string; width: number; height: number } | null> {
        try {
            const imageBlob = await firstValueFrom(this.http.get(path, { responseType: 'blob' }));
            const dataUrl = await new Promise<string>((resolve, reject) => {
                const reader = new FileReader();
                reader.onloadend = () => resolve(reader.result as string);
                reader.onerror = reject;
                reader.readAsDataURL(imageBlob);
            });

            // Get image dimensions
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => {
                    resolve({
                        dataUrl,
                        width: img.width,
                        height: img.height
                    });
                };
                img.onerror = reject;
                img.src = dataUrl;
            });
        } catch (error) {
            console.error('Error loading image:', error);
            return null;
        }
    }
}

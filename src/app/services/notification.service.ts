import { Injectable } from '@angular/core';
import { Subject, Observable } from 'rxjs';

export interface Notification {
    message: string;
    details?: string;
    duration?: number;
    icon?: string;
    iconColor?: string;
    messageColor?: string;
    action?: {
        label: string;
        callback: (dismiss: () => void) => void;
    };
}

@Injectable({
    providedIn: 'root',
})
export class NotificationService {
    private notificationsSubject = new Subject<Notification[]>();
    notifications$: Observable<Notification[]> = this.notificationsSubject.asObservable();
    private notifications: Notification[] = [];

    notify(notification: Notification): void {
        this.notifications.push(notification);
        this.notificationsSubject.next(this.notifications);
    }

    remove(notification: Notification): void {
        this.notifications = this.notifications.filter(n => n !== notification);
        this.notificationsSubject.next(this.notifications);
    }
}

import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { firstValueFrom } from 'rxjs';

@Injectable({
    providedIn: 'root',
})
export class ExportTableService {

    constructor(private http: HttpClient) { }

    exportMaterialTable(data: any[], format: 'pdf' | 'excel', fileName: string = 'exported_table', startDate?: string, endDate?: string, title?: string): void {
        if (format === 'pdf') {
            this.exportToPdf(data, fileName, startDate, endDate, title);
        } else if (format === 'excel') {
            this.exportToExcel(data, fileName);
        }
    }

    private async exportToPdf(data: any[], fileName: string, startDate?: string, endDate?: string, title?: string): Promise<void> {
        const doc = new jsPDF({
            orientation: 'landscape',
            unit: 'mm',
            format: 'a4',
        });

        // Load logo image (PNG)
        const logo = await this.loadImage('/images/logo/logo.png'); // Adjust the path to your PNG file
        const logoWidth = 40; // Adjust the width of the logo
        const logoHeight = 20; // Adjust the height of the logo

        // Add the PNG logo to the top-left corner
        if (logo) {
            doc.addImage(logo, 'PNG', 10, 10, logoWidth, logoHeight);
        }

        // Add start and end dates next to the logo if available
        if (startDate || endDate) {
            const dateRange = `${title} - Date Range: ${startDate ? startDate : ''} - ${endDate ? endDate : ''}`;
            doc.setFontSize(10); // Set smaller font size for the date range
            doc.text(dateRange, 60, 23); // Adjust the positioning of the date range
        }

        // Add table data below the logo and date
        autoTable(doc, {
            head: [Object.keys(data[0])],
            body: data.map(row => Object.values(row)),
            startY: 40, // Position the table below the logo and date
            styles: { halign: 'center' }, // Center the table on the page
        });

        doc.save(`${fileName}.pdf`);
    }

    private exportToExcel(data: any[], fileName: string): void {
        const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(data);
        const workbook: XLSX.WorkBook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
        XLSX.writeFile(workbook, `${fileName}.xlsx`);
    }

    private async loadImage(path: string): Promise<string | null> {
        try {
            // Fetch image from provided path
            const imageBlob = await firstValueFrom(this.http.get(path, { responseType: 'blob' }));
            const reader = new FileReader();
            return new Promise<string | null>((resolve, reject) => {
                reader.onloadend = () => resolve(reader.result as string);
                reader.onerror = reject;
                reader.readAsDataURL(imageBlob); // Convert image to base64 string
            });
        } catch (error) {
            console.error('Error loading image:', error);
            return null;
        }
    }
}

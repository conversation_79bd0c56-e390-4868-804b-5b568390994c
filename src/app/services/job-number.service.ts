// job-number.service.ts
import { Injectable } from '@angular/core';
import { ScheduledJobsService } from 'app/core/databaseModels/scheduledJobs/scheduledJob.service';
import { ScheduledJob } from 'app/core/databaseModels/scheduledJobs/scheduledJob.types';

@Injectable({
    providedIn: 'root'
})
export class JobNumberService {
    constructor(private readonly _schedulerService: ScheduledJobsService) { }

    async generateJobNumber(): Promise<string> {
        return new Promise((resolve, reject) => {
            this._schedulerService.getLastScheduledJob().subscribe({
                next: (lastJob: ScheduledJob | null) => {
                    try {
                        const today = new Date();

                        let nextNumber = 1;
                        if (lastJob && lastJob.jobNumber) {
                            const parts = lastJob.jobNumber.split('-');
                            if (parts.length === 4) { // Updated to match the new format
                                const lastNumberStr = parts[3];
                                const parsedNumber = parseInt(lastNumberStr, 10);
                                if (!isNaN(parsedNumber)) {
                                    nextNumber = parsedNumber + 1;
                                }
                            }
                        }

                        const jobNumber = this.formatJobNumber(today, nextNumber);
                        resolve(jobNumber);
                    } catch (error) {
                        console.error('Error generating job number:', error);
                        reject(error);
                    }
                },
                error: (error) => {
                    console.error('Error fetching last job:', error);
                    reject(error);
                }
            });
        });
    }

    private generateDateKey(date: Date): string {
        const year = date.getFullYear();
        const month = this.getMonthAbbreviation(date.getMonth());
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    private formatJobNumber(date: Date, number: number): string {
        const dateKey = this.generateDateKey(date);
        const formattedNumber = number.toString().padStart(5, '0'); // Updated to 5 digits
        return `${dateKey}-${formattedNumber}`;
    }

    private getMonthAbbreviation(monthIndex: number): string {
        const months = [
            'JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
            'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'
        ];
        return months[monthIndex];
    }
}

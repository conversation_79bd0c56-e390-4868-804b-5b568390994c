<div
    class="fixed lg:sticky top-0 bottom-0 lg:left-full w-full sm:w-96 lg:w-16 lg:h-screen lg:shadow"
>
    <div
        class="flex flex-col w-full sm:w-96 h-full transition-transform duration-400 ease-drawer bg-card"
        [ngClass]="{
            '-translate-x-full sm:-translate-x-96 lg:-translate-x-80 shadow':
                opened,
            'translate-x-0': !opened
        }"
    >
        <!-- Header -->
        <div
            class="quick-chat-header flex flex-0 items-center justify-start cursor-pointer"
        >
            <!-- Toggle -->
            @if (!opened || (opened && !selectedTeamMember)) {
                <div class="flex flex-auto items-center justify-center">
                    <!-- Chat Icon -->
                    <div
                        class="flex flex-0 items-center justify-center w-16"
                        (click)="toggle()"
                    >
                        <mat-icon
                            class="icon-size-6"
                            [svgIcon]="
                                'heroicons_outline:chat-bubble-left-right'
                            "
                        ></mat-icon>
                    </div>

                    <!-- Search Container -->
                    <div class="flex items-center relative search-wrapper">
                        <!-- Title and Search Input Container -->
                        @if (!searchActive) {
                            <div
                                class="flex items-center transition-all duration-300 ease-in-out"
                            >
                                <span
                                    class="text-lg font-medium text-secondary whitespace-nowrap"
                                    >Team Members Chat</span
                                >
                            </div>
                            <!-- Search Toggle Icon -->
                            <div
                                class="ml-3 cursor-pointer search-icon-wrapper"
                                (click)="toggleSearch()"
                            >
                                <i
                                    class="fas fa-search text-gray-500 hover:text-gray-700 transition-colors duration-200"
                                ></i>
                            </div>
                        } @else {
                            <!-- Search Input with Autocomplete -->
                            <div class="absolute left-0 w-full">
                                <div class="relative">
                                    <input
                                        type="text"
                                        class="w-full h-10 pl-10 pr-10 rounded-lg border border-gray-300 focus:outline-none focus:border-blue-500"
                                        placeholder="Search members..."
                                        #searchInput
                                        [(ngModel)]="searchQuery"
                                        (input)="onSearchInput($event)"
                                    />
                                    <i
                                        class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                                    ></i>
                                    <i
                                        class="fas fa-times absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 cursor-pointer"
                                        (click)="toggleSearch()"
                                    ></i>
                                </div>

                                <!-- Autocomplete Results -->
                                @if (
                                    searchActive &&
                                    filteredTeamMembers.length > 0
                                ) {
                                    <div
                                        class="absolute w-full mt-1 bg-white rounded-lg shadow-lg border border-gray-200 max-h-64 overflow-y-auto z-50"
                                    >
                                        @for (
                                            member of filteredTeamMembers;
                                            track member.$id
                                        ) {
                                            <div
                                                class="flex items-center px-4 py-2 cursor-pointer hover:bg-gray-100"
                                                (click)="
                                                    selectTeamMember(member)
                                                "
                                            >
                                                @if (member.avatar) {
                                                    <img
                                                        [src]="
                                                            previewTeamMemberFileUrl(
                                                                member.avatarImageId
                                                            )
                                                        "
                                                        class="w-8 h-8 rounded-full object-cover"
                                                        [alt]="member.name"
                                                    />
                                                } @else {
                                                    <div
                                                        class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center"
                                                    >
                                                        <span
                                                            class="text-sm font-medium"
                                                            >{{
                                                                getTeamMemberInitials(
                                                                    member.name
                                                                )
                                                            }}</span
                                                        >
                                                    </div>
                                                }
                                                <span class="ml-3">{{
                                                    member.name
                                                }}</span>
                                                @if (
                                                    member.unreadMessageCount >
                                                    0
                                                ) {
                                                    <div
                                                        class="ml-auto bg-blue-500 text-white text-xs rounded-full px-2 py-1"
                                                    >
                                                        {{
                                                            member.unreadMessageCount
                                                        }}
                                                    </div>
                                                }
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                        }
                    </div>

                    <!-- Close Button -->
                    <button
                        class="ml-auto mr-4"
                        mat-icon-button
                        (click)="toggle()"
                    >
                        <mat-icon
                            [svgIcon]="'heroicons_outline:x-mark'"
                        ></mat-icon>
                    </button>
                </div>
            }

            <!-- Contact info -->
            @if (opened && selectedTeamMember && teamMembers) {
                <div class="flex flex-auto items-center ml-3">
                    <div
                        class="relative flex flex-0 items-center justify-center w-10 h-10"
                    >
                        @if (selectedTeamMember.avatar) {
                            <img
                                class="w-full h-full rounded-full object-cover"
                                [src]="selectedTeamMember.avatar"
                                alt="Contact avatar"
                                placeholder
                            />
                        } @else {
                            <div
                                class="flex items-center justify-center w-full h-full rounded-full text-lg uppercase bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                            >
                                {{ selectedTeamMember.name.charAt(0) }}
                            </div>
                        }
                    </div>
                    <div class="ml-4 text-lg font-medium leading-5 truncate">
                        {{ selectedTeamMember.name }}
                    </div>
                    <button
                        class="ml-auto mr-4"
                        mat-icon-button
                        (click)="toggle()"
                    >
                        <mat-icon
                            [svgIcon]="'heroicons_outline:x-mark'"
                        ></mat-icon>
                    </button>
                </div>
            }
        </div>

        <!-- Content -->
        <div class="flex flex-auto border-t overflow-hidden">
            <!-- Chat list -->
            <div
                class="flex-0 w-16 h-full overflow-y-auto overscroll-y-contain sm:overflow-hidden sm:overscroll-auto"
                fuseScrollbar
                [fuseScrollbarOptions]="{ wheelPropagation: false }"
            >
                <div class="flex-auto">
                    @for (
                        teamMember of teamMembers;
                        track trackByFn($index, teamMember)
                    ) {
                        <div
                            class="flex items-center py-3 px-4 cursor-pointer"
                            [ngClass]="{
                                'hover:bg-gray-100 dark:hover:bg-hover':
                                    !selectedTeamMember ||
                                    selectedTeamMember.$id !== teamMember.$id,
                                'bg-primary-50 dark:bg-hover':
                                    selectedTeamMember &&
                                    selectedTeamMember.$id === teamMember.$id
                            }"
                            (click)="selectChat(teamMember.$id)"
                        >
                            <div
                                class="relative flex flex-0 items-center justify-center w-8 h-8"
                            >
                                @if (teamMember.unreadMessagesCount > 0) {
                                    <div
                                        class="ring-bg-card absolute bottom-0 right-0 -ml-0.5 h-2 w-2 flex-0 rounded-full bg-primary text-on-primary ring-2 dark:bg-primary-500 dark:ring-gray-900"
                                        [class.ring-primary-50]="
                                            selectedTeamMember &&
                                            selectedTeamMember.$id ===
                                                teamMember.$id
                                        "
                                    ></div>
                                }
                                @if (teamMember && teamMember.avatar) {
                                    <img
                                        class="w-full h-full rounded-full object-cover"
                                        [src]="
                                            previewTeamMemberFileUrl(
                                                teamMember.avatarImageId
                                            )
                                        "
                                        alt="Contact avatar"
                                        placeholder
                                    />
                                } @else if (teamMember && !teamMember.avatar) {
                                    <div
                                        class="flex items-center justify-center w-full h-full rounded-full text-lg uppercase bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                    >
                                        {{
                                            getTeamMemberInitials(
                                                teamMember.name
                                            )
                                        }}
                                    </div>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>

            <!-- Conversation -->
            @if (!selectedTeamMember) {
                <!-- Select chat or start new template -->
                <div
                    class="flex flex-col flex-auto items-center justify-center w-full h-full p-4"
                >
                    <mat-icon
                        class="icon-size-24"
                        [svgIcon]="
                            'heroicons_outline:chat-bubble-bottom-center-text'
                        "
                    ></mat-icon>
                    <div
                        class="mt-4 text-xl text-center font-medium tracking-tight text-secondary"
                    >
                        Select a conversation
                    </div>
                </div>
            } @else {
                <div
                    class="flex flex-col flex-auto border-l overflow-hidden bg-gray-50 dark:bg-transparent"
                >
                    @if (isLoadingMessages) {
                        <div
                            class="flex flex-auto items-center justify-center mt-20"
                        >
                            <ng-lottie
                                class="flex-col"
                                width="150px"
                                [options]="options"
                            />
                        </div>
                    } @else {
                        <div
                            class="flex flex-col-reverse overflow-y-auto overscroll-y-contain overflow-hidden"
                        >
                            <div class="flex flex-col flex-auto shrink p-6">
                                @for (
                                    message of teamMessages;
                                    track message;
                                    let i = $index;
                                    let first = $first;
                                    let last = $last
                                ) {
                                    @if (
                                        first ||
                                        (teamMessages[i - 1].createdAt
                                            | date: "d") !==
                                            (message.createdAt | date: "d")
                                    ) {
                                        <div
                                            class="flex items-center justify-center my-3 -mx-6"
                                        >
                                            <div
                                                class="flex-auto border-b"
                                            ></div>
                                            <div
                                                class="flex-0 mx-4 text-sm font-medium leading-5 text-secondary"
                                            >
                                                {{
                                                    message.createdAt
                                                        | date: "longDate"
                                                }}
                                            </div>
                                            <div
                                                class="flex-auto border-b"
                                            ></div>
                                        </div>
                                    }
                                    <div
                                        class="flex flex-col"
                                        [ngClass]="{
                                            'items-end':
                                                message.sender === 'platform',
                                            'items-start':
                                                message.sender !== 'platform',
                                            'mt-0.5':
                                                i > 0 &&
                                                teamMessages[i - 1].sender ===
                                                    message.sender,
                                            'mt-3':
                                                i > 0 &&
                                                teamMessages[i - 1].sender !==
                                                    message.sender
                                        }"
                                    >
                                        <!-- Bubble -->
                                        <div
                                            class="relative max-w-3/4 px-3 py-2 rounded-lg"
                                            [ngClass]="{
                                                'bg-blue-500 text-blue-50':
                                                    message.sender ===
                                                    'platform',
                                                'bg-gray-500 text-gray-50':
                                                    message.sender !==
                                                    'platform'
                                            }"
                                        >
                                            <!-- Speech bubble tail -->
                                            @if (
                                                last ||
                                                teamMessages[i + 1].sender !==
                                                    message.sender
                                            ) {
                                                <div
                                                    class="absolute bottom-0 w-3"
                                                    [ngClass]="{
                                                        'text-blue-500 -right-1 -mr-px mb-px':
                                                            message.sender ===
                                                            'platform',
                                                        'text-gray-500 -left-1 -ml-px mb-px -scale-x-1':
                                                            message.sender !==
                                                            'platform'
                                                    }"
                                                >
                                                    <ng-container
                                                        *ngTemplateOutlet="
                                                            speechBubbleExtension
                                                        "
                                                    ></ng-container>
                                                </div>
                                            }
                                            <!-- Message -->
                                            @if (
                                                message.messageType === "media"
                                            ) {
                                                <div class="flex flex-wrap">
                                                    <div
                                                        class="flex flex-col items-center m-3 ng-star-inserted"
                                                    >
                                                        @for (
                                                            file of message.media;
                                                            track file
                                                        ) {
                                                            @switch (
                                                                fileDetails(
                                                                    file
                                                                )
                                                                    .fileName.split(
                                                                        "."
                                                                    )
                                                                    .pop()
                                                            ) {
                                                                @case ("pdf") {
                                                                    <div
                                                                        class="flex items-center bg-gray-300 justify-center w-10 h-10 rounded-md overflow-hidden bg-primary-100 ng-star-inserted"
                                                                    >
                                                                        <div
                                                                            class="flex items-center justify-center text-sm font-semibold text-primary"
                                                                        >
                                                                            PDF
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="ml-3 mr-3 mb-5"
                                                                    >
                                                                        <div
                                                                            class="text-md font-medium overflow-hidden"
                                                                            [title]="
                                                                                fileDetails(
                                                                                    file
                                                                                )
                                                                                    .fileName
                                                                            "
                                                                        >
                                                                            {{
                                                                                fileDetails(
                                                                                    file
                                                                                )
                                                                                    .fileName
                                                                            }}
                                                                        </div>
                                                                        <div
                                                                            class="text-sm truncate text-white font-bold"
                                                                            title="Preview file"
                                                                        >
                                                                            <a
                                                                                [href]="
                                                                                    fileDetails(
                                                                                        file
                                                                                    )
                                                                                        .fileUrl
                                                                                "
                                                                                target="_blank"
                                                                                >Preview</a
                                                                            >
                                                                        </div>
                                                                    </div>
                                                                }
                                                                @case ("doc") {
                                                                    <div
                                                                        class="flex items-center justify-center w-10 h-10 rounded-md overflow-hidden bg-primary-100 ng-star-inserted"
                                                                    >
                                                                        <div
                                                                            class="flex items-center justify-center text-sm font-semibold text-primary"
                                                                        >
                                                                            DOC
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="ml-3 mr-3 mb-5"
                                                                    >
                                                                        <div
                                                                            class="text-md font-medium overflow-hidden"
                                                                            [title]="
                                                                                fileDetails(
                                                                                    file
                                                                                )
                                                                                    .fileName
                                                                            "
                                                                        >
                                                                            {{
                                                                                fileDetails(
                                                                                    file
                                                                                )
                                                                                    .fileName
                                                                            }}
                                                                        </div>
                                                                        <div
                                                                            class="text-sm truncate text-white font-bold"
                                                                            title="Preview file"
                                                                        >
                                                                            <a
                                                                                [href]="
                                                                                    fileDetails(
                                                                                        file
                                                                                    )
                                                                                        .fileUrl
                                                                                "
                                                                                target="_blank"
                                                                                >Preview</a
                                                                            >
                                                                        </div>
                                                                    </div>
                                                                }
                                                                @default {
                                                                    <div
                                                                        class="group relative mt-2"
                                                                    >
                                                                        <div
                                                                            class="absolute w-full h-full bg-gray-900/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg flex items-center justify-center"
                                                                        >
                                                                            <a
                                                                                [href]="
                                                                                    fileDetails(
                                                                                        file
                                                                                    )
                                                                                        .fileUrl
                                                                                "
                                                                                target="_blank"
                                                                                class="inline-flex items-center justify-center rounded-full h-10 w-10 bg-white/30 hover:bg-white/50 focus:ring-4 focus:outline-none dark:text-white focus:ring-gray-50"
                                                                            >
                                                                                <i
                                                                                    class="fa-duotone fa-download icon-size-5 text-white"
                                                                                ></i>
                                                                            </a>
                                                                        </div>
                                                                        <img
                                                                            class="w-60 rounded-lg overflow-hidden ng-star-inserted"
                                                                            [src]="
                                                                                previewFileUrl(
                                                                                    file
                                                                                )
                                                                            "
                                                                        />
                                                                    </div>
                                                                }
                                                            }
                                                        }
                                                    </div>
                                                </div>
                                            } @else {
                                                <div
                                                    class="min-w-4 leading-5"
                                                    [innerHTML]="
                                                        sanitizeContent(
                                                            message.value
                                                        )
                                                    "
                                                ></div>
                                            }
                                        </div>
                                        <!-- Time -->
                                        <div
                                            class="my-0.5 text-sm font-medium text-secondary"
                                            [ngClass]="{
                                                'mr-3':
                                                    message.sender ===
                                                    'platform',
                                                'ml-3':
                                                    message.sender !==
                                                    'platform'
                                            }"
                                        >
                                            {{
                                                message.createdAt
                                                    | date: "HH:mm"
                                            }}
                                        </div>
                                    </div>
                                } @empty {
                                    <div
                                        class="flex flex-auto items-center justify-center mt-20"
                                    >
                                        <h3
                                            class="text-lg font-medium text-center"
                                        >
                                            No messages
                                        </h3>
                                    </div>
                                }

                                @if (sending) {
                                    <div class="flex flex-col items-end">
                                        <div
                                            class="relative max-w-3/4 px-3 py-2 rounded-lg bg-blue-500 text-blue-50"
                                        >
                                            <div
                                                class="absolute bottom-0 w-3 text-blue-500 -right-1 -mr-px mb-px"
                                            >
                                                <ng-container
                                                    *ngTemplateOutlet="
                                                        speechBubbleExtension
                                                    "
                                                ></ng-container>
                                            </div>
                                            <div role="status">
                                                <svg
                                                    aria-hidden="true"
                                                    class="inline w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-white"
                                                    viewBox="0 0 100 101"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                    <path
                                                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                                        fill="currentColor"
                                                    />
                                                    <path
                                                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                                        fill="currentFill"
                                                    />
                                                </svg>
                                                <span class="sr-only"
                                                    >Loading...</span
                                                >
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>

                        <!-- Message field -->
                        <div
                            class="flex items-end p-4 border-t bg-gray-50 dark:bg-transparent"
                        >
                            <div class="flex items-center h-11 my-px">
                                <button
                                    mat-icon-button
                                    (click)="openBottomSheet()"
                                >
                                    <mat-icon
                                        [svgIcon]="
                                            'heroicons_outline:paper-clip'
                                        "
                                    ></mat-icon>
                                </button>
                            </div>
                            <mat-form-field
                                class="fuse-mat-dense fuse-mat-rounded fuse-mat-bold w-full"
                                [subscriptSizing]="'dynamic'"
                            >
                                <textarea
                                    matInput
                                    [(ngModel)]="inputValue"
                                    cdkTextareaAutosize
                                    #messageInput
                                    (keydown.enter)="
                                        !$event.shiftKey && sendMessage($event)
                                    "
                                    (keydown.shift.enter)="(true)"
                                >
                                </textarea>
                            </mat-form-field>
                            <div class="flex items-center h-11 my-px ml-4">
                                <button mat-icon-button (click)="sendMessage()">
                                    <mat-icon
                                        [svgIcon]="
                                            'heroicons_outline:paper-airplane'
                                        "
                                    ></mat-icon>
                                </button>
                            </div>
                        </div>
                    }
                </div>
            }
        </div>
    </div>
</div>

<!-- Speech bubble tail SVG -->
<!-- @formatter:off -->
<ng-template #speechBubbleExtension>
    <svg
        width="100%"
        height="100%"
        viewBox="0 0 66 66"
        xmlns="http://www.w3.org/2000/svg"
    >
        <g
            id="Page-1"
            stroke="none"
            stroke-width="1"
            fill="none"
            fill-rule="evenodd"
        >
            <path
                d="M1.01522827,0.516204834 C-8.83532715,54.3062744 61.7609863,70.5215302 64.8009949,64.3061218 C68.8074951,54.8859711 30.1663208,52.9997559 37.5036011,0.516204834 L1.01522827,0.516204834 Z"
                fill="currentColor"
                fill-rule="nonzero"
            ></path>
        </g>
    </svg>
</ng-template>
<!-- @formatter:on -->

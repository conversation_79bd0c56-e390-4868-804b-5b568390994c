import { ScrollStrategy, ScrollStrategyOptions } from '@angular/cdk/overlay';
import { TextFieldModule } from '@angular/cdk/text-field';
import { DatePipe, NgClass, NgTemplateOutlet } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, HostBinding, HostListener, Inject, NgZone, OnDestroy, OnInit, Renderer2, ViewChild, ViewEncapsulation, DOCUMENT } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import _, { forEach } from 'lodash';
import { <PERSON><PERSON>anitizer, SafeHtml } from '@angular/platform-browser';
import { FuseScrollbarDirective } from '@fuse/directives/scrollbar';
import { TeamChatMessagesService } from 'app/core/databaseModels/teamChatMessages/teamChatMessages.service';
import { TeamChatMessages } from 'app/core/databaseModels/teamChatMessages/teamChatMessages.types';
import { TeamMember } from 'app/core/databaseModels/teammembers/teammember.types';
import { TeamMembersService } from 'app/core/databaseModels/teammembers/teammembers.service';
import { AnimationOptions, LottieComponent } from 'ngx-lottie';
import { FileInputDropzoneModule } from 'app/modules/widgets/file-input-dropzone/file-input-dropzone.module';
import { FileInputDropzoneComponent } from 'app/modules/widgets/file-input-dropzone/file-input-dropzone.component';

import { catchError, combineLatest, finalize, of, Subject, switchMap, takeUntil, tap, throwError } from 'rxjs';
import { FcmNotification } from 'app/core/databaseModels/fcmNotification/fcmNotification.types';
import { FcmNotificationService } from 'app/core/databaseModels/fcmNotification/fcmNotification.service';

@Component({
    selector: 'quick-chat',
    templateUrl: './quick-chat.component.html',
    styleUrls: ['./quick-chat.component.scss'],
    encapsulation: ViewEncapsulation.None,
    exportAs: 'quickChat',
    imports: [LottieComponent, FormsModule, NgClass, MatIconModule, MatButtonModule, FuseScrollbarDirective, NgTemplateOutlet, MatFormFieldModule, MatInputModule, TextFieldModule, FileInputDropzoneModule, DatePipe]
})
export class QuickChatComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('messageInput') messageInput: ElementRef;
    @ViewChild('searchInput') searchInput!: ElementRef;

    isLoading: boolean = false;
    isLoadingMessages: boolean = false;
    selectedTeamMember: TeamMember;
    teamMembers: TeamMember[] = [];
    teamMessages: TeamChatMessages[] = [];
    opened: boolean = false;
    sending: boolean = false;
    searchActive = false;
    searchQuery: string = '';
    filteredTeamMembers: TeamMember[] = [];

    inputValue: string = '';

    fcmNotification: FcmNotification;

    options: AnimationOptions = {
        path: 'lottie/wired-complaint.json',
        autoplay: true,
        loop: true,

    };

    private _mutationObserver: MutationObserver;
    private _scrollStrategy: ScrollStrategy = this._scrollStrategyOptions.block();
    private _overlay: HTMLElement;
    private _unsubscribeAll: Subject<any> = new Subject<any>();

    /**
     * Constructor
     */
    constructor(
        @Inject(DOCUMENT) private _document: Document,
        private _elementRef: ElementRef,
        private _renderer2: Renderer2,
        private _ngZone: NgZone,
        private _teamChatMessagesService: TeamChatMessagesService,
        private _teamMemberService: TeamMembersService,
        private _scrollStrategyOptions: ScrollStrategyOptions,
        private _changeDetectorRef: ChangeDetectorRef,
        private _bottomSheet: MatBottomSheet,
        private sanitizer: DomSanitizer,
        private _fcmNotificationService: FcmNotificationService,
    ) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Decorated methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Host binding for component classes
     */
    @HostBinding('class') get classList(): any {
        return {
            'quick-chat-opened': this.opened,
        };
    }

    /**
     * Resize on 'input' and 'ngModelChange' events
     *
     * @private
     */
    @HostListener('input')
    @HostListener('ngModelChange')
    // private _resizeMessageInput(): void {
    //     // This doesn't need to trigger Angular's change detection by itself
    //     this._ngZone.runOutsideAngular(() => {
    //         setTimeout(() => {
    //             // Set the height to 'auto' so we can correctly read the scrollHeight
    //             this.messageInput.nativeElement.style.height = 'auto';

    //             // Get the scrollHeight and subtract the vertical padding
    //             this.messageInput.nativeElement.style.height = `${this.messageInput.nativeElement.scrollHeight}px`;
    //         });
    //     });
    // }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {

        this.loadTeamChats();
    }



    loadTeamChats() {
        this.isLoading = true;

        // Start by getting the team members
        this._teamMemberService.getTeamMembers().pipe(
            takeUntil(this._unsubscribeAll),
            // After getTeamMembers completes, call listenToRealTimeTeamMembers
            tap(() => this._teamMemberService.listenToRealTimeTeamMembers()),
            // Switch to teamMembers$ observable to listen for real-time updates
            switchMap(() => this._teamMemberService.teamMembers$.pipe(
                catchError(error => {
                    console.error('Error:', error);
                    return throwError(error);
                })
            )),
            takeUntil(this._unsubscribeAll)
        ).subscribe((teamMembers) => {
            this.teamMembers = teamMembers;
            this.isLoading = false;
            this._changeDetectorRef.markForCheck();
        });
    }



    /**
     * After view init
     */
    ngAfterViewInit(): void {
        // Fix for Firefox.
        //
        // Because 'position: sticky' doesn't work correctly inside a 'position: fixed' parent,
        // adding the '.cdk-global-scrollblock' to the html element breaks the navigation's position.
        // This fixes the problem by reading the 'top' value from the html element and adding it as a
        // 'marginTop' to the navigation itself.
        this._mutationObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                const mutationTarget = mutation.target as HTMLElement;
                if (mutation.attributeName === 'class') {
                    if (mutationTarget.classList.contains('cdk-global-scrollblock')) {
                        const top = parseInt(mutationTarget.style.top, 10);
                        this._renderer2.setStyle(this._elementRef.nativeElement, 'margin-top', `${Math.abs(top)}px`);
                    }
                    else {
                        this._renderer2.setStyle(this._elementRef.nativeElement, 'margin-top', null);
                    }
                }
            });
        });
        this._mutationObserver.observe(this._document.documentElement, {
            attributes: true,
            attributeFilter: ['class'],
        });
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Disconnect the mutation observer
        this._mutationObserver.disconnect();


        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Open the panel
     */
    open(): void {
        // Return if the panel has already opened
        if (this.opened) {
            return;
        }

        // Open the panel
        this._toggleOpened(true);
    }

    /**
     * Close the panel
     */
    close(): void {
        // Return if the panel has already closed
        if (!this.opened) {
            return;
        }

        // Close the panel
        this._toggleOpened(false);
    }

    /**
     * Toggle the panel
     */
    toggle(): void {
        if (this.opened) {
            this.close();
            this.selectedTeamMember = null;
        }
        else {
            this.open();
        }
    }



    /**
     * Track by function for ngFor loops
     *
     * @param index
     * @param item
     */
    trackByFn(index: number, item: any): any {
        return item.id || index;
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Private methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Show the backdrop
     *
     * @private
     */
    private _showOverlay(): void {
        // Try hiding the overlay in case there is one already opened
        this._hideOverlay();

        // Create the backdrop element
        this._overlay = this._renderer2.createElement('div');

        // Return if overlay couldn't be create for some reason
        if (!this._overlay) {
            return;
        }

        // Add a class to the backdrop element
        this._overlay.classList.add('quick-chat-overlay');

        // Append the backdrop to the parent of the panel
        this._renderer2.appendChild(this._elementRef.nativeElement.parentElement, this._overlay);

        // Enable block scroll strategy
        this._scrollStrategy.enable();

        // Add an event listener to the overlay
        this._overlay.addEventListener('click', () => {
            this.close();
        });
    }

    /**
     * Hide the backdrop
     *
     * @private
     */
    private _hideOverlay(): void {
        if (!this._overlay) {
            return;
        }

        // If the backdrop still exists...
        if (this._overlay) {
            // Remove the backdrop
            this._overlay.parentNode.removeChild(this._overlay);
            this._overlay = null;
        }

        // Disable block scroll strategy
        this._scrollStrategy.disable();
    }

    /**
     * Open/close the panel
     *
     * @param open
     * @private
     */
    private _toggleOpened(open: boolean): void {
        // Set the opened
        this.opened = open;

        // If the panel opens, show the overlay
        if (open) {
            this._showOverlay();
        }
        // Otherwise, hide the overlay
        else {
            this._hideOverlay();
        }
    }

    fileDetails(file: string): any {

        return JSON.parse(file);
    }

    previewFileUrl(file: string): string {
        const fileDetail = this.fileDetails(file);
        // if (fileDetail && fileDetail.fileUrl) {
        //     return fileDetail.fileUrl.replace('/view?', '/preview?width=250&');
        // }
        if (fileDetail && fileDetail.fileId) {
            return this._teamChatMessagesService.getChatFilePreview(fileDetail.fileId, 100, 100);
        }
        return '';
    }

    previewTeamMemberFileUrl(fileId: string): string {
        if (fileId) {
            return this._teamMemberService.getFilePreview(fileId, 50, 50);
        }
    }
    sanitizeContent(content: string): SafeHtml {

        const replacedContent = content.replace(/\n/g, '<br/>'); // Convert line breaks to <br>
        return this.sanitizer.bypassSecurityTrustHtml(replacedContent);
    }
    /**
    * Select the chat
    *
    * @param id
    */
    selectChat(id: string): void {
        // Open the panel
        this.teamMessages = [];
        this._toggleOpened(true);
        this.isLoadingMessages = true;
        this._changeDetectorRef.markForCheck();
        this.selectedTeamMember = this.teamMembers.find((team) => team.$id === id);

        if (this.selectedTeamMember) {
            this._teamChatMessagesService.listenToRealTimeTeamChatMessages(id);

            this._teamMemberService.updateUnreadMessage(this.selectedTeamMember.$id).subscribe();



            // Get the chat data

            this._teamChatMessagesService.getTeamChatMessages(id)
                .pipe(
                    switchMap(teamMessages => {
                        if (!teamMessages || teamMessages.length == 0) {
                            return of([]);
                        }
                        return this._teamChatMessagesService.teamChatMessages$.pipe(takeUntil(this._unsubscribeAll));
                    }),
                    takeUntil(this._unsubscribeAll)
                )

                .subscribe((teamMessages) => {

                    // Set the selected chat

                    this.teamMessages = teamMessages;
                    //  this._chatService.messages = messages;
                    this.isLoadingMessages = false;
                    this._changeDetectorRef.markForCheck();

                    this._changeDetectorRef.detectChanges();

                });
        }
    }
    sendMessage(): void {

        const savingMessage = this.inputValue.trim();
        this.inputValue = '';
        if (!savingMessage || !this.selectedTeamMember) return; // Ensure there's input and a contact

        this.sending = true;
        this._changeDetectorRef.markForCheck();

        // Create the message object
        const message: TeamChatMessages = {
            $id: null,
            organisationID: this.selectedTeamMember.organisationID,
            teamMemberID: this.selectedTeamMember.$id,
            sender: 'platform',
            value: savingMessage.trim(),
            createdAt: new Date(),
            messageType: 'text',
            messageStatus: 'sent',
            media: [],
        };
        this.sendNotification(savingMessage, '💭 Admin Message!', this.selectedTeamMember.deviceTokenId);
        this._teamChatMessagesService.createTeamChatMessage(message).pipe(
            finalize(() => {
                this.sending = false;
                this.inputValue = ''; // Reset input value regardless of operation result
                this._changeDetectorRef.markForCheck(); // Trigger change detection for UI update
            })
        ).subscribe({
            next: () => {
                // Update last message details on chat
                // this.chat.lastMessage = message.value;
                // this.chat.lastMessageAt = message.createdAt;
                // this.chat.unreadCount = 0;
                // this._chatService.updateChat(this.chat.$id, this.chat).subscribe();
                this._teamMemberService.updateTeamMemberUnreadMessage(this.selectedTeamMember.$id).subscribe();
            },
            error: (error) => console.error('Error sending message', error), // Handle any errors
        });
    }

    openBottomSheet(): void {
        const bottomSheetRef = this._bottomSheet.open(FileInputDropzoneComponent);

        bottomSheetRef.afterDismissed().subscribe(result => {
            if (result) {
                console.log('Files selected:', result);

                if (result.length > 0) {
                    this.sending = true;
                    this._changeDetectorRef.markForCheck();
                    const _attachedFiles = [];
                    forEach(result, (file) => {

                        this._teamChatMessagesService.uploadFile(file).subscribe((savedFile) => {
                            let fileObject = { fileName: file.name, fileUrl: savedFile.fileUrl, fileId: savedFile.fileId };
                            let fileString = JSON.stringify(fileObject);
                            _attachedFiles.push(fileString);
                            if (_attachedFiles.length == result.length && this.selectedTeamMember) {

                                const message: TeamChatMessages = {
                                    $id: null,
                                    organisationID: this.selectedTeamMember.organisationID,
                                    teamMemberID: this.selectedTeamMember.$id,
                                    sender: 'platform',
                                    value: this.inputValue,
                                    createdAt: new Date(),
                                    messageType: 'media',
                                    messageStatus: 'sent',
                                    media: _attachedFiles,
                                }

                                // Send Notification to team member
                                this.sendNotification('...', '💭 admin message ', this.selectedTeamMember.deviceTokenId, JSON.parse(_attachedFiles[0]).fileUrl);
                                this._teamChatMessagesService.createTeamChatMessage(message).subscribe(() => {
                                    this._teamMemberService.updateTeamMemberUnreadMessage(this.selectedTeamMember.$id).subscribe();
                                    this._changeDetectorRef.markForCheck();
                                    this.inputValue = '';
                                    this.sending = false;

                                })
                            }
                        })


                    })
                    // Here you can handle the selected files, such as updating the UI or processing the files
                }
            }
        });



    }
    sendNotification(body: string, title: string, to: string, imageUrl?: string) {
        this.fcmNotification = {
            body: body,
            title: title,
            to: to,
            imageUrl: imageUrl,
            type: 'chat'
        }
        this._fcmNotificationService.sendNotification(this.fcmNotification);
    }

    getTeamMemberInitials(name: string): string {
        if (!name) {
            return '';
        }
        const nameParts = name.trim().split(' ');
        if (nameParts.length > 1) {
            return nameParts[0].charAt(0) + nameParts[1].charAt(0);
        } else {
            return nameParts[0].charAt(0);
        }
    }
    onSearchInput(event: any): void {
        const query = event.target.value.toLowerCase();
        if (query) {
            this.filteredTeamMembers = this.teamMembers.filter(member =>
                member.name.toLowerCase().includes(query)
            );
        } else {
            this.filteredTeamMembers = [];
        }
    }
    toggleSearch(): void {
        this.searchActive = !this.searchActive;
        if (this.searchActive) {
            setTimeout(() => {
                this.searchInput.nativeElement.focus();
            }, 300);
        } else {
            this.searchQuery = '';
            this.filteredTeamMembers = [];
        }
    }

    selectTeamMember(member: TeamMember): void {
        this.searchQuery = '';
        this.searchActive = false;
        this.filteredTeamMembers = [];
        this.selectChat(member.$id);
    }

}

import { BooleanInput } from '@angular/cdk/coercion';
import { NgClass } from '@angular/common';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    Input,
    OnDestroy,
    OnInit,
    ViewEncapsulation,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { Router } from '@angular/router';
import { FuseConfig, FuseConfigService, Scheme } from '@fuse/services/config';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { User } from 'app/core/databaseModels/user/user.types';
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'user',
    templateUrl: './user.component.html',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    exportAs: 'user',
    imports: [
        MatButtonModule,
        MatMenuModule,
        MatIconModule,
        NgClass,
        MatDividerModule,
    ]
})
export class UserComponent implements OnInit, OnDestroy {
    /* eslint-disable @typescript-eslint/naming-convention */
    static ngAcceptInputType_showAvatar: BooleanInput;
    /* eslint-enable @typescript-eslint/naming-convention */

    @Input() showAvatar: boolean = true;
    user: User;
    config: FuseConfig;
    scheme: 'dark' | 'light';

    private _unsubscribeAll: Subject<any> = new Subject<any>();

    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _router: Router,
        private _userService: UserService,
        private _fuseConfirmationService: FuseConfirmationService,
        private _fuseConfigService: FuseConfigService

    ) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {

        // this For update test AppWrite

        // this.user = { id: 'cfaad35d-07a3-4447-a6c3-d8c3d54fd5df', name: 'Amirreza Ya', email: '<EMAIL>', avatar: 'assets/images/avatars/brian-hughes.jpg', status: 'online' };
        // Subscribe to user changes

        this._userService.user$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((user: User) => {
                this.user = user;
                //console.log('user', user);
                // Mark for check
                this._changeDetectorRef.markForCheck();
            });

        // Subscribe to config changes
        this._fuseConfigService.config$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((config: FuseConfig) => {
                // Store the config
                this.config = config;
            });
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Update the user status
     *
     * @param status
     */
    updateUserStatus(status: string): void {
        // Return if user is not available
        if (!this.user) {
            return;
        }

        // Update the user
        // this._userService.update({
        //     ...this.user,
        //     userOnlineStatus,
        // }).subscribe();
    }

    /**
     * Sign out
     */
    signOut(): void {
        const deleting = this._fuseConfirmationService.open({
            title: "Signing out...",
            message: "Please wait while we delete your session.",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "info"
            },
            actions: {
                confirm: {
                    show: false,
                    label: "Remove",
                    color: "warn"
                },
                cancel: {
                    show: false,
                    label: "Cancel"
                }
            },
            dismissible: false
        });
        this._router.navigate(['/sign-out']).then(() => {
            deleting.close();
        });
    }
    adminTools(): void {

        this._router.navigate(['/tools']);
    }

    settings(): void {
        this._router.navigate(['/settings']);
    }

    setScheme(scheme: Scheme): void {
        this._fuseConfigService.config = { scheme };
    }

}

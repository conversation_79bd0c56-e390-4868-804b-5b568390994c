<!-- Button -->
<button mat-icon-button [matMenuTriggerFor]="userActions">
    <span class="relative">
        @if (showAvatar && user.avatar) {
            <img class="h-7 w-7 rounded-full" [src]="user.avatar" />
        }
        @if (!showAvatar || !user.avatar) {
            <mat-icon [svgIcon]="'heroicons_outline:user-circle'"></mat-icon>
        }
        <span
            class="absolute bottom-0 right-0 h-2 w-2 rounded-full"
            [ngClass]="{
                'mb-px mr-px': !showAvatar || !user.avatar,
                'bg-green-500': user.userOnlineStatus === 'online',
                'bg-amber-500': user.userOnlineStatus === 'away',
                'bg-red-500': user.userOnlineStatus === 'busy',
                'bg-gray-400': user.userOnlineStatus === 'not-visible'
            }"
        ></span>
    </span>
</button>

<mat-menu [xPosition]="'before'" #userActions="matMenu">
    <button mat-menu-item>
        <span class="flex flex-col leading-none">
            <span>Signed in as</span>
            <span class="mt-1.5 text-md font-medium">{{ user.email }}</span>
        </span>
    </button>
    <mat-divider class="my-2" />
    <button mat-menu-item (click)="adminTools()">
        <mat-icon [svgIcon]="'heroicons_outline:wrench-screwdriver'"></mat-icon>
        <span>Admin Tools</span>
    </button>
    <button mat-menu-item (click)="settings()">
        <mat-icon [svgIcon]="'heroicons_outline:cog-8-tooth'"></mat-icon>
        <span>Settings</span>
    </button>
    <button mat-menu-item [matMenuTriggerFor]="userStatus">
        <mat-icon
            [svgIcon]="'heroicons_outline:ellipsis-horizontal-circle'"
        ></mat-icon>
        <span>Status</span>
    </button>
    <div class="mt-2 p-2 grid grid-cols-3 justify-items-start gap-3">
        <!-- Auto -->
        <div
            class="bg-hover flex cursor-pointer items-center rounded-full px-2 py-1 text-xs font-medium ring-inset ring-primary"
            [class.ring-2]="config.scheme === 'auto'"
            matTooltip="Automatically sets the scheme based on user's operating system's color scheme preference using 'prefer-color-scheme' media query."
            (click)="setScheme('auto')"
        >
            <div class="flex items-center overflow-hidden rounded-full">
                <mat-icon
                    class="icon-size-5"
                    [svgIcon]="'heroicons_solid:bolt'"
                ></mat-icon>
            </div>
            <div
                class="ml-2 flex items-center font-medium leading-5"
                [class.text-secondary]="config.scheme !== 'auto'"
            >
                Auto
            </div>
        </div>
        <!-- Dark -->
        <div
            class="bg-hover flex cursor-pointer items-center rounded-full px-2 py-1 text-xs font-medium pl-2 pr-2 ring-inset ring-primary"
            [class.ring-2]="config.scheme === 'dark'"
            (click)="setScheme('dark')"
        >
            <div class="flex items-center overflow-hidden rounded-full">
                <mat-icon
                    class="icon-size-5"
                    [svgIcon]="'heroicons_solid:moon'"
                ></mat-icon>
            </div>
            <div
                class="ml-2 flex items-center font-medium leading-5"
                [class.text-secondary]="config.scheme !== 'dark'"
            >
                Dark
            </div>
        </div>
        <!-- Light -->
        <div
            class="bg-hover flex cursor-pointer items-center rounded-full px-2 py-1 text-xs font-medium pl-2 pr-2 ring-inset ring-primary"
            [class.ring-2]="config.scheme === 'light'"
            (click)="setScheme('light')"
        >
            <div class="flex items-center overflow-hidden rounded-full">
                <mat-icon
                    class="icon-size-5"
                    [svgIcon]="'heroicons_solid:sun'"
                ></mat-icon>
            </div>
            <div
                class="ml-2 flex items-center font-medium leading-5"
                [class.text-secondary]="config.scheme !== 'light'"
            >
                Light
            </div>
        </div>
    </div>
    <mat-divider class="my-2" />
    <button mat-menu-item (click)="signOut()">
        <mat-icon
            [svgIcon]="'heroicons_outline:arrow-right-on-rectangle'"
        ></mat-icon>
        <span>Sign out</span>
    </button>
</mat-menu>

<mat-menu class="user-status-menu" #userStatus="matMenu">
    <button mat-menu-item (click)="updateUserStatus('online')">
        <span class="mr-3 inline-flex h-4 w-4 rounded-full bg-green-500"></span>
        <span>Online</span>
    </button>
    <button mat-menu-item (click)="updateUserStatus('away')">
        <span class="mr-3 inline-flex h-4 w-4 rounded-full bg-amber-500"></span>
        <span>Away</span>
    </button>
    <button mat-menu-item (click)="updateUserStatus('busy')">
        <span class="mr-3 inline-flex h-4 w-4 rounded-full bg-red-500"></span>
        <span>Busy</span>
    </button>
    <button mat-menu-item (click)="updateUserStatus('not-visible')">
        <span class="mr-3 inline-flex h-4 w-4 rounded-full bg-gray-400"></span>
        <span>Invisible</span>
    </button>
</mat-menu>


import { Route } from '@angular/router';
import { initialDataResolver } from 'app/app.resolvers';
import { AuthGuard } from 'app/core/auth/appwrite.auth.guard';
import { NoAuthGuard } from 'app/core/auth/appwrite.noAuth.guard';
import { LayoutComponent } from 'app/layout/layout.component';
import { UnauthorisedComponent } from './modules/admin/error/unauthorised/unauthorised.component';
import { SuperAdminGuard } from './core/auth/super-admin.guard';

export const appRoutes: Route[] = [
    // Routes for non-authenticated users
    {
        path: '',
        canActivate: [NoAuthGuard],
        component: LayoutComponent,
        data: { layout: 'empty' },
        children: [
            { path: '', pathMatch: 'full', loadChildren: () => import('app/modules/landing/landing.routes') },
            { path: 'home', loadChildren: () => import('app/modules/landing/landing.routes') },
            { path: 'confirmation-required', loadChildren: () => import('app/modules/auth/confirmation-required/confirmation-required.routes') },
            { path: 'forgot-password', loadChildren: () => import('app/modules/auth/forgot-password/forgot-password.routes') },
            { path: 'reset-password', loadChildren: () => import('app/modules/auth/reset-password/reset-password.routes') },
            { path: 'sign-in', loadChildren: () => import('app/modules/auth/sign-in/sign-in.routes') },
            { path: 'sign-up', loadChildren: () => import('app/modules/auth/sign-up/sign-up.routes') },
            { path: 'trial-expired', loadChildren: () => import('app/modules/auth/trial-expired/trial-expired.routes') },
            { path: 'subscription-expired', loadChildren: () => import('app/modules/auth/subscription-expired/subscription-expired.routes') },
            { path: 'unauthorised', component: UnauthorisedComponent }
        ]
    },

    // Routes for authenticated users
    {
        path: '',
        canActivate: [AuthGuard],
        component: LayoutComponent,
        resolve: { initialData: initialDataResolver },
        children: [
            { path: '', pathMatch: 'full', loadChildren: () => import('app/modules/admin/dashboard/dashboard.routes') },
            { path: 'dashboard', loadChildren: () => import('app/modules/admin/dashboard/dashboard.routes') },
            { path: 'livemap', loadChildren: () => import('app/modules/admin/livemap/livemap.routes') },
            { path: 'scheduler', loadChildren: () => import('app/modules/admin/scheduler/scheduler.routes') },
            { path: 'reports', loadChildren: () => import('app/modules/admin/reports/reports.routes') },
            { path: 'tools', loadChildren: () => import('app/modules/admin/tools/tools.routes') },
            { path: 'chat', loadChildren: () => import('app/modules/admin/chat/chat.routes') },
            { path: 'servicing', loadChildren: () => import('app/modules/admin/servicing/servicing.routes') },
            { path: 'expenses', loadChildren: () => import('app/modules/admin/expenses/expenses.routes') },
            { path: 'teamMemberShifts', loadChildren: () => import('app/modules/admin/driver-shifts/driver-shifts.routes') },
            { path: 'example', loadChildren: () => import('app/modules/admin/example/example.routes') },
            { path: 'help-center', loadChildren: () => import('app/modules/admin/help-center/help-center.routes') },
            { path: 'settings', loadChildren: () => import('app/modules/admin/settings/settings.routes') },
            { path: 'invoicing', loadChildren: () => import('app/modules/admin/invoicing/invoicing.routes') }
        ]
    },

    // SUPER ADMIN ROUTES
    {
        path: '',
        canActivate: [AuthGuard, SuperAdminGuard],
        component: LayoutComponent,
        resolve: { initialData: initialDataResolver },
        children: [
            { path: 'super-admin', loadChildren: () => import('app/modules/super-admin/super-admin.routes') }
        ]
    },

    //Auth routes for authenticated users
    {
        path: '',
        canActivate: [AuthGuard],
        canActivateChild: [AuthGuard],
        component: LayoutComponent,
        data: {
            layout: 'empty'
        },
        children: [
            { path: 'sign-out', loadChildren: () => import('app/modules/auth/sign-out/sign-out.routes') },
            { path: 'unlock-session', loadChildren: () => import('app/modules/auth/unlock-session/unlock-session.routes') }
        ]
    },
    // Redirect signed-in user to the '/dashboard'
    { path: 'signed-in-redirect', pathMatch: 'full', redirectTo: 'dashboard' },


    // Unauthorized route
    {
        path: 'unauthorized',
        component: UnauthorisedComponent,
        data: { layout: 'empty' }
    },
    // Fallback Routes for authenticated users
    { path: '**', canActivate: [AuthGuard], loadChildren: () => import('app/modules/admin/dashboard/dashboard.routes') },

    // Fallback Routes for non-authenticated users
    { path: '**', canActivate: [NoAuthGuard], loadChildren: () => import('app/modules/landing/landing.routes') }
];




// import { Route } from '@angular/router';
// import { initialDataResolver } from 'app/app.resolvers';
// import { AuthGuard } from 'app/core/auth/appwrite.auth.guard';
// import { NoAuthGuard } from 'app/core/auth/appwrite.noAuth.guard';
// import { LayoutComponent } from 'app/layout/layout.component';

// // @formatter:off
// /* eslint-disable max-len */
// /* eslint-disable @typescript-eslint/explicit-function-return-type */
// export const appRoutes: Route[] = [

//     // Redirect empty path to '/dashboard'
//     { path: '', pathMatch: 'full', redirectTo: 'dashboard' },

//     // Redirect signed-in user to the '/dashboard'
//     //
//     // After the user signs in, the sign-in page will redirect the user to the 'signed-in-redirect'
//     // path. Below is another redirection for that path to redirect the user to the desired
//     // location. This is a small convenience to keep all main routes together here on this file.
//     { path: 'signed-in-redirect', pathMatch: 'full', redirectTo: 'dashboard' },

//     // Auth routes for guests
//     {
//         path: '',
//         canActivate: [NoAuthGuard],
//         canActivateChild: [NoAuthGuard],
//         component: LayoutComponent,
//         data: {
//             layout: 'empty'
//         },
//         children: [
//             { path: 'confirmation-required', loadChildren: () => import('app/modules/auth/confirmation-required/confirmation-required.routes') },
//             { path: 'forgot-password', loadChildren: () => import('app/modules/auth/forgot-password/forgot-password.routes') },
//             { path: 'reset-password', loadChildren: () => import('app/modules/auth/reset-password/reset-password.routes') },
//             { path: 'sign-in', loadChildren: () => import('app/modules/auth/sign-in/sign-in.routes') },
//             { path: 'sign-up', loadChildren: () => import('app/modules/auth/sign-up/sign-up.routes') }
//         ]
//     },

//     // Auth routes for authenticated users
//     {
//         path: '',
//         canActivate: [AuthGuard],
//         canActivateChild: [AuthGuard],
//         component: LayoutComponent,
//         data: {
//             layout: 'empty'
//         },
//         children: [
//             { path: 'sign-out', loadChildren: () => import('app/modules/auth/sign-out/sign-out.routes') },
//             { path: 'unlock-session', loadChildren: () => import('app/modules/auth/unlock-session/unlock-session.routes') }
//         ]
//     },

//     // Landing routes
//     {
//         path: '',
//         component: LayoutComponent,
//         data: {
//             layout: 'empty'
//         },
//         children: [
//             { path: 'home', loadChildren: () => import('app/modules/landing/landing.routes') },
//         ]
//     },


//     //SUPE ADMIN ROUTES
//     {
//         path: '',
//         canActivate: [AuthGuard],
//         canActivateChild: [AuthGuard],
//         component: LayoutComponent,
//         resolve: {
//             initialData: initialDataResolver
//         },
//         children: [
//             { path: 'super-admin', loadChildren: () => import('app/modules/admin/super-admin/super-admin.routes') },
//         ]
//     },

//     // Admin routes

//     {
//         path: '',
//         canActivate: [AuthGuard],
//         canActivateChild: [AuthGuard],
//         component: LayoutComponent,
//         resolve: {
//             initialData: initialDataResolver
//         },
//         children: [

//             // App Routes
//             { path: '', loadChildren: () => import('app/modules/admin/dashboard/dashboard.routes') },
//             { path: 'dashboard', loadChildren: () => import('app/modules/admin/dashboard/dashboard.routes') },
//             { path: 'livemap', loadChildren: () => import('app/modules/admin/livemap/livemap.routes') },
//             { path: 'scheduler', loadChildren: () => import('app/modules/admin/scheduler/scheduler.routes') },
//             { path: 'reports', loadChildren: () => import('app/modules/admin/reports/reports.routes') },
//             { path: 'tools', loadChildren: () => import('app/modules/admin/tools/tools.routes') },
//             { path: 'chat', loadChildren: () => import('app/modules/admin/chat/chat.routes') },
//             { path: 'servicing', loadChildren: () => import('app/modules/admin/servicing/servicing.routes') },
//             { path: 'expenses', loadChildren: () => import('app/modules/admin/expenses/expenses.routes') },
//             { path: 'driverShifts', loadChildren: () => import('app/modules/admin/driver-shifts/driver-shifts.routes') },
//             { path: 'example', loadChildren: () => import('app/modules/admin/example/example.routes') },
//             { path: 'help-center', loadChildren: () => import('app/modules/admin/help-center/help-center.routes') },
//             { path: 'settings', loadChildren: () => import('app/modules/admin/settings/settings.routes') },
//             // Default Routes

//             { path: '**', loadChildren: () => import('app/modules/admin/dashboard/dashboard.routes') },



//         ]
//     }
// ];

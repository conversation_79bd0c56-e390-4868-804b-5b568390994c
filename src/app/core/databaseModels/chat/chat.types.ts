import { Customer } from "../customers/contacts.types";

export interface Chat {
    $id: string,
    organisationID: string,
    contactId: string,
    unreadCount?: number,
    muted?: boolean,
    lastMessage?: string,
    lastMessageAt?: Date,
    contact?: Customer
}

export interface ChatMessages {
    $id: string,
    organisationID: string,
    chatId: string,
    sender: string,
    value: string,
    createdAt: Date,
    messageType: string,
    media: string[],
    messageStatus: string,

}

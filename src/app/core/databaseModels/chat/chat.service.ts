import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, forkJoin, map, Observable, of, switchMap, take, tap, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { Chat, ChatMessages } from './chat.types'
import { Customer } from '../customers/contacts.types';
import { Organisation } from '../organisations/organisations.types';

@Injectable({
    providedIn: 'root'
})
export class ChatService {
    private _chat = new BehaviorSubject<Chat | null>(null);
    private _chats = new BehaviorSubject<Chat[]>([]);
    private _message = new BehaviorSubject<ChatMessages | null>(null);
    private _messages = new BehaviorSubject<ChatMessages[]>([]);
    private _allMessages = new BehaviorSubject<ChatMessages[]>([]);
    private _contact = new BehaviorSubject<Customer | null>(null);
    private _contacts = new BehaviorSubject<Customer[]>([]);
    private _profile = new BehaviorSubject<Organisation | null>(null);

    constructor(private readonly aws: AppwriteService, private _userService: UserService,) {
    }
    public unsubscribe(): void {
        this.aws.unsubscribe();
    }
    getOrganisationID(): Observable<string> {
        return this._userService.user$.pipe(
            take(1),
            map(user => user.organisationID),
            catchError(this.handleError<string>('Failed to get organisation ID'))
        );
    }



    get chat$(): Observable<Chat | null> { return this._chat.asObservable(); }
    get chats$(): Observable<Chat[]> { return this._chats.asObservable(); }
    get message$(): Observable<ChatMessages | null> { return this._message.asObservable(); }
    get messages$(): Observable<ChatMessages[]> { return this._messages.asObservable(); }
    get allMessages$(): Observable<ChatMessages[]> { return this._allMessages.asObservable(); }
    get contact$(): Observable<Customer | null> { return this._contact.asObservable(); }
    get contacts$(): Observable<Customer[]> { return this._contacts.asObservable(); }
    get profile$(): Observable<Organisation | null> { return this._profile.asObservable(); }

    set chat(chat: Chat) {
        this._chat.next(chat);
    }



    set chats(chats: Chat[]) {
        this._chats.next(chats);
    }




    set allMessages(allMessages: ChatMessages[]) {
        this._allMessages.next(allMessages);
    }


    set message(message: ChatMessages) {
        this._message.next(message);
    }


    set messages(messages: ChatMessages[]) {
        this._messages.next(messages);
    }





    /**
    * Get contact
    *
    * @param id
    */
    getContact(id: string): Observable<Customer> {
        return this.aws.getDocument<Customer>(this.aws.customersID, id).pipe(
            tap(contact => this._contact.next(contact)),
            catchError(this.handleError<Customer>('Error fetching contact'))
        );
    }

    /**
     * Get contacts
     */
    getContacts(): Observable<Customer[]> {
        return this.getOrganisationID().pipe(
            switchMap(organisationID => {
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.orderAscQuery('name'),
                ];
                return this.aws.getDocumentList<Customer>(this.aws.customersID, queries).pipe(
                    map(response => response.map(item => {

                        return { $id: item.id, ...item.data }
                    })),
                    tap((contacts) => {
                        this._contacts.next(contacts);
                    }),

                );
            }),
        )
    }

    /**
     * Get profile
     */
    getProfile(): Observable<any> {
        return this.getOrganisationID().pipe(
            switchMap((organisationID) => {
                return this.aws.getDocument<Organisation>(this.aws.organisationsID, organisationID).pipe(
                    tap((profile) => {
                        this._profile.next({ $id: organisationID, ...profile });
                    }),
                );
            })
        )
    }

    // Utilizing a more refined approach in fetching chats
    getChats(): Observable<Chat[]> {
        return this.getOrganisationID().pipe(
            switchMap(organisationID => this.aws.getDocumentList<Chat>(this.aws.chatID, [
                this.aws.equalQuery("organisationID", [organisationID]),
                this.aws.orderDescQuery('lastMessageAt'), // Changed to DESC to show newest first
            ])),
            map(response => response.map(item => ({ $id: item.id, ...item.data }))),
            switchMap(chats => chats.length ?
                forkJoin(chats.map(chat => this.enrichChatWithCustomer(chat))) :
                of([])
            ),
            tap(chats => {
                // Only update if chats have changed
                const currentChats = this._chats.getValue();
                if (JSON.stringify(currentChats) !== JSON.stringify(chats)) {
                    this._chats.next(chats);
                }
            }),
            catchError(this.handleError<Chat[]>('getChats', []))
        );
    }

    // Streamlined method for fetching and enriching a single chat
    getChat(id: string): Observable<Chat | null> {

        return this.aws.getDocument<Chat>(this.aws.chatID, id,).pipe(
            tap(chat => chat && this._chat.next({ $id: id, ...chat })),
            switchMap(chat => chat?.contactId ? this.enrichChatWithCustomer(chat) : of(chat)),
            catchError(this.handleError<Chat>('getChat'))
        );
    }

    // Refinement in enrichChatWithCustomer to avoid repetitive error handling
    private enrichChatWithCustomer(chat: Chat): Observable<Chat> {
        return this.aws.getDocument<Customer>(this.aws.customersID, chat.contactId).pipe(
            map(customer => ({ ...chat, contact: customer })),
            tap(updatedChat => this._chat.next(updatedChat)),
            catchError(this.handleError<Chat>('enrichChatWithCustomer', chat))
        );
    }



    createChat(chat: Chat): Observable<Chat> {

        const { contact, ...chatWithoutContact } = chat;
        return this.chats$.pipe(
            take(1),
            switchMap((chats) => {
                return this.aws.addDocument(this.aws.chatID, chatWithoutContact).pipe(
                    map((newChat) => {
                        const _newChat: Chat = { $id: newChat.id, contact: contact, ...newChat.data as Chat }

                        this._chats.next([...chats, _newChat]);
                        return _newChat;
                    })
                )

            })
        )
    }

    updateChat(chatID: string, data: Chat): Observable<Chat> {
        // Create a shallow copy of data without the contact attribute to avoid direct mutation
        const { contact, ...dataWithoutContact } = data;

        return this.aws.updateDocument<Chat>(this.aws.chatID, chatID, dataWithoutContact).pipe(
            tap(updatedChat => {
                // Reassign the contact attribute to the updated chat object for local use
                updatedChat.contact = contact;
                // Update the local BehaviorSubjects appropriately
                this._chat.next(updatedChat); // Assuming _chat is a BehaviorSubject for the current chat
            }),
            switchMap(updatedChat => {
                // Update the chats array with the updated chat
                return this._chats.pipe(
                    take(1),
                    map(chats => {
                        const index = chats.findIndex(item => item.$id === chatID);
                        if (index !== -1) {
                            chats[index] = { ...updatedChat, contact }; // Ensure the contact is included in the updated chat in the array
                        }
                        // It's usually not a good idea to mutate the array directly; consider using a BehaviorSubject to emit the new array instead
                        return updatedChat;
                    })
                );
            }),
            catchError(this.handleError<Chat>('updateChat'))
        );
    }


    deleteChat(chatID: string): Observable<void> {
        return this.aws.deleteDocument(this.aws.chatID, chatID).pipe(
            switchMap(() => this._chats.pipe(take(1))), // Take the latest chat list once
            map(chats => {
                // Find and remove the chat from the list
                const index = chats.findIndex(item => item.$id === chatID);
                if (index !== -1) {
                    const updatedChats = [...chats.slice(0, index), ...chats.slice(index + 1)];
                    this._chats.next(updatedChats); // Update the BehaviorSubject with the new chats array
                }
                return; // This function doesn't need to return anything
            }),
            catchError(error => {
                console.error('Error deleting chat:', error);
                return throwError(() => new Error('Failed to delete chat'));
            })
        );
    }




    getMessages(chatID: string): Observable<ChatMessages[]> {
        return this.getOrganisationID().pipe(
            switchMap((organisationID) => {
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.equalQuery("chatId", [chatID]),
                    this.aws.orderAscQuery('createdAt'),
                    this.aws.limitQuery(5000),
                ];
                return this.aws.getDocumentList<ChatMessages>(this.aws.chatMessagesID, queries).pipe(
                    map(response => response.map(item => {

                        return { $id: item.id, ...item.data }
                    })),
                    tap((messages) => {
                        this.messages = messages;
                    }),
                );
            }),
        )
    }


    createMessage(message: ChatMessages): Observable<ChatMessages> {
        return this.messages$.pipe(
            take(1),
            switchMap((messages) =>
                this.aws.addDocument(this.aws.chatMessagesID, message).pipe(
                    map((newMessage) => {
                        const _newMessage: ChatMessages = { $id: newMessage.id, ...newMessage.data as ChatMessages }

                        this._messages.next([...messages, _newMessage]);
                        return _newMessage;
                    })
                )
            )
        )
    }

    updateMessage(messageID: string, data: ChatMessages): Observable<ChatMessages> {
        return this.aws.updateDocument<ChatMessages>(this.aws.chatMessagesID, messageID, data).pipe(
            switchMap(updatedMessage => {
                this._message.next(updatedMessage);
                return this._messages.pipe(
                    take(1),
                    map(messages => {
                        const index = messages.findIndex(item => item.$id === messageID);
                        if (index !== -1) {
                            messages[index] = updatedMessage;
                            this._messages.next(messages);
                        }
                        return updatedMessage;
                    })
                )
            }),
            catchError(error => {

                console.log(error);
                return throwError(() => error);
            })
        );
    }

    deleteMessage(messageID: string): Observable<void> {
        return this.aws.deleteDocument(this.aws.chatMessagesID, messageID).pipe(
            tap(() => {
                this._messages.pipe(
                    take(1),
                    map(messages => {
                        const index = messages.findIndex(item => item.$id === messageID);
                        if (index !== -1) {
                            messages.splice(index, 1);
                            this._messages.next(messages);
                        }
                        return messages;
                    }),
                    catchError(error => {
                        console.error(error);
                        return of(false);
                    })
                )
            })
        )
    }

    resetChat(): void {
        this._chat.next(null);
    }



    // public listenToRealTimeClientMessages(chatId?: string): void {
    //     this.getOrganisationID().pipe(
    //         switchMap(organisationId => {
    //             return this.getMessages(chatId).pipe(
    //                 switchMap((messages) => {
    //                     if (messages.length === 0) return of([]);
    //                     const formattedData = messages.map(item => ({
    //                         id: item.$id,
    //                         data: item
    //                     }));
    //                     return this.aws.subscribeToCollection(formattedData, this.aws.chatMessagesID, organisationId);
    //                 }),
    //                 map(formattedArray => {
    //                     return formattedArray.map(item => {
    //                         return { $id: item.id, ...item.data }
    //                     });
    //                 })
    //             );
    //         })
    //     ).subscribe(messages => {
    //         // Update filtered messages if chatId is provided
    //         if (chatId) {
    //             this._messages.next(messages.filter(item => item.chatId === chatId));
    //         }
    //         // Update all messages
    //         this._allMessages.next(messages);
    //     });
    // }
    public listenToRealTimeClientMessages(chatId?: string): void {
        this.getOrganisationID().pipe(
            switchMap(organisationId => {
                // Use existing messages if available, otherwise fetch
                const currentMessages = chatId ? this._messages.getValue() : this._allMessages.getValue();
                const initialData$ = currentMessages?.length > 0
                    ? of(currentMessages.map(item => ({ id: item.$id, data: item })))
                    : this.getMessages(chatId || '').pipe( // Default to empty chatId if undefined
                        map(messages => messages.map(item => ({ id: item.$id, data: item })))
                    );

                return initialData$.pipe(
                    switchMap(messages => {
                        if (messages.length === 0) return of([]);
                        return this.aws.subscribeToCollection(messages, this.aws.chatMessagesID, organisationId);
                    }),
                    map(updatedData => updatedData.map(item => ({ $id: item.id, ...item.data }))),
                    tap(updatedMessages => {
                        if (chatId) {
                            this._messages.next(updatedMessages.filter(item => item.chatId === chatId));
                        }
                        this._allMessages.next(updatedMessages);
                    })
                );
            })
        ).subscribe({
            error: (error) => console.error('Error in real-time client messages:', error)
        });
    }

    public listenToRealTimeChats(): void {
        this.getOrganisationID().pipe(
            switchMap(organisationId => {
                const currentChats = this._chats.getValue() || [];
                const initialData$ = currentChats.length > 0
                    ? of(currentChats.map(chat => ({ id: chat.$id, data: chat })))
                    : this.getChats().pipe(
                        map(chats => chats.map(chat => ({ id: chat.$id, data: chat })))
                    );

                return initialData$.pipe(
                    switchMap(chats => {
                        if (chats.length === 0) return of([]);
                        return this.aws.subscribeToCollection(chats, this.aws.chatID, organisationId);
                    }),
                    map(updatedChats => updatedChats.map(chat => ({ $id: chat.id, ...chat.data }))),
                    switchMap(updatedChats => {
                        // Only enrich chats that have changed and lack contact data
                        const chatsToEnrich = updatedChats.filter(chat => chat.contactId && !chat.contact);
                        return chatsToEnrich.length > 0
                            ? forkJoin(chatsToEnrich.map(chat => this.enrichChatWithCustomer(chat))).pipe(
                                map(enriched => {
                                    const enrichedMap = new Map(enriched.map(c => [c.$id, c]));
                                    return updatedChats.map(chat => enrichedMap.get(chat.$id) || chat);
                                })
                            )
                            : of(updatedChats);
                    }),
                    tap(updatedChats => this._chats.next(updatedChats))
                );
            })
        ).subscribe({
            error: (error) => console.error('Error subscribing to chat updates:', error)
        });
    }

    // public listenToRealTimeChats(): void {
    //     this.getOrganisationID().pipe(
    //         switchMap(organisationId => {
    //             return this.getChats().pipe(
    //                 // Assuming `getChats` returns enriched chats, we now subscribe to their updates.
    //                 switchMap(chats => {
    //                     if (chats.length === 0) {
    //                         return of([]); // No chats to subscribe to.
    //                     }
    //                     // Format chats for subscription.
    //                     const formattedData = chats.map(chat => ({ id: chat.$id, data: chat }));
    //                     // Subscribe to real-time updates for these chats.
    //                     return this.aws.subscribeToCollection(formattedData, this.aws.chatID, organisationId);
    //                 }),
    //                 // Assuming subscribeForArray emits an array of updated chat data.
    //                 map(updatedChats => {
    //                     // console.log('Updated chats:', updatedChats);
    //                     // Here, we need to possibly re-enrich the chats (depending on what `subscribeForArray` returns).
    //                     // This is a simplification. You might need to adjust based on the actual data structure returned.
    //                     return updatedChats.map(chat => ({ $id: chat.id, ...chat.data }));
    //                 }),
    //                 // Re-enrich each chat with customer data if necessary.
    //                 switchMap(chats => chats.length === 0 ? of([]) : forkJoin(chats.map(chat => this.enrichChatWithCustomer(chat))))
    //             );
    //         })
    //     ).subscribe(chats => {
    //         // Update your BehaviorSubject or state management solution with the new, enriched chats.
    //         this._chats.next(chats);
    //     }, error => {
    //         console.error('Error subscribing to chat updates:', error);
    //     });
    // }


    uploadFile(file: File): Observable<{ fileId: string, fileUrl: string }> {
        return this.aws.addChatFile(file).pipe(
            map(result => {
                if (!result) {
                    throw new Error('File upload failed');
                }
                return {

                    fileId: result.fileId,
                    fileUrl: result.fileUrl
                };
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to upload and retrieve file details');
            })
        );
    }

    /**
    * Deletes a file with the given fileId.
    *
    * @param {string} fileId - The ID of the file to be deleted.
    * @return {Observable<boolean>} - A boolean indicating whether the file was successfully deleted.
    */

    deleteFile(fileId: string): Observable<boolean> {
        return this.aws.deleteChatFile(fileId);
    }

    // Improved error handling using a more concise pattern
    private handleError<T>(operation = 'operation', result?: T) {
        return (error: any): Observable<T> => {
            console.error(`${operation} failed: ${error.message}`);
            return of(result as T);
        };
    }


}

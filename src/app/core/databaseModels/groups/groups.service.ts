import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, map, Observable, of, switchMap, take, tap, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';

import { Group } from './groups.types';
import { UserService } from 'app/core/databaseModels/user/user.service';


@Injectable({ providedIn: 'root' })
export class GroupService {

    private _group: BehaviorSubject<Group> = new BehaviorSubject(null);
    private _groups: BehaviorSubject<Group[]> = new BehaviorSubject(null);


    constructor(private readonly aws: AppwriteService,
        private _userService: UserService,) {
    }


    getOrganisationID(): Observable<string> {
        return this._userService.user$.pipe(
            map(user => user.organisationID)
        );
    }


    set groupo(value: Group) {
        this._group.next(value);
    }

    get group$(): Observable<Group> {
        return this._group.asObservable();

    }

    get groups$(): Observable<Group[]> {
        return this._groups.asObservable();
    }


    getGroup(id: string): Observable<Group> {
        return this.aws.getDocument<Group>(this.aws.groupsID, id).pipe(
            tap((group) => {
                this._group.next(group);
            }),
        );

    }
    getGroups(): Observable<Group[]> {
        return this.getOrganisationID().pipe(
            switchMap(organisationID => {
                if (organisationID) {
                    let queries: string[] = [
                        this.aws.equalQuery("organisationID", [organisationID]),
                        this.aws.orderAscQuery('groupName'),
                    ];

                    return this.aws.getDocumentList<Group>(this.aws.groupsID, queries).pipe(
                        map(response => response.map(item => {
                            return { $id: item.id, ...item.data }
                        })),
                        tap((groups) => {
                            this._groups.next(groups);
                            return groups

                        }),
                    )
                }
            })


        )

    }

    createGroup(data: Group): Observable<Group> {
        return this.groups$.pipe(
            take(1),
            switchMap(groups =>
                this.aws.addDocument(this.aws.groupsID, data).pipe(
                    map(newGroup => {
                        const _newGroup: Group = {
                            $id: newGroup.id,
                            ...newGroup.data as Group
                        }
                        this._groups.next([_newGroup, ...groups]);
                        return _newGroup;
                    })
                )
            )
        )

    }


    updateGroup(id: string, data: Group): Observable<Group> {
        return this.aws.updateDocument<Group>(this.aws.groupsID, id, data).pipe(
            switchMap(updatedGroup => {
                // Update the individual team member if it's the one currently being observed
                this._group.next(updatedGroup);

                // Update the list of team members
                return this.groups$.pipe(
                    take(1),
                    map(groups => {
                        const index = groups.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            groups[index] = updatedGroup;
                            this._groups.next(groups);
                        }
                        return updatedGroup;
                    })
                );
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to update team member');
            })
        );
    }

    deleteGroup(id: string): Observable<boolean> {
        return this.groups$.pipe(
            take(1),
            switchMap(groups => {
                return this.aws.deleteDocument(this.aws.groupsID, id).pipe(
                    map(() => {
                        // Find the index of the deleted group
                        const index = groups.findIndex(item => item.$id === id);
                        // If the group is found, delete it
                        if (index !== -1) {
                            groups.splice(index, 1);

                        }

                        // update the item BehaviorSubject
                        this._groups.next(groups);
                        return true;
                    }),
                    catchError(error => {
                        console.error(error);
                        // Return false to indicate failed deletion
                        return of(false);
                    })
                )
            })
        )
    }


    /**
        * Uploads a file to the server and returns an observable that emits an object with the fileId and fileUrl.
        *
        * @param {File} file - The file to be uploaded.
        * @return {Observable<{ fileId: string, fileUrl: string }>} An observable that emits the fileId and fileUrl of the uploaded file.
        */


    uploadFile(file: File): Observable<{ fileId: string, fileUrl: string }> {
        return this.aws.addFile(file).pipe(
            map(result => {
                if (!result) {
                    throw new Error('File upload failed');
                }
                return {
                    fileId: result.fileId,
                    fileUrl: result.fileUrl
                };
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to upload and retrieve file details');
            })
        );
    }

    /**
    * Deletes a file with the given fileId.
    *
    * @param {string} fileId - The ID of the file to be deleted.
    * @return {Observable<boolean>} - A boolean indicating whether the file was successfully deleted.
    */

    deleteFile(fileId: string): Observable<boolean> {
        return this.aws.deleteFile(fileId);
    }



}

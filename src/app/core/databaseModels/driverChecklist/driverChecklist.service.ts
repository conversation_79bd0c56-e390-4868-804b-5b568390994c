import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, map, Observable, of, switchMap, take, tap, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { DriverChecklist } from "./driverChecklist.types";

@Injectable({
    providedIn: 'root'
})
export class DriverChecklistService {
    private _driverChecklist: BehaviorSubject<DriverChecklist> = new BehaviorSubject(null);
    private _driverChecklists: BehaviorSubject<DriverChecklist[]> = new BehaviorSubject(null);
    constructor(
        private readonly aws: AppwriteService,
        private _userService: UserService,) {

    }
    public unsubscribe(): void {
        this.aws.unsubscribe();
    }

    getOrganisationID(): Observable<string> {
        return this._userService.user$.pipe(
            map(user => user.organisationID)
        );
    }

    set driverChecklist(value: DriverChecklist) {
        // Store the value
        this._driverChecklist.next(value);
    }

    get driverChecklist$(): Observable<DriverChecklist> {
        return this._driverChecklist.asObservable();
    }

    get driverChecklists$(): Observable<DriverChecklist[]> {

        return this._driverChecklists.asObservable();
    }

    getDriverChecklist(id: string): Observable<DriverChecklist> {

        return this.aws.getDocument<DriverChecklist>(this.aws.driverChecklistID, id).pipe(
            tap((contact) => {
                this._driverChecklist.next({ $id: id, ...contact });
            })
        );
    }

    getDriverChecklists(): Observable<DriverChecklist[]> {

        return this.getOrganisationID().pipe(
            switchMap(organisationID => {
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.orderAscQuery('checklistNote'),
                ];
                return this.aws.getDocumentList<DriverChecklist>(this.aws.driverChecklistID, queries).pipe(
                    map(response => response.map(item => {
                        return { $id: item.id, ...item.data }
                    })),
                    tap((driverChecklists) => {
                        this._driverChecklists.next(driverChecklists);
                    }),
                );
            }),
        );
    }

    createDriverChecklist(data: DriverChecklist): Observable<DriverChecklist> {
        // This for User signup form
        if (!Array.isArray(this.driverChecklists$)) {
            return this.aws.addDocument(this.aws.driverChecklistID, data).pipe(
                switchMap(newDriverChecklist => {
                    const _newDriverChecklist: DriverChecklist = {
                        $id: newDriverChecklist.id,
                        ...newDriverChecklist.data as DriverChecklist
                    }

                    return of(_newDriverChecklist);
                })
            )
        } else {
            return this.driverChecklists$.pipe(
                take(1),
                switchMap(driverChecklist =>
                    this.aws.addDocument(this.aws.driverChecklistID, data).pipe(
                        map(newDriverChecklist => {
                            const _newDriverChecklist: DriverChecklist = {
                                $id: newDriverChecklist.id,
                                ...newDriverChecklist.data as DriverChecklist
                            }
                            this._driverChecklists.next([_newDriverChecklist, ...driverChecklist]);
                            return _newDriverChecklist;
                        })
                    )
                )
            )
        }
    }

    updateDriverChecklist(id: string, data: DriverChecklist): Observable<DriverChecklist> {

        return this.aws.updateDocument<DriverChecklist>(this.aws.driverChecklistID, id, data).pipe(
            switchMap(updatedDriverChecklist => {

                // Update the individual driverChecklist if it's the one currently being observed
                this._driverChecklist.next(updatedDriverChecklist);
                return this.driverChecklists$.pipe(
                    take(1),
                    map(driverChecklists => {
                        const index = driverChecklists.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            driverChecklists[index] = updatedDriverChecklist;
                            this._driverChecklists.next(driverChecklists);
                        }
                        return updatedDriverChecklist;
                    })
                )
            }),
            catchError(error => {
                return throwError(() => error);
            })
        );
    }

    deleteDriverChecklist(id: string): Observable<boolean> {

        return this.driverChecklists$.pipe(
            take(1),
            switchMap(driverChecklist => {
                return this.aws.deleteDocument(this.aws.driverChecklistID, id).pipe(
                    map(() => {
                        const index = driverChecklist.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            driverChecklist.splice(index, 1);
                            this._driverChecklist.next(null);
                            this._driverChecklists.next(driverChecklist);
                        }
                        return true;
                    })
                )
            }),
            catchError(error => {
                return throwError(() => error);
            })
        )
    }

    public listenToRealTimeData(): void {
        this.getDriverChecklists().pipe(
            switchMap(initialDriverChecklists => {
                const formattedData = initialDriverChecklists.map(item => ({
                    id: item.$id,
                    data: item
                }));
                return this.aws.subscribeForArray(formattedData, this.aws.driverChecklistID);
            }),
            map(formattedArray => {
                return formattedArray.map(item => ({
                    ...item.data,
                    $id: item.id  // Assigning the id value back to the $id property
                }));
            })
        ).subscribe(updatedDriverChecklists => {
            this._driverChecklists.next(updatedDriverChecklists);
        })
    }

}

import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, map, Observable, of, switchMap, take, tap, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';

import { DriverShift } from './driverShifts.types';
import { UserService } from 'app/core/databaseModels/user/user.service';

@Injectable({ providedIn: 'root' })
export class DriverShiftsService {

    private _driverShifts: BehaviorSubject<DriverShift[]> = new BehaviorSubject(null);
    private _driverShift: BehaviorSubject<DriverShift> = new BehaviorSubject(null);

    constructor(private readonly aws: AppwriteService,
        private _userService: UserService,) {

    }

    getOrganisationID(): Observable<string> {

        //TODO: Add Update for Time Zone
        return this._userService.user$.pipe(
            map(user => user.organisationID)
        );
    }

    get driverShifts$(): Observable<DriverShift[]> {
        return this._driverShifts.asObservable();
    }

    get driverShift$(): Observable<DriverShift> {
        return this._driverShift.asObservable();
    }

    set driverShift(value: DriverShift) {
        // Store the value
        this._driverShift.next(value);
    }

    getDriverShifts(): Observable<DriverShift[]> {

        return this.getOrganisationID().pipe(

            switchMap(organisationID => {

                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.orderAscQuery('startShift'),
                ];


                return this.aws.getDocumentList<DriverShift>(this.aws.driverShiftsID, queries).pipe(
                    map(response => response.map(item => {
                        return { $id: item.id, ...item.data }
                    })),
                    tap((driverShifts) => {
                        this._driverShifts.next(driverShifts);
                        return driverShifts;

                    }),
                )
            })
        )
    }


    getDriverShiftsByDate(date: Date, endDate?: Date, teamMemberId?: string): Observable<DriverShift[]> {

        //   console.log(date, teamMemberId);
        const formattedDate = new Date(this.formatDate(date));

        //TODO: Modify for get timezone from user profile
        let { start, end } = this.getUTCDateRange(formattedDate, 11);
        if (endDate) {
            const formattedEndDate = new Date(this.formatDate(endDate));

            ({ end } = this.getUTCDateRange(formattedEndDate, 11));
        }

        // console.log(`Start: ${start}, End: ${end}`);
        return this.getOrganisationID().pipe(
            switchMap(organisationID => {

                //TODO:paging for query and Limit
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.greaterThanEqualQuery("startShift", [`${start}`]),
                    this.aws.lessThanEqualQuery("startShift", [`${end}`]),
                    this.aws.limitQuery(1000),
                ];
                if (teamMemberId) {
                    queries.push(this.aws.equalQuery("teamMemberId", [teamMemberId]));
                }
                return this.aws.getDocumentList<DriverShift>(this.aws.driverShiftsID, queries).pipe(
                    map(response => response.map(item => {
                        // Convert dueDate to the desired timezone
                        const _startDate = new Date(item.data.startShift); // Assuming dueDate is in a valid date format
                        const _endDate = new Date(item.data.endShift);
                        //TODO: Modify for get timezone
                        // convert UTC To Local
                        const startShiftWithTimezone = new Date(_startDate.getTime() + (_startDate.getTimezoneOffset() * 60 * 1000));
                        const endShiftWithTimezone = new Date(_endDate.getTime() + (_endDate.getTimezoneOffset() * 60 * 1000));
                        // console.log(startShiftWithTimezone, endShiftWithTimezone);
                        return { $id: item.id, startShift: startShiftWithTimezone, endShift: endShiftWithTimezone, ...item.data }
                    })),
                    tap((driverShifts) => {

                        this._driverShifts.next(driverShifts);
                        return driverShifts;

                    })
                )
            })
        )

    }



    getDriverShift(id: string): Observable<DriverShift> {
        return this.aws.getDocument<DriverShift>(this.aws.driverShiftsID, id).pipe(
            tap((driverShift) => {
                this._driverShift.next(driverShift);
                return driverShift;
            }),
        )
    }

    createDriverShift(data: DriverShift): Observable<DriverShift> {
        return this.driverShifts$.pipe(
            take(1),
            switchMap(driverShifts =>
                this.aws.addDocument(this.aws.driverShiftsID, data).pipe(
                    map(newDriverShift => {
                        const _newDriverShift: DriverShift = {
                            $id: newDriverShift.id,
                            ...newDriverShift.data as DriverShift
                        }
                        this._driverShifts.next([_newDriverShift, ...driverShifts]);
                        return _newDriverShift;
                    })
                )
            )
        )
    }



    getUTCDateRange(localDate: Date, timezoneOffsetHours: number): { start: string, end: string } {
        // 1. Adjust local date to start of day (00:00:00)
        const startLocal = new Date(localDate);
        startLocal.setUTCHours(0, 0, 0, 0); // Set time in UTC to avoid timezone ambiguity

        // 2. Calculate UTC start time (subtract offset)
        const startUTC = new Date(startLocal.getTime() - (timezoneOffsetHours * 60 * 60 * 1000));

        // 3. Adjust local date to end of day (23:59:59)
        const endLocal = new Date(localDate);
        endLocal.setUTCHours(23, 59, 59, 999); // Set time in UTC

        // 4. Calculate UTC end time (subtract offset)
        const endUTC = new Date(endLocal.getTime() - (timezoneOffsetHours * 60 * 60 * 1000));

        // 5. Format UTC times in 24-hour format (without 'Z')
        const formatIn24Hour = (date) => {
            const isoString = date.toISOString();
            return isoString.replace(/\.\d{3}Z$/, ''); // Remove milliseconds and 'Z'
        };

        return {
            start: formatIn24Hour(startUTC),
            end: formatIn24Hour(endUTC)
        };
    }

    updateDriverShift(id: string, data: DriverShift): Observable<DriverShift> {

        return this.aws.updateDocument<DriverShift>(this.aws.driverShiftsID, id, data).pipe(
            switchMap(updatedDriverShift => {
                // Update the individual team member if it's the one currently being observed
                this._driverShift.next(updatedDriverShift);
                return this.driverShifts$.pipe(
                    take(1),
                    map(driverShifts => {
                        const index = driverShifts.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            driverShifts[index] = updatedDriverShift;
                            this._driverShifts.next(driverShifts);
                        }
                        return updatedDriverShift;
                    })
                );
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to update team member');
            })
        );
    }

    deleteDriverShift(id: string): Observable<boolean> {
        return this.driverShifts$.pipe(
            take(1),
            switchMap(driverShifts => {
                return this.aws.deleteDocument(this.aws.driverShiftsID, id).pipe(
                    map(() => {
                        // Find the index of the deleted team Member
                        const index = driverShifts.findIndex(item => item.$id === id);
                        // If the team member is found, delete it
                        if (index !== -1) {
                            driverShifts.splice(index, 1);
                        }
                        // update the item BehaviorSubject
                        this._driverShifts.next(driverShifts);
                        return true;
                    }),
                    catchError(error => {
                        console.error(error);
                        // Return false to indicate failed deletion
                        return of(false);
                    })
                )

            })
        )
    }

    uploadFile(file: File): Observable<{ fileId: string, fileUrl: string }> {
        return this.aws.addFile(file).pipe(
            map(result => {
                if (!result) {
                    throw new Error('File upload failed');
                }
                return {
                    fileId: result.fileId,
                    fileUrl: result.fileUrl
                };
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to upload and retrieve file details');
            })
        );
    }

    /**
    * Deletes a file with the given fileId.
    *
    * @param {string} fileId - The ID of the file to be deleted.
    * @return {Observable<boolean>} - A boolean indicating whether the file was successfully deleted.
    */

    deleteFile(fileId: string): Observable<boolean> {
        return this.aws.deleteFile(fileId);
    }


    private formatDate(date: Date): string {
        const _month = (date.getMonth() + 1) < 10 ? '0' + (date.getMonth() + 1) : (date.getMonth() + 1);
        const _day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
        return `${date.getFullYear()}-${_month}-${_day}`;
    }






}

import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map, mapTo, switchMap, take, tap } from 'rxjs/operators';
import { AppwriteService } from '../appwrite.service';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { DriverChecklistDoc } from './driverChecklistDoc.types';

@Injectable({
    providedIn: 'root'
})
export class DriverChecklistDocService {
    private _driverChecklistDoc = new BehaviorSubject<DriverChecklistDoc | null>(null);
    private _driverChecklistDocs = new BehaviorSubject<DriverChecklistDoc[] | null>(null);

    constructor(
        private readonly aws: AppwriteService,
        private readonly _userService: UserService
    ) { }

    // Unsubscribe from AppwriteService
    public unsubscribe(): void {
        this.aws.unsubscribe();
    }

    // Get the organization ID from the user service
    private getOrganisationID(): Observable<string> {
        return this._userService.user$.pipe(
            map(user => user.organisationID)
        );
    }

    // Set the individual driver checklist document
    set driverChecklistDoc(value: DriverChecklistDoc) {
        this._driverChecklistDoc.next(value);
    }

    // Get the observable for the individual driver checklist document
    get driverChecklistDoc$(): Observable<DriverChecklistDoc> {
        return this._driverChecklistDoc.asObservable();
    }

    // Get the observable for the list of driver checklist documents
    get driverChecklistDocs$(): Observable<DriverChecklistDoc[]> {
        return this._driverChecklistDocs.asObservable();
    }

    // Helper methods to update state
    private updateDriverChecklistDocState(driverChecklistDoc: DriverChecklistDoc): void {
        this._driverChecklistDoc.next(driverChecklistDoc);
    }

    private updateDriverChecklistDocsState(driverChecklistDocs: DriverChecklistDoc[]): void {
        this._driverChecklistDocs.next(driverChecklistDocs);
    }

    // Fetch an individual driver checklist document by ID
    getDriverChecklistDoc(id: string): Observable<DriverChecklistDoc> {
        return this.aws.getDocument<DriverChecklistDoc>(this.aws.driverChecklistDocID, id).pipe(
            map(driverChecklistDoc => {
                this.updateDriverChecklistDocState(driverChecklistDoc);
                return driverChecklistDoc;
            }),
            catchError(error => throwError(() => error))
        );
    }

    // Fetch all driver checklist documents for the current organization
    getDriverChecklistDocs(): Observable<DriverChecklistDoc[]> {
        return this.getOrganisationID().pipe(
            switchMap(organisationID => {
                const queries = [
                    this.aws.equalQuery("organisationID", [organisationID])
                ];
                return this.aws.getDocumentList<DriverChecklistDoc>(this.aws.driverChecklistDocID, queries).pipe(
                    map(response => response.map(item => ({ $id: item.id, ...item.data }))),
                    tap(driverChecklistDocs => this.updateDriverChecklistDocsState(driverChecklistDocs)),
                    catchError(error => throwError(() => error))
                );
            })
        );
    }

    // Create a new driver checklist document
    createDriverChecklistDoc(driverChecklistDoc: DriverChecklistDoc): Observable<DriverChecklistDoc> {
        return this.aws.addDocument(this.aws.driverChecklistDocID, driverChecklistDoc).pipe(
            map(response => {
                const newDriverChecklistDoc = { $id: response.id, ...driverChecklistDoc };
                this.updateDriverChecklistDocState(newDriverChecklistDoc);
                return newDriverChecklistDoc;
            }),
            catchError(error => throwError(() => error))
        );
    }

    // Update an existing driver checklist document by ID
    updateDriverChecklistDoc(id: string, data: DriverChecklistDoc): Observable<DriverChecklistDoc> {
        return this.aws.updateDocument<DriverChecklistDoc>(this.aws.driverChecklistDocID, id, data).pipe(
            switchMap(updatedDriverChecklistDoc => {
                this.updateDriverChecklistDocState(updatedDriverChecklistDoc);
                return this.driverChecklistDocs$.pipe(
                    take(1),
                    map(driverChecklistDocs => {
                        const updatedDocs = driverChecklistDocs.map(doc =>
                            doc.$id === id ? updatedDriverChecklistDoc : doc
                        );
                        this.updateDriverChecklistDocsState(updatedDocs);
                        return updatedDriverChecklistDoc;
                    })
                );
            }),
            catchError(error => throwError(() => error))
        );
    }

    // Delete a driver checklist document by ID
    deleteDriverChecklistDoc(id: string): Observable<boolean> {
        return this.aws.deleteDocument(this.aws.driverChecklistDocID, id).pipe(
            tap(() => {
                const updatedDocs = this._driverChecklistDocs.getValue()?.filter(doc => doc.$id !== id) || [];
                this.updateDriverChecklistDocsState(updatedDocs);
                this._driverChecklistDoc.next(null);
            }),
            mapTo(true),
            catchError(error => throwError(() => error))
        );
    }

    // Upload a file and return the file ID and URL
    uploadFile(file: File): Observable<{ fileId: string, fileUrl: string }> {
        return this.aws.addFile(file).pipe(
            map(result => ({
                fileId: result.fileId,
                fileUrl: result.fileUrl
            })),
            catchError(error => {
                console.error('File upload failed:', error);
                return throwError(() => 'Failed to upload and retrieve file details');
            })
        );
    }

    // Delete a file by its ID
    deleteFile(fileId: string): Observable<boolean> {
        return this.aws.deleteFile(fileId).pipe(
            catchError(error => throwError(() => error))
        );
    }
}

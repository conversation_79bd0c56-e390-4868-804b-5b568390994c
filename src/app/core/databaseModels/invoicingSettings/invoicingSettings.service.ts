import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { InvoicingSettings } from './invoicingSettings.type';
import { catchError, map, switchMap, take, tap } from 'rxjs/operators';

@Injectable({
    providedIn: 'root'
})
export class InvoicingSettingsService {
    private _invoicingSettings = new BehaviorSubject<InvoicingSettings>(null);

    constructor(
        private readonly aws: AppwriteService,
        private _userService: UserService
    ) { }

    public unsubscribe(): void {
        this.aws.unsubscribe();
    }

    private getOrganisationID(): Observable<string> {
        return this._userService.user$.pipe(
            take(1),
            map((user) => user.organisationID)
        );
    }

    get invoicingSettings$(): Observable<InvoicingSettings> {
        return this._invoicingSettings.asObservable();
    }

    getInvoicingSettings(): Observable<InvoicingSettings> {
        return this.getOrganisationID().pipe(
            switchMap((organisationID) => {
                //console.log(organisationID);
                const queries: string[] = [
                    this.aws.equalQuery('organisationID', [organisationID])
                ];
                return this.aws.getDocumentList<InvoicingSettings>(this.aws.invoicingSettingsID, queries).pipe(
                    map((response) => {

                        if (response && response.length > 0) {
                            const settings = {
                                $id: response[0].id,
                                ...response[0].data
                            };
                            this._invoicingSettings.next(settings);
                            return settings;
                        } else {
                            return null;
                        }
                    })
                );
            }),
            catchError((error) => {
                console.error(error);
                return throwError('Failed to retrieve invoicing settings');
            })
        );
    }

    createInvoicingSettings(data: InvoicingSettings): Observable<InvoicingSettings> {
        return this.aws.addDocument(this.aws.invoicingSettingsID, data).pipe(
            map((newSettings) => {
                const _newSettings: InvoicingSettings = {
                    $id: newSettings.id,
                    ...newSettings.data
                };
                this._invoicingSettings.next(_newSettings);
                return _newSettings;
            }),
            catchError((error) => {
                console.error(error);
                return throwError('Failed to create invoicing settings');
            })
        );
    }

    updateInvoicingSettings(id: string, data: InvoicingSettings): Observable<InvoicingSettings> {
        return this.aws.updateDocument<InvoicingSettings>(this.aws.invoicingSettingsID, id, data).pipe(
            map((updatedSettings) => {
                this._invoicingSettings.next(updatedSettings);
                return updatedSettings;
            }),
            catchError((error) => {
                console.error(error);
                return throwError('Failed to update invoicing settings');
            })
        );
    }

    deleteInvoicingSettings(id: string): Observable<boolean> {
        return this.aws.deleteDocument(this.aws.invoicingSettingsID, id).pipe(
            tap(() => {
                this._invoicingSettings.next(null);
            }),
            map(() => true),
            catchError((error) => {
                console.error(error);
                return throwError('Failed to delete invoicing settings');
            })
        );
    }
}

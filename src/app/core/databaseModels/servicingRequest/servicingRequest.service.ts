import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, map, Observable, of, switchMap, take, tap, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { ServicingRequest } from './servicingRequest.types';

@Injectable({
    providedIn: 'root'
})
export class ServicingRequestService {
    private _servicingRequest: BehaviorSubject<ServicingRequest> = new BehaviorSubject(null);
    private _servicingRequests: BehaviorSubject<ServicingRequest[]> = new BehaviorSubject([]);
    constructor(private readonly aws: AppwriteService, private _userService: UserService,) {
    }

    public unsubscribe(): void {
        this.aws.unsubscribe();
    }
    getOrganisationID(): Observable<string> {
        return this._userService.user$.pipe(
            take(1),
            map((user) => user.organisationID),
        );
    }
    set servicingRequest(value: ServicingRequest) {
        this._servicingRequest.next(value);
    }
    get servicingRequest$(): Observable<ServicingRequest> {
        return this._servicingRequest.asObservable();
    }
    set servicingRequests(value: ServicingRequest[]) {
        this._servicingRequests.next(value);
    }
    get servicingRequests$(): Observable<ServicingRequest[]> {
        return this._servicingRequests.asObservable();
    }

    getServicingRequest(id: string): Observable<ServicingRequest> {
        return this.aws.getDocument<ServicingRequest>(this.aws.servicingRequestID, id).pipe(
            tap((servicingRequest) => {
                this.servicingRequest = servicingRequest;
            }),
        );

    }
    getServicingRequests(): Observable<ServicingRequest[]> {
        return this.getOrganisationID().pipe(
            switchMap((organisationID) => {
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.orderAscQuery('requestDate'),
                ];
                return this.aws.getDocumentList<ServicingRequest>(this.aws.servicingRequestID, queries).pipe(
                    map(response => response.map(item => {
                        return { $id: item.id, ...item.data }
                    }))
                );
            }),
            catchError((error) => {
                return throwError(error);
            }),
            tap((servicingRequests) => {
                this.servicingRequests = servicingRequests;
            }),
        );
    }

    createServicingRequest(servicingRequest: ServicingRequest): Observable<ServicingRequest> {
        return this.servicingRequests$.pipe(
            take(1),
            switchMap(servicingRequests =>
                this.aws.addDocument(this.aws.servicingRequestID, servicingRequest).pipe(
                    map((newServicingRequest) => {
                        const _newServicingRequest: ServicingRequest = { $id: newServicingRequest.id, ...newServicingRequest.data as ServicingRequest }

                        this.servicingRequests = [...servicingRequests, _newServicingRequest];
                        return _newServicingRequest;
                    }),
                )
            ),
        );
    }

    updateServicingRequest(id: string, data: ServicingRequest): Observable<ServicingRequest> {
        return this.aws.updateDocument<ServicingRequest>(this.aws.servicingRequestID, id, data).pipe(
            switchMap(updatedServicingRequest => {
                this._servicingRequest.next(updatedServicingRequest);
                return this._servicingRequests.pipe(
                    take(1),
                    map(servicingRequests => {
                        const index = servicingRequests.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            servicingRequests[index] = updatedServicingRequest;
                            this._servicingRequests.next(servicingRequests);
                        }
                        return updatedServicingRequest;
                    })
                )

            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to update update Servicing Request');
            })
        );
    }
    updateServicingRequestStatus(id: string): Observable<ServicingRequest> {
        return this.aws.getDocument<ServicingRequest>(this.aws.servicingRequestID, id).pipe(
            switchMap((servicingRequest) => {
                servicingRequest.requestStatus = 'Approved';
                return this.updateServicingRequest(id, servicingRequest);
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to update update Servicing Request');
            })
        );
    }
    deleteServicingRequest(id: string): Observable<void> {
        return this.aws.deleteDocument(this.aws.servicingRequestID, id).pipe(

            tap(() =>
                this._servicingRequests.pipe(
                    take(1),
                    map(servicingRequests => {
                        const index = servicingRequests.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            servicingRequests.splice(index, 1);
                            this._servicingRequests.next(servicingRequests);
                        }
                        return servicingRequests;
                    })
                )
            ),
            catchError(error => {
                console.error('Error deleting chat:', error);
                return throwError('Failed to delete Servicing Request');
            })
        );
    }

    uploadFile(file: File): Observable<{ fileId: string, fileUrl: string }> {
        return this.aws.addJobsBucketFile(file).pipe(
            map(result => {
                if (!result) {
                    throw new Error('File upload failed');
                }
                return {

                    fileId: result.fileId,
                    fileUrl: result.fileUrl
                };
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to upload and retrieve file details');
            })
        );
    }

    /**
    * Deletes a file with the given fileId.
    *
    * @param {string} fileId - The ID of the file to be deleted.
    * @return {Observable<boolean>} - A boolean indicating whether the file was successfully deleted.
    */

    deleteFile(fileId: string): Observable<boolean> {
        return this.aws.deleteJobsBucketFile(fileId);
    }

}

export interface Invoicing {
    $id: string,
    organisationID: string,
    customerId: string,
    scheduledJobId: string,
    invoiceDate: Date,
    invoiceStatus: InvoiceStatus,
    total: number,
    taxPercent: number,
    tax: number,
    netTotal: number,
    dueDate: Date,
    amountPaid: number,
    paidDate: Date,
    paymentRef: string,
    items: InvoicingItem[],
    description: string,
    invoiceNumber: string,

}

export interface InvoicingItem {
    description: string,
    quantity: number,
    price: number,
    discount: number,
    total: number,
}

export enum InvoiceStatus {
    Paid = 'Paid',
    Void = 'Void',
    Uncollectible = 'Uncollectible',
    OnHold = 'On-hold',
    Pending = 'Pending',
    Approved = 'Approved',
    Outstanding = 'Outstanding',


}

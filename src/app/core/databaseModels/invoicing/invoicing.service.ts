import { Injectable } from '@angular/core';
import { BehaviorSubject, combineLatest, Observable, of, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { Invoicing, InvoiceStatus } from './invoicing.type';
import { catchError, map, switchMap, take, tap } from 'rxjs/operators';
import { InvoicingSettingsService } from '../invoicingSettings/invoicingSettings.service';

@Injectable({
    providedIn: 'root'
})
export class InvoicingService {
    private _invoicing: BehaviorSubject<Invoicing> = new BehaviorSubject(null);
    private _invoicingList: BehaviorSubject<Invoicing[]> = new BehaviorSubject<Invoicing[]>([]);

    constructor(
        private readonly aws: AppwriteService,
        private _userService: UserService,
        private _invoicingSettingsService: InvoicingSettingsService
    ) { }

    public unsubscribe(): void {
        this.aws.unsubscribe();
    }

    getOrganisationID(): Observable<string> {
        return this._userService.user$.pipe(
            take(1),
            map((user) => user.organisationID)
        );
    }

    set invoicing(value: Invoicing) {
        this._invoicing.next(value);
    }

    get invoicing$(): Observable<Invoicing> {
        return this._invoicing.asObservable();
    }

    get invoicingList$(): Observable<Invoicing[]> {
        return this._invoicingList.asObservable();
    }

    getInvoicing(id: string): Observable<Invoicing> {
        return this.aws.getDocument<Invoicing>(this.aws.invoicingID, id).pipe(
            tap((invoicing) => {
                this._invoicing.next(invoicing);
            })
        );
    }

    getInvoicings(limited = false): Observable<Invoicing[]> {
        return this.getOrganisationID().pipe(
            switchMap((organisationID) => {
                const queries: string[] = [
                    this.aws.equalQuery('organisationID', [organisationID]),
                    this.aws.orderDescQuery('invoiceDate')
                ];
                if (limited) {
                    queries.push(this.aws.limitQuery(5));
                } else {
                    queries.push(this.aws.limitQuery(5000));
                    // Limit data after july last year first calculation date
                    const now = new Date();
                    const lastYear = new Date(now.getFullYear() - 1, 6, 30);
                    console.log(lastYear);
                    queries.push(this.aws.greaterThanEqualQuery('invoiceDate', [lastYear]));

                }
                return this.aws.getDocumentList<Invoicing>(this.aws.invoicingID, queries).pipe(
                    map((response) =>
                        response.map((item) => ({
                            $id: item.id,
                            ...item.data
                        }))
                    ),
                    tap((invoicingList) => {
                        this._invoicingList.next(invoicingList);
                    })
                );
            })
        );
    }

    createInvoicing(data: any): Observable<Invoicing> {

        data.invoiceStatus = InvoiceStatus.Approved;
        data.items = data.items.map(obj => JSON.stringify(obj));
        return this.invoicingList$.pipe(
            take(1),
            switchMap((invoicingList) =>
                this.aws.addDocument(this.aws.invoicingID, data).pipe(
                    map((newInvoicing) => {
                        const _newInvoicing: Invoicing = {
                            $id: newInvoicing.id,
                            ...newInvoicing.data as Invoicing
                        };
                        this._invoicingList.next(invoicingList ? [_newInvoicing, ...invoicingList] : [_newInvoicing]);
                        return _newInvoicing;
                    })
                )
            )
        );
    }

    updateInvoicing(id: string, data: any, didPayment?: boolean): Observable<Invoicing> {

        if (!didPayment) {
            data.items = data.items.map(obj => JSON.stringify(obj));
        } ``
        return this.aws.updateDocument<Invoicing>(this.aws.invoicingID, id, data).pipe(
            switchMap((updatedInvoicing) => {
                this._invoicing.next(updatedInvoicing);
                return this._invoicingList.pipe(
                    take(1),
                    map((invoicingList) => {
                        const index = invoicingList.findIndex((item) => item.$id === id);
                        if (index !== -1) {
                            invoicingList[index] = updatedInvoicing;
                            this._invoicingList.next(invoicingList);
                        }
                        return updatedInvoicing;
                    })
                );
            }),
            catchError((error) => {
                console.error(error);
                return throwError('Failed to update invoicing');
            })
        );
    }

    deleteInvoicing(id: string): Observable<any> {
        return this.aws.deleteDocument(this.aws.invoicingID, id).pipe(
            tap(() => {
                this._invoicingList.pipe(
                    take(1),
                    map((invoicingList) => {
                        const index = invoicingList.findIndex((item) => item.$id === id);
                        if (index !== -1) {
                            invoicingList.splice(index, 1);
                            this._invoicingList.next(invoicingList);
                        }
                    }),
                    catchError((error) => {
                        console.error(error);
                        return of(false);
                    })
                );
            })
        );
    }

    /**
     * Uploads a file and returns an object with the fileId and fileUrl.
     *
     * @param {File} file - The file to be uploaded.
     * @return {Observable<{ fileId: string, fileUrl: string }>}
     */
    uploadFile(file: File): Observable<{ fileId: string; fileUrl: string }> {
        return this.aws.addJobsBucketFile(file).pipe(
            map((result) => {
                if (!result) {
                    throw new Error('File upload failed');
                }
                return {
                    fileId: result.fileId,
                    fileUrl: result.fileUrl
                };
            }),
            catchError((error) => {
                console.error(error);
                return throwError('Failed to upload and retrieve file details');
            })
        );
    }

    /**
     * Deletes a file with the given fileId.
     *
     * @param {string} fileId - The ID of the file to be deleted.
     * @return {Observable<boolean>}
     */
    deleteFile(fileId: string): Observable<boolean> {
        return this.aws.deleteJobsBucketFile(fileId);
    }

    getNextInvoiceNumber(): Observable<{ nextNumber: string; prefix: string; gst: number; detailsTemplate: string, dueDays: number }> {
        return combineLatest([
            this.invoicingList$,
            this._invoicingSettingsService.getInvoicingSettings()
        ]).pipe(
            take(1),
            map(([invoices, settings]) => {
                let nextNumber = `${settings?.invoicePrefix || ''}${settings?.invoiceStartNumber + 1}`;

                // Update nextNumber of the settings
                this._invoicingSettingsService.updateInvoicingSettings(settings.$id, {
                    ...settings,
                    invoiceStartNumber: settings?.invoiceStartNumber + 1
                }).subscribe();


                return {
                    nextNumber,
                    prefix: settings?.invoicePrefix || '',
                    gst: settings?.gst || 0,
                    detailsTemplate: settings?.detailsTemplate || '',
                    dueDays: settings?.dueDays || 1,

                };
            })
        );
    }

    getInvoiceSettings(): Observable<{ prefix: string, gst: number, dueDays: number }> {
        return this._invoicingSettingsService.getInvoicingSettings().pipe(
            map(settings => ({
                prefix: settings?.invoicePrefix || '',
                gst: settings?.gst || 0,
                dueDays: settings?.dueDays || 1,
            }))
        );
    }

    /// Invoice Statement Total amount Balance Due Total Paid Base on date range and return total amount, balance due and total paid

    getInvoiceStatement(startDate: Date, endDate: Date): Observable<{ totalAmount: number, balanceDue: number, totalPaid: number }> {
        return this.getOrganisationID().pipe(
            switchMap((organisationID) => {
                const queries: string[] = [
                    this.aws.equalQuery('organisationID', [organisationID]),
                    this.aws.orderDescQuery('invoiceDate'),
                    this.aws.greaterThanEqualQuery('invoiceDate', [startDate]),
                    this.aws.lessThanEqualQuery('invoiceDate', [endDate])
                ];
                return this.aws.getDocumentList<Invoicing>(this.aws.invoicingID, queries).pipe(
                    map((response) =>
                        response.map((item) => ({
                            $id: item.id,
                            ...item.data
                        }))
                    ),
                    map((invoicingList) => {
                        const totalAmount = invoicingList.reduce((total, inv) => total + (inv.netTotal || 0), 0);
                        const balanceDue = invoicingList.reduce((total, inv) => total + (inv.netTotal || 0) - (inv.amountPaid || 0), 0);
                        const totalPaid = invoicingList.reduce((total, inv) => total + (inv.amountPaid || 0), 0);
                        return {
                            totalAmount,
                            balanceDue,
                            totalPaid
                        };
                    })
                );
            })
        );


    }

    // Unallocated transaction Total unallocated Total Invoices 

    getUnallocatedTransaction(): Observable<{ totalUnallocated: number, totalInvoices: number }> {
        return this.getOrganisationID().pipe(
            switchMap((organisationID) => {
                const queries: string[] = [
                    this.aws.equalQuery('organisationID', [organisationID]),
                    this.aws.equalQuery('invoiceStatus', [InvoiceStatus.Approved])
                ];
                return this.aws.getDocumentList<Invoicing>(this.aws.invoicingID, queries).pipe(
                    map((response) =>
                        response.map((item) => ({
                            $id: item.id,
                            ...item.data
                        }))
                    ),
                    map((invoicingList) => {
                        const totalUnallocated = invoicingList.reduce((total, inv) => total + (inv.netTotal || 0) - (inv.amountPaid || 0), 0);
                        const totalInvoices = invoicingList.length;
                        return {
                            totalUnallocated,
                            totalInvoices
                        };
                    })
                );
            })
        );
    }
    // Monthly invoicing summary Total Invoices total unpaid Invoices Total Amount Total Paid Total Unallocated base on Approved status and paid status
    getMonthlyInvoicingSummary(startDate: Date, endDate: Date): Observable<{ totalInvoices: number, totalPaidInvoices: number, totalAmount: number, totalPaid: number, totalUnallocated: number }> {
        return this.getOrganisationID().pipe(
            switchMap((organisationID) => {


                const queries: string[] = [
                    this.aws.equalQuery('organisationID', [organisationID]),
                    this.aws.greaterThanEqualQuery('invoiceDate', [startDate]),
                    this.aws.lessThanEqualQuery('invoiceDate', [endDate]),
                    this.aws.orderDescQuery('invoiceDate'),
                ];
                return this.aws.getDocumentList<Invoicing>(this.aws.invoicingID, queries).pipe(
                    map((response) =>
                        response.map((item) => ({
                            $id: item.id,
                            ...item.data
                        }))
                    ),
                    map((invoicingList) => {
                        // console.log(invoicingList, startDate, endDate);
                        const totalInvoices = invoicingList.length;
                        const totalPaidInvoices = invoicingList.filter(inv => inv.invoiceStatus === InvoiceStatus.Paid).length;
                        const totalAmount = invoicingList.reduce((total, inv) => total + (inv.netTotal || 0), 0);
                        const totalPaid = invoicingList.reduce((total, inv) => total + (inv.amountPaid || 0), 0);
                        const totalUnallocated = totalAmount - totalPaid;
                        return {
                            totalInvoices,
                            totalPaidInvoices,
                            totalAmount,
                            totalPaid,
                            totalUnallocated
                        };
                    })
                );
            })
        );
    }


}

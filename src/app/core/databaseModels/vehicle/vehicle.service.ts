import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, map, Observable, of, switchMap, take, tap, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';
import { Vehicle } from './vehicle.type';
import { UserService } from 'app/core/databaseModels/user/user.service';

@Injectable({ providedIn: 'root' })
export class VehicleService {
    private _vehicle: BehaviorSubject<Vehicle> = new BehaviorSubject(null);
    private _vehicles: BehaviorSubject<Vehicle[]> = new BehaviorSubject(null);

    /**
         * Constructor
         */
    constructor(private readonly aws: AppwriteService, private _userService: UserService,) {
    }
    getOrganisationID(): Observable<string> {
        return this._userService.user$.pipe(
            map(user => user.organisationID)
        );
    }


    set vehicle(value: Vehicle) {
        // Store the value
        this._vehicle.next(value);
    }

    get vehicle$(): Observable<Vehicle> {
        return this._vehicle.asObservable();

    }

    get vehicles$(): Observable<Vehicle[]> {
        return this._vehicles.asObservable();
    }


    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    getVehicle(id: string): Observable<Vehicle> {
        return this.aws.getDocument<Vehicle>(this.aws.vehiclesID, id).pipe(
            tap((vehicle) => {
                this._vehicle.next(vehicle);
            }),
        );

    }

    getVehicles(): Observable<Vehicle[]> {

        return this.getOrganisationID().pipe(
            switchMap(organisationID => {
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.orderAscQuery('vehicleName'),
                ];
                return this.aws.getDocumentList<Vehicle>(this.aws.vehiclesID, queries).pipe(
                    map(response => response.map(item => {
                        return { $id: item.id, ...item.data }
                    })),
                    tap((vehicles) => {
                        this._vehicles.next(vehicles);
                    }),
                );


            }),
        )
    }

    createVehicle(data: Vehicle): Observable<Vehicle> {
        return this.vehicles$.pipe(
            take(1),
            switchMap(vehicles =>
                this.aws.addDocument(this.aws.vehiclesID, data).pipe(
                    map(newVehicle => {
                        const _newVehicle: Vehicle = {
                            $id: newVehicle.id,
                            ...newVehicle.data as Vehicle
                        }
                        this._vehicles.next([_newVehicle, ...vehicles]);
                        return _newVehicle;
                    })
                )
            )
        )
    }


    updateVehicle(id: string, data: Vehicle): Observable<Vehicle> {
        return this.aws.updateDocument<Vehicle>(this.aws.vehiclesID, id, data).pipe(
            switchMap(updatedVehicle => {
                // Update the individual team member if it's the one currently being observed
                this._vehicle.next(updatedVehicle);

                // Update the list of team members
                return this.vehicles$.pipe(
                    take(1),
                    map(vehicles => {
                        const index = vehicles.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            vehicles[index] = updatedVehicle;
                            this._vehicles.next(vehicles);
                        }
                        return updatedVehicle;
                    })
                );
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to update team member');
            })
        );

    }

    deleteVehicle(id: string): Observable<boolean> {
        return this.vehicles$.pipe(
            take(1),
            switchMap(vehicles => {
                return this.aws.deleteDocument(this.aws.vehiclesID, id).pipe(
                    map(() => {
                        // Find the index of the deleted team Member
                        const index = vehicles.findIndex(item => item.$id === id);
                        // If the team member is found, delete it
                        if (index !== -1) {
                            vehicles.splice(index, 1);

                        }

                        // update the item BehaviorSubject
                        this._vehicles.next(vehicles);
                        return true;
                    }),
                    catchError(error => {
                        console.error(error);
                        // Return false to indicate failed deletion
                        return of(false);
                    })
                )
            })
        )
    }

    /**
        * Uploads a file to the server and returns an observable that emits an object with the fileId and fileUrl.
        *
        * @param {File} file - The file to be uploaded.
        * @return {Observable<{ fileId: string, fileUrl: string }>} An observable that emits the fileId and fileUrl of the uploaded file.
        */


    uploadFile(file: File): Observable<{ fileId: string, fileUrl: string }> {
        return this.aws.addFile(file).pipe(
            map(result => {
                if (!result) {
                    throw new Error('File upload failed');
                }
                return {
                    fileId: result.fileId,
                    fileUrl: result.fileUrl
                };
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to upload and retrieve file details');
            })
        );
    }

    /**
    * Deletes a file with the given fileId.
    *
    * @param {string} fileId - The ID of the file to be deleted.
    * @return {Observable<boolean>} - A boolean indicating whether the file was successfully deleted.
    */

    deleteFile(fileId: string): Observable<boolean> {
        return this.aws.deleteFile(fileId);
    }

    getFilePreview(fileId: string, width: number, height: number): string {
        return this.aws.getPlatformFilePreview(fileId, width, height);
    }

}

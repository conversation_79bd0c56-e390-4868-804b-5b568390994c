export interface Vehicle {
    $id: string,
    organisationID: string,
    groupIDs: string[],
    vehicleName: string,
    vehicleNumber: string,
    registrationNumber: string,
    costPerKilometre: string,
    vin: string,
    make: string,
    model: string,
    year: string,
    fuelType: string,
    lastUpdate: Date,
    note: string,
    gpsTrackerID: string,
    status: boolean,
    avatar: string,
    avatarImageId: string | null;
    odometer: string,
    odometerReading: Date,
    hoursOfEngine: string,
    hoursOfEngineReading: Date,

}

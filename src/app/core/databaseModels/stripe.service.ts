import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { BehaviorSubject, catchError, filter, forkJoin, from, map, Observable, of, ReplaySubject, switchMap, take, tap, throwError } from 'rxjs';
import { AppwriteService } from './appwrite.service';

@Injectable({ providedIn: 'root' })
export class StripeService implements OnDestroy {

    constructor(
        private aws: AppwriteService
    ) { }
    ngOnDestroy(): void {
        this.aws.unsubscribe();
    }

    createStripeCustomer(data: any): Observable<any> {
        const payload = {
            email: data.customerEmail,
            name: data.customerName,
        }

        return this.aws.executeSubscription<any>('/create-customer', payload).pipe(
            map(response => {
                if (response) {
                    console.log('Send email bu function:', response);
                    return response
                }
            }),
            catchError(error => {
                console.error('Failed to send email:', error);
                return throwError(() => new Error(error.message || 'Failed to send email'));
            })
        );
    }


    createSubscription(data: any): Observable<any> {
        const payload = {
            userId: data.organisationID,
            priceId: data.stripePriceId,
            quantity: data.quantity,
            customerID: data.stripeCustomerId,
            customerEmail: data.customerEmail,
            customerName: data.customerName,
            successUrl: data.successUrl,
            failureUrl: data.failureUrl,
            trialDays: data.trialDays,
        }

        return this.aws.executeSubscription<any>('/subscribe', payload).pipe(
            map(response => {
                if (response) {
                    //  console.log(response);
                    return response
                }

            }),

            catchError(error => {
                console.error('Failed to create subscription:', error);
                return throwError(() => new Error(error.message || 'Failed to create subscription'));
            }),

        );
    }

    updateSubscription(data: any): Observable<any> {

        const payload = {
            customerId: data.stripeCustomerId,
            qty: data.quantity
        }

        return this.aws.executeSubscription<any>('/update-subscription', payload).pipe(
            map(response => {
                if (response) {
                    return response
                }
            }),
            catchError(error => {
                console.error('Failed to create subscription:', error);
                return throwError(() => new Error(error.message || 'Failed to create subscription'));
            }),
        );
    }

    navigateToDashboard(customerID: string): Observable<any> {
        return this.aws.executeSubscription<any>('/dashboard', { customer_ID: customerID }).pipe(
            map(response => {
                if (response) {
                    console.log('Dashboard response:', response);
                    return response;
                }
            }),
            catchError(error => {
                console.error('Failed to create dashboard session:', error);
                return throwError(() => new Error(error.message || 'Failed to create dashboard session'));
            })
        );
    }


}

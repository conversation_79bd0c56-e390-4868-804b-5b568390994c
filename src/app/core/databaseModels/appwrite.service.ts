import { Injectable } from '@angular/core';
import { Account, Client, Databases, ID, Models, Query, RealtimeResponseEvent, Storage, Functions, ExecutionMethod, ImageFormat } from 'appwrite';
import { BehaviorSubject, Observable, asyncScheduler, from, merge, of, scheduled, throwError } from 'rxjs';
import { catchError, debounceTime, delay, distinctUntilChanged, filter, finalize, map, retryWhen, scan, shareReplay, switchMap, take, tap } from 'rxjs/operators';
import { environment } from 'environments/environment';


@Injectable({
    providedIn: 'root'
})
export class AppwriteService {



    private readonly client = new Client();
    private readonly account = new Account(this.client);

    private readonly databases = new Databases(this.client);
    private readonly storage = new Storage(this.client);
    public readonly databaseId = environment.databaseId;
    public readonly platformBucketId = environment.platformBucketId;
    public readonly jobsBucket = environment.jobsBucket;
    public readonly chatBucket = environment.chatBucket;
    private readonly functions = new Functions(this.client);

    //Platform Domain URL
    private readonly platformUrl = environment.platformUrl;

    //Database Collection ID //

    public readonly organisationsID = environment.dbCollections.organisations;
    public readonly groupsID = environment.dbCollections.groups;
    public readonly userID = environment.dbCollections.users;
    public readonly teamMembersID = environment.dbCollections.teamMembers;
    public readonly vehiclesID = environment.dbCollections.vehicle;
    public readonly customersID = environment.dbCollections.customers;
    public readonly customerTagsID = environment.dbCollections.customerTags;
    public readonly scheduledJobsID = environment.dbCollections.scheduledJobs;
    public readonly scheduledJobTagsID = environment.dbCollections.scheduledJobTags;
    public readonly jobTemplatesID = environment.dbCollections.jobsTemplates;
    public readonly driverShiftsID = environment.dbCollections.driverShifts;
    public readonly assetsID = environment.dbCollections.assets;
    public readonly driverChecklistID = environment.dbCollections.driverChecklist;
    public readonly driverChecklistDocID = environment.dbCollections.driverChecklistDoc;
    public readonly servicingID = environment.dbCollections.servicing;
    public readonly servicingRequestID = environment.dbCollections.servicingRequest;
    public readonly expensesID = environment.dbCollections.expense;
    public readonly expenseCategoriesID = environment.dbCollections.expenseCategory;
    public readonly chatID = environment.dbCollections.chat;
    public readonly chatMessagesID = environment.dbCollections.chatMessages;
    public readonly teamChatMessagesID = environment.dbCollections.teamChatMessages;
    public readonly subscriptionPlansID = environment.dbCollections.subscriptionPlans;
    public readonly invoicingID = environment.dbCollections.invoicing;
    public readonly invoicingSettingsID = environment.dbCollections.invoicingSettings;
    public readonly schedulePresetsID = environment.dbCollections.schedulePresets;


    //Functions
    public readonly userFunction = environment.functions.userFunctionID;
    public readonly stripeFunction = environment.functions.stripeFunctionID;
    public readonly sendEmailFunction = environment.functions.sendEmailFunctionID;

    constructor() {
        this.client.setEndpoint(environment.endpoint)
            .setProject(environment.projectId);
    }


    //===================== CRUD  =======================================
    public documentDataObject(document: Models.Document) {
        const { $id, $collectionId, $databaseId, $createdAt, $updatedAt, $permissions, ...data } = document;

        return data;
    }
    public getDocumentList<T>(collectionId: string, queries?: string[] | undefined) {
        return scheduled(this.databases.listDocuments(this.databaseId, collectionId, queries),
            asyncScheduler
        ).pipe(map(actions => {
            return actions.documents.map(document => {

                const data: T = <T>this.documentDataObject(document);
                const id: string = document.$id;
                return { id, data };
            })
        }))
    }
    public getDocument<T>(collectionId: string, documentId: string, queries?: string[] | undefined) {
        return scheduled(this.databases.getDocument(this.databaseId, collectionId, documentId, queries),
            asyncScheduler
        ).pipe(map(document => {


            return { $id: document.$id, ...this.documentDataObject(document) } as T;
        }))
    }


    public addDocument(collectionId: string, data: Object, documentId: string = ID.unique()) {
        return scheduled(
            this.databases.createDocument(this.databaseId, collectionId, documentId, data)
                .then(result => {
                    // Assuming result contains the newly created document
                    return { id: result.$id, data: this.documentDataObject(result) };
                })
                .catch(error => {
                    // Handle the error appropriately
                    throw error;
                }),
            asyncScheduler
        );
    }




    public updateDocument<T>(collectionId: string, documentId: string, data: Object): Observable<T> {
        return scheduled(this.databases.updateDocument(this.databaseId, collectionId, documentId, data)
            .then(result => {
                return { $id: result.$id, ... this.documentDataObject(result) } as T;
                return this.documentDataObject(result) as T; // Cast the result to the expected type
            }).catch(error => {
                throw error; // Propagate the error
            }),
            asyncScheduler);
    }


    public deleteDocument(collectionId: string, documentId: string) {
        return scheduled(this.databases.deleteDocument(this.databaseId, collectionId, documentId)
            .then(result => {
                //  console.log(result);
                // this.toastr.success("Deleted Successfully!");
            }).catch(error => {
                console.error("Deleted Unsuccessfully:", error);
            }),
            asyncScheduler);
    }

    public deleteDocumentRange(collectionId: string, documentsIdArray: string[]) {
        documentsIdArray.forEach(id => {
            this.databases.deleteDocument(this.databaseId, collectionId, id)
                .then(result => {
                    // this.toastr.success("Deleted Successfully!");
                }).catch(error => {
                    // this.toastr.error("Deleted Unuccessfully!");
                });
        });
    }
    //=====================Account=====================================//
    // Create a new user
    public addUser(uid: string, email: string, password: string, name: string): Promise<Models.User<Models.Preferences>> {

        return this.account.create(uid, email, password, name);
    }

    public login(email: string, password: string) {
        return this.account.createEmailPasswordSession(email, password);
    }

    public getCurrentSession() {
        return this.account.getSession('current');
    }

    public getAccount() {
        return this.account.get();
    }

    public listLogs() {
        return this.account.listLogs();
    }

    public logout(): Promise<void> {
        return this.account.deleteSession('current')
            .catch((error) => {
                console.error('Error while logging out:', error);
                throw error;
            })
            .then(() => {
                // Ensure that the Promise resolves to void
                return undefined;
            });
    }

    public updatePassword(oldPassword: string, newPassword: string): Observable<boolean> {
        return from(this.account.updatePassword(newPassword, oldPassword)).pipe(
            map(() => true), // If successful, return true
            catchError((error) => {
                console.error('Error while updating password:', error);
                return of(false); // Return false on error
            })
        );
    }


    public executeUserFunction<T>(
        endpoint: string,
        payload: any,
        method: ExecutionMethod = ExecutionMethod.POST
    ): Observable<T> {

        const payloadString = JSON.stringify(payload);
        return from(this.functions.createExecution(
            this.userFunction,
            payloadString,
            false,
            endpoint,
            method,
            {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            }
        )).pipe(
            map((execution: Models.Execution) => {
                if (execution.responseStatusCode === 200) {
                    const response = JSON.parse(execution.responseBody || '{}');
                    return response as T;
                } else {
                    // console.error('Failed to execute body1:', execution.responseBody);
                    throw new Error(`Failed to execute function: ${execution.responseBody}`);
                }
            }),
            catchError((error) => {
                // console.error('Error while executing function:', error);
                // console.error('Failed to execute body2:', error);
                return throwError(() => new Error(`Failed to execute function: ${error.message}`));
            })
        );
    }

    public createEmailVerification(): Observable<boolean> {
        return from(this.account.createVerification(`${this.platformUrl}/dashboard`))
            .pipe(
                catchError((error) => {
                    console.log(error); // Failure
                    return of(false);
                }),
                map(() => true)
            );
    }

    public verifyingEmail(userId: string, secret: string): Observable<boolean> {
        return from(this.account.updateVerification(userId, secret))
            .pipe(
                catchError((error) => {
                    console.log(error); // Failure
                    return of(false);
                }),
                map(() => true)
            )
    }

    public createPhoneVerification(): Observable<boolean> {
        return from(this.account.createPhoneVerification())
            .pipe(
                catchError((error) => {
                    console.log(error); // Failure
                    return of(false);
                }),
                map(() => true)
            )

    }
    public verifyingOTP(otp: string, userID: string): Observable<boolean> {
        return from(this.account.updatePhoneVerification(userID, otp)).pipe(
            catchError((error) => {
                console.log(error); // Failure
                return of(false);
            }),
            map(() => true)
        )
    }

    public createRecovery(email: string): Observable<boolean> {
        return from(this.account.createRecovery(email, `${this.platformUrl}/reset-password`))
            .pipe(
                catchError((error) => {
                    console.log(error); // Failure
                    return of(false);
                }),
                map((res) => {
                    //console.log(res);
                    return true
                }
                )
            );
    }
    //=====================SUBSCRIPTIONS=====================

    public executeSubscription<T>(
        endpoint: string,
        payload: any,
        method: ExecutionMethod = ExecutionMethod.POST
    ): Observable<T> {
        if (payload.failureUrl && payload.successUrl) {
            payload.failureUrl = `${this.platformUrl}/${payload.failureUrl}`;
            payload.successUrl = `${this.platformUrl}/${payload.successUrl}`;
        }

        // console.log('executeSubscription payload:', payload);
        const payloadString = JSON.stringify(payload);
        return from(this.functions.createExecution(
            this.stripeFunction,
            payloadString,
            false,
            endpoint,
            method,
            {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            }
        )).pipe(
            map((execution: Models.Execution) => {
                if (execution.responseStatusCode === 303) {
                    const locationHeader = execution.responseHeaders.find(header => header.name === 'location');
                    if (locationHeader && locationHeader.value) {
                        return locationHeader.value as T;
                    } else {
                        throw new Error('Location header not found in response');
                    }
                } else if (execution.responseStatusCode === 200) {
                    // Handle the JSON response for a successful subscription update
                    const responseBody = execution.responseBody ? JSON.parse(execution.responseBody) : null;

                    if (responseBody && responseBody.success) {

                        return (responseBody.subscription || responseBody.customer) as T;

                    } else if (responseBody && responseBody.url) {
                        return responseBody.url;
                    }
                    else {
                        throw new Error('URL or Subscription object not found in response body');
                    }
                } else {
                    throw new Error(`Execution failed with status code ${execution.responseStatusCode}`);
                }
            }),
            catchError((error) => {
                console.error('Error while executing function:', error);
                return throwError(() => new Error('Failed to execute subscription function'));
            })
        );
    }


    //=====================Emails=======================================

    public executeSendEmailFunction<T>(
        endpoint: string,
        payload: any,
        method: ExecutionMethod = ExecutionMethod.POST
    ): Observable<T> {

        const payloadString = JSON.stringify(payload);
        return from(this.functions.createExecution(
            this.sendEmailFunction,
            payloadString,
            false,
            endpoint,
            method,
            {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            }
        )).pipe(
            map((execution: Models.Execution) => {

                if (execution.responseStatusCode === 200) {
                    const response = JSON.parse(execution.responseBody || '{}');
                    return response as T;
                } else {
                    // console.error('Failed to execute body1:', execution.responseBody);
                    throw new Error(`Failed to execute function: ${execution.responseBody}`);
                }
            }),
            catchError((error) => {
                // console.error('Error while executing function:', error);
                // console.error('Failed to execute body2:', error);
                return throwError(() => new Error(`Failed to execute function: ${error.message}`));
            })
        );
    }

    public executeDownloadFileFunction<T>(
        endpoint: string,
        payload: any,
        method: ExecutionMethod = ExecutionMethod.POST
    ): Observable<T> {
        const payloadString = JSON.stringify(payload);
        return from(this.functions.createExecution(
            this.sendEmailFunction,
            payloadString,
            false,
            endpoint,
            method,
            {
                'Content-Type': 'application/json',
                'Accept': 'application/pdf', // Ensure we're asking for PDF
            }
        )).pipe(
            map((execution: Models.Execution) => {

                if (execution.responseStatusCode === 200) {
                    // Check if the responseBody is Base64 encoded.
                    // Base64 strings contain only A-Z, a-z, 0-9, +, /, and optionally '=' for padding.
                    const base64Regex = /^[A-Za-z0-9+/=]+$/;
                    let binaryString: string;
                    if (base64Regex.test(execution.responseBody)) {
                        binaryString = atob(execution.responseBody);
                    } else {
                        binaryString = execution.responseBody;
                    }
                    const len = binaryString.length;
                    const bytes = new Uint8Array(len);
                    for (let i = 0; i < len; i++) {
                        bytes[i] = binaryString.charCodeAt(i);
                    }
                    return new Blob([bytes], { type: 'application/pdf' }) as any as T;
                } else {
                    throw new Error(`Failed to execute function: ${execution.responseBody}`);
                }
            }),
            catchError((error) => {
                return throwError(() => new Error(`Failed to execute function: ${error.message}`));
            })
        );
    }




    //=====================FILES=======================================
    public addJobsBucketFile(file: File) {
        const fileId: string = (Math.floor((Math.random() * 1000) + Date.now())).toString(16);
        let fileUrl: string;
        return scheduled(this.storage.createFile(this.jobsBucket, fileId, file)
            .then(result => {
                //  this.toastr.success("File Uploaded Successfully!");
                fileUrl = this.storage.getFileView(this.jobsBucket, fileId);
                return { fileId, fileUrl };
            }).catch(error => {
                //   this.toastr.error("File Uploaded Unuccessfully!");
                return null;
            }),
            asyncScheduler
        )
    }

    public deleteJobsBucketFile(fileId: string): Observable<boolean> {
        return from(
            this.storage.deleteFile(this.jobsBucket, fileId)
                .then(() => true)
                .catch(() => false)
        );
    }

    public previewFile(fileId: string, bucketId: string): String {
        return this.storage.getFilePreview(bucketId, fileId, 100, 100);
    }


    //=====================FILES PlatformBucket=======================================
    public addFile(file: File) {
        const fileId: string = (Math.floor((Math.random() * 1000) + Date.now())).toString(16);
        let fileUrl: string;
        return scheduled(this.storage.createFile(this.platformBucketId, fileId, file)
            .then(result => {
                //  this.toastr.success("File Uploaded Successfully!");
                fileUrl = this.storage.getFileView(this.platformBucketId, fileId);
                return { fileId, fileUrl };
            }).catch(error => {
                //   this.toastr.error("File Uploaded Unuccessfully!");
                return null;
            }),
            asyncScheduler
        )
    }

    public deleteFile(fileId: string): Observable<boolean> {
        return from(
            this.storage.deleteFile(this.platformBucketId, fileId)
                .then(() => true)
                .catch(() => false)
        );
    }

    public getPlatformFilePreview(fileId: string, width: number, height: number): string {

        if (fileId) {
            return this.storage.getFilePreview(this.platformBucketId, fileId, width, height);
        } else {
            return null;
        }

    }


    //===================== Chat FILES=======================================
    public addChatFile(file: File) {
        const fileId: string = (Math.floor((Math.random() * 1000) + Date.now())).toString(16);
        let fileUrl: string;
        return scheduled(this.storage.createFile(this.chatBucket, fileId, file)
            .then(result => {
                fileUrl = this.storage.getFileView(this.chatBucket, fileId);
                return { fileId, fileUrl };
            }).catch(error => {
                return null;
            }),
            asyncScheduler
        )
    }

    public deleteChatFile(fileId: string): Observable<boolean> {
        return from(
            this.storage.deleteFile(this.chatBucket, fileId)
                .then(() => true)
                .catch(() => false)
        );
    }

    public getChatFilePreview(fileId: string, width: number, height: number): string {
        if (fileId) {
            return this.storage.getFilePreview(
                this.chatBucket,
                fileId,
                width,
                height,

            );
        } else {
            return null;
        }
    }



    //===================QUERIES=====================

    public selectQuery(attribute: string[]): string {
        return Query.select(attribute);
    }

    public searchQuery(attribute: string, value: string): string {
        return Query.search(attribute, value);
    }
    public equalQuery(attribute: string, value: any[]): string {
        return Query.equal(attribute, [...value]);
    }

    public betweenQuery(attribute: string, value1: string, value2: string): string {
        return Query.between(attribute, value1, value2);
    }

    public lessThanEqualQuery(attribute: string, value: any[]): string {
        return Query.lessThanEqual(attribute, [...value]);
    }

    public greaterThanEqualQuery(attribute: string, value: any[]): string {
        return Query.greaterThanEqual(attribute, [...value]);
    }

    public notEqualQuery(attribute: string, value: any[]): string {
        return Query.notEqual(attribute, [...value]);
    }
    public orderDescQuery(attribute: string): string {
        return Query.orderDesc(attribute);
    }
    public orderAscQuery(attribute: string): string {
        return Query.orderAsc(attribute);
    }
    public limitQuery(limit: number): string {
        return Query.limit(limit);
    }
    public offsetQuery(offset: number): string {
        return Query.offset(offset);
    }
    public cursorAfterQuery(cursorAfter: string): string {
        return Query.cursorAfter(cursorAfter);
    }
    public isNotNullQuery(attribute: string): string {
        return Query.isNotNull(attribute);
    }
    public isNullQuery(attribute: string): string {
        return Query.isNull(attribute);
    }

    public orQuery(queries: string[]): string {
        return Query.or(queries);
    }

    public andQuery(queries: string[]): string {
        return Query.and(queries);
    }

    //===================SUBSCRIBE=====================
    // public subscribe<T>(callback: (payload: RealtimeResponseEvent<T>) => void): () => void {
    //   return this.client.subscribe<T>('documents', callback);
    // }
    public unsubscribe = this.client.subscribe(['collections', 'documents', 'files'],
        response => {
            //  console.log("unsubscribe");
        });


    public subscribeForArray<T>(
        Data: Array<{ id: string, data: T }>,
        collectionId: string
    ): Observable<Array<{ id: string, data: T }>> {
        const eventRoot: string = `databases.${this.databaseId}.collections.${collectionId}.documents`;
        const dataSubject: BehaviorSubject<Array<{ id: string, data: T }>> = new BehaviorSubject(Data);

        // Store the unsubscribe function
        const unsubscribe = this.client.subscribe<Models.Document>([eventRoot], callback => {
            const payload = callback.payload as Models.Document;
            const id: string = payload.$id;
            const data: T = <T>this.documentDataObject(payload);
            const index = Data.findIndex(e => e.id === id);

            if (callback.events.includes(`${eventRoot}.${id}.create`)) {
                Data.push({ id, data });
            } else if (callback.events.includes(`${eventRoot}.${id}.update`)) {
                if (index > -1) {
                    Data[index].data = data;
                }
            } else if (callback.events.includes(`${eventRoot}.${id}.delete`)) {
                if (index > -1) {
                    Data.splice(index, 1);
                }
            }

            dataSubject.next(Data);
        });

        // Return an observable that cleans up the subscription when unsubscribed
        return dataSubject.asObservable().pipe(
            finalize(() => {
                // Call the unsubscribe function when the observable is unsubscribed
                unsubscribe();
            })
        );
    }

    // public subscribeToCollection<T>(
    //     initialData: Array<{ id: string, data: T }>,
    //     collectionId: string
    // ): Observable<Array<{ id: string, data: T }>> {
    //     const eventRoot: string = `databases.${this.databaseId}.collections.${collectionId}.documents`;
    //     const dataSubject: BehaviorSubject<Array<{ id: string, data: T }>> = new BehaviorSubject(initialData);

    //     const unsubscribe = this.client.subscribe<Models.Document>([eventRoot], (response: RealtimeResponseEvent<Models.Document>) => {
    //         const currentData = dataSubject.getValue();
    //         const payload = response.payload;
    //         const id: string = payload.$id;
    //         const data: T = <T>this.documentDataObject(payload);
    //         const index = currentData.findIndex(e => e.id === id);

    //         let updatedData = [...currentData];

    //         if (response.events.includes(`${eventRoot}.${id}.create`)) {
    //             updatedData.push({ id, data });
    //         } else if (response.events.includes(`${eventRoot}.${id}.update`)) {
    //             if (index > -1) {
    //                 updatedData[index] = { id, data };
    //             }
    //         } else if (response.events.includes(`${eventRoot}.${id}.delete`)) {
    //             if (index > -1) {
    //                 updatedData.splice(index, 1);
    //             }
    //         }

    //         dataSubject.next(updatedData);
    //     });

    //     return dataSubject.asObservable().pipe(
    //         distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)),
    //         finalize(() => {
    //             unsubscribe();
    //         })
    //     );
    // }


    public subscribeToCollection<T>(
        initialData: Array<{ id: string; data: T }>,
        collectionId: string,
        organisationId: string
    ): Observable<Array<{ id: string; data: T }>> {
        const eventRoot: string = `databases.${this.databaseId}.collections.${collectionId}.documents`;
        const dataSubject: BehaviorSubject<Array<{ id: string; data: T }>> = new BehaviorSubject(initialData);

        const unsubscribe = this.client.subscribe<Models.Document>([eventRoot], (response) => {
            const currentData = dataSubject.getValue();
            const payload = response.payload;
            const id: string = payload.$id;
            const data: T = <T>this.documentDataObject(payload);

            //DUE To AppWrite lag for subscribe query we need to check the payload Only proceed if the payload matches our organisationId
            if (data['organisationID'] !== organisationId) {
                return;
            }

            const index = currentData.findIndex(e => e.id === id);
            let updatedData = [...currentData];

            if (response.events.includes(`${eventRoot}.${id}.create`)) {

                updatedData.push({ id, data });
            } else if (response.events.includes(`${eventRoot}.${id}.update`)) {
                if (index > -1) {
                    updatedData[index] = { id, data };
                } else {
                    // Check if this item should be in our list
                    // For scheduled jobs, check if it matches our criteria:
                    if (collectionId === this.scheduledJobsID) {
                        // For pending jobs list:
                        if (data['jobStatus'] === 'pending') {
                            console.log(`Adding newly pending job ${id} to list`);
                            updatedData.push({ id, data });
                        }
                        // For regular jobs list, we'd check the date but we don't
                        // have access to the date filter here, so we'll add it
                        // and let the component filter it
                        else if (data['teamMemberID']) {
                            console.log(`Adding job ${id} with team member to list`);
                            updatedData.push({ id, data });
                        }
                    } else {
                        // For other collections, just add the item
                        console.log(`Adding updated item ${id} to list`);
                        updatedData.push({ id, data });
                    }
                }
            } else if (response.events.includes(`${eventRoot}.${id}.delete`)) {
                if (index > -1) {
                    updatedData.splice(index, 1);
                }
            }

            dataSubject.next(updatedData);
        });

        return dataSubject.asObservable().pipe(
            finalize(() => {
                unsubscribe();
            })
        );
    }




}

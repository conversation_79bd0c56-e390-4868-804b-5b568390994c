import { Injectable } from '@angular/core';
import { FcmNotification } from './fcmNotification.types';
import { environment } from 'environments/environment';


@Injectable({
    providedIn: 'root'
})
export class FcmNotificationService {

    private readonly FCM_ENDPOINT = environment.notificationEndpoint;


    public async sendNotification(fcmNotification: FcmNotification): Promise<void> {
        const response = await fetch(this.FCM_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(fcmNotification)
        });
        const data = await response.json();
        // console.log(data);
        //TODO: Add popup message  handling
    }


}

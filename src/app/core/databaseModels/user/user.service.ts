import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { User } from 'app/core/databaseModels/user/user.types';
import { BehaviorSubject, catchError, filter, forkJoin, from, map, Observable, of, ReplaySubject, switchMap, take, tap, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';
import { ID } from 'appwrite';

@Injectable({ providedIn: 'root' })
export class UserService implements OnDestroy {

    private _user: ReplaySubject<User> = new ReplaySubject<User>(1);
    private _users: BehaviorSubject<User[]> = new BehaviorSubject(null)
    /**
     * Constructor
     */
    constructor(
        private readonly aws: AppwriteService) {
    }
    ngOnDestroy(): void {
        this.aws.unsubscribe();
    }

    getOrganisationID(): Observable<string> {
        return this.user$.pipe(
            map(user => user.organisationID),
        )
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Accessors
    // -----------------------------------------------------------------------------------------------------

    /**
     * Setter & getter for user
     *
     * @param value
     */
    set user(value: any) {
        // Store the value

        this._user.next(value);
    }

    set users(value: any) {
        // Store the value
        this._users.next(value);
    }

    get user$(): Observable<User> {
        return this._user.asObservable();
    }

    get users$(): Observable<User[]> {
        return this._users.asObservable();
    }


    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------


    getUser(id: string): Observable<User> {
        return this.aws.getDocument<User>(this.aws.userID, id).pipe(
            tap((user) => {
                this._user.next({ $id: id, ...user });
            }),
        )
    }




    getUsers(): Observable<User[]> {
        return this.user$.pipe(
            filter(user => user !== null),
            switchMap(user => {

                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [user.organisationID]),
                    this.aws.orderAscQuery('name'),
                ];
                return this.aws.getDocumentList<User>(this.aws.userID, queries).pipe(
                    map(response => response.map(item => {
                        return {
                            $id: item.id, organisationID
                                : item.data.organisationID, ...item.data
                        }
                    })),
                    tap((users) => {
                        this._users.next(users);
                    }),
                    catchError(error => {
                        console.error('Error fetching users:', error);
                        return of([]); // Return an empty array to avoid errors
                    })
                );
            })
        );
    }
    getMasterUser(id: string): Observable<User | null> {
        const queries: string[] = [
            this.aws.equalQuery("organisationID", [id]),
            this.aws.orderAscQuery('name'),
        ];

        return this.aws.getDocumentList<User>(this.aws.userID, queries).pipe(
            // Transform each item to include $id and other necessary fields
            map(response => response.map(item => ({
                $id: item.id,
                organisationID: item.data.organisationID,
                ...item.data
            }))),


            // Find the first user where defaultUser is true
            map(users => users.find(user => user.defaultUser === true) || null),

            // Handle any errors
            catchError(error => {
                console.error('Error fetching users:', error);
                return of(null); // Emit null in case of error
            })
        );
    }



    create(user: User): Observable<User> {
        return this.checkExistingEmail(user.email).pipe(
            switchMap(emailExists => {
                if (emailExists) {
                    return throwError(() => new Error(`User with email ${user.email} already exists`));
                }

                const password = this.generateRandomPassword();

                // Step 1: Call serverless function to register new user without logging out
                const payload = {
                    email: user.email,
                    password: password,
                    name: user.name,
                    phone: user.phone
                };
                return this.aws.executeUserFunction<any>('/create-user', payload).pipe(
                    map(response => {
                        if (response.userId) {
                            // Step 2: Call serverless function to create new user
                            // Assuming response contains user ID
                            user.authId = response.userId;
                            return user;
                        } else {
                            throw new Error('Failed to create user via serverless function');
                        }
                    }),

                    switchMap(newUser => {
                        return from(this.aws.addDocument(this.aws.userID, newUser)).pipe(
                            switchMap(addedUser => {
                                const _newUser: User = { $id: addedUser.id, ...addedUser.data as User };
                                //check if the this.users$ is not null and exist first For Admin User Panel
                                if (!Array.isArray(this.user$)) {
                                    // console.log('this.user$ is not an array:', _newUser);
                                    return of(_newUser);
                                } else {
                                    return this.users$.pipe(
                                        take(1),
                                        map(users => {
                                            const updatedUsers = [...users, _newUser];
                                            this._users.next(updatedUsers);
                                            return _newUser;
                                        })
                                    );
                                }
                            }),
                        );
                    }),
                    catchError(error => {
                        console.error('Failed to create User:', error);
                        return throwError(() => new Error(error.message || 'Failed to create User'));
                    })
                );
            })
        );
    }


    delete(user: User): Observable<any> {
        const payload = { userId: user.authId };

        return this.aws.executeUserFunction<any>('/delete-user', payload).pipe(
            switchMap(() => {
                return from(this.aws.deleteDocument(this.aws.userID, user.$id));
            }),
            catchError(error => {
                console.error('Failed to delete User:', error);
                return throwError(() => new Error(error.message || 'Failed to delete User'));
            })
        );
    }






    async register(email: string, password: string, name: string): Promise<string> {
        try {
            const userId = ID.unique();

            const user = await this.aws.addUser(userId, email, password, name);
            return user.$id;
        } catch (error: any) {
            if (error && error.code === 409) {
                throw new Error(`User with email ${email} already exists: ${error.message}`);
            } else {
                throw error;
            }
        }
    }






    /**
     * Update the user
     *
     * @param user
     */
    update(user: User, emailChanged: boolean, phoneChanged: boolean): Observable<any> {
        return this.aws.updateDocument<User>(this.aws.userID, user.$id, user).pipe(
            switchMap(updatedUser => {
                const updateOperations: Observable<any>[] = [];

                if (emailChanged) {
                    // Call serverless function to update user email
                    const emailPayload = { userId: user.authId, email: user.email };
                    updateOperations.push(this.aws.executeUserFunction<any>('/update-user-email', emailPayload));
                }

                if (phoneChanged) {
                    // Call serverless function to update user phone
                    const phonePayload = { userId: user.authId, phone: user.phone };
                    updateOperations.push(this.aws.executeUserFunction<any>('/update-user-phone', phonePayload));
                }

                if (updateOperations.length > 0) {
                    // Wait for all update operations to complete
                    return forkJoin(updateOperations).pipe(
                        switchMap(() => {
                            this._user.next({ $id: user.$id, ...updatedUser });
                            return this.getUser(user.$id);
                        })
                    );
                } else {
                    // No update operations, just return the updated user
                    this._user.next({ $id: user.$id, ...updatedUser });
                    return of(updatedUser);
                }
            }),
            catchError(error => {
                console.error('Failed to update User:', error);
                return throwError(() => new Error('Failed to update User'));
            })
        );
    }


    updatePhoneVerified(userID: string): Observable<any> {
        return this.aws.updateDocument<User>(this.aws.userID, userID, { verifiedPhone: true }).pipe(
            switchMap(updatedUser => {
                this._user.next({ $id: userID, ...updatedUser });
                return this.getUser(userID);
            }),
            catchError(error => {
                console.error('Failed to update User:', error);
                return throwError(() => new Error('Failed to update User'));
            })
        );

    }

    updateEmailVerified(userID: string): Observable<any> {
        return this.aws.updateDocument<User>(this.aws.userID, userID, { verifiedEmail: true }).pipe(
            switchMap(updatedUser => {
                this._user.next({ $id: userID, ...updatedUser });
                return this.getUser(userID);
            }),
            catchError(error => {
                console.error('Failed to update User:', error);
                return throwError(() => new Error('Failed to update User'));
            })
        );
    }



    checkExistingEmail(email: string): Observable<boolean> {
        if (!email) return of(false);
        return this.aws.getDocumentList<User>(this.aws.userID, [this.aws.equalQuery('email', [email])]).pipe(
            map(response => response.length > 0),
            catchError(error => {
                console.error('Error checking existing email:', error);
                return of(false);
            })
        );
    }


    checkExistingPhone(phone: string): Observable<boolean> {
        if (phone == null) return of(false);

        return this.aws.getDocumentList<User>(this.aws.userID, [this.aws.equalQuery('phone', [phone])]).pipe(
            map(response => response.length > 0),
            catchError(error => {
                console.error('Error fetching users Phone:', error);
                return of(false); // Return an empty array to avoid errors
            })
        );
    }

    requestPhoneVerification(): Observable<any> {
        return this.aws.createPhoneVerification();
    }
    verifyOTPcode(otp: string, userID: string): Observable<any> {
        return this.aws.verifyingOTP(otp, userID);
    }

    requestEmailVerification(): Observable<any> {
        return this.aws.createEmailVerification();
    }

    verifyEmail(userId: string, secret: string): Observable<any> {
        return this.aws.verifyingEmail(userId, secret);
    }

    createRecovery(email: string): Observable<any> {
        return this.aws.createRecovery(email);
    }

    updatePassword(oldPassword: string, newPassword: string): Observable<boolean> {
        if (!oldPassword || !newPassword) {
            return throwError(() => new Error('Invalid parameters: oldPassword and newPassword cannot be null or empty'));
        }
        return this.aws.updatePassword(newPassword, oldPassword)
            .pipe(
                catchError(error => {
                    console.error('Error updating password:', error);
                    return of(false);
                })
            );
    }

    /**
          * Uploads a file to the server and returns an observable that emits an object with the fileId and fileUrl.
          *
          * @param {File} file - The file to be uploaded.
          * @return {Observable<{ fileId: string, fileUrl: string }>} An observable that emits the fileId and fileUrl of the uploaded file.
          */


    uploadFile(file: File): Observable<{ fileId: string, fileUrl: string }> {
        return this.aws.addFile(file).pipe(
            map(result => {
                if (!result) {
                    throw new Error('File upload failed');
                }
                return {
                    fileId: result.fileId,
                    fileUrl: result.fileUrl
                };
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to upload and retrieve file details');
            })
        );
    }
    deleteFile(fileId: string): Observable<boolean> {
        return this.aws.deleteFile(fileId);
    }
    generateRandomPassword(length: number = 12): string {
        const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+[]{}|;:,.<>?";
        let password = "";
        for (let i = 0; i < length; i++) {
            const randomIndex = Math.floor(Math.random() * charset.length);
            password += charset[randomIndex];
        }
        return password;
    }

}

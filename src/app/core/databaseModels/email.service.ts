import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { BehaviorSubject, catchError, filter, forkJoin, from, map, Observable, of, ReplaySubject, switchMap, take, tap, throwError } from 'rxjs';
import { AppwriteService } from './appwrite.service';


@Injectable({ providedIn: 'root' })
export class EmailService implements OnDestroy {
    constructor(
        private aws: AppwriteService
    ) { }

    ngOnDestroy(): void {
        this.aws.unsubscribe();
    }

    sendContactEmail(data: any): Observable<any> {

        if (!data) {
            return of(null);
        }
        if (!data.captchaResponse) {
            return of(null);
        }

        let sendTo = data.inquiryType == "inquiry" ? "<EMAIL>" : "<EMAIL>";

        const payload = {
            to: sendTo,
            subject: `🛟 Website contact -${data.inquiryType}  🗣️`,
            fullName: data.name,
            email: data.email,
            phone: data.phone,
            message: data.message,
            type: "needHelp"
        }
        return this.aws.executeSendEmailFunction('/', payload).pipe(
            map(response => {
                if (response) {
                    // console.log('Send email:', response);
                    return response
                }
            }),
            catchError(error => {
                console.error('Failed to send email:', error);
                return throwError(() => new Error(error.message || 'Failed to send email'));
            })
        );
    }

    sendInvoiceEmail(payloadData: any): Observable<any> {
        if (!payloadData) {
            return of(null);
        }

        return this.aws.executeSendEmailFunction('/', payloadData).pipe(
            map(response => {
                if (response) {

                    return response
                }
            }),
            catchError(error => {
                // Only log essential information
                console.error('Failed to send invoice email:', {
                    error: error.message,
                    invoiceNumber: payloadData.invoiceNumber
                });
                return throwError(() => new Error(error.message || 'Failed to send invoice email'));
            })
        );
    }

    downloadInvoicePDF(payloadData: any): Observable<any> {
        if (!payloadData) {
            return of(null);
        }

        return this.aws.executeDownloadFileFunction('/', payloadData).pipe(
            map(response => {
                if (response) {

                    return response
                }
            }),
            catchError(error => {
                // Only log essential information
                console.error('Failed to send invoice email:', {
                    error: error.message,
                    invoiceNumber: payloadData.invoiceNumber
                });
                return throwError(() => new Error(error.message || 'Failed to send invoice email'));
            })
        );
    }


}

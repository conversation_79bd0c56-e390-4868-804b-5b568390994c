export interface Customer {
    $id: string;
    organisationID: string;
    avatar?: string | null;
    avatarImageId?: string | null;
    name: string;
    emails?: {
        email: string;
        label: string;
    }[];
    phoneNumbers?: {
        phoneNumber: string;
        label: string;
    }[];

    company?: string;
    birthday?: Date | null;
    addresses?: {
        address: string;
        label: string;
        latLon: string;
    }[];
    notes?: string | null;
    tags: string[];
    chatPhoneNumber: string;
    deviceTokenId?: string;
}



export interface CustomerTags {
    $id?: string;
    organisationID: string,
    title?: string;
}

import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { BehaviorSubject, catchError, concatMap, finalize, forkJoin, from, map, Observable, of, reduce, shareReplay, Subject, Subscription, switchMap, take, takeUntil, tap, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { Customer, CustomerTags } from './contacts.types';

@Injectable({ providedIn: 'root' })
export class CustomerService implements OnDestroy {

    private _customer: BehaviorSubject<Customer> = new BehaviorSubject(null);
    private _customers: BehaviorSubject<Customer[]> = new BehaviorSubject(null);

    private _customerTag: BehaviorSubject<CustomerTags> = new BehaviorSubject(null);
    private _CustomerTags: BehaviorSubject<CustomerTags[]> = new BehaviorSubject(null);

    // Cache-related properties
    private customersCache: Customer[] = null;
    private realTimeSubscription: Subscription;
    private destroy$ = new Subject<void>();
    private enableRealTimeUpdates: boolean = true;
    private currentPage: number = 1;
    private pageSize: number = 1000; // Increased page size for faster loading
    // Total number of customers (accessible to components)
    totalCustomers: number = 0;
    private isLoadingMore: boolean = false;
    private loadAllInProgress: boolean = false;
    private loadAllComplete: boolean = false;

    constructor(private readonly aws: AppwriteService,
        private _userService: UserService,) {
        // Start listening for real-time updates
        this.listenToRealTimeCustomers();
    }

    getOrganisationID(): Observable<string> {
        return this._userService.user$.pipe(
            map(user => user.organisationID)
        );
    }


    set customer(value: Customer) {
        this._customer.next(value);
    }

    set tag(value: CustomerTags) {
        this._customerTag.next(value);
    }

    get customer$(): Observable<Customer> {
        return this._customer.asObservable();
    }

    get tag$(): Observable<CustomerTags> {
        return this._customerTag.asObservable();
    }

    get customers$(): Observable<Customer[]> {
        return this._customers.asObservable();
    }

    get tags$(): Observable<CustomerTags[]> {
        return this._CustomerTags.asObservable();
    }

    getCustomer(id: string): Observable<Customer> {
        return this.aws.getDocument<Customer>(this.aws.customersID, id).pipe(
            tap((contact) => {
                this._customer.next({ $id: id, ...contact });
            }),
        );
    }

    getCustomerTag(id: string): Observable<CustomerTags> {
        return this.aws.getDocument<CustomerTags>(this.aws.customerTagsID, id).pipe(
            tap((tag) => {
                this._customerTag.next(tag);
            }),
        );
    }



    /**
     * Clean up subscriptions on destroy
     */
    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
        if (this.realTimeSubscription) {
            this.realTimeSubscription.unsubscribe();
        }
    }

    /**
     * Get customers with caching and pagination
     * Uses in-memory cache to reduce Appwrite read operations
     * Implements pagination for large datasets
     */
    getCustomers(page: number = 1, pageSize: number = 1000, forceRefresh: boolean = false): Observable<Customer[]> {
        this.pageSize = pageSize;
        this.currentPage = page;

        // Check if we have valid cached data and not forcing refresh
        if (this.customersCache && !forceRefresh) {
            console.log('Using cached customers data');
            // Return cached data if it's available
            this._customers.next(this.customersCache);

            // If we haven't loaded all customers yet, start loading all in the background
            if (!this.loadAllComplete && !this.loadAllInProgress) {
                this.loadAllCustomers().subscribe();
            }

            return of(this.customersCache);
        }

        // If no valid cache or forcing refresh, fetch from Appwrite
        console.log(`Fetching customers data from Appwrite (page ${page}, size ${pageSize})`);
        return this.getOrganisationID().pipe(
            switchMap(organisationID => {
                const offset = (page - 1) * pageSize;
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.orderAscQuery('name'),
                    this.aws.limitQuery(pageSize),
                    this.aws.offsetQuery(offset)
                ];
                return this.aws.getDocumentList<Customer>(this.aws.customersID, queries).pipe(
                    map(response => {
                        // Handle different response formats from Appwrite
                        let customers: Customer[] = [];
                        let total = 0;

                        // Check if response is an array (older Appwrite SDK)
                        if (Array.isArray(response)) {
                            customers = response.map(item => ({ $id: item.id, ...item.data }));
                            total = Math.max(
                                this.totalCustomers,
                                (page - 1) * pageSize + customers.length
                            );

                            // If we got fewer results than pageSize, we're at the end
                            if (customers.length < pageSize) {
                                total = (page - 1) * pageSize + customers.length;
                                this.loadAllComplete = true;
                            }
                        }
                        // Check if response has documents property (newer Appwrite SDK)
                        else if (response && typeof response === 'object') {
                            const resp = response as any; // Type assertion to handle dynamic properties
                            if (resp.documents && Array.isArray(resp.documents)) {
                                customers = resp.documents.map((item: any) => ({ $id: item.$id || item.id, ...item }));
                                if (typeof resp.total === 'number') {
                                    total = resp.total;
                                } else {
                                    total = Math.max(
                                        this.totalCustomers,
                                        (page - 1) * pageSize + customers.length
                                    );
                                }

                                // If we got fewer results than pageSize, we're at the end
                                if (resp.documents.length < pageSize) {
                                    this.loadAllComplete = true;
                                }
                            }
                        }

                        // Update total count for pagination
                        this.totalCustomers = total;

                        return customers;
                    }),
                    tap((contacts) => {
                        // Initialize or append to cache based on page
                        if (page === 1) {
                            this.customersCache = contacts;
                        } else if (this.customersCache) {
                            // Append new page to existing cache
                            this.customersCache = [...this.customersCache, ...contacts];
                        } else {
                            this.customersCache = contacts;
                        }

                        // Update BehaviorSubject
                        this._customers.next(this.customersCache);

                        // If this is the first page and we have more to load, start loading all in the background
                        if (page === 1 && contacts.length === pageSize && !this.loadAllInProgress && !this.loadAllComplete) {
                            this.loadAllCustomers().subscribe();
                        }
                    }),
                    shareReplay(1) // Share the result with multiple subscribers
                );
            }),
        )
    }

    /**
     * Load more customers (next page)
     * Used for infinite scrolling or pagination controls
     */
    loadMoreCustomers(): Observable<Customer[]> {
        if (this.isLoadingMore) {
            return of(this.customersCache || []);
        }

        // Check if we have more data to load
        if (this.customersCache && this.totalCustomers > this.customersCache.length) {
            this.isLoadingMore = true;
            const nextPage = this.currentPage + 1;

            return this.getCustomers(nextPage, this.pageSize).pipe(
                finalize(() => {
                    this.isLoadingMore = false;
                    this.currentPage = nextPage;
                })
            );
        }

        // No more data to load
        return of(this.customersCache || []);
    }

    /**
     * Load all customers in the background
     * This will load all customers in batches until all are loaded
     */
    loadAllCustomers(): Observable<Customer[]> {
        // If we're already loading or have completed loading all customers, return the current cache
        if (this.loadAllInProgress || this.loadAllComplete) {
            return of(this.customersCache || []);
        }

        this.loadAllInProgress = true;
        console.log('Starting background loading of all customers');

        // Create an observable that will load all customers in batches
        return new Observable<Customer[]>(observer => {
            const loadNextBatch = (page: number) => {
                // If we already have all customers, complete the observable
                if (this.loadAllComplete) {
                    this.loadAllInProgress = false;
                    observer.next(this.customersCache);
                    observer.complete();
                    return;
                }

                console.log(`Loading customer batch ${page} (${this.customersCache?.length || 0}/${this.totalCustomers})`);

                this.getCustomers(page, this.pageSize).subscribe({
                    next: (customers) => {
                        // Check if we've loaded all customers
                        if (customers.length < this.pageSize ||
                            (this.customersCache && this.customersCache.length >= this.totalCustomers)) {
                            console.log('All customers loaded successfully');
                            this.loadAllComplete = true;
                            this.loadAllInProgress = false;
                            observer.next(this.customersCache);
                            observer.complete();
                        } else {
                            // Load the next batch
                            setTimeout(() => loadNextBatch(page + 1), 300); // Small delay to avoid overwhelming Appwrite
                        }
                    },
                    error: (error) => {
                        console.error('Error loading all customers:', error);
                        this.loadAllInProgress = false;
                        observer.error(error);
                    }
                });
            };

            // Start loading from the next page
            loadNextBatch(this.currentPage + 1);

            // Return a teardown function
            return () => {
                this.loadAllInProgress = false;
            };
        }).pipe(
            shareReplay(1)
        );
    }

    /**
     * Check if there are more customers to load
     */
    hasMoreCustomers(): boolean {
        return this.customersCache && this.totalCustomers > this.customersCache.length && !this.loadAllComplete;
    }

    /**
     * Check if all customers have been loaded
     */
    isAllCustomersLoaded(): boolean {
        return this.loadAllComplete;
    }

    /**
     * Check if customers are currently being loaded in the background
     */
    isLoadingAllCustomers(): boolean {
        return this.loadAllInProgress;
    }

    /**
     * Listen to real-time customer updates
     * This keeps the cache in sync with the server
     */
    listenToRealTimeCustomers(): void {
        if (!this.enableRealTimeUpdates) {
            return;
        }

        // Unsubscribe from any existing subscription
        if (this.realTimeSubscription) {
            this.realTimeSubscription.unsubscribe();
        }

        this.realTimeSubscription = this.getOrganisationID().pipe(
            switchMap(organisationId => {
                console.log('Setting up real-time customer updates for organisation:', organisationId);

                // Check if we already have data in the BehaviorSubject
                const currentCustomers = this._customers.getValue() || [];

                // Use existing data if available, otherwise fetch
                const initialData$ = currentCustomers.length > 0
                    ? of(currentCustomers.map(item => ({ id: item.$id, data: item })))
                    : this.getCustomers().pipe(
                        map(customers => customers.map(item => ({ id: item.$id, data: item })))
                    );

                return initialData$.pipe(
                    switchMap(initialData => {
                        if (initialData.length === 0) {
                            console.log('No initial customers data, skipping real-time subscription');
                            return of([]);
                        }
                        console.log(`Subscribing to real-time updates for ${initialData.length} customers`);

                        // Subscribe to the collection for real-time updates
                        return this.aws.subscribeToCollection(
                            initialData,
                            this.aws.customersID,
                            organisationId
                        );
                    }),
                    map(updatedData => updatedData.map(item => ({ $id: item.id, ...item.data }))),
                    tap(updatedCustomers => {
                        // Get current customers from the BehaviorSubject
                        const currentCustomers = this._customers.getValue() || [];

                        // Merge the updated customers with existing ones
                        const mergedCustomers = this.mergeCustomersData(currentCustomers, updatedCustomers);

                        // Update the cache
                        this.customersCache = mergedCustomers;

                        // Update the BehaviorSubject
                        this._customers.next(mergedCustomers);

                        // Update total count
                        this.totalCustomers = mergedCustomers.length;
                    })
                );
            }),
            takeUntil(this.destroy$)
        ).subscribe({
            next: () => console.log('Real-time customer subscription active'),
            error: (error) => console.error('Error in real-time customers subscription:', error),
            complete: () => console.log('Real-time customer subscription completed')
        });
    }

    // We no longer need the processRealTimeUpdates method as we're using mergeCustomersData directly



    /**
     * Get filtered customers by search term
     * This tries to use cache first for better performance
     */
    getFilteredCustomers(searchTerm: string): Observable<Customer[]> {
        // If we have a cache and the search term is empty, use the cache
        if (this.customersCache && !searchTerm.trim()) {
            console.log('Using cached customers for empty search');
            return of(this.customersCache);
        }

        // If we have a cache, try to filter locally first for better performance
        if (this.customersCache && searchTerm.trim()) {
            const lowercaseSearch = searchTerm.toLowerCase();
            const filteredResults = this.customersCache.filter(customer =>
                customer.name.toLowerCase().includes(lowercaseSearch)
            );

            // If we have results from cache, use them
            if (filteredResults.length > 0) {
                console.log('Using filtered cached customers');
                this._customers.next(filteredResults); // Update the behavior subject
                return of(filteredResults);
            }
        }

        // Otherwise, query Appwrite
        console.log('Searching customers from Appwrite');
        return this.getOrganisationID().pipe(
            switchMap(organisationID => {
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.searchQuery('name', `${searchTerm}`),
                    //TODO:this.aws.searchQuery('phoneNumbers', searchTerm),
                    this.aws.orderAscQuery('name'),
                ];
                return this.aws.getDocumentList<Customer>(this.aws.customersID, queries).pipe(
                    map(response => response.map(item => {
                        return { $id: item.id, ...item.data }
                    })),
                    tap((contacts) => {
                        // Don't update the main cache for filtered results
                        // Just update the behavior subject
                        this._customers.next(contacts);
                    }),
                );
            }),
        )
    }


    /**
     * Create a new customer
     */
    createCustomer(data: any): Observable<Customer> {
        data.addresses = data.addresses.map((obj: any) => JSON.stringify(obj));
        data.phoneNumbers = data.phoneNumbers.map((obj: any) => JSON.stringify(obj));
        data.emails = data.emails.map((obj: any) => JSON.stringify(obj));

        return this.customers$.pipe(
            take(1),
            switchMap(customers =>

                this.aws.addDocument(this.aws.customersID, data).pipe(
                    map(newCustomer => {
                        const _newCustomer: Customer = {
                            $id: newCustomer.id,
                            ...newCustomer.data as Customer
                        }
                        // Update cache if it exists
                        if (this.customersCache) {
                            this.customersCache = [_newCustomer, ...this.customersCache];
                            // Cache updated
                        }

                        this._customers.next([_newCustomer, ...customers]);
                        return _newCustomer;
                    })
                )
            )
        )
    }

    /**
     * Update an existing customer
     */
    updateCustomer(id: string, data: any): Observable<Customer> {
        data.addresses = data.addresses.map((obj: any) => JSON.stringify(obj));
        data.phoneNumbers = data.phoneNumbers.map((obj: any) => JSON.stringify(obj));
        data.emails = data.emails.map((obj: any) => JSON.stringify(obj));

        return this.aws.updateDocument<Customer>(this.aws.customersID, id, data)
            .pipe(
                switchMap(updatedCustomer => {
                    // Update the individual item if it's the one currently being observed
                    updatedCustomer.$id = id;
                    this._customer.next(updatedCustomer);

                    // Update the list of items
                    return this.customers$.pipe(
                        take(1),
                        map(customers => {
                            const index = customers.findIndex(item => item.$id === id);
                            updatedCustomer.$id = id;
                            if (index !== -1) {
                                customers[index] = updatedCustomer;
                                this._customers.next(customers);

                                // Update cache if it exists
                                if (this.customersCache) {
                                    const cacheIndex = this.customersCache.findIndex(item => item.$id === id);
                                    if (cacheIndex !== -1) {
                                        this.customersCache[cacheIndex] = updatedCustomer;
                                        // Cache updated
                                    }
                                }
                            }
                            return updatedCustomer;
                        })
                    );
                }),
                catchError(error => {
                    console.error(error);
                    return throwError(() => 'Failed to update customer');
                })
            );
    }

    /**
     * Delete a customer
     */
    deleteCustomer(id: string): Observable<boolean> {
        return this.customers$.pipe(
            take(1),
            switchMap(customers => {
                return this.aws.deleteDocument(this.aws.customersID, id).pipe(
                    map(() => {
                        // Update the customers BehaviorSubject
                        const index = customers.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            // Create a new array instead of modifying the existing one
                            const updatedCustomers = [...customers];
                            updatedCustomers.splice(index, 1);
                            this._customers.next(updatedCustomers);

                            // Update cache if it exists
                            if (this.customersCache) {
                                const cacheIndex = this.customersCache.findIndex(item => item.$id === id);
                                if (cacheIndex !== -1) {
                                    // Create a new array for the cache too
                                    const updatedCache = [...this.customersCache];
                                    updatedCache.splice(cacheIndex, 1);
                                    this.customersCache = updatedCache;

                                    // Update total count
                                    this.totalCustomers = this.customersCache.length;
                                    console.log(`Customer deleted from cache: ${id}`);
                                }
                            }

                            return true;
                        }
                        return false;
                    }),
                    catchError(error => {
                        console.error('Error deleting customer:', error);
                        // Return false to indicate failed deletion
                        return of(false);
                    })
                )
            })
        )
    }


    getCustomerTags(): Observable<CustomerTags[]> {
        return this.getOrganisationID().pipe(
            switchMap(organisationID => {
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.orderAscQuery('title'),
                ];
                return this.aws.getDocumentList<CustomerTags>(this.aws.customerTagsID, queries).pipe(
                    map(response => response.map(item => {
                        return { $id: item.id, ...item.data }
                    })),
                    tap((tags) => {
                        this._CustomerTags.next(tags);
                    }),
                );

            }),
        )
    }

    createCustomerTag(data: CustomerTags): Observable<CustomerTags> {
        return this.tags$.pipe(
            take(1),
            switchMap(tags =>
                this.aws.addDocument(this.aws.customerTagsID, data).pipe(
                    map(newTag => {
                        const _newTag: CustomerTags = {
                            $id: newTag.id,
                            ...newTag.data as CustomerTags
                        }
                        this._CustomerTags.next([_newTag, ...tags]);
                        return _newTag;
                    })
                )
            )
        )
    }
    updateCustomerTag(id: string, data: CustomerTags): Observable<CustomerTags> {
        return this.aws.updateDocument<CustomerTags>(this.aws.customerTagsID, id, data).pipe(
            switchMap(updatedTag => {
                // Update the individual item if it's the one currently being observed
                this._customerTag.next(updatedTag);

                // Update the list of items
                return this.tags$.pipe(
                    take(1),
                    map(tags => {
                        const index = tags.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            tags[index] = updatedTag;
                            this._CustomerTags.next(tags);
                        }
                        return updatedTag;
                    })

                );
            }),
            catchError(error => {
                console.error(error);
                return throwError(() => 'Failed to update team member');

            })
        )
    }

    deleteCustomerTag(id: string): Observable<boolean> {
        return this.tags$.pipe(
            take(1),
            switchMap(tags => {
                return this.aws.deleteDocument(this.aws.customerTagsID, id).pipe(
                    map(() => {
                        const index = tags.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            tags.splice(index, 1);
                            this._CustomerTags.next(tags);
                            return true;
                        }
                    }), catchError(error => {
                        console.error(error);
                        // Return false to indicate failed deletion
                        return of(false);
                    })
                )
            })
        )

    }
    /**
     * Force refresh the customers cache
     * Use this when you need to ensure fresh data
     */
    refreshCustomersCache(): Observable<Customer[]> {
        // Invalidate cache
        this.customersCache = null;
        this.currentPage = 1;
        this.totalCustomers = 0;

        // Fetch fresh data
        return this.getCustomers(1, this.pageSize, true);
    }

    /**
     * Pause real-time updates
     */
    pauseRealTimeUpdates(): void {
        this.enableRealTimeUpdates = false;
        if (this.realTimeSubscription) {
            this.realTimeSubscription.unsubscribe();
        }
        // Signal all observables to complete
        this.destroy$.next();
    }

    /**
     * Resume real-time updates
     */
    resumeRealTimeUpdates(): void {
        this.enableRealTimeUpdates = true;
        // Create a new Subject for takeUntil
        this.destroy$ = new Subject<void>();
        // Start listening again
        this.listenToRealTimeCustomers();
    }

    /**
     * Helper method to merge customer data properly
     * This handles additions, updates, and deletions in one place
     */
    private mergeCustomersData(currentCustomers: Customer[], updatedCustomers: Customer[]): Customer[] {
        // Create a new array to avoid mutating the original
        const result = [...currentCustomers];

        // Get the IDs of all updated customers
        const updatedIds = new Set(updatedCustomers.map(customer => customer.$id));

        // Handle deletions: remove customers that are in current but not in updated
        // This is important for real-time deletion events
        const customersAfterDeletion = result.filter(customer => {
            // If the customer is not in the updated list and is in the updated IDs set,
            // it means it was deleted
            const wasDeleted = updatedIds.has(customer.$id) &&
                !updatedCustomers.some(updated => updated.$id === customer.$id);

            if (wasDeleted) {
                console.log(`Customer deleted (via merge): ${customer.$id}`);
            }

            // Keep the customer if it wasn't deleted
            return !wasDeleted;
        });

        // Handle updates and additions
        updatedCustomers.forEach(updatedCustomer => {
            const index = customersAfterDeletion.findIndex(customer => customer.$id === updatedCustomer.$id);

            if (index !== -1) {
                // Update existing customer
                customersAfterDeletion[index] = updatedCustomer;
            } else {
                // Add new customer
                customersAfterDeletion.push(updatedCustomer);
            }
        });

        // Sort by name to maintain consistent order
        return customersAfterDeletion.sort((a, b) => a.name?.localeCompare(b.name || '') || 0);
    }


    /**
     * Uploads a file to the server and returns an observable that emits an object with the fileId and fileUrl.
     *
     * @param {File} file - The file to be uploaded.
     * @return {Observable<{ fileId: string, fileUrl: string }>} An observable that emits the fileId and fileUrl of the uploaded file.
     */


    uploadFile(file: File): Observable<{ fileId: string, fileUrl: string }> {
        return this.aws.addFile(file).pipe(
            map(result => {
                if (!result) {
                    throw new Error('File upload failed');
                }
                return {
                    fileId: result.fileId,
                    fileUrl: result.fileUrl
                };
            }),
            catchError(error => {
                console.error(error);
                return throwError(() => 'Failed to upload and retrieve file details');
            })
        );
    }

    /**
    * Deletes a file with the given fileId.
    *
    * @param {string} fileId - The ID of the file to be deleted.
    * @return {Observable<boolean>} - A boolean indicating whether the file was successfully deleted.
    */

    deleteFile(fileId: string): Observable<boolean> {
        return this.aws.deleteFile(fileId);
    }



}

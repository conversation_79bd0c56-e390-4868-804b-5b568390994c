import { Injectable } from '@angular/core';
import { TeamMember } from './teammember.types'
import { BehaviorSubject, catchError, map, Observable, of, Subject, Subscription, switchMap, take, takeUntil, tap, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';
import { UserService } from 'app/core/databaseModels/user/user.service';


@Injectable({ providedIn: 'root' })
export class TeamMembersService {

    private _teamMember: BehaviorSubject<TeamMember> = new BehaviorSubject(null);
    private _teamMembers: BehaviorSubject<TeamMember[]> = new BehaviorSubject(null);

    // Control flag to enable or disable real-time updates
    private enableRealTimeUpdates = true;

    // Initialize Subscription reference for real-time updates
    private realTimeDataSubscription: Subscription = new Subscription();
    private destroy$ = new Subject<void>();

    /**
     * Constructor
     */
    constructor(private readonly aws: AppwriteService, private _userService: UserService,) {


    }




    getOrganisationID(): Observable<string> {
        return this._userService.user$.pipe(
            map(user => user.organisationID)
        );
    }


    // -----------------------------------------------------------------------------------------------------
    // @ Accessors
    // -----------------------------------------------------------------------------------------------------

    /**
     * Setter & getter for user
     *
     * @param value
     */
    set teamMember(value: any) {
        // Store the value

        this._teamMember.next(value);
    }

    get teamMember$(): Observable<TeamMember> {
        return this._teamMember.asObservable();
    }

    get teamMembers$(): Observable<TeamMember[]> {
        return this._teamMembers.asObservable();
    }
    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    getTeamMember(id: string): Observable<TeamMember> {
        return this.aws.getDocument<TeamMember>(this.aws.teamMembersID, id).pipe(
            tap((response) => {
                this._teamMember.next(response);
            }),
        );

    }


    getTeamMembers(): Observable<TeamMember[]> {
        return this.getOrganisationID().pipe(
            switchMap(organisationID => {
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.orderAscQuery('name'),
                    this.aws.limitQuery(100),
                ];

                return this.aws.getDocumentList<TeamMember>(this.aws.teamMembersID, queries).pipe(
                    map(response => response.map(item => {

                        return { $id: item.id, ...item.data }
                    })), // Extract 'data' from each item
                    tap(response => {

                        this._teamMembers.next(response);
                    }),

                );
            }),
        )
    }

    /**
    * Creates a new team member.
    *
    * @param {TeamMember} data - The data of the new team member.
    * @return {Observable<TeamMember>} The observable that emits the newly created team member.
    */



    createTeamMember(data: TeamMember): Observable<TeamMember> {
        data.phone = data.phone.replace(/\s+/g, '');
        return this.teamMembers$.pipe(
            take(1),
            switchMap(teamMembers =>
                this.aws.addDocument(this.aws.teamMembersID, data).pipe(
                    map(newTeamMember => {
                        const _newTeamMember: TeamMember = {
                            $id: newTeamMember.id,
                            ...newTeamMember.data as TeamMember
                        }
                        this._teamMembers.next([_newTeamMember, ...teamMembers]);
                        return _newTeamMember;
                    })
                )
            )
        )
    }

    /**
     * Updates a team member.
     *
     * @param {string} id - The ID of the team member to be updated.
     * @param {TeamMember} teamMember - The updated team member object.
     * @return {Observable<TeamMember>} - An observable that emits the updated team member.
     */


    updateTeamMember(id: string, teamMember: TeamMember): Observable<TeamMember> {
        return this.aws.updateDocument<TeamMember>(this.aws.teamMembersID, id, teamMember).pipe(
            switchMap(updatedTeamMember => {
                // Update the individual team member if it's the one currently being observed
                this._teamMember.next(updatedTeamMember);

                // Update the list of team members
                return this.teamMembers$.pipe(
                    take(1),
                    map(teamMembers => {
                        const index = teamMembers.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            teamMembers[index] = updatedTeamMember;
                            this._teamMembers.next(teamMembers);
                        }
                        return updatedTeamMember;
                    })
                );
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to update team member');
            })
        );
    }



    updateUnreadMessage(id: string): Observable<boolean> {

        return this.aws.updateDocument<TeamMember>(this.aws.teamMembersID, id, { unreadMessagesCount: 0 })
            .pipe(
                switchMap(updatedTeamMember => {
                    this._teamMember.next({ $id: id, ...updatedTeamMember });
                    return of(true);
                }),
                catchError(error => {
                    console.error(error);
                    return throwError('Failed to update team member');
                })

            )

    }

    updateTeamMemberUnreadMessage(id: string): Observable<boolean> {
        // first get current value then add to unread message count
        const teamMember = this._teamMember.getValue();
        teamMember.teamMemberUnreadMessagesCount = teamMember.teamMemberUnreadMessagesCount + 1;
        this._teamMember.next({ ...teamMember });

        return this.aws.updateDocument<TeamMember>(this.aws.teamMembersID, id, { teamMemberUnreadMessagesCount: teamMember.teamMemberUnreadMessagesCount })
            .pipe(
                switchMap(updatedTeamMember => {
                    this._teamMember.next({ $id: id, ...updatedTeamMember });
                    return of(true);
                }),
                catchError(error => {
                    console.error(error);
                    return throwError('Failed to update team member');
                }))
    }


    /**
    * Deletes a team member with the specified ID.
    *
    * @param {string} id - The ID of the team member to delete.
    * @return {Observable<boolean>} Returns an Observable that emits `true` if the deletion was successful, or `false` if it failed.
    */

    deleteTeamMember(id: string): Observable<boolean> {
        return this.teamMembers$.pipe(
            take(1),
            switchMap(teamMembers => {
                return this.aws.deleteDocument(this.aws.teamMembersID, id).pipe(
                    map(() => {
                        // Find the index of the deleted team Member
                        const index = teamMembers.findIndex(item => item.$id === id);

                        // If the team member is found, delete it
                        if (index !== -1) {
                            teamMembers.splice(index, 1);
                        }

                        // Update the teamMembers BehaviorSubject
                        this._teamMembers.next(teamMembers);
                        //  console.log('deleteTeamMember:', this._teamMembers);
                        // Return true to indicate successful deletion
                        return true;
                    }),
                    catchError(error => {
                        console.error(error);
                        // Return false to indicate failed deletion
                        return of(false);
                    })
                );
            })
        );
    }





    // -----------------------------------------------------------------------------------------------------
    // @ Real-time methods
    // -----------------------------------------------------------------------------------------------------


    // public listenToRealTimeTeamMembers(): void {

    //     // Clear any existing subscriptions
    //     this.destroy$.next();

    //     this.getOrganisationID().pipe(
    //         switchMap(organisationId => {
    //             return this.getTeamMembers().pipe(
    //                 switchMap(initialTeamMembers => {
    //                     if (initialTeamMembers.length === 0) return of([]);
    //                     const formattedData = initialTeamMembers.map(item => ({
    //                         id: item.$id,
    //                         data: item
    //                     }));
    //                     return this.aws.subscribeToCollection(formattedData, this.aws.teamMembersID, organisationId);
    //                 }),
    //                 map(formattedArray => {
    //                     return formattedArray.map(item => ({
    //                         ...item.data,
    //                         $id: item.id  // Assigning the id value back to the $id property
    //                     }));
    //                 })
    //             );
    //         }),
    //         takeUntil(this.destroy$)
    //     ).subscribe({
    //         next: (updatedTeamMembers) => {
    //             if (this.enableRealTimeUpdates) {
    //                 this._teamMembers.next(updatedTeamMembers);
    //             }
    //         },
    //         error: (error) => {
    //             console.error('Error in listenToRealTimeData:', error);
    //         }
    //     });
    // }

    public listenToRealTimeTeamMembers(): void {
        // Clear any existing subscriptions
        this.destroy$.next();

        this.getOrganisationID().pipe(
            switchMap(organisationId => {
                // Check if we already have data in _teamMembers
                const currentTeamMembers = this._teamMembers.getValue() || [];

                // Use existing data if available, otherwise fetch initial data
                const initialData$ = currentTeamMembers.length > 0
                    ? of(currentTeamMembers.map(item => ({ id: item.$id, data: item })))
                    : this.getTeamMembers().pipe(
                        map(teamMembers => teamMembers.map(item => ({ id: item.$id, data: item })))
                    );

                return initialData$.pipe(
                    switchMap(initialData => {
                        if (initialData.length === 0) return of([]);
                        return this.aws.subscribeToCollection(initialData, this.aws.teamMembersID, organisationId);
                    }),
                    map(updatedData => updatedData.map(item => ({ $id: item.id, ...item.data }))),
                    tap(updatedTeamMembers => {
                        if (this.enableRealTimeUpdates) {
                            this._teamMembers.next(updatedTeamMembers);
                        }
                    })
                );
            }),
            takeUntil(this.destroy$)
        ).subscribe({
            next: () => { }, // Optionally log updates for debugging
            error: (error) => console.error('Error in real-time team members subscription:', error)
        });
    }

    /**
         * Pause real-time updates
         */
    public pauseRealTimeUpdates(): void {
        this.enableRealTimeUpdates = false;
        this.destroy$.next();
        if (this.realTimeDataSubscription) {
            this.realTimeDataSubscription.unsubscribe();
            // After unsubscribing, reinitialize to ensure future subscriptions can be added
            this.realTimeDataSubscription = new Subscription();
        }
    }

    /**
     * Resume real-time updates
     */
    public resumeRealTimeUpdates(): void {
        this.enableRealTimeUpdates = true;
        this.listenToRealTimeTeamMembers();
    }

    /**
     * Complete cleanup of all subscriptions and resources
     */
    public unsubscribe(): void {
        this.destroy$.next();
        this.destroy$.complete();
        this.aws.unsubscribe();
        if (this.realTimeDataSubscription) {
            this.realTimeDataSubscription.unsubscribe();
        }
    }



    /**
    * Uploads a file to the server and returns an observable that emits an object with the fileId and fileUrl.
    *
    * @param {File} file - The file to be uploaded.
    * @return {Observable<{ fileId: string, fileUrl: string }>} An observable that emits the fileId and fileUrl of the uploaded file.
    */


    uploadFile(file: File): Observable<{ fileId: string, fileUrl: string }> {
        return this.aws.addFile(file).pipe(
            map(result => {
                if (!result) {
                    throw new Error('File upload failed');
                }
                return {
                    fileId: result.fileId,
                    fileUrl: result.fileUrl
                };
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to upload and retrieve file details');
            })
        );
    }

    /**
    * Deletes a file with the given fileId.
    *
    * @param {string} fileId - The ID of the file to be deleted.
    * @return {Observable<boolean>} - A boolean indicating whether the file was successfully deleted.
    */

    deleteFile(fileId: string): Observable<boolean> {
        return this.aws.deleteFile(fileId);
    }



    getFilePreview(fileId: string, width: number, height: number): string {
        return this.aws.getPlatformFilePreview(fileId, width, height);
    }


}

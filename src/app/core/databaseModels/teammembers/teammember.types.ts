export interface TeamMember {
    $id: string,
    organisationID: string,
    name: string,
    employeeNumber: string,
    phone: string,
    email: string,
    driverLicense: string,
    hourlyRate: string,
    assignedVehicle: string,
    homeAddresses: string,
    homeLatLon: string,
    assignmentHistory: string[],
    avatar?: string | null;
    avatarImageId: string | null;
    approved: string,
    nots: string,
    assignedVehicleStartDate: Date,
    assignedVehicleEndDate: Date,
    status: boolean,
    deviceTokenId: string,
    dutyStatus: string,
    lastLocation: string,
    groupMemberIDs: string[],
    attachedDocs: string[],
    unreadMessagesCount: number,
    teamMemberUnreadMessagesCount: number,
}

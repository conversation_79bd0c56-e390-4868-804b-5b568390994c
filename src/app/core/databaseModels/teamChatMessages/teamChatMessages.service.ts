import { Injectable } from "@angular/core";

import { BehaviorSubject, catchError, forkJoin, map, Observable, of, Subject, switchMap, take, tap, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { TeamChatMessages } from "./teamChatMessages.types";

@Injectable({
    providedIn: 'root'
})
export class TeamChatMessagesService {
    private _teamChatMessage = new BehaviorSubject<TeamChatMessages>(null);
    private _teamChatMessages = new BehaviorSubject<TeamChatMessages[]>([]);
    private destroy$ = new Subject<void>();

    constructor(
        private readonly aws: AppwriteService,
        private _userService: UserService) {
    }
    public unsubscribe(): void {
        this.destroy$.next();
        this.destroy$.complete();
        this.aws.unsubscribe();
        this.aws.unsubscribe();
    }
    getOrganisationID(): Observable<string> {
        return this._userService.user$.pipe(
            take(1),
            map(user => user.organisationID),
            catchError(this.handleError<string>('Failed to get organisation ID'))
        );
    }

    get teamChatMessage$(): Observable<TeamChatMessages> {
        return this._teamChatMessage.asObservable();
    }
    get teamChatMessages$(): Observable<TeamChatMessages[]> {
        return this._teamChatMessages.asObservable();
    }

    set teamChatMessage(value: TeamChatMessages) {
        this._teamChatMessage.next(value);
    }
    set teamChatMessages(value: TeamChatMessages[]) {
        this._teamChatMessages.next(value);
    }


    getTeamChatAllMessages(): Observable<TeamChatMessages[]> {
        return this.getOrganisationID().pipe(
            switchMap(organisationID =>
                this.aws.getDocumentList<TeamChatMessages>(this.aws.teamChatMessagesID,
                    [
                        this.aws.equalQuery("organisationID", [organisationID]),

                        this.aws.orderAscQuery('createdAt'),
                        this.aws.limitQuery(5000),
                    ])),
            map(response => response.map(item => ({ $id: item.id, ...item.data }))),
            tap(chats => this.teamChatMessages = chats),
            catchError(this.handleError<TeamChatMessages[]>('getTeamChatMessages', []))
        );
    }

    getTeamChatMessages(teamMemberID: string): Observable<TeamChatMessages[]> {
        return this.aws.getDocumentList<TeamChatMessages>(this.aws.teamChatMessagesID, [

            this.aws.equalQuery("teamMemberID", [teamMemberID]),
            this.aws.orderAscQuery('createdAt'),
            this.aws.limitQuery(5000),
        ]).pipe(
            map(response => response.map(item => ({ $id: item.id, ...item.data }))),
            tap(chats => this.teamChatMessages = chats),
            catchError(this.handleError<TeamChatMessages[]>('getTeamChatMessages', []))

        )
    }

    createTeamChatMessage(message: TeamChatMessages): Observable<TeamChatMessages> {
        return this.teamChatMessages$.pipe(
            take(1),
            switchMap((teamChatMessages) => {
                return this.aws.addDocument(this.aws.teamChatMessagesID, message).pipe(
                    map((teamChatMessage) => {
                        const _teamChatMessage: TeamChatMessages = { $id: teamChatMessage.id, ...teamChatMessage.data as TeamChatMessages }

                        this._teamChatMessages.next([...teamChatMessages, _teamChatMessage]);
                        return _teamChatMessage;
                    }),
                    catchError(this.handleError<TeamChatMessages>('createTeamChatMessage'))
                )
            })
        )
    }

    updateTeamChatMessage(id: string, message: TeamChatMessages): Observable<TeamChatMessages> {
        return this.aws.updateDocument<TeamChatMessages>(this.aws.teamChatMessagesID, id, message).pipe(
            switchMap(updatedTeamChatMessage => {
                this._teamChatMessage.next(updatedTeamChatMessage);
                return this._teamChatMessages.pipe(
                    take(1),
                    map(teamChatMessages => {
                        const index = teamChatMessages.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            teamChatMessages[index] = updatedTeamChatMessage;
                            this._teamChatMessages.next(teamChatMessages);
                        }
                        return updatedTeamChatMessage;
                    })
                )
            }),
            catchError(this.handleError<TeamChatMessages>('updateTeamChatMessage'))
        );
    }

    deleteTeamChatMessage(id: string): Observable<void> {
        return this.aws.deleteDocument(this.aws.teamChatMessagesID, id).pipe(
            tap(() => {
                this._teamChatMessages.pipe(
                    take(1),
                    map(teamChatMessages => {
                        const index = teamChatMessages.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            teamChatMessages.splice(index, 1);
                            this._teamChatMessages.next(teamChatMessages);
                        }
                        return teamChatMessages;
                    }),
                    catchError(this.handleError<TeamChatMessages>('deleteTeamChatMessage'))
                )
            })
        );
    }


    // public listenToRealTimeMessages(): void {
    //     this.getOrganisationID().pipe(
    //         switchMap(organisationId => {
    //             return this.getTeamChatAllMessages().pipe(
    //                 switchMap((teamChatMessage) => {
    //                     if (teamChatMessage.length === 0) return of([]);
    //                     const formattedData = teamChatMessage.map(item => ({
    //                         id: item.$id,
    //                         data: item
    //                     }));
    //                     return this.aws.subscribeToCollection(formattedData, this.aws.teamChatMessagesID, organisationId);
    //                 }),
    //                 map(formattedArray => {
    //                     return formattedArray.map(item => {
    //                         return { $id: item.id, ...item.data }
    //                     });
    //                 })
    //             );
    //         })
    //     ).subscribe((teamChatMessages) => {
    //         this._teamChatMessages.next(teamChatMessages);
    //     });
    // }
    public listenToRealTimeMessages(): void {
        this.getOrganisationID().pipe(
            switchMap(organisationId => {
                const currentMessages = this._teamChatMessages.getValue() || [];
                const initialData$ = currentMessages.length > 0
                    ? of(currentMessages.map(item => ({ id: item.$id, data: item })))
                    : this.getTeamChatAllMessages().pipe(
                        map(messages => messages.map(item => ({ id: item.$id, data: item })))
                    );

                return initialData$.pipe(
                    switchMap(messages => {
                        if (messages.length === 0) return of([]);
                        return this.aws.subscribeToCollection(messages, this.aws.teamChatMessagesID, organisationId);
                    }),
                    map(updatedData => updatedData.map(item => ({ $id: item.id, ...item.data }))),
                    tap(updatedMessages => this._teamChatMessages.next(updatedMessages))
                );

            })
        ).subscribe({
            error: (error) => console.error('Error in real-time team chat messages:', error)
        });
    }

    // public listenToRealTimeTeamChatMessages(messageId: string): void {
    //     this.getOrganisationID().pipe(
    //         switchMap(organisationId => {
    //             return this.getTeamChatMessages(messageId).pipe(
    //                 switchMap((teamChatMessage) => {
    //                     const formattedData = teamChatMessage.map(item => ({
    //                         id: item.$id,
    //                         data: item
    //                     }));
    //                     return this.aws.subscribeToCollection(formattedData, this.aws.teamChatMessagesID, organisationId);
    //                 }),
    //                 map(formattedArray => {
    //                     return formattedArray.map(item => {
    //                         return { $id: item.id, ...item.data }
    //                     });
    //                 })
    //             );
    //         })
    //     ).subscribe((teamChatMessages) => {
    //         // Note: You have two next calls here - was this intentional?
    //         if (messageId) {
    //             this._teamChatMessages.next(teamChatMessages.filter(item => item.$id === messageId));
    //         }
    //         this._teamChatMessages.next(teamChatMessages); // This will override the previous next call
    //     });
    // }
    public listenToRealTimeTeamChatMessages(teamMemberID: string): void {
        this.getOrganisationID().pipe(
            switchMap(organisationId => {
                const currentMessages = this._teamChatMessages.getValue() || [];
                const filteredCurrent = currentMessages.filter(item => item.teamMemberID === teamMemberID);
                const initialData$ = filteredCurrent.length > 0
                    ? of(filteredCurrent.map(item => ({ id: item.$id, data: item })))
                    : this.getTeamChatMessages(teamMemberID).pipe(
                        map(messages => messages.map(item => ({ id: item.$id, data: item })))
                    );

                return initialData$.pipe(
                    switchMap(messages => {
                        if (messages.length === 0) return of([]);
                        return this.aws.subscribeToCollection(messages, this.aws.teamChatMessagesID, organisationId);
                    }),
                    map(updatedData => updatedData.map(item => ({ $id: item.id, ...item.data }))),
                    tap(updatedMessages => {
                        this._teamChatMessages.next(updatedMessages); // Only update with filtered messages
                    })
                );
            })
        ).subscribe({
            error: (error) => console.error('Error in real-time team chat messages:', error)
        });
    }
    uploadFile(file: File): Observable<{ fileId: string, fileUrl: string }> {
        return this.aws.addChatFile(file).pipe(
            map(result => {
                if (!result) {
                    throw new Error('File upload failed');
                }
                return {

                    fileId: result.fileId,
                    fileUrl: result.fileUrl
                };
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to upload and retrieve file details');
            })
        );
    }

    /**
    * Deletes a file with the given fileId.
    *
    * @param {string} fileId - The ID of the file to be deleted.
    * @return {Observable<boolean>} - A boolean indicating whether the file was successfully deleted.
    */

    deleteFile(fileId: string): Observable<boolean> {
        return this.aws.deleteChatFile(fileId);
    }

    getChatFilePreview(fileId: string, width: number, height: number): string {
        return this.aws.getChatFilePreview(fileId, width, height);
    }

    private handleError<T>(operation = 'operation', result?: T) {
        return (error: any): Observable<T> => {
            console.error(`${operation} failed: ${error.message}`);
            return of(result as T);
        };
    }
}

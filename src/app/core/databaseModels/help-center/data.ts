/* eslint-disable */
export const faqCategories = [
    {
        id: '28924eab-97cc-465a-ba21-f232bb95843f',
        slug: 'most-asked',
        title: 'Most asked',
    },
    {
        id: '395b0d41-b9a8-4cd6-8b5c-f07855e82d62',
        slug: 'general-inquiries',
        title: 'General inquiries',
    },
    {
        id: 'b388a87f-bfbb-44d0-800c-0ddbce2a5d22',
        slug: 'licenses',
        title: 'Licenses',
    },
    {
        id: '71c34043-d89d-4aca-951d-8606c3943c43',
        slug: 'payments',
        title: 'Payments',
    },
    {
        id: 'bea49ee0-26da-46ad-97be-116cd7ab416d',
        slug: 'support',
        title: 'Support',
    },
];
export const faqs = [
    // Most asked
    {
        id: 'f65d517a-6f69-4c88-81f5-416f47405ce1',
        categoryId: '28924eab-97cc-465a-ba21-f232bb95843f',
        question: 'Is there a 30-days trial?',
        answer: 'Magna consectetur culpa duis ad est tempor pariatur velit ullamco aute exercitation magna sunt commodo minim enim aliquip eiusmod ipsum adipisicing magna ipsum reprehenderit lorem magna voluptate magna aliqua culpa.\n\nSit nisi adipisicing pariatur enim enim sunt officia ad labore voluptate magna proident velit excepteur pariatur cillum sit excepteur elit veniam excepteur minim nisi cupidatat proident dolore irure veniam mollit.',
    },
    {
        id: '0fcece82-1691-4b98-a9b9-b63218f9deef',
        categoryId: '28924eab-97cc-465a-ba21-f232bb95843f',
        question: 'What’s the benefits of the Premium Membership?',
        answer: 'Et in lorem qui ipsum deserunt duis exercitation lorem elit qui qui ipsum tempor nulla velit aliquip enim consequat incididunt pariatur duis excepteur elit irure nulla ipsum dolor dolore est.\n\nAute deserunt nostrud id non ipsum do adipisicing laboris in minim officia magna elit minim mollit elit velit veniam lorem pariatur veniam sit excepteur irure commodo excepteur duis quis in.',
    },
    {
        id: '2e6971cd-49d5-49f1-8cbd-fba5c71e6062',
        categoryId: '28924eab-97cc-465a-ba21-f232bb95843f',
        question: 'How much time I will need to learn this app?',
        answer: 'Id fugiat et cupidatat magna nulla nulla eu cillum officia nostrud dolore in veniam ullamco nulla ex duis est enim nisi aute ipsum velit et laboris est pariatur est culpa.\n\nCulpa sunt ipsum esse quis excepteur enim culpa est voluptate reprehenderit consequat duis officia irure voluptate veniam dolore fugiat dolor est amet nostrud non velit irure do voluptate id sit.',
    },
    {
        id: '974f93b8-336f-4eec-b011-9ddb412ee828',
        categoryId: '28924eab-97cc-465a-ba21-f232bb95843f',
        question: 'Are there any free tutorials available?',
        answer: 'Excepteur deserunt tempor do lorem elit id magna pariatur irure ullamco elit dolor consectetur ad officia fugiat incididunt do elit aute esse eu voluptate adipisicing incididunt ea dolor aliqua dolor.\n\nConsequat est quis deserunt voluptate ipsum incididunt laboris occaecat irure laborum voluptate non sit labore voluptate sunt id sint ut laboris aute cupidatat occaecat eiusmod non magna aliquip deserunt nisi.',
    },
    {
        id: '5d877fc7-b881-4527-a6aa-d39d642feb23',
        categoryId: '28924eab-97cc-465a-ba21-f232bb95843f',
        question: 'Is there a month-to-month payment option?',
        answer: 'Labore mollit in aliqua exercitation aliquip elit nisi nisi voluptate reprehenderit et dolor incididunt cupidatat ullamco nulla consequat voluptate adipisicing dolor qui magna sint aute do excepteur in aliqua consectetur.\n\nElit laborum non duis irure ad ullamco aliqua enim exercitation quis fugiat aute esse esse magna et ad cupidatat voluptate sint nulla nulla lorem et enim deserunt proident deserunt consectetur.',
    },
    // General inquiries
    {
        id: '3d1c26c5-1e5e-4eb6-8006-ed6037ed9aca',
        categoryId: '395b0d41-b9a8-4cd6-8b5c-f07855e82d62',
        question: 'How to download your items',
        answer: 'Sunt mollit irure dolor aliquip sit veniam amet ut sunt dolore cillum sint pariatur qui irure proident velit non excepteur quis ut et quis velit aliqua ea sunt cillum sit.\n\nReprehenderit est culpa ut incididunt sit dolore mollit in occaecat velit culpa consequat reprehenderit ex lorem cupidatat proident reprehenderit ad eu sunt sit ut sit culpa ea reprehenderit aliquip est.',
    },
    {
        id: '11bd2b9a-85b4-41c9-832c-bd600dfa3a52',
        categoryId: '395b0d41-b9a8-4cd6-8b5c-f07855e82d62',
        question: 'View and download invoices',
        answer: 'Sint mollit consectetur voluptate fugiat sunt ipsum adipisicing labore exercitation eiusmod enim excepteur enim proident velit sint magna commodo dolor ex ipsum sit nisi deserunt labore eu irure amet ea.\n\nOccaecat ut velit et sint pariatur laboris voluptate duis aliqua aliqua exercitation et duis duis eu laboris excepteur occaecat quis esse enim ex dolore commodo fugiat excepteur adipisicing in fugiat.',
    },
    {
        id: 'f55c023a-785e-4f0f-b5b7-47da75224deb',
        categoryId: '395b0d41-b9a8-4cd6-8b5c-f07855e82d62',
        question: "I've forgotten my username or password",
        answer: 'In exercitation sunt ad anim commodo sunt do in sunt est officia amet ex ullamco do nisi consectetur lorem proident lorem adipisicing incididunt consequat fugiat voluptate sint est anim officia.\n\nVelit sint aliquip elit culpa amet eu mollit veniam esse deserunt ex occaecat quis lorem minim occaecat culpa esse veniam enim duis excepteur ipsum esse ut ut velit cillum adipisicing.',
    },
    {
        id: 'c577a67d-357a-4b88-96e8-a0ee1fe9162e',
        categoryId: '395b0d41-b9a8-4cd6-8b5c-f07855e82d62',
        question: 'Where is my license code?',
        answer: 'Ad adipisicing duis consequat magna sunt consequat aliqua eiusmod qui et nostrud voluptate sit enim reprehenderit anim exercitation ipsum ipsum anim ipsum laboris aliqua ex lorem aute officia voluptate culpa.\n\nNostrud anim ex pariatur ipsum et nostrud esse veniam ipsum ipsum irure velit ad quis irure tempor nulla amet aute id esse reprehenderit ea consequat consequat ea minim magna magna.',
    },
    {
        id: '1a680c29-7ece-4a80-9709-277ad4da8b4b',
        categoryId: '395b0d41-b9a8-4cd6-8b5c-f07855e82d62',
        question: 'How to contact an author',
        answer: 'Magna laborum et amet magna fugiat officia deserunt in exercitation aliquip nulla magna velit ea labore quis deserunt ipsum occaecat id id consequat non eiusmod mollit est voluptate ea ex.\n\nReprehenderit mollit ut excepteur minim veniam fugiat enim id pariatur amet elit nostrud occaecat pariatur et esse aliquip irure quis officia reprehenderit voluptate voluptate est et voluptate sint esse dolor.',
    },
    {
        id: 'c49c2216-8bdb-4df0-be25-d5ea1dbb5688',
        categoryId: '395b0d41-b9a8-4cd6-8b5c-f07855e82d62',
        question: 'How does the affiliate program work?',
        answer: 'Adipisicing laboris ipsum fugiat et cupidatat aute esse ad labore et est cillum ipsum sunt duis do veniam minim officia deserunt in eiusmod eu duis dolore excepteur consectetur id elit.\n\nAnim excepteur occaecat laborum sunt in elit quis sit duis adipisicing laboris anim laborum et pariatur elit qui consectetur laborum reprehenderit occaecat nostrud pariatur aliqua elit nisi commodo eu excepteur.',
    },
    // Licenses
    {
        id: '3ef176fa-6cba-4536-9f43-540c686a4faa',
        categoryId: 'b388a87f-bfbb-44d0-800c-0ddbce2a5d22',
        question: 'How do licenses work for items I bought?',
        answer: 'Culpa duis nostrud qui velit sint magna officia fugiat ipsum eiusmod enim laborum pariatur anim culpa elit ipsum lorem pariatur exercitation laborum do labore cillum exercitation nisi reprehenderit exercitation quis.\n\nMollit aute dolor non elit et incididunt eiusmod non in commodo occaecat id in excepteur aliqua ea anim pariatur sint elit voluptate dolor eu non laborum laboris voluptate qui duis.',
    },
    {
        id: '7bc6b7b4-7ad8-4cbe-af36-7301642d35fb',
        categoryId: 'b388a87f-bfbb-44d0-800c-0ddbce2a5d22',
        question: 'Do licenses have an expiry date?',
        answer: 'Ea proident dolor tempor dolore incididunt velit incididunt ullamco quis proident consectetur magna excepteur cillum officia ex do aliqua reprehenderit est esse officia labore dolore aute laboris eu commodo aute.\n\nOfficia quis id ipsum adipisicing ipsum eu exercitation cillum ex elit pariatur adipisicing ullamco ullamco nulla dolore magna aliqua reprehenderit eu laborum voluptate reprehenderit non eiusmod deserunt velit magna do.',
    },
    {
        id: '56c9ed66-a1d2-4803-a160-fba29b826cb4',
        categoryId: 'b388a87f-bfbb-44d0-800c-0ddbce2a5d22',
        question: 'I want to make multiple end products with the same item',
        answer: 'Elit cillum incididunt enim cupidatat ex elit cillum aute dolor consectetur proident non minim eu est deserunt proident mollit ullamco laborum anim ea labore anim ex enim ullamco consectetur enim.\n\nEx magna consectetur esse enim consequat non aliqua nulla labore mollit sit quis ex fugiat commodo eu cupidatat irure incididunt consequat enim ut deserunt consequat elit consequat sint adipisicing sunt.',
    },
    {
        id: '21c1b662-33c8-44d7-9530-91896afeeac7',
        categoryId: 'b388a87f-bfbb-44d0-800c-0ddbce2a5d22',
        question: 'How easy is it to change the license type?',
        answer: 'Duis culpa ut veniam voluptate consequat proident magna eiusmod id est magna culpa nulla enim culpa mollit velit lorem mollit ut minim dolore in tempor reprehenderit cillum occaecat proident ea.\n\nVeniam fugiat ea duis qui et eu eiusmod voluptate id cillum eiusmod eu reprehenderit minim reprehenderit nisi cillum nostrud duis eu magna minim sunt voluptate eu pariatur nulla ullamco elit.',
    },
    {
        id: '5fa52c90-82be-41ae-96ec-5fc67cf054a4',
        categoryId: 'b388a87f-bfbb-44d0-800c-0ddbce2a5d22',
        question: 'Do I need a Regular License or an Extended License?',
        answer: 'Mollit nostrud ea irure ex ipsum in cupidatat irure sit officia reprehenderit adipisicing et occaecat cupidatat exercitation mollit esse in excepteur qui elit exercitation velit fugiat exercitation est officia excepteur.\n\nQuis esse voluptate laborum non veniam duis est fugiat tempor culpa minim velit minim ut duis qui officia consectetur ex nostrud ut elit elit nulla in consectetur voluptate aliqua aliqua.',
    },
    // Payments
    {
        id: '81ac908c-35a2-4705-8d75-539863c35c09',
        categoryId: '71c34043-d89d-4aca-951d-8606c3943c43',
        question: 'Common PayPal, Skrill, and credit card issues',
        answer: 'Sit occaecat sint nulla in esse dolor occaecat in ea sit irure magna magna veniam fugiat consequat exercitation ipsum ex officia velit consectetur consequat voluptate lorem eu proident lorem incididunt.\n\nExcepteur exercitation et qui labore nisi eu voluptate ipsum deserunt deserunt eu est minim dolor ad proident nulla reprehenderit culpa minim voluptate dolor nostrud dolor anim labore aliqua officia nostrud.',
    },
    {
        id: 'b6d8909f-f36d-4885-8848-46b8230d4476',
        categoryId: '71c34043-d89d-4aca-951d-8606c3943c43',
        question: 'How do I find my transaction ID?',
        answer: 'Laboris ea nisi commodo nulla cillum consequat consectetur nisi velit adipisicing minim nulla culpa amet quis sit duis id id aliqua aute exercitation non reprehenderit aliquip enim eiusmod eu irure.\n\nNon irure consectetur sunt cillum do adipisicing excepteur labore proident ut officia dolor fugiat velit sint consectetur cillum qui amet enim anim mollit laboris consectetur non do laboris lorem aliqua.',
    },
    {
        id: '9496235d-4d0c-430b-817e-1cba96404f95',
        categoryId: '71c34043-d89d-4aca-951d-8606c3943c43',
        question: 'PayPal disputes And chargebacks',
        answer: 'Ullamco eiusmod do pariatur pariatur consectetur commodo proident ex voluptate ullamco culpa commodo deserunt pariatur incididunt nisi magna dolor est minim eu ex voluptate deserunt labore id magna excepteur et.\n\nReprehenderit dolore pariatur exercitation ad non fugiat quis proident fugiat incididunt ea magna pariatur et exercitation tempor cillum eu consequat adipisicing est laborum sit cillum ea fugiat mollit cupidatat est.',
    },
    {
        id: '7fde17e6-4ac1-47dd-a363-2f4f14dcf76a',
        categoryId: '71c34043-d89d-4aca-951d-8606c3943c43',
        question: 'Saving your credit card details',
        answer: 'Qui quis nulla excepteur voluptate elit culpa occaecat id ex do adipisicing est mollit id anim nisi irure amet officia ut sint aliquip dolore labore cupidatat magna laborum esse ea.\n\nEnim magna duis sit incididunt amet anim et nostrud laborum eiusmod et ea fugiat aliquip velit sit fugiat consectetur ipsum anim do enim excepteur cupidatat consequat sunt irure tempor ut.',
    },
    {
        id: '90a3ed58-e13b-40cf-9219-f933bf9c9b8f',
        categoryId: '71c34043-d89d-4aca-951d-8606c3943c43',
        question: 'Why do prepaid credits expire?',
        answer: 'Consequat consectetur commodo deserunt sunt aliquip deserunt ex tempor esse nostrud sit dolore anim nostrud nulla dolore veniam minim laboris non dolor veniam lorem veniam deserunt laborum aute amet irure.\n\nEiusmod officia veniam reprehenderit ea aliquip velit anim aute minim aute nisi tempor qui sunt deserunt voluptate velit elit ut adipisicing ipsum et excepteur ipsum eu ullamco nisi esse dolor.',
    },
    {
        id: '153376ed-691f-4dfd-ae99-e204a49edc44',
        categoryId: '71c34043-d89d-4aca-951d-8606c3943c43',
        question: 'Why is there a minimum $20 credit?',
        answer: 'Duis sint velit incididunt exercitation eiusmod nisi sunt ex est fugiat ad cupidatat sunt nisi elit do duis amet voluptate ipsum aliquip lorem aliqua sint esse in magna irure officia.\n\nNon eu ex elit ut est voluptate tempor amet ut officia in duis deserunt cillum labore do culpa id dolore magna anim consectetur qui consectetur fugiat labore mollit magna irure.',
    },
    // Support
    {
        id: '4e7ce72f-863a-451f-9160-cbd4fbbc4c3d',
        categoryId: 'bea49ee0-26da-46ad-97be-116cd7ab416d',
        question: 'What is item support?',
        answer: 'Exercitation sit eiusmod enim officia exercitation eiusmod sunt eiusmod excepteur ad commodo eiusmod qui proident quis aliquip excepteur sit cillum occaecat non dolore sit in labore ut duis esse duis.\n\nConsequat sunt voluptate consectetur dolor laborum enim nostrud deserunt incididunt sint veniam laboris sunt amet velit anim duis aliqua sunt aliqua aute qui nisi mollit qui irure ullamco aliquip laborum.',
    },
    {
        id: '0795a74f-7a84-4edf-8d66-296cdef70003',
        categoryId: 'bea49ee0-26da-46ad-97be-116cd7ab416d',
        question: 'How to contact an author',
        answer: 'Minim commodo cillum do id qui irure aliqua laboris excepteur laboris magna enim est lorem consectetur tempor laboris proident proident eu irure dolor eiusmod in officia lorem quis laborum ullamco.\n\nQui excepteur ex sit esse dolore deserunt ullamco occaecat laboris fugiat cupidatat excepteur laboris amet dolore enim velit ipsum velit sint cupidatat consectetur cupidatat deserunt sit eu do ullamco quis.',
    },
    {
        id: '05532574-c102-4228-89a8-55fff32ec6fc',
        categoryId: 'bea49ee0-26da-46ad-97be-116cd7ab416d',
        question: 'Extending and renewing item support',
        answer: 'Reprehenderit anim consectetur anim dolor magna consequat excepteur tempor enim duis magna proident ullamco aute voluptate elit laborum mollit labore id ex lorem est mollit do qui ex labore nulla.\n\nUt proident elit proident adipisicing elit fugiat ex ullamco dolore excepteur excepteur labore laborum sunt ipsum proident magna ex voluptate laborum voluptate sint proident eu reprehenderit non excepteur quis eiusmod.',
    },
    {
        id: 'b3917466-aa51-4293-9d5b-120b0ce6635c',
        categoryId: 'bea49ee0-26da-46ad-97be-116cd7ab416d',
        question: 'Rating or review removal policy',
        answer: 'Ipsum officia mollit qui laboris sunt amet aliquip cupidatat minim non elit commodo eiusmod labore mollit pariatur aute reprehenderit ullamco occaecat enim pariatur aute amet occaecat incididunt irure ad ut.\n\nIncididunt cupidatat pariatur magna sint sit culpa ad cupidatat cillum exercitation consequat minim pariatur consectetur aliqua non adipisicing magna ad nulla ea do est nostrud eu aute id occaecat ut.',
    },
    {
        id: '2f2fb472-24d4-4a00-aa80-d513fa6c059c',
        categoryId: 'bea49ee0-26da-46ad-97be-116cd7ab416d',
        question: 'Purchasing supported and unsupported items',
        answer: 'Dolor cupidatat do qui in tempor dolor magna magna ut dolor est aute veniam consectetur enim sunt sunt duis magna magna aliquip id reprehenderit dolor in veniam ullamco incididunt occaecat.\n\nId duis pariatur anim cillum est sint non veniam voluptate deserunt anim nostrud duis voluptate occaecat elit ut veniam voluptate do qui est ad velit irure sint lorem ullamco aliqua.',
    },
    {
        id: '2fffd148-7644-466d-8737-7dde88c54154',
        categoryId: 'bea49ee0-26da-46ad-97be-116cd7ab416d',
        question: "I haven't received a response from the author",
        answer: 'Velit commodo pariatur ullamco elit sunt dolor quis irure amet tempor laboris labore tempor nisi consectetur ea proident dolore culpa nostrud esse amet commodo do esse laboris laboris in magna.\n\nAute officia labore minim laborum irure cupidatat occaecat laborum ex labore ipsum aliqua cillum do exercitation esse et veniam excepteur mollit incididunt ut qui irure culpa qui deserunt nostrud tempor.',
    },
    {
        id: '24a1034e-b4d6-4a86-a1ea-90516e87e810',
        categoryId: 'bea49ee0-26da-46ad-97be-116cd7ab416d',
        question: 'Responding to requests outside of support',
        answer: 'Exercitation eu in officia lorem commodo pariatur pariatur nisi consectetur qui elit in aliquip et ullamco duis nostrud aute laborum laborum est dolor non qui amet deserunt ex et aliquip.\n\nProident consectetur eu amet minim labore anim ad non aute duis eiusmod sit ad elit magna do aliquip aliqua laborum dolor laboris ea irure duis mollit fugiat tempor eu est.',
    },
];
export const guideCategories = [
    {
        id: 'guideCategories-01',
        slug: 'getting-started',
        title: 'Getting Started',
    },
    {
        id: 'guideCategories-02',
        slug: 'dashboard',
        title: 'Dashboard',
    },
    {
        id: 'guideCategories-03',
        slug: 'scheduler',
        title: 'Scheduler',
    },
    {
        id: 'guideCategories-04',
        slug: 'live-map',
        title: 'Live Map',
    },
    {
        id: 'guideCategories-05',
        slug: 'reports',
        title: 'Reports',
    },
    {
        id: 'guideCategories-06',
        slug: 'client-chat',
        title: 'Client Chat',
    },
    {
        id: 'guideCategories-07',
        slug: 'utilities',
        title: 'Utilities',
    },
    {
        id: 'guideCategories-08',
        slug: 'system-settings',
        title: 'System & Settings',
    },
    {
        id: 'guideCategories-09',
        slug: 'invoicing',
        title: 'Invoicing',
    },
];
export const guides = [
    // Getting started
    {
        id: 'a008ffa3-7b3f-43be-8a8f-dbf5272ed2dd',
        categoryId: 'guideCategories-01',
        slug: 'what-is-this-app',
        title: 'What is this app?',
        subtitle:
            'Enhance your business efficiency and make smarter decisions with an all-in-one platform solution. Streamline job templates, scheduling, team member tracking, and workflow management seamlessly.',
        contentLink: 'getting-started/what-is-this-app',
        lastUpdatedAt: '24-Nov-2024',
        nextGuidePath: '../start-using-the-app',
        nextGuideTitle: 'Start using the app',
    },
    {
        id: '7643d388-12ab-4025-a2f1-5045ac7b1c4c',
        categoryId: 'guideCategories-01',
        slug: 'start-using-the-app',
        title: 'Start using the app',
        subtitle:
            'You need to have an account to use this app. If you do not have an account, you can sign up for one here.',
        contentLink: 'getting-started/start-using-the-app',
        lastUpdatedAt: '24-Nov-2024',
        nextGuidePath: '../signing-in-to-the-dashboard',
        nextGuideTitle: 'Signing in to the dashboard',
    },
    {
        id: '1fecee67-c4b4-413a-b0f2-949dcab73249',
        categoryId: 'guideCategories-01',
        slug: 'signing-in-to-the-dashboard',
        title: 'Signing in to the dashboard',
        subtitle:
            'You have to sign in to your account to use this app. If you do not have an account, you can sign up for one here.',
        contentLink: 'getting-started/signing-in-to-the-dashboard',
        lastUpdatedAt: '24-Nov-2024',
        nextGuidePath: '../navigating-within-the-app',
        nextGuideTitle: 'Navigating within the app',
    },
    {
        id: 'd2e2ea8f-5298-4ba2-898b-afc60c064bba',
        categoryId: 'guideCategories-01',
        slug: 'navigating-within-the-app',
        title: 'Navigating within the app',
        subtitle:
            'Full access to all features and functionality within the app.',
        contentLink: 'getting-started/navigating-within-the-app',
        lastUpdatedAt: '24-Nov-2024',
    },
    // Dashboard
    {
        id: 'f2592886-11b8-4b56-baab-96802c2ed93e',
        categoryId: 'guideCategories-02',
        slug: 'main-dashboard',
        title: 'Main Dashboard',
        subtitle:
            'This dashboard design provides a clean and efficient layout for users to monitor and manage tasks, jobs, and team performance effectively.',
        contentLink: 'dashboard/main-dashboard',
        lastUpdatedAt: '24-Nov-2024',
        nextGuidePath: '../team-members-tab',
        nextGuideTitle: 'Team members Tab',
    },
    {
        id: '9ec3f4b9-a355-4f57-9e93-efa8611cc1c9',
        categoryId: 'guideCategories-02',
        slug: 'team-members-tab',
        title: 'Team members Tab',
        subtitle:
            'Team members tab provides insights into the team’s performance and assignments.',
        contentLink: 'dashboard/team-members-tab',
        lastUpdatedAt: '24-Nov-2024',
    },

    // Scheduler
    {
        id: '1cbdeaeb-bbf1-4d04-b43d-f37b55e6a229',
        categoryId: 'guideCategories-03',
        slug: 'general-view',
        title: 'General View',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: '98de7d4a-2ca2-4d47-bbe6-083ed26467db',
        categoryId: 'guideCategories-03',
        slug: 'pending-tab',
        title: 'Pending Tab',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: '145f497c-1fdb-47b5-a6c1-31f856403571',
        categoryId: 'guideCategories-03',
        slug: 'job-summary-tab',
        title: 'Job Summary Tab',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: '0a007f59-a5ea-4875-991d-f22d6fd69898',
        categoryId: 'guideCategories-03',
        slug: 'job-map-tab',
        title: 'Job Map Tab',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: '4707c8eb-31f9-415c-bd07-86f226c75feb',
        categoryId: 'guideCategories-03',
        slug: 'schedule-calendar',
        title: 'Schedule Calendar',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: '5858d9b6-9c1d-4f2e-8a2d-8f8f9e2f8f2f',
        categoryId: 'guideCategories-03',
        slug: 'create-job',
        title: 'Create Job',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
        contentLink: 'scheduler/create-job',
        lastUpdatedAt: '24-Nov-2024',
        nextGuidePath: '../create-job',
        nextGuideTitle: 'Create Job',
    },
    {
        id: '2d9c9b4b-9c1d-4f2e-8a2d-8f8f9e2f8f2f',
        categoryId: 'guideCategories-03',
        slug: 'modify-job',
        title: 'Modify Job',
        subtitle: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
        contentLink: 'scheduler/edit-job',
        lastUpdatedAt: '24-Nov-2024',
        nextGuidePath: '../create-pending-job',
        nextGuideTitle: 'Create Pending Job',

    },
    {
        id: '3d7150d2-feb3-4f20-bd3f-8e525cef77a4',
        categoryId: 'guideCategories-03',
        slug: 'pending-job',
        title: 'Pending Job',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
        contentLink: 'scheduler/create-pending-job',
        lastUpdatedAt: '24-Nov-2024',
    },
    {
        id: '79239bc4-4fb5-428b-b30d-62c5289b061d',
        categoryId: 'guideCategories-03',
        slug: 'recurring-job',
        title: 'Recurring Job',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
        contentLink: 'scheduler/edit-pending-job',
        lastUpdatedAt: '24-Nov-2024',
    },
    // Live Map
    {
        id: 'c771bf0a-1e0c-4b6d-af7e-189e10cc6fb8',
        categoryId: 'guideCategories-04',
        slug: 'live-map-general-view',
        title: 'General View',
        subtitle:
            'Live map provides insights into the team’s performance and assignments in real-time.',
        contentLink: 'live-map/live-map-general-view',
        lastUpdatedAt: '24-Nov-2024',
        nextGuidePath: '../live-map-list-tabs',
        nextGuideTitle: 'List Tab\'s',
    },
    {
        id: '3d7150d2-feb3-4f20-bd3f-8e525cef77a4',
        categoryId: 'guideCategories-04',
        slug: 'live-map-list-tabs',
        title: 'List Tab\'s',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
        contentLink: 'live-map/live-map-list-tabs',
        lastUpdatedAt: '24-Nov-2024',
    },

    // Reports
    {
        id: '60df0d4c-dda1-439c-bd44-179c57a7597d',
        categoryId: 'guideCategories-05',
        slug: 'general-view',
        title: 'General View',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: '0a9c3321-1db3-42bc-92b6-7e257368123e',
        categoryId: 'guideCategories-05',
        slug: 'generate-report',
        title: 'Generate Report',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    // client chat
    {
        id: '80ba5106-5f9c-4ed7-b8f3-8544035e3095',
        categoryId: 'guideCategories-06',
        slug: 'general-view',
        title: 'General View',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: 'db2e97a6-d657-4e9d-9b6c-5f213ea3301c',
        categoryId: 'guideCategories-06',
        slug: 'start-chat',
        title: 'Start Chat',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    // Utilities
    {
        id: '3374c887-2fb7-4223-9f40-7f2cbbf76795',
        categoryId: 'guideCategories-07',
        slug: 'team-members-shift',
        title: 'Team members shift',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: 'cc65f92a-7d46-4557-b15b-6f8f59a60576',
        categoryId: 'guideCategories-07',
        slug: 'servicing',
        title: 'Servicing',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    // Settings

    {
        id: '434e3c5e-1e0c-4b6d-af7e-189e10cc6fb8',
        categoryId: 'guideCategories-08',
        slug: 'admin-tools-Driver and Team Members',
        title: 'Diver and Team Members',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: '654e3c5e-1e0c-4b6d-af7e-189e10cc6fb8',
        categoryId: 'guideCategories-08',
        slug: 'admin-tools-vehicles',
        title: 'Vehicles',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: 'f54e3c5e-1e0c-4b6d-af7e-189e10cc6fb8',
        categoryId: 'guideCategories-08',
        slug: 'admin-tools-assets-qr-tag',
        title: 'Assets/QR/TAG',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: '6fghfgfg-1e0c-4b6d-af7e-189e10cc6fb8',
        categoryId: 'guideCategories-08',
        slug: 'admin-tools-groups',
        title: 'Groups',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: 'c771bf0a-1e0c-4b6d-af7e-189e10cc6fb8',
        categoryId: 'guideCategories-08',
        slug: 'admin-tools-customer-and-ratings',
        title: 'Customer & Ratings',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: 'c771bf0a-1e0c-4b6d-af7e-189e10cc6fb8',
        categoryId: 'guideCategories-08',
        slug: 'admin-tools-user-accounts',
        title: 'User Accounts',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: 'c771bf0a-1e0c-4b6d-af7e-189e10cc6fb8',
        categoryId: 'guideCategories-08',
        slug: 'admin-tools-app-and-email-templates',
        title: 'App & Email Templates',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: '546e3c5e-1e0c-4b6d-af7e-189e10cc6fb8',
        categoryId: 'guideCategories-08',
        slug: 'settings-account',
        title: 'Account',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: '546e3c5e-1e0c-4b6d-af7e-189e10cc6fb8',
        categoryId: 'guideCategories-08',
        slug: 'settings-security',
        title: 'Security',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: '546e3c5e-1e0c-4b6d-af7e-189e10cc6fb8',
        categoryId: 'guideCategories-08',
        slug: 'settings-company',
        title: 'Company',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: '546e3c5e-1e0c-4b6d-af7e-189e10cc6fb8',
        categoryId: 'guideCategories-08',
        slug: 'settings-license-plan-and-billing',
        title: 'License , Plan & Billing',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    // invoicing
    {
        id: 'c771bf0a-1e0c-4b6d-af7e-189e10cc6fb8',
        categoryId: 'guideCategories-09',
        slug: 'general-view',
        title: 'General View',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: '3d7150d2-feb3-4f20-bd3f-8e525cef77a4',
        categoryId: 'guideCategories-09',
        slug: 'create-invoice',
        title: 'Create invoice',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: '79239bc4-4fb5-428b-b30d-62c5289b061d',
        categoryId: 'guideCategories-09',
        slug: 'payment',
        title: 'Payment',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
    {
        id: '8d68c5e6-5404-450c-9d5f-d9800c164041',
        categoryId: 'guideCategories-09',
        slug: 'overdue-payments',
        title: 'Overdue payments',
        subtitle:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt',
    },
];

// Since we only have one content for the demo, we will
// use the following mock-api on every request for every guide.
export const guideContent = `
<h2>Coming soon</h2>`;

export const content = `
<h2>Description of the Team Members Tab</h2>

<p>
    The image represents the Team Members page of the Streamliner app. This page provides an organized view of all team members, including their current status and contact options. Here's a detailed breakdown:
</p>
<img
    class="relative overflow-hidden rounded-3xl"
    src="/images/help-center/dashboard/main-dashboard-02.png"
    alt="Step 1"
/>
<h2>1 - Navigation Bar</h2>
<ul>
    <li>
        At the top, the Team Members tab is highlighted, showing that this section of the app is currently active.
    </li>
    <li>
        A badge next to the tab indicates the total number of team members, in this case, 11 members.
    </li>
</ul>
<h2>2 - Team Member Cards</h2>
<ul>
    <h4>Each team member is represented by a card that contains the following details:
    </h4>
    <li>Initials Avatar: The initials of the team member (e.g., "<PERSON><PERSON>" for <PERSON>) displayed in a circular icon.</li>
    <li>
        Name: The full name of the team member is shown below the avatar.
    </li>
    <h4>Status Badge:</h4>
    <li>A blue "On Duty" badge indicates the team member is currently active and working.</li>
    <li>A red "Off Duty" badge shows the team member is not currently working or unavailable.</li>
    <h4>Contact Options:</h4>
    <li>Email Button: A clickable button to send an email to the team member.</li>
    <li>Call Button: A clickable button to make a phone call to the team member.</li>
</ul>
<h2>3 - Additional Information for "On Duty" Members</h2>
<ul>
    
    <li>
        Team members who are On Duty also display their assigned vehicle below their name (e.g., "Chris ZWY 948" for Chris Marshall). This helps identify their current vehicle.
    </li>
</ul>

<hr />
<dl>
    <dd>
        This page allows administrators or team leads to quickly check the availability of their team, communicate with them directly, and assign tasks efficiently. The clear design ensures that important details are easily accessible at a glance.
    </dd>
</dl>

`;

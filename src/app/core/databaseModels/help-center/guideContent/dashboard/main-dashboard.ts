export const content = `
<h2>Description of the Dashboard Image with 5 Key Points</h2>

<p>
    The Streamliner app is designed for seamless navigation, allowing you to
    efficiently manage your tasks, team, and clients. Here’s an overview of the
    key sections and their functionalities:
</p>
<img
    class="relative overflow-hidden rounded-3xl"
    src="/images/help-center/dashboard/main-dashboard-01.png"
    alt="Step 1"
/>
<h2>1 - Top Action Buttons</h2>
<ul>
    <li>The "Create Job" button allows users to quickly add a new job to the system.</li>
    <li>The "Admin Tools" button provides access to administrative functionalities, enabling users to configure platform settings or manage system behaviors.</li>
   
</ul>
<h2>2 - Navigation Bar</h2>
<ul>
    <li>The top navigation bar includes tabs like Home and Team Members.</li>
    <li>The Home tab displays the dashboard overview, while the Team Members tab provides insights into the team’s performance and assignments.</li>
   
</ul>
<h2>3 - Summary Cards</h2>
<ul>
    <h4>Quick overview cards summarize the key metrics:</h4>
    <li>Total Jobs: Displays the total number of jobs in the system.</li>
    <li>Overdue Jobs: Highlights jobs that are past their deadlines.</li>
    <li>Active Jobs: Shows the number of ongoing jobs.</li>
    <li>Finished Jobs: Indicates how many jobs have been successfully completed.</li>
   
</ul>
<h2>4 - Jobs Summary Chart and Overview</h2>
<ul>
    <li>Jobs Summary: A bar chart showing job trends over the week, helping users track job activity day by day.</li>  
</ul>
<ul>
    <h4>Overview Section: Displays metrics such as:</h4>
    <li>New Jobs added this week.</li>
    <li>Finished Jobs completed during the selected time period.</li>
    <li>Pending Jobs that need attention.</li>
    <li>Progressing Jobs currently being worked on.</li>
</ul>
<h2>5 - Task Distribution and Schedule</h2>
<ul>
    <li>Task Distribution Chart: Visualizes how tasks are allocated over the week, helping to identify workload patterns.</li>
    <li>Schedule Section: Lists upcoming tasks for today and tomorrow, offering a snapshot of the immediate schedule.    </li>
   
</ul>
<hr />
<dl>
    <dd>
        This dashboard design provides a clean and efficient layout for users to monitor and manage tasks, jobs, and team performance effectively.
    </dd>
</dl>

`;

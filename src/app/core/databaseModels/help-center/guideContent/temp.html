<h2>Description of the Live Map Page with Different Tabs</h2>
<p>
    The Live Map page includes multiple tabs, each displaying specific types of
    data to enhance task monitoring and asset tracking. Here’s a breakdown of
    the two views based on tab selection:
</p>
<img
    class="relative overflow-hidden rounded-3xl"
    src="/images/help-center/live-map/live-map-general-view-02.png"
    alt="Step 1"
/>
<h2>First Tab: Asset Tracking with QR Codes</h2>
<ul>
    <h3>
        Purpose: Displays the location of assets captured by team members during
        drop-off or pick-up tasks.
    </h3>
    <h4>Sidebar Details:</h4>
    <li>Asset Name or ID: For example, "6MT-BIn-S" or "excavator".</li>
    <li>
        Last Update Time: Indicates how recently the location was updated (e.g.,
        "2 months ago").
    </li>
    <li>
        Location: Includes the full address and latitude/longitude of the asset.
    </li>
    <li>
        QR Code: Scannable code for quick verification of the asset's
        information.
    </li>
    <h4>
        A "Track Car" button is also included for certain entries, allowing
        users to follow the asset’s movement if applicable.
    </h4>
</ul>
<ul>
    <h4>Map View:</h4>
    <li>
        Shows asset locations as pins on the map, each marked with its
        respective identifier.
    </li>
    <li>
        Clicking on a pin reveals more details about the asset in the sidebar.
    </li>
</ul>
<hr />
<img
    class="relative overflow-hidden rounded-3xl"
    src="/images/help-center/live-map/live-map-general-view-03.png"
    alt="Step 1"
/>
<h2>Second Tab: Today’s Jobs Location and Status</h2>
<ul>
    <h3>
        Purpose: Displays all jobs scheduled for today, along with their current
        status and locations.
    </h3>
    <h4>Sidebar Details:</h4>
    <li>Job Title or ID: For example, "8t remove" or "6m delivery dd".</li>
    <li>Job Status: Clearly indicated (e.g., "Finished" in green).</li>
    <li>
        Last Update Time: Specifies when the job was last updated (e.g., "about
        7 hours ago").
    </li>
    <li>
        Location: Includes the full address and latitude/longitude of the job
        site.
    </li>
</ul>
<ul>
    <h4>Map View:</h4>
    <li>
        Displays job locations on the map, with icons marking each job’s
        position.
    </li>
    <li>
        Jobs are grouped visually, providing an overview of workload
        distribution across the area.
    </li>
</ul>

<hr />
<h2>Key Features for Both Tabs</h2>
<ul>
    <li>
        Interactive Map: Allows zooming and panning to explore specific areas in
        detail.
    </li>
    <li>
        Sidebar Synchronization: Clicking on a map pin highlights the
        corresponding entry in the sidebar for detailed information.
    </li>
    <li>
        Efficient Data Filtering: Each tab is tailored to show specific data,
        ensuring clarity and usability for asset and job tracking.
    </li>
</ul>
<dl>
    <dd>
        These tabs make it easy for managers to oversee daily operations and
        asset management efficiently, providing a clear view of both current
        jobs and asset locations.
    </dd>
</dl>

import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, map, Observable, of, switchMap, take, tap, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';

import { SubscriptionPlans } from './subscriptionPlans.types';

@Injectable({
    providedIn: 'root'
})

export class SubscriptionPlansService {
    private _subscriptionPlans: BehaviorSubject<SubscriptionPlans[]> = new BehaviorSubject<SubscriptionPlans[]>([]);
    private _subscriptionPlan: BehaviorSubject<SubscriptionPlans> = new BehaviorSubject<SubscriptionPlans>(null);

    constructor(private readonly aws: AppwriteService) {

    }
    public unsubscribe(): void {
        this.aws.unsubscribe();
    }


    set subscriptionPlans(value: SubscriptionPlans[]) {
        this._subscriptionPlans.next(value);
    }

    get subscriptionPlans$(): Observable<SubscriptionPlans[]> {
        return this._subscriptionPlans.asObservable();
    }

    getSubscriptionPlan(id: string): Observable<SubscriptionPlans> {
        return this.aws.getDocument<SubscriptionPlans>(this.aws.subscriptionPlansID, id).pipe(
            tap((data) => this._subscriptionPlan.next(data))
        );

    }
    getSubscriptionPlanByName(name: string): Observable<SubscriptionPlans> {
        // Find the subscriptionPlan with the matching name
        return this.getSubscriptionPlans().pipe(
            map((subscriptionPlans) => subscriptionPlans.find((subscriptionPlan) => subscriptionPlan.subscriptionName === name)),
        )


    }



    getSubscriptionPlans(): Observable<SubscriptionPlans[]> {
        return this.aws.getDocumentList<SubscriptionPlans>(this.aws.subscriptionPlansID).pipe(
            map(response => response.map(item => {

                return { $id: item.id, ...item.data }
            })),
            tap((subscriptionPlans) => {
                this._subscriptionPlans.next(subscriptionPlans);
            }),
        );
    }


    createSubscriptionPlan(subscriptionPlan: SubscriptionPlans): Observable<SubscriptionPlans> {
        return this.subscriptionPlans$.pipe(
            take(1),
            switchMap((subscriptionPlans) => {
                return this.aws.addDocument(this.aws.subscriptionPlansID, subscriptionPlan).pipe(
                    map((newSubscriptionPlan) => {

                        const _newSubscriptionPlan: SubscriptionPlans = {
                            $id: newSubscriptionPlan.id,
                            ...newSubscriptionPlan.data as SubscriptionPlans
                        }
                        this._subscriptionPlans.next([_newSubscriptionPlan, ...subscriptionPlans]);
                        return _newSubscriptionPlan;
                    })
                )
            }),

        )
    }

    updateSubscriptionPlan(id: string, data: SubscriptionPlans): Observable<SubscriptionPlans> {
        return this.aws.updateDocument<SubscriptionPlans>(this.aws.subscriptionPlansID, id, data).pipe(
            switchMap((updatedSubscriptionPlan) => {

                this._subscriptionPlan.next(updatedSubscriptionPlan);
                return this._subscriptionPlans.pipe(
                    take(1),
                    map((subscriptionPlans) => {
                        const index = subscriptionPlans.findIndex((item) => item.$id === id);
                        if (index !== -1) {
                            subscriptionPlans[index] = updatedSubscriptionPlan;
                            this._subscriptionPlans.next(subscriptionPlans);
                        }
                        return updatedSubscriptionPlan;
                    })
                )

            }),
            catchError(error => {
                console.error(error);
                return throwError(() => error);
            })
        );
    }


    deleteSubscriptionPlan(id: string): Observable<boolean> {
        return this.aws.deleteDocument(this.aws.subscriptionPlansID, id).pipe(
            switchMap(() => {
                return this._subscriptionPlans.pipe(
                    take(1),
                    map((subscriptionPlans) => {
                        const index = subscriptionPlans.findIndex((item) => item.$id === id);
                        if (index !== -1) {
                            subscriptionPlans.splice(index, 1);
                            this._subscriptionPlans.next(subscriptionPlans);
                        }
                        return true;
                    })
                )
            })
        )
    }


}

import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, map, Observable, of, switchMap, take, tap, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { Servicing } from './servicing.type';

@Injectable({
    providedIn: 'root'
})
export class ServicingService {
    private _servicing: BehaviorSubject<Servicing> = new BehaviorSubject(null);
    private _servicingList: BehaviorSubject<Servicing[]> = new BehaviorSubject(null);

    constructor(private readonly aws: AppwriteService, private _userService: UserService,) {
    }

    public unsubscribe(): void {
        this.aws.unsubscribe();
    }
    getOrganisationID(): Observable<string> {
        return this._userService.user$.pipe(
            take(1),
            map((user) => user.organisationID),
        );
    }

    set servicing(value: Servicing) {
        // Store the value
        this._servicing.next(value);
    }

    get servicing$(): Observable<Servicing> {
        return this._servicing.asObservable();
    }


    get servicingList$(): Observable<Servicing[]> {
        return this._servicingList.asObservable();
    }

    getServicing(id: string): Observable<Servicing> {
        return this.aws.getDocument<Servicing>(this.aws.servicingID, id).pipe(
            tap((servicing) => {

                this._servicing.next(servicing);
            }),
        );
    }

    getServicings(): Observable<Servicing[]> {
        return this.getOrganisationID().pipe(
            switchMap((organisationID) => {
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.orderAscQuery('startDate'),
                ];
                return this.aws.getDocumentList<Servicing>(this.aws.servicingID, queries).pipe(
                    map(response => response.map(item => {
                        return { $id: item.id, ...item.data }
                    })),
                    tap((servicingList) => {
                        this._servicingList.next(servicingList);
                    }),
                );
            }
            ),
        );
    }

    createServicing(data: Servicing): Observable<Servicing> {
        return this.servicingList$.pipe(
            take(1),
            switchMap(servicingList =>
                this.aws.addDocument(this.aws.servicingID, data).pipe(
                    map(newServicing => {
                        const _newServicing: Servicing = {
                            $id: newServicing.id,
                            ...newServicing.data as Servicing
                        }
                        this._servicingList.next([_newServicing, ...servicingList]);
                        return _newServicing;

                    }),
                ),
            ),
        );
    }

    updateServicing(id: string, data: Servicing): Observable<Servicing> {
        return this.aws.updateDocument<Servicing>(this.aws.servicingID, id, data).pipe(
            switchMap(updatedServicing => {
                // Update the individual update Servicing if it's the one currently being observed

                this._servicing.next(updatedServicing);

                return this._servicingList.pipe(
                    take(1),
                    map(servicingList => {
                        const index = servicingList.findIndex(item => item.$id === id);
                        if (index !== -1) {

                            servicingList[index] = updatedServicing;
                            this._servicingList.next(servicingList);
                        }
                        return updatedServicing;
                    })
                )
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to update update Servicing');
            })
        )
    }

    deleteServicing(id: string): Observable<any> {
        return this.aws.deleteDocument(this.aws.servicingID, id).pipe(
            tap(() => {
                this._servicingList.pipe(
                    take(1),
                    map(servicingList => {
                        const index = servicingList.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            servicingList.splice(index, 1);
                            this._servicingList.next(servicingList);
                        }
                    }),
                    catchError(error => {
                        console.error(error);
                        return of(false);
                    })

                )
            })
        )
    }



    /**
    * Uploads a file to the server and returns an observable that emits an object with the fileId and fileUrl.
    *
    * @param {File} file - The file to be uploaded.
    * @return {Observable<{ fileId: string, fileUrl: string }>} An observable that emits the fileId and fileUrl of the uploaded file.
    */


    uploadFile(file: File): Observable<{ fileId: string, fileUrl: string }> {
        return this.aws.addJobsBucketFile(file).pipe(
            map(result => {
                if (!result) {
                    throw new Error('File upload failed');
                }
                return {

                    fileId: result.fileId,
                    fileUrl: result.fileUrl
                };
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to upload and retrieve file details');
            })
        );
    }

    /**
    * Deletes a file with the given fileId.
    *
    * @param {string} fileId - The ID of the file to be deleted.
    * @return {Observable<boolean>} - A boolean indicating whether the file was successfully deleted.
    */

    deleteFile(fileId: string): Observable<boolean> {
        return this.aws.deleteJobsBucketFile(fileId);
    }


}

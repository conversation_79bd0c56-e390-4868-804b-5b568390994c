/* eslint-disable */
import { FuseNavigationItem } from '@fuse/components/navigation';

export const defaultNavigation: FuseNavigationItem[] = [
    {
        id: 'platform',
        title: 'Platform',
        subtitle: '',
        type: 'group',
        icon: 'heroicons_outline:home',
        children: [
            {
                id: 'platform.dashboard',
                title: 'Dashboard',
                type: 'basic',
                icon: 'mat_outline:dashboard',
                link: '/dashboard',
            },
            {
                id: 'platform.livemap',
                title: 'Live Map',
                type: 'basic',
                icon: 'mat_outline:map',
                link: '/livemap',
            },
            {
                id: 'platform.invoicing',
                title: 'Invoicing',
                type: 'basic',
                icon: 'mat_outline:monetization_on',
                link: '/invoicing',
            },
            {
                id: 'platform.scheduler',
                title: 'Scheduler',
                type: 'basic',
                icon: 'heroicons_outline:calendar-days',
                link: '/scheduler',
            },

            {
                id: 'platform.reports',
                title: 'Reports',
                type: 'basic',
                icon: 'heroicons_outline:chart-bar-square',
                link: '/reports',
            },

            {
                id: 'platform.chat',
                title: 'Client Chat',
                type: 'basic',
                icon: 'heroicons_outline:chat-bubble-bottom-center-text',
                link: '/chat',
            },

        ]
    },
    {
        id: 'utilities',
        title: 'Utilities',
        subtitle: '',
        type: 'group',
        icon: 'heroicons_outline:swatch',
        children: [{
            id: 'utilities.teamMemberShifts',
            title: 'Team Member Shifts',
            type: 'basic',
            icon: 'heroicons_outline:user-group',
            link: '/teamMemberShifts',
        },
        {
            id: 'utilities.servicing',
            title: 'Servicing',
            type: 'basic',
            icon: 'heroicons_outline:wrench-screwdriver',
            link: '/servicing',
        },
        {
            id: 'utilities.expenses',
            title: 'Expenses',
            type: 'basic',
            icon: 'heroicons_outline:currency-dollar',
            link: '/expenses',
        },
            //     {
            //     id: 'utilities.example',
            //     title: 'Example',
            //     type: 'basic',
            //     icon: 'heroicons_outline:chart-pie',
            //     link: '/example'
            // }
        ]
    },
    {
        id: 'system',
        title: 'System',
        subtitle: '',
        type: 'group',
        icon: 'heroicons_outline:cog',
        children: [{
            id: 'platform.tools',
            title: 'Admin Tools',
            type: 'basic',
            icon: 'mat_solid:home_repair_service',
            link: '/tools',
        }, {
            id: 'pages.settings',
            title: 'Settings',
            type: 'basic',
            icon: 'heroicons_outline:cog-8-tooth',
            link: '/settings',
        },
        {
            id: 'apps.help-center',
            title: 'Help Center',
            type: 'collapsable',
            icon: 'heroicons_outline:information-circle',
            link: '/help-center',
            children: [
                {
                    id: 'apps.help-center.home',
                    title: 'Home',
                    type: 'basic',
                    link: '/help-center',
                    exactMatch: true,
                },
                {
                    id: 'apps.help-center.faqs',
                    title: 'FAQs',
                    type: 'basic',
                    link: '/help-center/faqs',
                },
                {
                    id: 'apps.help-center.guides',
                    title: 'Guides',
                    type: 'basic',
                    link: '/help-center/guides',
                },
                {
                    id: 'apps.help-center.support',
                    title: 'Support',
                    type: 'basic',
                    link: '/help-center/support',
                },
            ],
        },
        ]
    },

];
// For Other View Style ////
export const compactNavigation: FuseNavigationItem[] = [
    {
        id: 'example',
        title: 'Example',
        type: 'basic',
        icon: 'heroicons_outline:chart-pie',
        link: '/example'
    }
];
export const futuristicNavigation: FuseNavigationItem[] = [
    {
        id: 'example',
        title: 'Example',
        type: 'basic',
        icon: 'heroicons_outline:chart-pie',
        link: '/example'
    }
];
export const horizontalNavigation: FuseNavigationItem[] = [
    {
        id: 'platform.dashboard',
        title: 'Dashboard',
        type: 'basic',
        icon: 'mat_outline:dashboard',
        link: '/dashboard',
    }, {
        id: 'platform.scheduler',
        title: 'Scheduler',
        type: 'basic',
        icon: 'heroicons_outline:calendar-days',
        link: '/scheduler',
    },
    {
        id: 'platform.livemap',
        title: 'Live Map',
        type: 'basic',
        icon: 'mat_outline:map',
        link: '/livemap',
    },
    {
        id: 'platform.invoicing',
        title: 'Invoicing',
        type: 'basic',
        icon: 'mat_outline:monetization_on',
        link: '/invoicing',
    },
    {
        id: 'platform.reports',
        title: 'Reports',
        type: 'basic',
        icon: 'heroicons_outline:chart-bar-square',
        link: '/reports',
    },

    {
        id: 'platform.chat',
        title: 'Client Chat',
        type: 'basic',
        icon: 'heroicons_outline:chat-bubble-bottom-center-text',
        link: '/chat',
    },
    {
        id: 'utilities',
        title: 'Utilities',
        subtitle: '',
        type: 'group',
        icon: 'heroicons_outline:swatch',
        children: [{
            id: 'utilities.teamMemberShifts',
            title: 'Team Member Shifts',
            type: 'basic',
            icon: 'heroicons_outline:user-group',
            link: '/teamMemberShifts',
        }, {
            id: 'utilities.servicing',
            title: 'Servicing',
            type: 'basic',
            icon: 'heroicons_outline:wrench-screwdriver',
            link: '/servicing',
        },
        {
            id: 'utilities.expenses',
            title: 'Expenses',
            type: 'basic',
            icon: 'heroicons_outline:currency-dollar',
            link: '/expenses',
        },
            // {
            // id: 'utilities.example',
            // title: 'Example',
            // type: 'basic',
            // icon: 'heroicons_outline:chart-pie',
            // link: '/example'
            // }
        ]
    },
    {
        id: 'system',
        title: 'System',
        subtitle: '',
        type: 'group',
        icon: 'heroicons_outline:cog',
        children: [{
            id: 'platform.tools',
            title: 'Admin Tools',
            type: 'basic',
            icon: 'mat_solid:home_repair_service',
            link: '/tools',
        }, {
            id: 'pages.settings',
            title: 'Settings',
            type: 'basic',
            icon: 'heroicons_outline:cog-8-tooth',
            link: '/settings',
        },
        {
            id: 'apps.help-center',
            title: 'Help Center',
            type: 'collapsable',
            icon: 'heroicons_outline:information-circle',
            link: '/help-center',
            children: [
                {
                    id: 'apps.help-center.home',
                    title: 'Home',
                    type: 'basic',
                    link: '/help-center',
                    exactMatch: true,
                },
                {
                    id: 'apps.help-center.faqs',
                    title: 'FAQs',
                    type: 'basic',
                    link: '/help-center/faqs',
                },
                {
                    id: 'apps.help-center.guides',
                    title: 'Guides',
                    type: 'basic',
                    link: '/help-center/guides',
                },
                {
                    id: 'apps.help-center.support',
                    title: 'Support',
                    type: 'basic',
                    link: '/help-center/support',
                },
            ],
        },
        ]
    },
];

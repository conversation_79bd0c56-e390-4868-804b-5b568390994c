import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, map, Observable, of, switchMap, take, tap, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';
import { Assets } from "./assets.types";
import { UserService } from 'app/core/databaseModels/user/user.service';

@Injectable({ providedIn: 'root' })
export class AssetsService {
    private _asset: BehaviorSubject<Assets> = new BehaviorSubject(null)
    private _assets: BehaviorSubject<Assets[]> = new BehaviorSubject(null)

    constructor(
        private readonly aws: AppwriteService,
        private _userService: UserService,) {

    }

    public unsubscribe(): void {
        this.aws.unsubscribe();
    }

    getOrganisationID(): Observable<string> {
        return this._userService.user$.pipe(
            map(user => user.organisationID)
        );
    }

    set asset(value: Assets) {
        // Store the value
        this._asset.next(value);
    }

    get asset$(): Observable<Assets> {

        return this._asset.asObservable();
    }

    get assets$(): Observable<Assets[]> {

        return this._assets.asObservable();
    }

    getAsset(id: string): Observable<Assets> {

        return this.aws.getDocument<Assets>(this.aws.assetsID, id).pipe(
            tap((contact) => {
                this._asset.next({ $id: id, ...contact });
            })
        )
    }

    getAssets(): Observable<Assets[]> {

        return this.getOrganisationID().pipe(
            switchMap(organisationID => {
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.orderAscQuery('assetName'),
                ];
                return this.aws.getDocumentList<Assets>(this.aws.assetsID, queries).pipe(
                    map(response => response.map(item => {

                        return { $id: item.id, ...item.data }
                    })),
                    tap((assets) => {
                        this._assets.next(assets);
                    })
                )
            })
        );
    }



    createAsset(asset: Assets): Observable<Assets> {
        return this.assets$.pipe(
            take(1),
            switchMap(assets =>
                // Add new contact
                this.aws.addDocument(this.aws.assetsID, asset).pipe(
                    map(newAsset => {
                        // Update the contacts
                        const _newAsset: Assets = {
                            $id: newAsset.id,
                            ...newAsset.data as Assets
                        }
                        this._assets.next([_newAsset, ...assets]);
                        return _newAsset;
                    })
                )
            )
        )

    }

    updateAsset(id: string, data: Assets): Observable<Assets> {

        return this.aws.updateDocument<Assets>(this.aws.assetsID, id, data).pipe(
            switchMap(updatedAsset => {

                // Update the individual asset if it's the one currently being observed
                this._asset.next(updatedAsset);
                return this.assets$.pipe(
                    take(1),
                    map(assets => {
                        const index = assets.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            assets[index] = updatedAsset;
                            this._assets.next(assets);
                        }
                        return updatedAsset;
                    })
                )
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to update asset');
            })
        );
    }

    deleteAsset(id: string): Observable<boolean> {
        return this.assets$.pipe(
            take(1),
            switchMap(assets => {
                // Delete the contact
                return this.aws.deleteDocument(this.aws.assetsID, id).pipe(
                    map(() => {
                        // Update the contacts
                        const index = assets.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            assets.splice(index, 1);
                        }
                        this._assets.next(assets);
                        return true;
                    })
                )
            }),
            catchError(error => {
                console.error(error);
                return of(false);
            })
        )
    }

    public listenToRealTimeData(): void {
        this.getAssets().pipe(
            switchMap(initialAssets => {
                const formattedData = initialAssets.map(item => ({
                    id: item.$id,
                    data: item
                }));
                return this.aws.subscribeForArray(formattedData, this.aws.assetsID);
            }),
            map(formattedArray => {
                return formattedArray.map(item => ({
                    ...item.data,
                    $id: item.id  // Assigning the id value back to the $id property
                }));
            })
        ).subscribe(updatedAssets => {
            this._assets.next(updatedAssets);
        });
    }

    uploadFile(file: File): Observable<{ fileId: string, fileUrl: string }> {
        return this.aws.addFile(file).pipe(
            map(result => {
                if (!result) {
                    throw new Error('File upload failed');
                }
                return {
                    fileId: result.fileId,
                    fileUrl: result.fileUrl
                };
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to upload and retrieve file details');
            })
        );
    }
    deleteFile(fileId: string): Observable<boolean> {
        return this.aws.deleteFile(fileId);
    }

}

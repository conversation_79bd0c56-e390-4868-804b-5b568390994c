export interface Assets {
    $id: string,
    organisationID: string,
    groupIDs: string[],
    assetName: string,
    make: string,
    model: string,
    tagData: string,
    currentLocation: string,
    locationDate: Date,
    currentAddress: string,
    relatedJobId: string,
    note: string,
    imageURL: string,
    imageId: string,
    status: string,
    locationHistory: string[],
}

export interface AssetsLocation {
    id: string,
    assetName: string,
    imageURL: string,
    relatedJobId: string,
    location: string,
    locationDate: Date,
    address: string,
    status: string,
}

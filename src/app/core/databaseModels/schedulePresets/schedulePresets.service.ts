import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { catchError, map, switchMap, take, tap } from 'rxjs/operators';
import { SchedulePreset } from './schedulePresets.types';

@Injectable({
    providedIn: 'root'
})
export class SchedulePresetsService {
    private _schedulePreset: BehaviorSubject<SchedulePreset> = new BehaviorSubject<SchedulePreset>(null);
    private _schedulePresets: BehaviorSubject<SchedulePreset[]> = new BehaviorSubject<SchedulePreset[]>(null);

    constructor(
        private readonly aws: AppwriteService,
        private _userService: UserService,
    ) { }

    public unsubscribe(): void {
        this.aws.unsubscribe();
    }

    getOrganisationID(): Observable<string> {
        return this._userService.user$.pipe(
            take(1),
            map((user) => user.organisationID)
        );
    }

    set schedulePreset(value: SchedulePreset) {
        this._schedulePreset.next(value);
    }

    get schedulePreset$(): Observable<SchedulePreset> {
        return this._schedulePreset.asObservable();
    }

    get schedulePresets$(): Observable<SchedulePreset[]> {
        return this._schedulePresets.asObservable();
    }

    getSchedulePreset(): Observable<SchedulePreset> {
        return this.aws.getDocument<SchedulePreset>(this.aws.schedulePresetsID, this._schedulePreset.value.$id).pipe(
            tap((schedulePreset) => {
                // Parse the templateItems strings into objects for easier manipulation
                if (schedulePreset.templateItems && Array.isArray(schedulePreset.templateItems)) {
                    schedulePreset.templateItems = schedulePreset.templateItems.map(item =>
                        typeof item === 'string' ? item : JSON.stringify(item)
                    );
                }
                this._schedulePreset.next(schedulePreset);
            }),
            catchError((error) => {
                console.error('Error fetching schedule preset:', error);
                return throwError(error);
            })
        );
    }

    getSchedulePresets(): Observable<SchedulePreset[]> {
        return this.getOrganisationID().pipe(
            switchMap((organisationID) => {
                const queries: string[] = [
                    this.aws.equalQuery('organisationID', [organisationID]),
                    this.aws.orderAscQuery('presetName')
                ];

                return this.aws.getDocumentList<SchedulePreset>(this.aws.schedulePresetsID, queries).pipe(
                    map((response) =>
                        response.map((item) => {
                            const preset = {
                                $id: item.id,
                                ...item.data
                            };

                            // Ensure templateItems is always an array of strings
                            if (preset.templateItems && Array.isArray(preset.templateItems)) {
                                preset.templateItems = preset.templateItems.map(templateItem =>
                                    typeof templateItem === 'string' ? templateItem : JSON.stringify(templateItem)
                                );
                            } else {
                                preset.templateItems = [];
                            }

                            return preset;
                        })
                    ),
                    tap((schedulePresets) => {
                        this._schedulePresets.next(schedulePresets);
                    }),
                    catchError((error) => {
                        console.error('Error fetching schedule presets:', error);
                        return throwError(error);
                    })
                );
            })
        );
    }

    createSchedulePreset(schedulePreset: SchedulePreset): Observable<SchedulePreset> {
        // Ensure templateItems are strings
        schedulePreset.templateItems = schedulePreset.templateItems.map(obj =>
            typeof obj === 'string' ? obj : JSON.stringify(obj)
        );

        return this.schedulePresets$.pipe(
            take(1),
            switchMap((schedulePresets) =>
                this.aws.addDocument(this.aws.schedulePresetsID, schedulePreset).pipe(
                    map((newSchedulePreset) => {
                        const _newSchedulePreset: SchedulePreset = {
                            $id: newSchedulePreset.id,
                            ...newSchedulePreset.data as SchedulePreset
                        };
                        this._schedulePresets.next(schedulePresets ? [_newSchedulePreset, ...schedulePresets] : [_newSchedulePreset]);
                        return _newSchedulePreset;
                    })
                )
            ),
            catchError((error) => {
                console.error('Error creating schedule preset:', error);
                return throwError(error);
            })
        );
    }

    updateSchedulePreset(schedulePreset: SchedulePreset): Observable<SchedulePreset> {
        // Ensure templateItems are strings
        schedulePreset.templateItems = schedulePreset.templateItems.map(obj =>
            typeof obj === 'string' ? obj : JSON.stringify(obj)
        );

        return this.aws.updateDocument<SchedulePreset>(this.aws.schedulePresetsID, schedulePreset.$id, schedulePreset).pipe(
            switchMap((updatedSchedulePreset) => {
                this._schedulePreset.next(updatedSchedulePreset);
                return this._schedulePresets.pipe(
                    take(1),
                    map((schedulePresets) => {
                        const index = schedulePresets.findIndex((item) => item.$id === schedulePreset.$id);
                        if (index !== -1) {
                            schedulePresets[index] = updatedSchedulePreset;
                            this._schedulePresets.next(schedulePresets);
                        }
                        return updatedSchedulePreset;
                    })
                );
            }),
            catchError((error) => {
                console.error('Error updating schedule preset:', error);
                return throwError(error);
            })
        );
    }

    deleteSchedulePreset(schedulePreset: SchedulePreset): Observable<SchedulePreset> {
        return this.aws.deleteDocument(this.aws.schedulePresetsID, schedulePreset.$id).pipe(
            switchMap(() => {
                return this._schedulePresets.pipe(
                    take(1),
                    map((schedulePresets) => {
                        const index = schedulePresets.findIndex((item) => item.$id === schedulePreset.$id);
                        if (index !== -1) {
                            schedulePresets.splice(index, 1);
                            this._schedulePresets.next(schedulePresets);
                        }
                        return schedulePreset;
                    })
                );
            }),
            catchError((error) => {
                console.error('Error deleting schedule preset:', error);
                return throwError(error);
            })
        );
    }

    // Add method to listen to real-time updates if needed
    listenToRealTimePresets(): void {
        this.getOrganisationID().pipe(
            switchMap(organisationId => {
                return this.getSchedulePresets();
            })
        ).subscribe({
            error: (error) => {
                console.error('Error in listening to real-time presets:', error);
            }
        });
    }
}

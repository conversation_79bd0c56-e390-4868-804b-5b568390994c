import { Injectable, signal, Signal } from '@angular/core';
import { AppwriteService } from '../appwrite.service';
import { UserService } from '../user/user.service';
import { ScheduledJobsService } from '../scheduledJobs/scheduledJob.service';
import { map } from 'rxjs';
import { ScheduledJob } from '../scheduledJobs/scheduledJob.types';
import { DriverShift } from '../driverShifts/driverShifts.types';
import { DriverShiftsService } from '../driverShifts/driverShifts.service';
import { AssetsService } from '../assets/assets.service';
import { Assets, AssetsLocation } from '../assets/assets.types';

// Import reports Types
@Injectable({ providedIn: 'root' })
export class ReportsService {

    errorMessage = signal('');
    reportScheduledJob = signal<ScheduledJob[]>([]);
    teamMemberShifts = signal<DriverShift[]>([]);
    teamMemberJobs = signal<ScheduledJob[]>([]);
    assetsLocations = signal<AssetsLocation[]>([]);
    organisationID = signal<string>('');

    //
    dataAvailableSignal = signal<boolean>(false);

    constructor(
        private readonly aws: AppwriteService,
        private _userService: UserService,
        private _scheduledJobsService: ScheduledJobsService,
        private _driverShiftsService: DriverShiftsService,
        private _assetsService: AssetsService,
    ) {
        // Get organisationID from user service and if not already set
        if (!this.organisationID()) {
            this._userService.user$.pipe(
                map(user => user.organisationID)
            ).subscribe(organisationID => {
                this.organisationID.set(organisationID);
            });
        }
    }

    clearReportData() {
        this.dataAvailableSignal.set(false);
        this.errorMessage.set('');  // Reset error message
        this.reportScheduledJob.set([]);
        this.teamMemberShifts.set([]);
        this.teamMemberJobs.set([]);
        this.assetsLocations.set([]);

    }
    /**
     * Fetch scheduled jobs based on the organisation ID, start date, and end date.
     */
    getScheduledJobs(startDate: Date, endDate: Date, customerID?: string): void {
        // clear report data if it exists

        this.clearReportData();

        // Fetch scheduled jobs using the _scheduledJobsService
        this._scheduledJobsService.getScheduledJobs(startDate, endDate, customerID).subscribe({
            next: (jobs: ScheduledJob[]) => {
                // Update the reportScheduledJob signal with the fetched data

                this.reportScheduledJob.set(jobs);
                // Mark data as available
                this.dataAvailableSignal.set(true);

            },
            error: (error) => {
                // Handle errors and set error message signal
                this.errorMessage.set('Failed to fetch scheduled jobs: ' + error.message);
                // Mark data as not available
                this.dataAvailableSignal.set(false);
            }
        });
    }

    getTeamMemberShifts(startDate: Date, endDate: Date, teamMemberID: string): void {
        this.clearReportData();

        this._driverShiftsService.getDriverShiftsByDate(startDate, endDate, teamMemberID).subscribe({
            next: (shifts: DriverShift[]) => {
                this.teamMemberShifts.set(shifts);
                this._scheduledJobsService.getTeamMemberJobs(startDate, endDate, teamMemberID).subscribe({
                    next: (jobs: ScheduledJob[]) => {
                        this.teamMemberJobs.set(jobs);
                        this.dataAvailableSignal.set(true);
                    },
                    error: (error) => {
                        this.errorMessage.set('Failed to fetch team member jobs: ' + error.message);
                        this.dataAvailableSignal.set(false);
                    }
                })

            },
            error: (error) => {
                this.errorMessage.set('Failed to fetch team member shifts: ' + error.message);
                this.dataAvailableSignal.set(false);
            }
        });

    }

    getAssetsLocations(startDate: Date, endDate: Date): void {

        this.clearReportData();
        this._assetsService.getAssets().subscribe({
            next: (assets: Assets[]) => {
                if (assets.length > 0) {

                    assets.map((asset) => {
                        this.assetsLocations.set([...this.assetsLocations(), ...this.mapLocationHistory(asset)]);
                    });
                    //filter assetsLocations date range
                    this.assetsLocations.set(this.assetsLocations().filter((location) => {
                        return location.locationDate >= startDate && location.locationDate <= endDate
                    }))
                    this.dataAvailableSignal.set(true);
                } else {
                    this.dataAvailableSignal.set(false);
                }

            },
            error: (error) => {
                this.errorMessage.set('Failed to fetch assets locations: ' + error.message);
                this.dataAvailableSignal.set(false);
            }
        });

    }

    mapLocationHistory(asset: Assets): AssetsLocation[] {
        return asset.locationHistory.map((locationStr) => {
            // Parse the JSON string to an object
            const locationObj = JSON.parse(locationStr);

            // Map to AssetsLocation interface
            return {
                id: asset.$id, // Set id to Assets.$id
                relatedJobId: locationObj.relatedJobId,
                assetName: asset.assetName,
                imageURL: asset.imageURL,
                location: locationObj.location,
                locationDate: new Date(locationObj.locationDate),
                address: locationObj.address,
                status: locationObj.status,
            } as AssetsLocation;
        });
    }
}

import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, map, Observable, of, switchMap, take, tap, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { Expense, ExpenseCategory } from "./expense.types";
@Injectable({
    providedIn: 'root'
})
export class ExpenseService {
    private _expense: BehaviorSubject<Expense> = new BehaviorSubject(null);
    private _expenses: BehaviorSubject<Expense[]> = new BehaviorSubject(null);

    private _ExpenseCategory: BehaviorSubject<ExpenseCategory> = new BehaviorSubject(null);
    private _ExpenseCategories: BehaviorSubject<ExpenseCategory[]> = new BehaviorSubject(null);


    constructor(private readonly aws: AppwriteService, private _userService: UserService,) {
    }
    public unsubscribe(): void {
        this.aws.unsubscribe();
    }
    getOrganisationID(): Observable<string> {
        return this._userService.user$.pipe(
            take(1),
            map((user) => user.organisationID),
        );
    }
    set expense(value: Expense) {
        this._expense.next(value);
    }

    set expenseCategory(value: ExpenseCategory) {
        this._ExpenseCategory.next(value);
    }


    get expense$(): Observable<Expense> {
        return this._expense.asObservable();
    }
    get expenses$(): Observable<Expense[]> {
        return this._expenses.asObservable();
    }

    get expenseCategory$(): Observable<ExpenseCategory> {
        return this._ExpenseCategory.asObservable();
    }

    get expenseCategories$(): Observable<ExpenseCategory[]> {
        return this._ExpenseCategories.asObservable();
    }

    getExpense(id: string): Observable<Expense> {
        return this.aws.getDocument<Expense>(this.aws.expensesID, id).pipe(
            tap((expense) => {
                this._expense.next(expense);
            })
        )
    }

    getExpenses(): Observable<Expense[]> {
        return this.getOrganisationID().pipe(
            switchMap((organisationID) => {
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.orderAscQuery('date'),
                ];
                return this.aws.getDocumentList<Expense>(this.aws.expensesID, queries).pipe(
                    map(response => response.map(item => {
                        return { $id: item.id, ...item.data }
                    })),
                    tap((expenses) => {
                        this._expenses.next(expenses);
                    }),
                );
            })
        )
    }

    createExpense(expense: Expense): Observable<Expense> {

        return this.expenses$.pipe(
            take(1),
            switchMap((expenses) =>
                this.aws.addDocument(this.aws.expensesID, expense).pipe(
                    map((newExpense) => {
                        const _newExpense: Expense = {
                            $id: newExpense.id,
                            ...newExpense.data as Expense
                        }
                        this._expenses.next([_newExpense, ...expenses]);
                        return _newExpense;
                    })
                ),
            ),);


    }

    updateExpense(id: string, data: Expense): Observable<Expense> {

        return this.aws.updateDocument<Expense>(this.aws.expensesID, id, data).pipe(
            switchMap(updatedExpense => {

                this._expense.next(updatedExpense);
                return this._expenses.pipe(
                    take(1),
                    map(expenses => {
                        const index = expenses.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            expenses[index] = updatedExpense;
                            this._expenses.next(expenses);
                        }
                        return updatedExpense;
                    })
                )
            }),
            catchError(error => {
                console.error(error);
                return throwError(() => error);
            })
        );
    }


    deleteExpense(id: string): Observable<any> {
        return this.aws.deleteDocument(this.aws.expensesID, id).pipe(
            tap(() => {
                this._expenses.pipe(
                    take(1),
                    map(expenses => {
                        const index = expenses.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            expenses.splice(index, 1);
                            this._expenses.next(expenses);
                        }
                    }),
                    catchError(error => {
                        console.error(error);
                        return of(false);
                    })
                )
            })
        )
    }



    getExpenseCategory(id: string): Observable<ExpenseCategory> {
        return this.aws.getDocument<ExpenseCategory>(this.aws.expenseCategoriesID, id).pipe(
            tap((category) => {
                this._ExpenseCategory.next(category);
            }),
        );
    }

    getExpenseCategories(): Observable<ExpenseCategory[]> {
        return this.getOrganisationID().pipe(
            switchMap((organisationID) => {
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                ];
                return this.aws.getDocumentList<ExpenseCategory>(this.aws.expenseCategoriesID, queries).pipe(
                    map(response => response.map(item => {
                        return { $id: item.id, ...item.data }
                    })),
                    tap((categories) => {
                        this._ExpenseCategories.next(categories);
                    }),
                );
            })
        )
    }

    createExpenseCategory(category: ExpenseCategory): Observable<ExpenseCategory> {

        return this.expenseCategories$.pipe(
            take(1),
            switchMap((categories) =>
                this.aws.addDocument(this.aws.expenseCategoriesID, category).pipe(
                    map((newCategory) => {
                        const _newCategory: ExpenseCategory = {
                            $id: newCategory.id,
                            ...newCategory.data as ExpenseCategory
                        }
                        this._ExpenseCategories.next([_newCategory, ...categories]);
                        return _newCategory;
                    })
                ),
            ),
        )
    }

    updateExpenseCategory(id: string, data: ExpenseCategory): Observable<ExpenseCategory> {

        return this.aws.updateDocument<ExpenseCategory>(this.aws.expenseCategoriesID, id, data).pipe(
            switchMap(updatedCategory => {

                this._ExpenseCategory.next(updatedCategory);
                return this._ExpenseCategories.pipe(
                    take(1),
                    map(categories => {
                        const index = categories.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            categories[index] = updatedCategory;
                            this._ExpenseCategories.next(categories);
                        }
                        return updatedCategory;
                    })
                )
            }),
            catchError(error => {
                console.error(error);
                return throwError(() => error);
            })
        );
    }

    deleteExpenseCategory(id: string): Observable<any> {

        return this.aws.deleteDocument(this.aws.expenseCategoriesID, id).pipe(
            tap(() => {
                this._ExpenseCategories.pipe(
                    take(1),
                    map(categories => {
                        const index = categories.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            categories.splice(index, 1);
                            this._ExpenseCategories.next(categories);
                        }
                    }),
                    catchError(error => {
                        console.error(error);
                        return of(false);
                    })
                )
            })
        )
    }

    /**
    * Uploads a file to the server and returns an observable that emits an object with the fileId and fileUrl.
    *
    * @param {File} file - The file to be uploaded.
    * @return {Observable<{ fileId: string, fileUrl: string }>} An observable that emits the fileId and fileUrl of the uploaded file.
    */


    uploadFile(file: File): Observable<{ fileId: string, fileUrl: string }> {
        return this.aws.addJobsBucketFile(file).pipe(
            map(result => {
                if (!result) {
                    throw new Error('File upload failed');
                }
                return {

                    fileId: result.fileId,
                    fileUrl: result.fileUrl
                };
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to upload and retrieve file details');
            })
        );
    }

    /**
    * Deletes a file with the given fileId.
    *
    * @param {string} fileId - The ID of the file to be deleted.
    * @return {Observable<boolean>} - A boolean indicating whether the file was successfully deleted.
    */

    deleteFile(fileId: string): Observable<boolean> {
        return this.aws.deleteJobsBucketFile(fileId);
    }



}

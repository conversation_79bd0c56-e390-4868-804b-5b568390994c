export interface Expense {
    $id: string,
    organisationID: string,
    expenseName: string,
    categoryTags: string[],
    date: Date,
    description: string,
    amount: number,
    expenseBy: string,
    attachedFiles: string[],
    address: string,
    addressLatLon: string,
    teamMemberId: string,
    status: boolean,
    paymentStatus: string,
    paymentDate: Date,
    paymentAmount: number,
    paymentMethod: string,
    paymentRef: string,
}

export interface ExpenseCategory {
    $id: string;
    organisationID: string;
    title: string;
}

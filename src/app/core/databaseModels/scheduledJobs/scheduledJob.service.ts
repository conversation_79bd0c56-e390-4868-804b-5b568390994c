import { Injectable } from '@angular/core';
import { JobTags, ScheduledJob } from "./scheduledJob.types";
import { BehaviorSubject, catchError, map, merge, Observable, of, Subject, Subscription, switchMap, take, takeUntil, tap, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';
import { UserService } from 'app/core/databaseModels/user/user.service';
import { TimezoneService } from 'app/utils/timezone.service';

@Injectable({ providedIn: 'root' })
export class ScheduledJobsService {



    private _scheduledJob: BehaviorSubject<ScheduledJob> = new BehaviorSubject(null);
    private _scheduledJobs: BehaviorSubject<ScheduledJob[]> = new BehaviorSubject(null);
    private _scheduledPendingJobs: BehaviorSubject<ScheduledJob[]> = new BehaviorSubject(null);
    private _jobsTemplate: BehaviorSubject<ScheduledJob> = new BehaviorSubject(null);
    private _jobsTemplates: BehaviorSubject<ScheduledJob[]> = new BehaviorSubject(null);
    private _teamMembersJobs: BehaviorSubject<ScheduledJob[]> = new BehaviorSubject(null);

    private _jobTag: BehaviorSubject<JobTags> = new BehaviorSubject(null);
    private _jobTags: BehaviorSubject<JobTags[]> = new BehaviorSubject([]);

    // Control flag to enable or disable real-time updates
    private enableRealTimeUpdates = true;

    // Initialize Subscription reference for real-time updates
    private realTimeDataSubscription: Subscription = new Subscription();
    private destroy$ = new Subject<void>();


    /**
     * Constructs a new instance of the class.
     *
     * @param {AppwriteService} aws - The AWS service.
     * @param {UserService} _userService - The user service.
     */
    constructor(private readonly aws: AppwriteService,
        private _userService: UserService,
        private timezoneService: TimezoneService) {


    }

    set tag(value: JobTags) {
        this._jobTag.next(value);
    }
    get tag$(): Observable<JobTags> {
        return this._jobTag.asObservable();
    }
    set tags(value: JobTags[]) {
        this._jobTags.next(value);
    }
    get tags$(): Observable<JobTags[]> {
        return this._jobTags.asObservable();
    }

    /**
    * Retrieves the organisation ID as an observable.
    *
    * @return {Observable<string>} The organisation ID as an observable.
     */
    getOrganisationID(): Observable<string> {


        return this._userService.user$.pipe(
            map(user => user.organisationID)
        );
    }

    /**
    * Set the value of the scheduledJobs property.
    *
    * @param {ScheduledJob[]} value - The new value for the scheduledJobs property.
    */
    set scheduledJobs(value: ScheduledJob[]) {
        this._scheduledJobs.next(value);

    }
    /**
    * Returns an Observable that emits the latest value of the scheduledJob.
    *
    * @return {Observable<ScheduledJob>} An Observable that emits the latest value of the scheduledJob.
    */
    get scheduledJob$(): Observable<ScheduledJob> {
        return this._scheduledJob.asObservable();

    }
    /**
     * Returns an observable of the scheduled jobs.
     *
     * @return {Observable<ScheduledJob[]>} An observable of the scheduled jobs.
     */
    get scheduledJobs$(): Observable<ScheduledJob[]> {

        return this._scheduledJobs.asObservable();
    }




    get scheduledPendingJobs$(): Observable<ScheduledJob[]> {

        return this._scheduledPendingJobs.asObservable();
    }

    get jobsTemplates$(): Observable<ScheduledJob[]> {

        return this._jobsTemplates.asObservable();
    }

    /**
     * Retrieves a scheduled job by its ID.
     *
     * @param {string} id - The ID of the scheduled job.
     * @return {Observable<ScheduledJob>} An observable that emits the scheduled job.
     */

    getScheduledJob(id: string): Observable<ScheduledJob> {
        return this.aws.getDocument<ScheduledJob>(this.aws.scheduledJobsID, id).pipe(
            tap((response) => {
                this._scheduledJob.next(response);
            }),
        );

    }

    getJobsTemplate(id: string): Observable<ScheduledJob> {
        return this.aws.getDocument<ScheduledJob>(this.aws.jobTemplatesID, id).pipe(
            tap((response) => {
                this._jobsTemplate.next(response);
            }),
        );

    }


    /**
     * Retrieves the scheduled jobs.
     *
     * @return {Observable<ScheduledJob[]>} An observable that emits an array of ScheduledJob objects.
     */
    getScheduledJobs(startDate?: Date, endDate?: Date, clientID?: string, jobStatus?: string, updateSubject: boolean = true): Observable<ScheduledJob[]> {
        return this.getOrganisationID().pipe(
            switchMap(organisationID => {
                // console.log(organisationID);
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.orderAscQuery('dueDate'),
                    this.aws.limitQuery(2000)
                ];
                if (clientID) {
                    queries.push(this.aws.equalQuery("customerId", [clientID]));
                }
                if (jobStatus) {
                    queries.push(this.aws.equalQuery("jobStatus", [jobStatus]));
                }
                if (startDate) {

                    // Ensure the date is formatted correctly for your query
                    const _startDate = new Date(startDate);
                    const _endDate = new Date(endDate);
                    _startDate.setHours(0, 0, 0, 0);
                    _endDate.setHours(23, 59, 59, 999);
                    const formattedDate = this.formatDate(_startDate);
                    const formattedEndDate = this.formatDate(_endDate);
                    // console.log(formattedDate, formattedEndDate);

                    queries.push(this.aws.greaterThanEqualQuery("dueDate", [`${formattedDate}T00:00:00`]));
                    queries.push(this.aws.lessThanEqualQuery("dueDate", [`${formattedEndDate}T23:59:59`]));
                }

                return this.aws.getDocumentList<ScheduledJob>(this.aws.scheduledJobsID, queries).pipe(
                    map(response => response.map(item => {
                        // Convert dueDate to the desired timezone
                        const dueDate = new Date(item.data.dueDate); // Assuming dueDate is in a valid date format

                        //TODO: Modify for get timezone
                        const timeZoneOffset = this.timezoneService.getTimeZoneOffset();
                        // console.log('User Timezone Offset:', timeZoneOffset);
                        const dueDateWithTimezone = this.convertToDateWithTimezone(dueDate, timeZoneOffset);
                        return { $id: item.id, dueDate: dueDateWithTimezone, ...item.data }
                    })),
                    tap((response) => {
                        // Only update the shared BehaviorSubject if updateSubject is true
                        if (updateSubject) {
                            this._scheduledJobs.next(response);
                        }
                    })
                );
            }),
            catchError(error => {
                console.error('Error fetching scheduled jobs:', error);
                return of([]); // Return an empty array or appropriate default value
            })
        );
    }

    getCustomerJobsByJobStatus(jobStatus: string, customerId?: string, pageSize: number = 20, offset: number = 0, searchQuery?: string): Observable<ScheduledJob[]> {
        return this.getOrganisationID().pipe(
            switchMap(organisationID => {

                // Initialize the conditions with mandatory filters
                const andConditions = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.equalQuery("jobStatus", [jobStatus]),
                ];

                // Conditionally add customerId filter if provided
                if (customerId) {
                    andConditions.push(this.aws.equalQuery("customerId", [customerId]));
                }

                // Add search conditions if search query is provided (job title only)
                if (searchQuery && searchQuery.trim()) {
                    const searchTerm = searchQuery.trim();
                    andConditions.push(this.aws.searchQuery("jobTitle", searchTerm));
                }

                let queries: string[] = [
                    this.aws.andQuery(andConditions),
                    this.aws.orQuery([this.aws.isNullQuery("invoiced"), this.aws.equalQuery("invoiced", [false])]),
                    this.aws.orderDescQuery('dueDate'),
                    this.aws.limitQuery(pageSize),
                    this.aws.offsetQuery(offset)
                ];
                return this.aws.getDocumentList<ScheduledJob>(this.aws.scheduledJobsID, queries).pipe(
                    map(response => response.map(item => {
                        // Convert dueDate to the desired timezone  
                        const dueDate = new Date(item.data.dueDate); // Assuming dueDate is in a valid date format
                        const timeZoneOffset = this.timezoneService.getTimeZoneOffset();
                        const dueDateWithTimezone = this.convertToDateWithTimezone(dueDate, timeZoneOffset);
                        return { $id: item.id, dueDate: dueDateWithTimezone, ...item.data }

                    })),
                    tap((response) => {
                        this._scheduledJobs.next(response);
                    })
                );
            }),
            catchError(error => {
                console.error('Error fetching scheduled jobs:', error);
                return of([]); // Return an empty array or appropriate default value
            })
        );
    }

    getCustomerJobsByJobStatusCount(jobStatus: string, customerId?: string, searchQuery?: string): Observable<number> {
        return this.getOrganisationID().pipe(
            switchMap(organisationID => {
                // Initialize the conditions with mandatory filters
                const andConditions = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.equalQuery("jobStatus", [jobStatus]),
                ];

                // Conditionally add customerId filter if provided
                if (customerId) {
                    andConditions.push(this.aws.equalQuery("customerId", [customerId]));
                }

                // Add search conditions if search query is provided
                if (searchQuery && searchQuery.trim()) {
                    const searchTerm = searchQuery.trim();

                    andConditions.push(this.aws.searchQuery("jobTitle", searchTerm));
                }

                let queries: string[] = [
                    this.aws.andQuery(andConditions),
                    this.aws.orQuery([this.aws.isNullQuery("invoiced"), this.aws.equalQuery("invoiced", [false])]),
                    this.aws.limitQuery(25000) // High limit to get all records for counting
                ];

                return this.aws.getDocumentList<ScheduledJob>(this.aws.scheduledJobsID, queries).pipe(
                    map(response => response.length)
                );
            }),
            catchError(error => {
                console.error('Error fetching job count:', error);
                return of(0);
            })
        );
    }

    getTeamMemberJobs(startDate: Date, endDate: Date, teamMemberID: string): Observable<ScheduledJob[]> {

        return this.getOrganisationID().pipe(
            switchMap(organisationID => {

                const _startDate = new Date(startDate);
                const _endDate = new Date(endDate);
                _startDate.setHours(0, 0, 0, 0);
                _endDate.setHours(23, 59, 59, 999);
                const formattedDate = this.formatDate(_startDate);
                const formattedEndDate = this.formatDate(_endDate);


                console.log(teamMemberID)

                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.equalQuery("teamMemberID", [teamMemberID]),
                    this.aws.greaterThanEqualQuery("dueDate", [`${formattedDate}T00:00:00`]),
                    this.aws.lessThanEqualQuery("dueDate", [`${formattedEndDate}T23:59:59`]),
                    this.aws.limitQuery(1000)
                ];
                return this.aws.getDocumentList<ScheduledJob>(this.aws.scheduledJobsID, queries).pipe(
                    map(response => response.map(item => {
                        // Convert dueDate to the desired timezone
                        const dueDate = new Date(item.data.dueDate); // Assuming dueDate is in a valid date format
                        //TODO: Modify for get timezone
                        const timeZoneOffset = this.timezoneService.getTimeZoneOffset();
                        const dueDateWithTimezone = this.convertToDateWithTimezone(dueDate, timeZoneOffset);
                        return { $id: item.id, dueDate: dueDateWithTimezone, ...item.data }
                    })),
                    tap((response) => {
                        this._teamMembersJobs.next(response);
                    })
                );

            }));
    }

    getNotFinishedScheduledJobs(): Observable<ScheduledJob[]> {
        return this.getOrganisationID().pipe(
            switchMap(organisationID => {
                // console.log(organisationID);
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.notEqualQuery("jobStatus", ['finished']),
                    this.aws.orderAscQuery('dueDate'),
                    this.aws.limitQuery(1000),

                ];

                return this.aws.getDocumentList<ScheduledJob>(this.aws.scheduledJobsID, queries).pipe(
                    map(response => response.map(item => {
                        // Convert dueDate to the desired timezone
                        const dueDate = new Date(item.data.dueDate); // Assuming dueDate is in a valid date format
                        //TODO: Modify for get timezone
                        const timeZoneOffset = this.timezoneService.getTimeZoneOffset();
                        const dueDateWithTimezone = this.convertToDateWithTimezone(dueDate, timeZoneOffset);
                        return { $id: item.id, dueDate: dueDateWithTimezone, ...item.data }
                    })),
                    tap((response) => {
                        this._scheduledJobs.next(response);
                    })
                );
            })
        );
    }

    getScheduledPendingJobs(): Observable<ScheduledJob[]> {
        return this.getOrganisationID().pipe(
            switchMap(organisationID => {
                // console.log(organisationID);
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.equalQuery("jobStatus", ['pending']),
                    this.aws.orderAscQuery('dueDate'),
                    this.aws.limitQuery(5000),
                ];

                return this.aws.getDocumentList<ScheduledJob>(this.aws.scheduledJobsID, queries).pipe(
                    map(response => response.map(item => {
                        // Convert dueDate to the desired timezone
                        const dueDate = new Date(item.data.dueDate); // Assuming dueDate is in a valid date format
                        //TODO: Modify for get timezone
                        const timeZoneOffset = this.timezoneService.getTimeZoneOffset();
                        const dueDateWithTimezone = this.convertToDateWithTimezone(dueDate, timeZoneOffset);
                        return { $id: item.id, dueDate: dueDateWithTimezone, ...item.data }
                    })),
                    tap((response) => {
                        this._scheduledPendingJobs.next(response);
                    })
                );
            })
        );
    }

    getJobsTemplates(): Observable<ScheduledJob[]> {
        return this.getOrganisationID().pipe(
            switchMap(organisationID => {
                // console.log(organisationID);
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.orderAscQuery('dueDate'),
                    this.aws.limitQuery(1000),
                ];

                return this.aws.getDocumentList<ScheduledJob>(this.aws.jobTemplatesID, queries).pipe(
                    map(response => response.map(item => {
                        // Convert dueDate to the desired timezone
                        const dueDate = new Date(item.data.dueDate); // Assuming dueDate is in a valid date format
                        //TODO: Modify for get timezone
                        const timeZoneOffset = this.timezoneService.getTimeZoneOffset();
                        const dueDateWithTimezone = this.convertToDateWithTimezone(dueDate, timeZoneOffset);
                        return { $id: item.id, dueDate: dueDateWithTimezone, ...item.data }
                    })),
                    tap((response) => {
                        this._jobsTemplates.next(response);
                    })
                );
            })
        );
    }

    getLastScheduledJob(): Observable<ScheduledJob | null> {
        return this.getOrganisationID().pipe(
            switchMap(organisationID => {
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.orderDescQuery('dueDate'),
                    this.aws.limitQuery(1),
                ];

                return this.aws.getDocumentList<ScheduledJob>(this.aws.scheduledJobsID, queries).pipe(
                    map(response => {
                        if (response.length > 0) {
                            const item = response[0];
                            const dueDate = new Date(item.data.dueDate);
                            const timeZoneOffset = this.timezoneService.getTimeZoneOffset();
                            const dueDateWithTimezone = this.convertToDateWithTimezone(dueDate, timeZoneOffset);
                            return { $id: item.id, dueDate: dueDateWithTimezone, ...item.data };
                        }
                        return null;
                    }),
                    catchError(error => {
                        console.error('Error fetching latest scheduled job:', error);
                        return of(null);
                    })
                );
            })
        );
    }

    // Utility method to format a Date object as a string (or however your query requires)
    private formatDate(date: Date): string {
        const _month = (date.getMonth() + 1) < 10 ? '0' + (date.getMonth() + 1) : (date.getMonth() + 1);
        const _day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
        return `${date.getFullYear()}-${_month}-${_day}`;
    }

    convertToDateWithTimezone(date, timezoneOffsetString) {
        // Parse the timezone offset string to get the offset in hours
        // The string can be in the format "+10", "-4", "+5.5", etc.
        const offsetHours = parseFloat(timezoneOffsetString);

        // Convert the offset in hours to milliseconds
        const offsetMilliseconds = offsetHours * 60 * 60 * 1000;

        // Get the UTC time in milliseconds
        const utcTime = date.getTime();

        // Adjust the UTC time by the timezone offset to get the time in the desired timezone
        const targetTime = new Date(utcTime + offsetMilliseconds);

        return targetTime;
    }
    /**
     * Creates a scheduled job.
     *
     * @param {ScheduledJob} data - The data for the scheduled job.
     * @return {Observable<ScheduledJob>} - An observable that emits the created scheduled job.
     */
    createScheduledJob(data: ScheduledJob & { [key: string]: any }): Observable<ScheduledJob> {

        // Remove the `pendingTitle` attribute from data
        // Delete pendingTitle directly
        delete data.pendingTitle;
        // console.log(data);
        data.dueDate = this.convertToUTCDateObject(data.dueDate);
        return this._scheduledJobs.pipe(
            take(1),
            switchMap(scheduledJobs =>
                this.aws.addDocument(this.aws.scheduledJobsID, data).pipe(
                    map(newScheduledJob => {
                        const _newScheduledJob: ScheduledJob = {
                            $id: newScheduledJob.id,
                            ...newScheduledJob.data as ScheduledJob
                        }
                        this._scheduledJobs.next([_newScheduledJob, ...scheduledJobs]);
                        return _newScheduledJob;
                    })
                )
            )
        )
    }

    createJobsTemplate(data: ScheduledJob & { [key: string]: any }): Observable<ScheduledJob> {

        delete data.pendingTitle;
        data.dueDate = this.convertToUTCDateObject(data.dueDate);
        return this._jobsTemplates.pipe(
            take(1),
            switchMap(jobsTemplates =>
                this.aws.addDocument(this.aws.jobTemplatesID, data).pipe(
                    map(newJobTemplates => {
                        const _newJobTemplates: ScheduledJob = {
                            $id: newJobTemplates.id,
                            ...newJobTemplates.data as ScheduledJob
                        }
                        this._jobsTemplates.next([_newJobTemplates, ...jobsTemplates]);
                        return _newJobTemplates;
                    })
                )
            )
        )
    }

    convertToUTCDateObject(date) {
        // First, create a Date object representing the same moment in time as `date` but in UTC
        const utcDate = new Date(Date.UTC(
            date.getFullYear(), date.getMonth(), date.getDate(),
            date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds()
        ));

        // Now set the hours, minutes, seconds, and milliseconds to zero to get midnight UTC time
        utcDate.setUTCHours(0, 0, 0, 0);

        // The utcDate is now a Date object for the UTC date at midnight
        return utcDate;
    }


    /**
     * Updates a scheduled job.
     *
     * @param {string} id - The ID of the scheduled job to update.
     * @param {ScheduledJob} scheduledJob - The updated scheduled job object.
     * @return {Observable<ScheduledJob>} - An observable that emits the updated scheduled job.
     */
    updateScheduledJob(id: string, scheduledJob: ScheduledJob & { [key: string]: any }): Observable<ScheduledJob> {
        // Delete pendingTitle directly
        delete scheduledJob.pendingTitle;
        const _date = new Date(scheduledJob.dueDate);
        scheduledJob.dueDate = this.convertToUTCDateObject(_date);
        return this.aws.updateDocument<ScheduledJob>(this.aws.scheduledJobsID, id, scheduledJob).pipe(

            switchMap(updatedScheduledJob => {
                // Update the individual scheduledJob if it's the one currently being observed
                this._scheduledJob.next(updatedScheduledJob);

                // Update the list of scheduledJob
                return this.scheduledJobs$.pipe(
                    take(1),
                    map(scheduledJobs => {
                        const index = scheduledJobs.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            scheduledJobs[index] = updatedScheduledJob;
                            this._scheduledJobs.next(scheduledJobs);
                        }
                        return updatedScheduledJob;
                    })
                )
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to update scheduledJob');
            })

        )
    }

    /**
     * Updates a job template and returns an observable of the updated scheduled job.
     *
     * @param {string} id - The identifier of the job template.
     * @param {ScheduledJob} scheduledJob - The scheduled job to be updated.
     * @return {Observable<ScheduledJob>} An observable of the updated scheduled job.
     */
    updateJobTemplate(id: string, scheduledJob: ScheduledJob & { [key: string]: any }): Observable<ScheduledJob> {
        // Delete pendingTitle directly
        delete scheduledJob.pendingTitle;
        const _date = new Date(scheduledJob.dueDate);
        scheduledJob.dueDate = this.convertToUTCDateObject(_date);
        return this.aws.updateDocument<ScheduledJob>(this.aws.jobTemplatesID, id, scheduledJob).pipe(

            switchMap(updatedJobTemplate => {
                // Update the individual scheduledJob if it's the one currently being observed
                this._jobsTemplate.next(updatedJobTemplate);

                // Update the list of jobsTemplates
                return this.jobsTemplates$.pipe(
                    take(1),
                    map(jobsTemplates => {
                        const index = jobsTemplates.findIndex(item => item.$id === id);
                        if (index !== -1) {
                            jobsTemplates[index] = updatedJobTemplate;
                            this._jobsTemplate.next(updatedJobTemplate);
                        }
                        return updatedJobTemplate;
                    })
                )
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to update jobsTemplates');
            })

        )
    }

    /**
     * Deletes a scheduled job with the given ID.
     *
     * @param {string} id - The ID of the scheduled job to be deleted.
     * @return {Observable<boolean>} An observable that emits a boolean indicating the success of the deletion.
     */
    deleteScheduledJob(id: string): Observable<boolean> {
        return this.aws.deleteDocument(this.aws.scheduledJobsID, id).pipe(
            map(() => {
                // Find the index of the deleted scheduledJob
                const index = this._scheduledJobs.value?.findIndex(item => item.$id === id);

                // If the scheduledJob is found, delete it
                if (index !== -1) {
                    this._scheduledJobs.value.splice(index, 1);

                }
                // Update the scheduledJobs BehaviorSubject
                // this._scheduledJobs.next(this._scheduledJobs.value);
                this._scheduledJobs.next(this._scheduledJobs.value.filter(item => item.$id !== id));

                return true;
            }),
            catchError(error => {
                console.error(error);
                // Return false to indicate failed deletion
                return of(false);
            })
        );
    }

    /**
    * Deletes a job template by ID and updates the jobsTemplates BehaviorSubject.
    *
    * @param {string} id - The ID of the job template to delete
    * @return {Observable<boolean>} An observable of true if successful, false otherwise
    */

    deleteJobsTemplates(id: string): Observable<boolean> {
        return this.aws.deleteDocument(this.aws.jobTemplatesID, id).pipe(
            map(() => {
                // Find the index of the deleted jobsTemplates
                const index = this._jobsTemplates.value.findIndex(item => item.$id === id);

                // If the jobsTemplates is found, delete it
                if (index !== -1) {
                    this._jobsTemplates.value.splice(index, 1);

                }
                // Update the jobsTemplates BehaviorSubject
                this._jobsTemplates.next(this._jobsTemplates.value);
                return true;
            }),
            catchError(error => {
                console.error(error);
                // Return false to indicate failed deletion
                return of(false);
            })
        );
    }



    public listenToRealTimeData(date: Date): void {
        this.getOrganisationID().pipe(
            switchMap(organisationId => {
                // Check if we already have data in the BehaviorSubjects
                const currentJobs = this._scheduledJobs.getValue() || [];
                const currentPendingJobs = this._scheduledPendingJobs.getValue() || [];

                // Use existing data if available, otherwise fetch
                const regularJobs$ = currentJobs.length > 0
                    ? of(currentJobs.map(item => ({ id: item.$id, data: item })))
                    : this.getScheduledJobs(date, date, null, null, false).pipe(
                        map(jobs => jobs.map(item => ({ id: item.$id, data: item })))
                    );

                const pendingJobs$ = currentPendingJobs.length > 0
                    ? of(currentPendingJobs.map(item => ({ id: item.$id, data: item })))
                    : this.getScheduledPendingJobs().pipe(
                        map(jobs => jobs.map(item => ({ id: item.$id, data: item })))
                    );

                // Track all known job IDs to detect deletions
                let knownRegularJobIds = new Set<string>();
                let knownPendingJobIds = new Set<string>();

                // Subscribe to real-time updates for regular jobs
                const regularSubscription$ = regularJobs$.pipe(
                    tap(initialData => {
                        // Initialize known IDs
                        knownRegularJobIds = new Set(initialData.map(item => item.id));
                    }),
                    switchMap(initialData => this.aws.subscribeToCollection(
                        initialData,
                        this.aws.scheduledJobsID,
                        organisationId
                    )),
                    map(updatedData => updatedData.map(item => ({ $id: item.id, ...item.data }))),
                    tap(updatedJobs => {
                        // Get current jobs
                        const currentJobs = this._scheduledJobs.getValue() || [];

                        // Get current IDs from the update
                        const updatedIds = new Set(updatedJobs.map(job => job.$id));

                        // Find deleted jobs (jobs that were in our known set but not in the update)
                        const deletedJobs = currentJobs.filter(job =>
                            knownRegularJobIds.has(job.$id) && !updatedIds.has(job.$id)
                        );

                        // Update our known IDs set
                        knownRegularJobIds = updatedIds;

                        // If there are deletions, filter them out
                        let resultJobs = currentJobs;
                        if (deletedJobs.length > 0) {
                            resultJobs = currentJobs.filter(job => !deletedJobs.some(d => d.$id === job.$id));
                        }

                        // Use the existing mergeJobsData helper for updates and additions
                        const mergedJobs = this.mergeJobsData(resultJobs, updatedJobs);
                        this._scheduledJobs.next(mergedJobs);
                    })
                );

                // Subscribe to real-time updates for pending jobs
                const pendingSubscription$ = pendingJobs$.pipe(
                    tap(initialData => {
                        // Initialize known IDs
                        knownPendingJobIds = new Set(initialData.map(item => item.id));
                    }),
                    switchMap(initialData => this.aws.subscribeToCollection(
                        initialData,
                        this.aws.scheduledJobsID,
                        organisationId
                    )),
                    map(updatedData => updatedData.map(item => ({ $id: item.id, ...item.data }))),
                    tap(updatedJobs => {
                        // Get current pending jobs
                        const currentPendingJobs = this._scheduledPendingJobs.getValue() || [];

                        // Get current IDs from the update
                        const updatedIds = new Set(updatedJobs.map(job => job.$id));

                        // Find deleted jobs (jobs that were in our known set but not in the update)
                        const deletedJobs = currentPendingJobs.filter(job =>
                            knownPendingJobIds.has(job.$id) && !updatedIds.has(job.$id)
                        );

                        // Update our known IDs set
                        knownPendingJobIds = updatedIds;

                        // If there are deletions, filter them out
                        let resultJobs = currentPendingJobs;
                        if (deletedJobs.length > 0) {
                            resultJobs = currentPendingJobs.filter(job => !deletedJobs.some(d => d.$id === job.$id));
                        }

                        // Use the existing mergeJobsData helper for updates and additions
                        const mergedJobs = this.mergeJobsData(resultJobs, updatedJobs);
                        this._scheduledPendingJobs.next(mergedJobs);
                    })
                );

                // Merge the streams
                return merge(regularSubscription$, pendingSubscription$);
            }),
            takeUntil(this.destroy$)
        ).subscribe({
            error: (error) => console.error('Error in real-time subscriptions:', error)
        });
    }

    // Helper method to merge job data properly
    private mergeJobsData(currentJobs: ScheduledJob[], updatedJobs: ScheduledJob[]): ScheduledJob[] {
        const result = [...currentJobs];

        updatedJobs.forEach(updatedJob => {
            const index = result.findIndex(job => job.$id === updatedJob.$id);
            if (index !== -1) {
                // Update existing job
                result[index] = updatedJob;
            } else {
                // Add new job
                result.push(updatedJob);
            }
        });

        return result;

    }

    public unsubscribeFromRealTimeData(): void {
        this.destroy$.next();
        this.destroy$ = new Subject<void>();
    }
    public pauseRealTimeData(): void {
        this.enableRealTimeUpdates = false;
        if (this.realTimeDataSubscription) {
            this.realTimeDataSubscription.unsubscribe();
            this.realTimeDataSubscription = new Subscription();
        }
        this.destroy$.next();
    }

    public enableRealTimeData(): void {
        this.enableRealTimeUpdates = true;
        //  console.log('Real-time updates enabled');
    }

    // Method to pause real-time updates



    // public listenToNotFinishedRealTimeData(): void {
    //     if (!this.enableRealTimeUpdates) {
    //         return;
    //     }

    //     this.destroy$.next();
    //     this.getOrganisationID().pipe(
    //         switchMap(organisationId => {
    //             return this.getNotFinishedScheduledJobs().pipe(
    //                 switchMap(initialScheduledJobs => {
    //                     const formattedData = initialScheduledJobs.map(item => ({
    //                         id: item.$id,
    //                         data: item
    //                     }));

    //                     return this.aws.subscribeToCollection(
    //                         formattedData,
    //                         this.aws.scheduledJobsID,
    //                         organisationId
    //                     );
    //                 })
    //             );
    //         }),
    //         map(formattedArray => {
    //             return formattedArray.map(item => ({
    //                 ...item.data,
    //                 $id: item.id
    //             }));
    //         })
    //     ).subscribe(updatedScheduledJobs => {
    //         this._scheduledJobs.next(updatedScheduledJobs.filter(item => item.jobStatus !== 'finished'));
    //     });
    // }

    public listenToNotFinishedRealTimeData(): void {
        if (!this.enableRealTimeUpdates) return;

        this.getOrganisationID().pipe(
            switchMap(organisationId => {
                const currentJobs = this._scheduledJobs.getValue() || [];

                // Use existing data if available, otherwise fetch
                const initialData$ = currentJobs.length > 0
                    ? of(currentJobs.map(item => ({ id: item.$id, data: item })))
                    : this.getNotFinishedScheduledJobs().pipe(
                        map(jobs => jobs.map(item => ({ id: item.$id, data: item })))
                    );

                return initialData$.pipe(
                    switchMap(initialData => this.aws.subscribeToCollection(
                        initialData,
                        this.aws.scheduledJobsID,
                        organisationId
                    )),
                    map(updatedData => updatedData.map(item => ({ $id: item.id, ...item.data }))),
                    tap(updatedJobs => {
                        const notFinishedJobs = updatedJobs.filter(item => item.jobStatus !== 'finished');
                        this._scheduledJobs.next(notFinishedJobs);
                    })
                );
            }),
            takeUntil(this.destroy$)
        ).subscribe({
            error: (error) => console.error('Error in real-time subscriptions:', error)
        });
    }






    /**
    * Uploads a file to the server and returns an observable that emits an object with the fileId and fileUrl.
    *
    * @param {File} file - The file to be uploaded.
    * @return {Observable<{ fileId: string, fileUrl: string }>} An observable that emits the fileId and fileUrl of the uploaded file.
    */


    uploadFile(file: File): Observable<{ fileId: string, fileUrl: string }> {
        return this.aws.addJobsBucketFile(file).pipe(
            map(result => {
                if (!result) {
                    throw new Error('File upload failed');
                }
                return {
                    fileId: result.fileId,
                    fileUrl: result.fileUrl
                };
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to upload and retrieve file details');
            })
        );
    }

    /**
    * Deletes a file with the given fileId.
    *
    * @param {string} fileId - The ID of the file to be deleted.
    * @return {Observable<boolean>} - A boolean indicating whether the file was successfully deleted.
    */

    deleteFile(fileId: string): Observable<boolean> {
        return this.aws.deleteJobsBucketFile(fileId);
    }


    /**
     * Job Tags
     */

    getJobTags(): Observable<JobTags[]> {
        return this.getOrganisationID().pipe(
            switchMap(organisationID => {
                let queries: string[] = [
                    this.aws.equalQuery("organisationID", [organisationID]),
                    this.aws.orderAscQuery('title'),
                ];
                return this.aws.getDocumentList<JobTags>(this.aws.scheduledJobTagsID, queries).pipe(
                    map(response => response.map(item => {
                        return { $id: item.id, ...item.data }
                    })),
                    tap((tags) => {
                        this._jobTags.next(tags);
                    }),

                );

            }),
        );
    }

    createJobTag(data: JobTags): Observable<JobTags> {
        return this.tags$.pipe(
            take(1),
            switchMap(tags =>
                this.aws.addDocument(this.aws.scheduledJobTagsID, data).pipe(
                    map(newTag => {
                        const _newTag: JobTags = {
                            $id: newTag.id,
                            ...newTag.data as JobTags
                        }
                        this._jobTags.next([_newTag, ...tags]);
                        return _newTag;
                    })
                )
            ),
            catchError(error => {
                console.error(error);
                return throwError('Failed to create team member');
            })
        );
    }
    updateJobTag(id: string, data: JobTags): Observable<JobTags> {
        return this.aws.updateDocument<JobTags>(this.aws.scheduledJobTagsID, id, data).pipe(
            map(updatedTag => {
                this.tags$.pipe(
                    take(1),
                    map(tags => {
                        const index = tags.findIndex(tag => tag.$id === id);
                        if (index !== -1) {
                            tags[index] = updatedTag;
                            this._jobTags.next(tags);
                        }
                    })
                ).subscribe();
                return updatedTag;
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to update team member');
            })
        );
    }

    deleteJobTag(tagId: string): Observable<void> {
        return this.tags$.pipe(
            take(1),
            switchMap(tags =>
                this.aws.deleteDocument(this.aws.scheduledJobTagsID, tagId).pipe(
                    tap(() => {
                        const updatedTags = tags.filter(tag => tag.$id !== tagId);
                        this._jobTags.next(updatedTags);
                    })
                )
            ),
            catchError(error => {
                console.error(error);
                return throwError('Failed to delete team member');
            })
        );
    }




}

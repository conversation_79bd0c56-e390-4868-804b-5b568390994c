export interface ScheduledJob {
    $id: string;
    organisationID: string;
    jobNumber: string;
    orderNumber: string;
    customerId: string;
    jobAddress: string;
    latLon: string;
    jobTitle: string;
    jobNotes: string;
    dueDate?: Date | null;
    durationHours: number;
    durationMinutes: number;
    priority: 0 | 1 | 2;
    startTime: string;
    finishTime: string;
    teamMemberID: string;
    adminNote: string;
    jobStatus: string;
    attachedFiles?: string[];
    tags: string[];
    status: boolean;
    dispatchStatus: string;
    driverAttachedFiles: string[];
    driverNotes: string;
    customerSignatureImage: string;
    finishLocationLatLon: string;
    clientAbsent: boolean;
    invoiced: boolean;
    jobCost: number;
    invoicingId: string;
    jobTags: string[];
}

export interface JobTags {
    $id?: string;
    organisationID: string,
    title?: string;
}

export interface Organisation {
    $id: string,
    organisationName: string;
    subscriptionStartDate?: Date;
    subscriptionEndDate: string | Date | null;
    paymentStatus?: string;
    paymentHistory?: string[];
    status?: boolean;
    abn?: string;
    emails?: {
        email: string;
        label: string;
    }[];
    phoneNumbers?: {
        phoneNumber: string;
        label: string;
    }[];
    website?: string;
    description?: string;
    address?: string;
    addressLatLon?: string;
    jobCompletionTerms?: string;
    avatar?: string;
    avatarImageId?: string;
    teamMemberCredits?: number;
    planId: any;
    timezone?: string;
    timezoneOffset?: string;
    country?: string;
    stripeCustomerId?: string;
    paymentEmail: string;
    trialMode?: boolean;
    trialEndDate?: Date;

}

import { Injectable } from '@angular/core';
import { Organisation } from './organisations.types'
import { catchError, map, Observable, of, ReplaySubject, switchMap, take, tap, throwError } from 'rxjs';
import { AppwriteService } from '../appwrite.service';
import { UserService } from 'app/core/databaseModels/user/user.service';

@Injectable({ providedIn: 'root' })
export class OrganisationsService {
    private _organisation: ReplaySubject<Organisation> = new ReplaySubject<Organisation>(1);
    private _organisations: ReplaySubject<Organisation[]> = new ReplaySubject<Organisation[]>(1);

    /**
     * Constructor
     */
    constructor(private readonly aws: AppwriteService,
        private _userService: UserService,) {
    }
    getOrganisationID(): Observable<string> {
        return this._userService.user$.pipe(
            map(user => user.organisationID)
        );
    }
    // -----------------------------------------------------------------------------------------------------
    // @ Accessors
    // -----------------------------------------------------------------------------------------------------

    /**
     * Setter & getter for user
     *
     * @param value
     */
    set organisation(value: any) {
        // Store the value

        this._organisation.next(value);
    }

    set organisations(value: any) {
        // Store the value

        this._organisations.next(value);
    }

    get organisation$(): Observable<Organisation> {
        return this._organisation.asObservable();
    }

    get organisations$(): Observable<Organisation[]> {
        return this._organisations.asObservable();
    }


    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    get(): Observable<Organisation> {
        return this.getOrganisationID().pipe(
            switchMap((organisationID) => {
                return this.aws.getDocument<Organisation>(this.aws.organisationsID, organisationID).pipe(
                    tap((organisation) => {
                        this._organisation.next({ $id: organisationID, ...organisation });
                    }),
                );
            })
        )
    }


    getOrganisation(id: string): Observable<Organisation> {
        return this.aws.getDocument<Organisation>(this.aws.organisationsID, id).pipe(
            tap((organisation) => {
                this._organisation.next({ $id: id, ...organisation });
            }),
        );
    }


    getOrganisations(limit: number = 5): Observable<Organisation[]> {

        let queries: string[] = [
            this.aws.orderDescQuery('subscriptionStartDate'),
            this.aws.limitQuery(limit),
        ];

        return this.aws.getDocumentList<Organisation>(this.aws.organisationsID, queries).pipe(
            map(response => response.map(item => {
                return { $id: item.id, ...item.data }
            })),
            tap((organisations) => {
                this._organisations.next(organisations);
                return organisations;
            }),
        );
    }


    createOrganisation(data: any): Observable<Organisation> {
        data.phoneNumbers = data.phoneNumbers.map(obj => JSON.stringify(obj));
        data.emails = data.emails.map(obj => JSON.stringify(obj));

        /// This for User signup form
        if (!Array.isArray(this.organisations$)) {
            return this.aws.addDocument(this.aws.organisationsID, data).pipe(
                switchMap((organisation) => {
                    const _newOrganisation: Organisation = {
                        $id: organisation.id,
                        ...organisation.data as Organisation
                    };
                    return of(_newOrganisation);
                }),

            );

        } else {
            return this.organisations$.pipe(
                take(1),
                switchMap(organisations =>
                    this.aws.addDocument(this.aws.organisationsID, data).pipe(
                        map((organisation) => {

                            const _newOrganisation: Organisation = {
                                $id: organisation.id,
                                ...organisation.data as Organisation
                            }

                            this._organisations.next([_newOrganisation, ...organisations]);
                            return _newOrganisation;
                        }),
                    ),
                ),
            )
        }
    }


    /**
     * Update the organisations
     *
     * @param organisations
     */


    updateOrganisation(id: string, data: any): Observable<Organisation> {
        data.phoneNumbers = data.phoneNumbers.map(obj => JSON.stringify(obj));
        data.emails = data.emails.map(obj => JSON.stringify(obj));
        return this.aws.updateDocument<Organisation>(this.aws.organisationsID, id, data).pipe(
            tap((organisation) => {
                this._organisation.next({ $id: id, ...organisation });
            }),
        );
    }
    updateOrganisationStripe(id: string, paymentEmail: String): Observable<Organisation> {
        return this.aws.updateDocument<Organisation>(this.aws.organisationsID, id, { paymentEmail }).pipe(
            tap((organisation) => {
                this._organisation.next({ $id: id, ...organisation });
            }),
        );
    }


    deleteOrganisation(id: string): Observable<boolean> {
        return this.aws.deleteDocument(this.aws.organisationsID, id).pipe(
            map(() => true),
            catchError(() => of(false))
        );
    }




    uploadFile(file: File): Observable<{ fileId: string, fileUrl: string }> {
        return this.aws.addFile(file).pipe(
            map(result => {
                if (!result) {
                    throw new Error('File upload failed');
                }
                return {
                    fileId: result.fileId,
                    fileUrl: result.fileUrl
                };
            }),
            catchError(error => {
                console.error(error);
                return throwError('Failed to upload and retrieve file details');
            })
        );
    }
    deleteFile(fileId: string): Observable<boolean> {
        return this.aws.deleteFile(fileId);
    }


}

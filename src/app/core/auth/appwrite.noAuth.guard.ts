import { inject } from '@angular/core';
import { CanActivateChildFn, CanActivateFn, Router } from '@angular/router';
import { AppwriteAuthService } from './appwrite.auth.service';
import { of, switchMap } from 'rxjs';

export const NoAuthGuard: CanActivateFn | CanActivateChildFn = (route, state) => {
    const router: Router = inject(Router);

    return inject(AppwriteAuthService).check().pipe(
        switchMap((authenticated) => {
            if (authenticated) {
                return of(router.parseUrl('dashboard')); // Redirect to dashboard if authenticated
            }
            return of(true); // Allow access if not authenticated
        }),
    );
};

import { Injectable } from '@angular/core';
import { Client, Account, Databases, Query, Models, Locale } from "appwrite";
import { environment } from 'environments/environment';
import { from, tap, catchError, Observable, of, switchMap, throwError, mergeMap, iif, Subject, EMPTY, map, forkJoin } from 'rxjs';
import { User } from '../databaseModels/user/user.types';
import { UserService } from '../databaseModels/user/user.service';
import { Organisation } from "../databaseModels/organisations/organisations.types";
import { OrganisationsService } from "../databaseModels/organisations/organisations.service";
import { SubscriptionPlans } from "../databaseModels/subscriptionPlans/subscriptionPlans.types";
import { query } from '@angular/animations';
import { Model } from '@bryntum/scheduler';
import { SubscriptionPlansService } from '../databaseModels/subscriptionPlans/subscriptionPlans.service';
import { SubscriptionInfoService } from './subscription-info.service';
import { DriverChecklist } from '../databaseModels/driverChecklist/driverChecklist.types';
import { DriverChecklistService } from '../databaseModels/driverChecklist/driverChecklist.service';
//import { AppwriteService } from '../databaseModels/appwrite.service';



@Injectable({
    providedIn: 'root'
})
export class AppwriteAuthService {
    client = new Client();
    account = new Account(this.client);
    private databases = new Databases(this.client);
    private locale = new Locale(this.client);
    private userAuthorized: boolean = false;
    private localStorageSubject = new Subject<String>();


    //Platform Domain URL
    private readonly platformUrl = environment.platformUrl;

    jobCompletionTerms = `<p><strong style="color: rgb(0, 0, 0);">Job Completion Terms Agreement</strong></p><ol><li><strong style="color: rgb(0, 0, 0);">Completion Confirmation</strong><span style="color: rgb(0, 0, 0);">: The contractor affirms that all services outlined in the original agreement have been fully performed to the client&#39;s satisfaction.</span></li><li><strong style="color: rgb(0, 0, 0);">Final Payment</strong><span style="color: rgb(0, 0, 0);">: The client agrees to release any remaining payments owed to the contractor upon confirmation of job completion.</span></li><li><strong style="color: rgb(0, 0, 0);">Release of Claims</strong><span style="color: rgb(0, 0, 0);">: Upon receipt of the final payment, the contractor releases the client from any further obligations or claims related to the completed services.</span></li><li><strong style="color: rgb(0, 0, 0);">Confidentiality</strong><span style="color: rgb(0, 0, 0);">: Both parties agree to keep all proprietary and sensitive information obtained during the project strictly confidential.</span></li><li><strong style="color: rgb(0, 0, 0);">Governing Law</strong><span style="color: rgb(0, 0, 0);">: This agreement is governed by and construed in accordance with the applicable laws of the relevant jurisdiction.</span></li><li><strong style="color: rgb(0, 0, 0);">No Further Obligations</strong><span style="color: rgb(0, 0, 0);">: Both parties acknowledge that all obligations under the original contract have been fulfilled and that no additional services or payments are required unless mutually agreed upon in writing.</span></li></ol><p></p>`
    timeZone: string;

    constructor(
        private _userService: UserService,
        private _organisationsService: OrganisationsService,
        private _subscriptionPlansService: SubscriptionPlansService,
        private subscriptionInfoService: SubscriptionInfoService,
        private _driverChecklistService: DriverChecklistService
    ) {
        this.client
            .setEndpoint(environment.endpoint) // Appwrite Endpoint
            .setProject(environment.projectId);

        if (this.userAuthorized === undefined) {
            this.userAuthorized = false;
        }
        //const result = this.locale.get();


    }
    /**
     * Forgot password
     *
     * @param email
     */
    forgotPassword(email: string): Observable<boolean> {
        return from(this.account.createRecovery(email, `${this.platformUrl}/reset-password`))
            .pipe(
                catchError((error) => {
                    console.log(error); // Failure
                    return of(false);
                }),
                map(() => true)
            );
    }

    /**
     * Reset password
     *
     * @param password
     */
    resetPassword(password: string, userId: string, secret: string): Observable<boolean> {

        return from(this.account.updateRecovery(userId, secret, password))
            .pipe(
                map(() => {
                    //fist get user Id base on authId then update

                    return true

                }),
                catchError((error) => {
                    console.log(error); // Failure
                    return of(false);
                })
            )
    }



    /**
     * Sign up
     *
     * @param user
     */
    signUp(userData: {
        step1: { name: string; email: string; phone: string; };
        step2: { company: string; country: string; language: string; };
        step3: { planName: string; };
        step4: { agreements: boolean; };
    }): Observable<string> {
        // console.log(userData);
        return this._subscriptionPlansService.getSubscriptionPlanByName(userData.step3.planName).pipe(
            switchMap(subscription => {
                if (subscription) {
                    const now = new Date();
                    const newOrganisationData: Organisation = {
                        $id: null,
                        organisationName: userData.step2.company,
                        paymentEmail: userData.step1.email,
                        subscriptionStartDate: now,
                        subscriptionEndDate: null,
                        paymentStatus: 'Trial',
                        country: userData.step2.country,
                        planId: subscription.$id,
                        jobCompletionTerms: this.jobCompletionTerms,
                        phoneNumbers: [],
                        emails: [],
                        trialMode: true,
                        trialEndDate: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000), // 30 days
                    };

                    return forkJoin({
                        emailExists: this.checkExistEmail(userData.step1.email.trim()),
                        phoneExists: this.checkExistPhone(userData.step1.phone.trim()),
                    }).pipe(
                        switchMap(({ emailExists, phoneExists }) => {
                            if (emailExists) {
                                return of('Email already exists');
                            }
                            if (phoneExists) {
                                return of('Phone already exists');
                            }

                            return this._organisationsService.createOrganisation(newOrganisationData).pipe(
                                switchMap(organisation => {
                                    if (organisation) {
                                        const newUserData: User = {
                                            $id: null,
                                            name: userData.step1.name,
                                            email: userData.step1.email.trim(),
                                            phone: userData.step1.phone.trim(),
                                            organisationID: organisation.$id,
                                            authId: '',
                                            approved: true,
                                            defaultUser: true,
                                            verifiedPhone: false,
                                            verifiedEmail: true,
                                            status: true,
                                            userOnlineStatus: 'offline',
                                            country: userData.step2.country,
                                            language: userData.step2.language,
                                        };
                                        const driverChecklistDoc: DriverChecklist = {
                                            $id: null,
                                            organisationID: organisation.$id,
                                            checklistNote: 'First Driver Checklist Sample',
                                            status: true
                                        };

                                        return this._userService.create(newUserData).pipe(
                                            switchMap(user => {
                                                return this._driverChecklistService.createDriverChecklist(driverChecklistDoc).pipe(
                                                    switchMap(() => {
                                                        return this._userService.createRecovery(user.email).pipe(
                                                            map(recoveryResult => {
                                                                return 'Success';
                                                            }),
                                                            catchError(error => {
                                                                console.error('Recovery creation failed:', error);
                                                                return of('Recovery creation failed');
                                                            })
                                                        );
                                                    })
                                                );
                                            }),
                                            catchError(error => {
                                                console.error(error);
                                                // Delete Organisation

                                                //return of(`User creation failed: ${error.message}`);
                                                return this.cleanupOrganisation(organisation.$id, 'User creation failed');
                                            })
                                        );
                                    } else {
                                        return of('Organisation not created');
                                    }
                                }),
                                catchError(error => {
                                    console.error(error);
                                    return of(`Organisation creation failed:${error.message}`);
                                })
                            );
                        })
                    );
                } else {
                    return of('Selected Plan not found');
                }
            }),
            catchError(error => {
                console.error(error);
                return of('Error fetching subscription plan');
            })
        );
    }



    checkExistEmail(email: string): Observable<boolean> {
        if (!email) {
            return of(false);
        }
        const formattedEmail = email.trim().toLowerCase();
        return this._userService.checkExistingEmail(formattedEmail);
    }


    checkExistPhone(phone: string): Observable<boolean> {
        if (!phone) {
            return of(false);
        }

        return this._userService.checkExistingPhone(phone);
    }

    private cleanupOrganisation(orgId: string, errorMessage: string): Observable<string> {
        return this._organisationsService.deleteOrganisation(orgId).pipe(
            map(() => errorMessage), // Return the error message after cleanup
            catchError(deleteError => {
                console.error('Failed to delete organisation:', deleteError);
                return of(`${errorMessage} and cleanup failed`);
            })
        );
    }



    /**
  * Unlock session
  *
  * @param credentials
  */
    unlockSession(credentials: { email: string; password: string }): Observable<any> {
        return of(true);
    }

    /**
 * Sign in
 *
 * @param credentials
 */


    signIn(credentials: { email: string; password: string }): Observable<any> {
        try {
            if (this.userAuthorized) {
                return of({ status: 'success' });
            }
            return of(credentials).pipe(
                switchMap(creds => from(this.account.createEmailPasswordSession(creds.email, creds.password))),
                tap(() => {
                    this.userAuthorized = true;
                }),
                switchMap(() => from(this.account.get())),
                switchMap((cuser) => this.loadAccountDataToLocalStorage(cuser)),
                catchError(error => {
                    console.error('Error in signIn:', error);
                    return throwError(error);
                })
            );
        } catch (error) {
            console.error('Error in signIn:', error);
            return throwError(error);
        }
    }


    checkTrialMode(organisationId: string): Observable<any> {
        return this._organisationsService.getOrganisation(organisationId).pipe(
            map((organisation) => {
                if (organisation) {
                    const currentDate = new Date();
                    const trialEndDate = organisation.trialEndDate ? new Date(organisation.trialEndDate) : null;
                    const subscriptionEndDate = organisation.subscriptionEndDate ? new Date(organisation.subscriptionEndDate) : null;

                    const trialExpired = trialEndDate ? trialEndDate < currentDate : true;
                    const subscriptionExpired = subscriptionEndDate ? subscriptionEndDate < currentDate : true;

                    return {
                        trialMode: organisation.trialMode,
                        trialEndDate: organisation.trialEndDate,
                        trialExpired: trialExpired,
                        subscriptionEndDate: organisation.subscriptionEndDate,
                        subscriptionExpired: subscriptionExpired,
                        organisationName: organisation.organisationName,
                        teamMemberCredits: organisation.teamMemberCredits,
                        stripeCustomerId: organisation.stripeCustomerId,
                        organisationId: organisation.$id,
                        paymentStatus: organisation.paymentStatus,
                        planId: organisation.planId,
                        paymentEmail: organisation.paymentEmail
                    };
                } else {
                    return {
                        trialMode: false,
                        trialEndDate: null,
                        trialExpired: true,
                        subscriptionEndDate: null,
                        subscriptionExpired: true,
                        organisationName: null,
                        stripeCustomerId: null,
                        organisationId: null,
                        paymentStatus: null,
                    };
                }
            }),
            catchError(error => {
                console.error('Error in checkTrialMode:', error);
                return of({
                    trialMode: false,
                    trialEndDate: null,
                    trialExpired: true,
                    subscriptionEndDate: null,
                    subscriptionExpired: true,
                    organisationName: null,
                    stripeCustomerId: null,
                    organisationId: null,
                    paymentStatus: null,
                });
            })
        );
    }





    /**
     * Sign out
     */
    signOut(): Observable<any> {
        // Set the authenticated flag to false
        this.userAuthorized = false;
        // Remove the access Local Data
        localStorage.removeItem("user");
        this.localStorageSubject.next('changed');
        // Remove the access Sessions
        const promise = this.account.deleteSessions();


        promise.then(function (response) {
            // console.log(response); // Success
        }, function (error) {
            //   console.log(error); // Failure
        });



        // Return the observable
        return of(true);
    }
    /**
     * Check the authentication status
     */
    check(): Observable<boolean | 'trialExpired' | 'subscriptionExpired' | 'suspended'> {
        // If the user is already authenticated, return true
        if (this.userAuthorized) {
            return of(true);
        }

        return new Observable<boolean | 'trialExpired' | 'subscriptionExpired' | 'suspended'>((observer) => {
            this.account.getSession('current').then(
                (response: any) => {
                    if (!response) {
                        observer.next(false);
                        observer.complete();
                    } else {
                        from(this.account.get()).pipe(
                            switchMap((cuser) => this.loadAccountDataToLocalStorage(cuser)),
                            tap((result) => {
                                if (result.status === 'success') {
                                    const subscriptionInfo = result.subscriptionInfo;
                                    // Store the subscriptionInfo in the service
                                    this.subscriptionInfoService.subscriptionInfo = subscriptionInfo;

                                    if (subscriptionInfo.trialMode) {
                                        if (!subscriptionInfo.trialExpired) {
                                            observer.next(true);
                                        } else {
                                            this.signOut();
                                            observer.next('trialExpired');
                                        }
                                    } else {
                                        if (!subscriptionInfo.subscriptionExpired) {
                                            observer.next(true);
                                        } else {
                                            this.signOut();
                                            observer.next('subscriptionExpired');
                                        }
                                    }
                                } else if (result.status === 'suspended') {
                                    this.signOut();
                                    observer.next('suspended');
                                } else {
                                    observer.next(false);
                                }
                                observer.complete();
                            }),
                            catchError((getUserError) => {
                                this.userAuthorized = false;
                                console.error('Error getting user:', getUserError);
                                observer.next(false);
                                observer.complete();
                                return EMPTY;
                            })
                        ).subscribe();
                    }
                }
            ).catch(
                (getSessionError) => {
                    this.userAuthorized = false;
                    observer.next(false);
                    observer.complete();
                }
            );
        });
    }




    getCurrentClient(): Client {
        return this.client;
    }

    getCurrentAccount(): Account {
        return this.account;
    }


    private loadAccountDataToLocalStorage(cuser: Models.User<Models.Preferences>): Observable<any> {
        const query = Query.equal('authId', [cuser.$id]);
        return from(this.databases.listDocuments(environment.databaseId, environment.dbCollections.users, [query]))
            .pipe(
                switchMap(result => {
                    if (result.documents.length > 0) {
                        const document = result.documents[0];

                        // Check if user is not active
                        if (document.status === false) {
                            console.log('User status Not Active:', document.status);
                            this.signOut();
                            return of({ status: 'suspended' });
                        }

                        // Now call checkTrialMode
                        return this.checkTrialMode(document.organisationID).pipe(
                            map(trialInfo => {
                                const _user: User = {
                                    $id: document.$id,
                                    organisationID: document.organisationID,
                                    authId: document.authId,
                                    name: document.name,
                                    email: document.email,
                                    status: document.status || false,
                                    userOnlineStatus: document.userOnlineStatus || 'offline',
                                    avatar: document.avatar || '',
                                    verifiedPhone: cuser.phoneVerification,
                                    verifiedEmail: cuser.emailVerification,
                                    phone: cuser.phone,
                                    country: document.country,
                                    language: document.language,

                                };

                                // Store the user on the user service
                                this._userService.user = _user;
                                localStorage.setItem("user", JSON.stringify(_user));
                                this.localStorageSubject.next('changed');

                                return { status: 'success', user: _user, subscriptionInfo: trialInfo };
                            })
                        );
                    } else {
                        return of({ status: 'no_user' });
                    }
                }),
                catchError(error => {
                    console.error('Error in loadAccountDataToLocalStorage:', error);
                    return throwError(error);
                })
            );
    }



    checkUserStatus(user: User): Observable<boolean> {

        const query = Query.equal('organisationID', [user.organisationID]);
        return from(this.databases.listDocuments(environment.databaseId, environment.dbCollections.users, [query])).pipe(
            switchMap(result => {
                if (result.documents.length > 0) {
                    return of(true);
                } else {
                    return of(false);
                }
            })
        );

    }

    getSessionUser(): Observable<Models.User<Models.Preferences> | null> {
        return from(this.account.getSession('current')).pipe(
            switchMap((session) => {
                if (!session) {
                    return of(null);
                }
                return from(this.account.get()).pipe(
                    catchError((error) => {
                        console.error('Error fetching user from session:', error);
                        return of(null);
                    })
                );
            }),
            catchError((error) => {
                console.error('Error fetching session:', error);
                return of(null);
            })
        );
    }

}

import { inject } from '@angular/core';
import { CanActivateChildFn, CanActivateFn, Router, UrlTree } from '@angular/router';
import { AppwriteAuthService } from './appwrite.auth.service';
import { Observable, of } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { SubscriptionInfoService } from './subscription-info.service';

export const AuthGuard: CanActivateFn | CanActivateChildFn = (route, state): Observable<boolean | UrlTree> => {
    const router: Router = inject(Router);
    const subscriptionInfoService = inject(SubscriptionInfoService);

    return inject(AppwriteAuthService).check().pipe(
        switchMap((result) => {
            if (result === true) {
                return of(true); // Allow access if authenticated
            } else if (result === 'trialExpired') {
                // Redirect to trial expired page
                return of(router.createUrlTree(['/trial-expired']));
            } else if (result === 'subscriptionExpired') {
                // Redirect to subscription expired page
                return of(router.createUrlTree(['/subscription-expired']));
            } else if (result === 'suspended') {
                // Redirect to account suspended page
                return of(router.createUrlTree(['/sign-in']));
            } else {
                const redirectURL = state.url === '/sign-out' ? '' : `redirectURL=${state.url}`;
                const urlTree = router.parseUrl(`sign-in?${redirectURL}`);
                return of(urlTree); // Redirect to sign-in if not authenticated
            }
        }),
    );
};

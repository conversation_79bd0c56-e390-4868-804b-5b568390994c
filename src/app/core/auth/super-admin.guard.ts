import { Injectable } from '@angular/core';
import {
    CanActivate,
    CanActivateChild,
    ActivatedRouteSnapshot,
    RouterStateSnapshot,
    UrlTree,
    Router
} from '@angular/router';
import { Observable, of } from 'rxjs';
import { AppwriteAuthService } from './appwrite.auth.service';
import { map, catchError, switchMap } from 'rxjs/operators';

@Injectable({
    providedIn: 'root'
})
export class SuperAdminGuard implements CanActivate, CanActivateChild {

    constructor(
        private authService: AppwriteAuthService,
        private router: Router
    ) { }

    canActivate(
        route: ActivatedRouteSnapshot,
        state: RouterStateSnapshot
    ): Observable<boolean | UrlTree> {
        return this.checkSuperAdmin(state.url);
    }

    canActivateChild(
        childRoute: ActivatedRouteSnapshot,
        state: RouterStateSnapshot
    ): Observable<boolean | UrlTree> {
        return this.checkSuperAdmin(state.url);
    }

    private checkSuperAdmin(url: string): Observable<boolean | UrlTree> {
        return this.authService.check().pipe(
            switchMap((authenticated) => {
                if (!authenticated) {
                    // Redirect to sign-in if not authenticated
                    const redirectURL = `sign-in?redirectURL=${encodeURIComponent(url)}`;
                    return of(this.router.parseUrl(redirectURL));
                }

                // Fetch the current session's user data
                return this.authService.getSessionUser().pipe(
                    map((user) => {
                        // Check if the user is a SuperAdmin
                        //console.log('SuperAdmin', user);
                        if (user && user.labels && user.labels.includes('SuperAdmin')) {
                            return true; // User is a SuperAdmin
                        } else {
                            // Redirect to unauthorized page
                            return this.router.parseUrl('/unauthorized');
                        }
                    }),
                    catchError((error) => {
                        console.error('Error in SuperAdminGuard:', error);
                        // Optionally redirect to an error page
                        return of(this.router.parseUrl('/error'));
                    })
                );
            }),
            catchError((error) => {
                console.error('Error during authentication check:', error);
                // Optionally redirect to an error page
                return of(this.router.parseUrl('/error'));
            })
        );
    }
}

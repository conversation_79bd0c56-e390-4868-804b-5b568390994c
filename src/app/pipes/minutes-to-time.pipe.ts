import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'minutesToTime',
    standalone: false
})
export class MinutesToTimePipe implements PipeTransform {
    transform(value: number): string {
        const hours = Math.floor(value / 60);
        const minutes = value % 60;
        const formattedHours = hours.toString().padStart(2, '0');
        const formattedMinutes = minutes.toString().padStart(2, '0');
        return `${formattedHours}:${formattedMinutes}`;
    }
}

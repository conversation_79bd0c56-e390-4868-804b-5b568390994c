// timezone.service.ts
import { Injectable } from '@angular/core';

@Injectable({
    providedIn: 'root', // This makes the service available application-wide
})
export class TimezoneService {
    /**
     * Returns the timezone offset as a string in the format "+10", "-4", "+5.5", etc.
     */
    getTimeZoneOffset(): string {
        const offsetInMinutes = new Date().getTimezoneOffset();
        const totalOffsetMinutes = -offsetInMinutes; // Invert the sign

        const sign = totalOffsetMinutes >= 0 ? '+' : '-';
        const absoluteOffsetMinutes = Math.abs(totalOffsetMinutes);
        const hours = Math.floor(absoluteOffsetMinutes / 60);
        const minutes = absoluteOffsetMinutes % 60;

        let offsetString = `${sign}${hours}`;

        // Convert minutes to decimal if necessary
        if (minutes !== 0) {
            // For common minute offsets, convert to decimal
            // 15 minutes = 0.25, 30 minutes = 0.5, 45 minutes = 0.75
            let decimalPart = '';
            if (minutes === 15) {
                decimalPart = '.25';
            } else if (minutes === 30) {
                decimalPart = '.5';
            } else if (minutes === 45) {
                decimalPart = '.75';
            } else {
                // For uncommon offsets, use decimal representation up to two decimals
                decimalPart = `.${(minutes / 60).toFixed(2).split('.')[1]}`;
            }
            offsetString += decimalPart;
        }

        return offsetString;
    }
}

export class DistanceCalculatorService {

    // Radius of the Earth in kilometers
    R = 6371; // kilometers

    // Convert degrees to radians
    toRadians(degrees: number): number {
        return degrees * (Math.PI / 180);
    }

    // Haversine formula to calculate the distance between two points
    haversine(lat1: number, lng1: number, lat2: number, lng2: number): number {
        const dLat = this.toRadians(lat2 - lat1);
        const dLng = this.toRadians(lng2 - lng1);

        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
            Math.sin(dLng / 2) * Math.sin(dLng / 2);

        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        // Distance in kilometers
        return this.R * c;
    }

    // Calculate the total distance traveled
    calculateTotalDistance(points: { lat: number, lng: number }[]): number {
        let totalDistance = 0;

        for (let i = 0; i < points.length - 1; i++) {
            const currentPoint = points[i];
            const nextPoint = points[i + 1];
            totalDistance += this.haversine(
                currentPoint.lat,
                currentPoint.lng,
                nextPoint.lat,
                nextPoint.lng
            );
        }

        return totalDistance; // Total distance in kilometers
    }
}

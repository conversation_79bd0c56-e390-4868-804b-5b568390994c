
export const environment = {
    production: true,
    platformUrl: 'https://www.streamlinerapps.com',
    endpoint: 'https://cloud.appwrite.io/v1', //'https://back.wiseme.com.au/v1','https://cloud.appwrite.io/v1',//
    notificationEndpoint: 'https://streamliner-drivers-fcm.vercel.app/api/send-notification',//'https://6568527c1fb4b712fdf3.appwrite.global/',
    projectId: '64c9ba66713111673ea0',
    databaseId: '64d2f66799726496aa94',
    platformBucketId: '650178f596e82ae2a986',
    jobsBucket: '6544b5be004b3ab89ffa',
    driverShiftsVideoImages: '65702bc3afb41b1472fe',
    chatBucket: '660d079a549afd03a01e',
    functions: {
        userFunctionID: '6568527b419449282f15',
        stripeFunctionID: '66cd50b3000776f60c8a',
        sendEmailFunctionID: '66fcba9a0017cca44053',
    },
    dbCollections: {
        organisations: '64ddc3905c26b51b10b7',
        subscriptions: '64ddc3fbf103e1134e0c',
        groups: '64dee09c5f1d389e6406',
        users: '64f69660b0cf3dd797a8',
        teamMembers: '64fa7b493b150af177fa',
        vehicle: '64fa829195ab3503c2f9',
        customers: '64fa89f4115e96d55fc7',
        customerTags: '64fa8c3e2e466aef9c9c',
        scheduledJobs: '6511593ab84192bd036b',
        scheduledJobTags: '65115994af3339c3f800',
        jobsTemplates: '65af052d44aa508837d3',
        driverShifts: '656ecc3d0ba0b58da5b8',
        assets: '64fa86fe49e4c0ab3c9f',
        driverChecklist: '65b99d67702ff13002e8',
        driverChecklistDoc: '66c2f4b50009c184a712',
        servicing: '65f3ee64bfb4854436c4',
        servicingRequest: '6631c31100303460013e',
        expense: '66025896979835654ccd',
        expenseCategory: '660793050c659c0864ef',
        chat: '660b908b59ce87175e65',
        chatMessages: '660b8fb793f32113bf85',
        teamChatMessages: '667590c3002912185b52',
        subscriptionPlans: '64ddc3fbf103e1134e0c',
        invoicing: '674e63130017be8de20c',
        invoicingSettings: '674e6ab10019306579dd',
        schedulePresets: '67c90737002f68cec4ae',
    },
    recaptcha: {
        siteKey: '6LeSB1sqAAAAANCBqFrKEd4gTiFIEzAELV6s1PxD',
    },
    firebaseConfig: {
        apiKey: "your-api-key",
        authDomain: "your-auth-domain",
        projectId: "your-project-id",
        storageBucket: "your-storage-bucket",
        messagingSenderId: "your-sender-id",
        appId: "your-app-id",
        measurementId: "your-measurement-id"
    }

};

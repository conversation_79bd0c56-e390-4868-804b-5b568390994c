{"$schema": "./node_modules/@angular/service-worker/config/schema.json", "index": "/index.html", "assetGroups": [{"name": "app", "installMode": "prefetch", "resources": {"files": ["/favicon.ico", "/index.html", "/*.css", "/*.js"]}}, {"name": "assets", "installMode": "lazy", "updateMode": "prefetch", "resources": {"files": ["/assets/**", "/*.(png|jpg|jpeg|gif|webp|svg)"]}}], "dataGroups": [{"name": "network-images", "urls": ["https://back.wiseme.com.au/v1/storage/buckets/**"], "cacheConfig": {"strategy": "performance", "maxSize": 100, "maxAge": "1d", "timeout": "10s"}}]}
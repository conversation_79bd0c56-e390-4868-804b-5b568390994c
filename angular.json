{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"fuse": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"outputPath": "dist/fuse", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "allowedCommonJsDependencies": ["apexcharts", "crypto-js/enc-utf8", "crypto-js/hmac-sha256", "crypto-js/enc-base64", "quill-delta"], "assets": [{"glob": "**/*", "input": "public"}, {"glob": "_redirects", "input": "src", "output": "/"}, {"glob": "sitemap.xml", "input": "src", "output": "/"}, {"glob": "robots.txt", "input": "src", "output": "/"}], "stylePreprocessorOptions": {"includePaths": ["src/@fuse/styles"]}, "styles": ["src/@fuse/styles/tailwind.scss", "src/@fuse/styles/themes.scss", "src/styles/vendors.scss", "src/@fuse/styles/main.scss", "src/styles/styles.scss", "src/styles/tailwind.scss", "src/styles/splash-screen.css", "src/styles/inter.css", "public/fonts/fontawesome/css/all.min.css", "public/fonts/fontawesome/css/sharp-light.min.css"], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "75kb", "maximumError": "1000kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "fuse:build:production"}, "development": {"buildTarget": "fuse:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}, "test": {"builder": "@angular/build:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "12397880-9aaf-483f-9fd7-6590847d9400"}, "schematics": {"@schematics/angular:component": {"type": "component"}, "@schematics/angular:directive": {"type": "directive"}, "@schematics/angular:service": {"type": "service"}, "@schematics/angular:guard": {"typeSeparator": "."}, "@schematics/angular:interceptor": {"typeSeparator": "."}, "@schematics/angular:module": {"typeSeparator": "."}, "@schematics/angular:pipe": {"typeSeparator": "."}, "@schematics/angular:resolver": {"typeSeparator": "."}}}
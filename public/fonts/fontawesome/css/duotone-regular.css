/*!
 * Font Awesome Pro 6.7.0 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Commercial License)
 * Copyright 2024 Fonticons, Inc.
 */
:root, :host {
  --fa-style-family-duotone: 'Font Awesome 6 Duotone';
  --fa-font-duotone-regular: normal 400 1em/1 'Font Awesome 6 Duotone'; }

@font-face {
  font-family: 'Font Awesome 6 Duotone';
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src: url("../webfonts/fa-duotone-regular-400.woff2") format("woff2"), url("../webfonts/fa-duotone-regular-400.ttf") format("truetype"); }

.fadr,
.fa-duotone.fa-regular {
  position: relative;
  font-weight: 400;
  letter-spacing: normal; }

.fadr::before,
.fa-duotone.fa-regular::before {
  position: absolute;
  color: var(--fa-primary-color, inherit);
  opacity: var(--fa-primary-opacity, 1); }

.fadr::after,
.fa-duotone.fa-regular::after {
  color: var(--fa-secondary-color, inherit);
  opacity: var(--fa-secondary-opacity, 0.4); }

.fa-swap-opacity .fadr::before,
.fa-swap-opacity .fa-duotone.fa-regular::before,
.fadr.fa-swap-opacity::before,
.fa-duotone.fa-swap-opacity::before,
.fa-duotone.fa-regular.fa-swap-opacity::before {
  opacity: var(--fa-secondary-opacity, 0.4); }

.fa-swap-opacity .fadr::after,
.fa-swap-opacity .fa-duotone.fa-regular::after,
.fadr.fa-swap-opacity::after,
.fa-duotone.fa-swap-opacity::after,
.fa-duotone.fa-regular.fa-swap-opacity::after {
  opacity: var(--fa-primary-opacity, 1); }

.fadr.fa-inverse,
.fa-duotone.fa-regular.fa-inverse {
  color: var(--fa-inverse, #fff); }

.fadr.fa-stack-1x,
.fadr.fa-stack-2x,
.fa-duotone.fa-regular.fa-stack-1x,
.fa-duotone.fa-regular.fa-stack-2x {
  position: absolute; }

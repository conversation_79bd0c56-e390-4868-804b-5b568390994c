/*!
 * Font Awesome Pro 6.7.0 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Commercial License)
 * Copyright 2024 Fonticons, Inc.
 */
:host,:root{--fa-style-family-duotone:"Font Awesome 6 Duotone";--fa-font-duotone-regular:normal 400 1em/1 "Font Awesome 6 Duotone"}@font-face{font-family:"Font Awesome 6 Duotone";font-style:normal;font-weight:400;font-display:block;src:url(../webfonts/fa-duotone-regular-400.woff2) format("woff2"),url(../webfonts/fa-duotone-regular-400.ttf) format("truetype")}.fa-duotone.fa-regular,.fadr{position:relative;font-weight:400;letter-spacing:normal}.fa-duotone.fa-regular:before,.fadr:before{position:absolute;color:var(--fa-primary-color,inherit);opacity:var(--fa-primary-opacity,1)}.fa-duotone.fa-regular:after,.fadr:after{color:var(--fa-secondary-color,inherit);opacity:var(--fa-secondary-opacity,.4)}.fa-duotone.fa-regular.fa-swap-opacity:before,.fa-duotone.fa-swap-opacity:before,.fa-swap-opacity .fa-duotone.fa-regular:before,.fa-swap-opacity .fadr:before,.fadr.fa-swap-opacity:before{opacity:var(--fa-secondary-opacity,.4)}.fa-duotone.fa-regular.fa-swap-opacity:after,.fa-duotone.fa-swap-opacity:after,.fa-swap-opacity .fa-duotone.fa-regular:after,.fa-swap-opacity .fadr:after,.fadr.fa-swap-opacity:after{opacity:var(--fa-primary-opacity,1)}.fa-duotone.fa-regular.fa-inverse,.fadr.fa-inverse{color:var(--fa-inverse,#fff)}.fa-duotone.fa-regular.fa-stack-1x,.fa-duotone.fa-regular.fa-stack-2x,.fadr.fa-stack-1x,.fadr.fa-stack-2x{position:absolute}
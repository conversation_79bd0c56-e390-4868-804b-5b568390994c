{"v": "5.12.1", "fr": 60, "ip": 0, "op": 180, "w": 430, "h": 430, "nm": "wired-gradient-953-complaint", "ddd": 0, "assets": [{"id": "comp_1", "nm": "mask-in", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "outline 4", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 4, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [290.03, 341, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.17, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[68.25, 0], [67.519, 0]], "c": false}]}, {"t": 32, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[68.25, 0], [-68.25, 0]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [290.03, 358.343], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.17, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[16.323, 0], [16.5, 0]], "c": false}]}, {"t": 32, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-16.5, 0], [16.5, 0]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [341.78, 323.656], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.17, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[98.519, 0], [98.918, 0]], "c": false}]}, {"t": 32, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-37.25, 0], [37.25, 0]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [259.03, 323.656], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 8, "op": 843, "st": -1, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "outline 3", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [209.996, 160.107, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.35, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [{"i": [[0, 0.008], [-0.008, 0], [0, -0.008], [0.008, 0]], "o": [[0, -0.008], [0.008, 0], [0, 0.008], [-0.008, 0]], "v": [[-0.885, -0.651], [-0.87, -0.665], [-0.856, -0.651], [-0.87, -0.636]], "c": true}]}, {"t": 45, "s": [{"i": [[0, 5.959], [-5.959, 0], [0, -5.959], [5.959, 0]], "o": [[0, -5.959], [5.959, 0], [0, 5.959], [-5.959, 0]], "v": [[-10.791, 0], [0, -10.791], [10.791, 0], [0, 10.791]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [209.996, 199.005], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.1, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [{"i": [[0.007, 0], [0, 0], [0, 0.007], [0, 0], [-0.009, 0], [0, 0], [0, -0.009], [0, 0]], "o": [[0, 0], [-0.007, 0], [0, 0], [0, -0.009], [0, 0], [0.009, 0], [0, 0], [0, 0.007]], "v": [[-0.868, 57.862], [-0.873, 57.862], [-0.885, 57.85], [-0.888, 57.797], [-0.873, 57.781], [-0.868, 57.781], [-0.853, 57.797], [-0.855, 57.85]], "c": true}]}, {"t": 76, "s": [{"i": [[5.026, 0], [0, 0], [0.245, 5.02], [0, 0], [-6.377, 0], [0, 0], [0.311, -6.37], [0, 0]], "o": [[0, 0], [-5.026, 0], [0, 0], [-0.311, -6.37], [0, 0], [6.377, 0], [0, 0], [-0.245, 5.02]], "v": [[1.84, 30.036], [-1.84, 30.036], [-11.252, 21.073], [-13.176, -18.332], [-2.029, -30.036], [2.029, -30.036], [13.176, -18.332], [11.252, 21.073]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [209.996, 140.454], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 26, "op": 842, "st": -2, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Chat-2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.063], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": -1, "s": [-77.553]}, {"i": {"x": [0.527], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 29, "s": [8]}, {"i": {"x": [0.42], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 59, "s": [-6]}, {"t": 87, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.027, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": -1, "s": [375.119, 399.226, 0], "to": [0, -7.167, 0], "ti": [0, 4.5, 0]}, {"i": {"x": 0.527, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 21, "s": [375.119, 356.226, 0], "to": [0, -4.5, 0], "ti": [0, -2.667, 0]}, {"t": 51, "s": [375.119, 372.226, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [120.089, 70.226, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.027, 0.027, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": -1, "s": [100, 100, 100]}, {"i": {"x": [0.527, 0.527, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 25, "s": [106, 106, 100]}, {"t": 55, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": -1, "s": [{"i": [[0.033, 0], [0, 0], [0, -0.033], [0, 0], [-0.033, 0], [0, 0], [0, 0]], "o": [[0, 0], [-0.033, 0], [0, 0], [0, 0.033], [0, 0], [0, 0], [0, -0.033]], "v": [[120.03, 70.107], [120.03, 70.107], [119.97, 70.166], [119.97, 70.166], [120.03, 70.226], [120.089, 70.226], [120.089, 70.167]], "c": true}]}, {"i": {"x": 0.27, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 9, "s": [{"i": [[38.785, 0], [0, 0], [0, -38.785], [0, 0], [-38.785, 0], [0, 0], [0, 0]], "o": [[0, 0], [-38.785, 0], [0, 0], [0, 38.785], [0, 0], [0, 0], [0, -38.785]], "v": [[49.863, -70.226], [50.137, -70.457], [-20.089, -0.231], [-20.089, -0.231], [50.137, 69.995], [120.089, 70.226], [120.089, 0]], "c": true}]}, {"t": 29, "s": [{"i": [[38.785, 0], [0, 0], [0, -38.785], [0, 0], [-38.785, 0], [0, 0], [0, 0]], "o": [[0, 0], [-38.785, 0], [0, 0], [0, 38.785], [0, 0], [0, 0], [0, -38.785]], "v": [[49.863, -70.226], [-49.863, -70.226], [-120.089, 0], [-120.089, 0], [-49.863, 70.226], [120.089, 70.226], [120.089, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 843, "st": -1, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Chat-1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.063], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 15, "s": [76.553]}, {"i": {"x": [0.527], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 39.545, "s": [-8]}, {"i": {"x": [0.42], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [6]}, {"t": 89, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.027, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [54.907, 222.333, 0], "to": [0, -7.167, 0], "ti": [0, 4.5, 0]}, {"i": {"x": 0.527, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 33, "s": [54.907, 179.333, 0], "to": [0, -4.5, 0], "ti": [0, -2.667, 0]}, {"t": 59.453125, "s": [54.907, 195.333, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-120.089, 70.226, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.027, 0.027, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.159]}, "t": 15, "s": [100, 100, 100]}, {"i": {"x": [0.527, 0.527, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 36.273, "s": [106, 106, 100]}, {"t": 62.7265625, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[-0.368, 0], [0, 0], [0, -0.368], [0, 0], [0.368, 0], [0, 0], [0, 0]], "o": [[0, 0], [0.368, 0], [0, 0], [0, 0.368], [0, 0], [0, 0], [0, -0.368]], "v": [[-119.423, 68.894], [-119.421, 68.893], [-118.755, 69.559], [-118.755, 69.559], [-119.421, 70.225], [-120.089, 70.226], [-120.089, 69.56]], "c": true}]}, {"i": {"x": 0.27, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 23.182, "s": [{"i": [[-31.593, 0], [0, 0], [0, -31.593], [0, 0], [31.593, 0], [0, 0], [0, 0]], "o": [[0, 0], [31.593, 0], [0, 0], [0, 31.593], [0, 0], [0, 0], [0, -31.593]], "v": [[-62.885, -44.183], [-62.7, -44.28], [-5.496, 12.925], [-5.496, 12.925], [-62.7, 70.129], [-120.089, 70.226], [-120.089, 13.022]], "c": true}]}, {"t": 39.544921875, "s": [{"i": [[-38.785, 0], [0, 0], [0, -38.785], [0, 0], [38.785, 0], [0, 0], [0, 0]], "o": [[0, 0], [38.785, 0], [0, 0], [0, 38.785], [0, 0], [0, 0], [0, -38.785]], "v": [[-49.863, -70.226], [49.863, -70.226], [120.089, 0], [120.089, 0], [49.863, 70.226], [-120.089, 70.226], [-120.089, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 15, "op": 843, "st": -1, "ct": 1, "bm": 0}]}, {"id": "comp_2", "nm": "hover-slide", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "mask-h-s", "td": 1, "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 844, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "gradient", "tt": 1, "tp": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.4], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [-94]}, {"t": 180, "s": [266]}], "ix": 10}, "p": {"a": 0, "k": [271.941, 220.46, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [240, 240, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 29, "nm": "Gaussian Blur", "np": 5, "mn": "ADBE Gaussian Blur 2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Blurriness", "mn": "ADBE Gaussian Blur 2-0001", "ix": 1, "v": {"a": 0, "k": 175, "ix": 1}}, {"ty": 7, "nm": "Blur Dimensions", "mn": "ADBE Gaussian Blur 2-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Repeat Edge Pixels", "mn": "ADBE Gaussian Blur 2-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-97.478, 0], [0, -97.478], [97.478, 0], [30.894, 26.574], [0, 53.531]], "o": [[97.478, 0], [0, 97.478], [-43.948, 0], [-37.631, -32.369], [0, -97.478]], "v": [[0, -176.5], [176.5, 0], [0, 176.5], [-105.29, 115.869], [-176.5, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.796, 0.369, 0.933, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-gradient-953-complaint').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [113.242, -118.884], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "primary.design", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "design"}, {"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [500, 500], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.294, 0.882, 0.925, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-gradient-953-complaint').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "secondary.design", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false, "cl": "design"}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_3", "nm": "mask-h-s", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "outline 6", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 4, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [290.03, 341, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[68.25, 0], [-68.25, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [290.03, 358.343], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-16.5, 0], [16.5, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [341.78, 323.656], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-37.25, 0], [37.25, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [259.03, 323.656], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Chat-4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 95.879, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 123.92, "s": [-24]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 140.201, "s": [13]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 158.291, "s": [-2]}, {"t": 180, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.82, "y": 0}, "t": 95.879, "s": [375.119, 618.226, 0], "to": [0, -41, 0], "ti": [0, 41, 0]}, {"t": 140.201171875, "s": [375.119, 372.226, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [120.089, 70.226, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[38.785, 0], [0, 0], [0, -38.785], [0, 0], [-38.785, 0], [0, 0], [0, 0]], "o": [[0, 0], [-38.785, 0], [0, 0], [0, 38.785], [0, 0], [0, 0], [0, -38.785]], "v": [[49.863, -70.226], [-49.863, -70.226], [-120.089, 0], [-120.089, 0], [-49.863, 70.226], [120.089, 70.226], [120.089, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "outline 4", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 4, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [290.03, 341, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[68.25, 0], [-68.25, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [290.03, 358.343], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-16.5, 0], [16.5, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [341.78, 323.656], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-37.25, 0], [37.25, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [259.03, 323.656], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Chat-2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 28.041, "s": [-24]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44.322, "s": [13]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 62.412, "s": [-2]}, {"i": {"x": [0.18], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 84.121, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.82], "y": [0]}, "t": 91.357, "s": [0]}, {"t": 118.4921875, "s": [-30]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.82, "y": 0}, "t": 0, "s": [375.119, 372.226, 0], "to": [0, -29.667, 0], "ti": [0, 29.667, 0]}, {"i": {"x": 0.18, "y": 0.18}, "o": {"x": 0.333, "y": 0.333}, "t": 42.512, "s": [375.119, 194.226, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.82, "y": 0}, "t": 91.357, "s": [375.119, 194.226, 0], "to": [0, -58.5, 0], "ti": [0, 58.5, 0]}, {"t": 118.4921875, "s": [375.119, -156.774, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [120.089, 70.226, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[38.785, 0], [0, 0], [0, -38.785], [0, 0], [-38.785, 0], [0, 0], [0, 0]], "o": [[0, 0], [-38.785, 0], [0, 0], [0, 38.785], [0, 0], [0, 0], [0, -38.785]], "v": [[49.863, -70.226], [-49.863, -70.226], [-120.089, 0], [-120.089, 0], [-49.863, 70.226], [120.089, 70.226], [120.089, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "outline 3", "parent": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [209.996, 160.107, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 5.959], [-5.959, 0], [0, -5.959], [5.959, 0]], "o": [[0, -5.959], [5.959, 0], [0, 5.959], [-5.959, 0]], "v": [[-10.791, 0], [0, -10.791], [10.791, 0], [0, 10.791]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [209.996, 199.005], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[5.026, 0], [0, 0], [0.245, 5.02], [0, 0], [-6.377, 0], [0, 0], [0.311, -6.37], [0, 0]], "o": [[0, 0], [-5.026, 0], [0, 0], [-0.311, -6.37], [0, 0], [6.377, 0], [0, 0], [-0.245, 5.02]], "v": [[1.84, 30.036], [-1.84, 30.036], [-11.252, 21.073], [-13.176, -18.332], [-2.029, -30.036], [2.029, -30.036], [13.176, -18.332], [11.252, 21.073]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [209.996, 140.454], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Chat-1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.82], "y": [0]}, "t": 0, "s": [0]}, {"t": 27.134765625, "s": [30]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.82, "y": 0}, "t": 0, "s": [54.907, 195.333, 0], "to": [0, -59.167, 0], "ti": [0, 59.167, 0]}, {"t": 27.134765625, "s": [54.907, -159.667, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-120.089, 70.226, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-38.785, 0], [0, 0], [0, -38.785], [0, 0], [38.785, 0], [0, 0], [0, 0]], "o": [[0, 0], [38.785, 0], [0, 0], [0, 38.785], [0, 0], [0, 0], [0, -38.785]], "v": [[-49.863, -70.226], [49.863, -70.226], [120.089, 0], [120.089, 0], [49.863, 70.226], [-120.089, 70.226], [-120.089, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "outline 5", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [209.996, 160.107, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 5.959], [-5.959, 0], [0, -5.959], [5.959, 0]], "o": [[0, -5.959], [5.959, 0], [0, 5.959], [-5.959, 0]], "v": [[-10.791, 0], [0, -10.791], [10.791, 0], [0, 10.791]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [209.996, 199.005], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[5.026, 0], [0, 0], [0.245, 5.02], [0, 0], [-6.377, 0], [0, 0], [0.311, -6.37], [0, 0]], "o": [[0, 0], [-5.026, 0], [0, 0], [-0.311, -6.37], [0, 0], [6.377, 0], [0, 0], [-0.245, 5.02]], "v": [[1.84, 30.036], [-1.84, 30.036], [-11.252, 21.073], [-13.176, -18.332], [-2.029, -30.036], [2.029, -30.036], [13.176, -18.332], [11.252, 21.073]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [209.996, 140.454], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Chat-3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 7.236, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 35.277, "s": [24]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 51.559, "s": [-13]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 69.648, "s": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 91.357, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 119.396, "s": [24]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 135.678, "s": [-13]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 153.77, "s": [2]}, {"t": 175.4765625, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.82, "y": 0}, "t": 7.236, "s": [54.907, 618.333, 0], "to": [0, -41.167, 0], "ti": [0, 41.167, 0]}, {"i": {"x": 0.18, "y": 0.18}, "o": {"x": 0.167, "y": 0.167}, "t": 49.748, "s": [54.907, 371.333, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.82, "y": 0}, "t": 91.357, "s": [54.907, 371.333, 0], "to": [0, -29.333, 0], "ti": [0, 29.333, 0]}, {"t": 133.869140625, "s": [54.907, 195.333, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-120.089, 70.226, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-38.785, 0], [0, 0], [0, -38.785], [0, 0], [38.785, 0], [0, 0], [0, 0]], "o": [[0, 0], [38.785, 0], [0, 0], [0, 38.785], [0, 0], [0, 0], [0, -38.785]], "v": [[-49.863, -70.226], [49.863, -70.226], [120.089, 0], [120.089, 0], [49.863, 70.226], [-120.089, 70.226], [-120.089, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_5", "nm": "mask-h", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "chat-1-2", "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 69, "op": 844, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Chat-1", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 52, "s": [0]}, {"i": {"x": [0.07], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 70, "s": [-4]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.93], "y": [0]}, "t": 117, "s": [12]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 144, "s": [-8]}, {"t": 179, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.07, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52, "s": [55.119, 195.226, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.93, "y": 0}, "t": 107.457, "s": [55.119, 218.226, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 141, "s": [55.119, 185.226, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 173, "s": [55.119, 195.226, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-120.089, 70.226, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.16, "y": 1}, "o": {"x": 0.707, "y": 0}, "t": 52, "s": [{"i": [[-38.785, 0], [0, 0], [0, -38.785], [0, 0], [38.785, 0], [0, 0], [0, 0]], "o": [[0, 0], [38.785, 0], [0, 0], [0, 38.785], [0, 0], [0, 0], [0, -38.785]], "v": [[-49.863, -70.226], [49.863, -70.226], [120.089, 0], [120.089, 0], [49.863, 70.226], [-120.089, 70.226], [-120.089, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.863, "y": 0}, "t": 84.789, "s": [{"i": [[-55.378, 0], [0, 0], [0, -55.378], [0, 0], [55.378, 0], [0, 0], [0, 0]], "o": [[0, 0], [55.378, 0], [0, 0], [0, 55.378], [0, 0], [0, 0], [0, -55.378]], "v": [[-19.818, -130.316], [122.574, -130.316], [222.846, -30.045], [222.846, -30.045], [122.574, 70.226], [-120.089, 70.226], [-120.089, -30.045]], "c": true}]}, {"i": {"x": 0.266, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 136, "s": [{"i": [[-32.255, 0], [0, 0], [0, -32.255], [0, 0], [32.255, 0], [0, 0], [0, 0]], "o": [[0, 0], [32.255, 0], [0, 0], [0, 32.255], [0, 0], [0, 0], [0, -32.255]], "v": [[-61.686, -46.58], [21.251, -46.58], [79.654, 11.823], [79.654, 11.823], [21.251, 70.226], [-120.089, 70.226], [-120.089, 11.823]], "c": true}]}, {"t": 166, "s": [{"i": [[-38.785, 0], [0, 0], [0, -38.785], [0, 0], [38.785, 0], [0, 0], [0, 0]], "o": [[0, 0], [38.785, 0], [0, 0], [0, 38.785], [0, 0], [0, 0], [0, -38.785]], "v": [[-49.863, -70.226], [49.863, -70.226], [120.089, 0], [120.089, 0], [49.863, 70.226], [-120.089, 70.226], [-120.089, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 69, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "chat-2-2", "tt": 2, "tp": 2, "refId": "comp_7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 69, "op": 844, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "chat-2-2", "refId": "comp_7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 70, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Chat-2", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.568], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.229], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [-14.878]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.94], "y": [0]}, "t": 49, "s": [22]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 84, "s": [-3]}, {"t": 113, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.06, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [375.119, 372.226, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.94, "y": 0}, "t": 41, "s": [375.119, 348.226, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 81.148, "s": [375.119, 378.226, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 107.4609375, "s": [375.119, 372.226, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [120.089, 70.226, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.21, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[38.785, 0], [0, 0], [0, -38.785], [0, 0], [-38.785, 0], [0, 0], [0, 0]], "o": [[0, 0], [-38.785, 0], [0, 0], [0, 38.785], [0, 0], [0, 0], [0, -38.785]], "v": [[49.863, -70.226], [-49.863, -70.226], [-120.089, 0], [-120.089, 0], [-49.863, 70.226], [120.089, 70.226], [120.089, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.79, "y": 0}, "t": 30, "s": [{"i": [[55.696, 0], [0, 0], [0, -55.696], [0, 0], [-55.696, 0], [0, 0], [0, 0]], "o": [[0, 0], [-55.696, 0], [0, 0], [0, 55.696], [0, 0], [0, 0], [0, -55.696]], "v": [[19.243, -131.467], [-123.966, -131.467], [-224.812, -30.62], [-224.812, -30.62], [-123.966, 70.226], [120.089, 70.226], [120.089, -30.62]], "c": true}]}, {"i": {"x": 0.21, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 76, "s": [{"i": [[33.723, 0], [0, 0], [0, -33.723], [0, 0], [-33.723, 0], [0, 0], [0, 0]], "o": [[0, 0], [-33.723, 0], [0, 0], [0, 33.723], [0, 0], [0, 0], [0, -33.723]], "v": [[59.029, -51.895], [-27.681, -51.895], [-88.742, 9.166], [-88.742, 9.166], [-27.681, 70.226], [120.089, 70.226], [120.089, 9.166]], "c": true}]}, {"t": 103, "s": [{"i": [[38.785, 0], [0, 0], [0, -38.785], [0, 0], [-38.785, 0], [0, 0], [0, 0]], "o": [[0, 0], [-38.785, 0], [0, 0], [0, 38.785], [0, 0], [0, 0], [0, -38.785]], "v": [[49.863, -70.226], [-49.863, -70.226], [-120.089, 0], [-120.089, 0], [-49.863, 70.226], [120.089, 70.226], [120.089, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 70, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "chat-1-2", "tt": 2, "tp": 5, "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 70, "st": 0, "bm": 0}]}, {"id": "comp_6", "nm": "chat-1-2", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "outline 3", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-0.213, 0.108, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [209.996, 160.107, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.24, "y": 1}, "o": {"x": 0.632, "y": 0}, "t": 52.219, "s": [{"i": [[0, 5.959], [-5.959, 0], [0, -5.959], [5.959, 0]], "o": [[0, -5.959], [5.959, 0], [0, 5.959], [-5.959, 0]], "v": [[-10.791, 0], [0, -10.791], [10.791, 0], [0, 10.791]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.851, "y": 0}, "t": 85, "s": [{"i": [[0, 8.279], [-8.279, 0], [0, -8.279], [8.279, 0]], "o": [[0, -8.279], [8.279, 0], [0, 8.279], [-8.279, 0]], "v": [[31.408, -7.975], [46.398, -22.965], [61.389, -7.975], [46.398, 7.015]], "c": true}]}, {"i": {"x": 0.24, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 136, "s": [{"i": [[0, 3.618], [-3.618, 0], [0, -3.618], [3.618, 0]], "o": [[0, -3.618], [3.618, 0], [0, 3.618], [-3.618, 0]], "v": [[-25.201, -2.065], [-18.65, -8.616], [-12.098, -2.065], [-18.65, 4.487]], "c": true}]}, {"t": 166, "s": [{"i": [[0, 5.959], [-5.959, 0], [0, -5.959], [5.959, 0]], "o": [[0, -5.959], [5.959, 0], [0, 5.959], [-5.959, 0]], "v": [[-10.791, 0], [0, -10.791], [10.791, 0], [0, 10.791]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [209.996, 199.005], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.24, "y": 1}, "o": {"x": 0.632, "y": 0}, "t": 52.219, "s": [{"i": [[5.026, 0], [0, 0], [0.245, 5.02], [0, 0], [-6.377, 0], [0, 0], [0.311, -6.37], [0, 0]], "o": [[0, 0], [-5.026, 0], [0, 0], [-0.311, -6.37], [0, 0], [6.377, 0], [0, 0], [-0.245, 5.02]], "v": [[1.84, 30.036], [-1.84, 30.036], [-11.252, 21.073], [-13.176, -18.332], [-2.029, -30.036], [2.029, -30.036], [13.176, -18.332], [11.252, 21.073]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.851, "y": 0}, "t": 85, "s": [{"i": [[6.982, 0], [0, 0], [0.34, 6.973], [0, 0], [-8.86, 0], [0, 0], [0.432, -8.849], [0, 0]], "o": [[0, 0], [-6.982, 0], [0, 0], [-0.432, -8.849], [0, 0], [8.86, 0], [0, 0], [-0.34, 6.973]], "v": [[48.955, 10.963], [43.841, 10.963], [30.766, -1.49], [28.093, -56.231], [43.579, -72.491], [49.217, -72.491], [64.703, -56.231], [62.03, -1.49]], "c": true}]}, {"i": {"x": 0.24, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 136, "s": [{"i": [[3.051, 0], [0, 0], [0.149, 3.048], [0, 0], [-3.872, 0], [0, 0], [0.189, -3.867], [0, 0]], "o": [[0, 0], [-3.051, 0], [0, 0], [-0.189, -3.867], [0, 0], [3.872, 0], [0, 0], [-0.149, 3.048]], "v": [[-17.532, 39.173], [-19.767, 39.173], [-25.482, 33.731], [-26.65, 9.807], [-19.882, 2.701], [-17.418, 2.701], [-10.65, 9.807], [-11.818, 33.731]], "c": true}]}, {"t": 166, "s": [{"i": [[5.026, 0], [0, 0], [0.245, 5.02], [0, 0], [-6.377, 0], [0, 0], [0.311, -6.37], [0, 0]], "o": [[0, 0], [-5.026, 0], [0, 0], [-0.311, -6.37], [0, 0], [6.377, 0], [0, 0], [-0.245, 5.02]], "v": [[1.84, 30.036], [-1.84, 30.036], [-11.252, 21.073], [-13.176, -18.332], [-2.029, -30.036], [2.029, -30.036], [13.176, -18.332], [11.252, 21.073]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [209.996, 140.454], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Chat-1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 52, "s": [0]}, {"i": {"x": [0.07], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 70, "s": [-4]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.93], "y": [0]}, "t": 117, "s": [12]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 144, "s": [-8]}, {"t": 179, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.07, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52, "s": [55.119, 195.226, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.93, "y": 0}, "t": 107.457, "s": [55.119, 218.226, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 141, "s": [55.119, 185.226, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 173, "s": [55.119, 195.226, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-120.089, 70.226, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.16, "y": 1}, "o": {"x": 0.707, "y": 0}, "t": 52, "s": [{"i": [[-38.785, 0], [0, 0], [0, -38.785], [0, 0], [38.785, 0], [0, 0], [0, 0]], "o": [[0, 0], [38.785, 0], [0, 0], [0, 38.785], [0, 0], [0, 0], [0, -38.785]], "v": [[-49.863, -70.226], [49.863, -70.226], [120.089, 0], [120.089, 0], [49.863, 70.226], [-120.089, 70.226], [-120.089, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.863, "y": 0}, "t": 84.789, "s": [{"i": [[-55.378, 0], [0, 0], [0, -55.378], [0, 0], [55.378, 0], [0, 0], [0, 0]], "o": [[0, 0], [55.378, 0], [0, 0], [0, 55.378], [0, 0], [0, 0], [0, -55.378]], "v": [[-19.818, -130.316], [122.574, -130.316], [222.846, -30.045], [222.846, -30.045], [122.574, 70.226], [-120.089, 70.226], [-120.089, -30.045]], "c": true}]}, {"i": {"x": 0.266, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 136, "s": [{"i": [[-32.255, 0], [0, 0], [0, -32.255], [0, 0], [32.255, 0], [0, 0], [0, 0]], "o": [[0, 0], [32.255, 0], [0, 0], [0, 32.255], [0, 0], [0, 0], [0, -32.255]], "v": [[-61.686, -46.58], [21.251, -46.58], [79.654, 11.823], [79.654, 11.823], [21.251, 70.226], [-120.089, 70.226], [-120.089, 11.823]], "c": true}]}, {"t": 166, "s": [{"i": [[-38.785, 0], [0, 0], [0, -38.785], [0, 0], [38.785, 0], [0, 0], [0, 0]], "o": [[0, 0], [38.785, 0], [0, 0], [0, 38.785], [0, 0], [0, 0], [0, -38.785]], "v": [[-49.863, -70.226], [49.863, -70.226], [120.089, 0], [120.089, 0], [49.863, 70.226], [-120.089, 70.226], [-120.089, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_7", "nm": "chat-2-2", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "outline 4", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 4, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [290.03, 341, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.25, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[68.25, 0], [-68.25, 0]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.75, "y": 0}, "t": 30, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[60.182, -15.489], [-150.34, -15.489]], "c": false}]}, {"i": {"x": 0.25, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 76, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[72.545, 2.559], [-34.049, 2.559]], "c": false}]}, {"t": 103, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[68.25, 0], [-68.25, 0]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [290.03, 358.343], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.25, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-16.5, 0], [16.5, 0]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.75, "y": 0}, "t": 30, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-42.463, -34.299], [8.432, -34.299]], "c": false}]}, {"i": {"x": 0.25, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 76, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-4.975, 10.159], [20.795, 10.159]], "c": false}]}, {"t": 103, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-16.5, 0], [16.5, 0]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [341.78, 323.656], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.25, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-37.25, 0], [37.25, 0]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.75, "y": 0}, "t": 30, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-119.34, -34.299], [-4.439, -34.299]], "c": false}]}, {"i": {"x": 0.25, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 76, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-3.049, 10.159], [55.129, 10.159]], "c": false}]}, {"t": 103, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-37.25, 0], [37.25, 0]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.031372550875, 0.654901981354, 0.537254929543, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [259.03, 323.656], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Chat-2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.568], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.229], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [-14.878]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.94], "y": [0]}, "t": 49, "s": [22]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 84, "s": [-3]}, {"t": 113, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.06, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [375.119, 372.226, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.94, "y": 0}, "t": 41, "s": [375.119, 348.226, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 81.148, "s": [375.119, 378.226, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 107.4609375, "s": [375.119, 372.226, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [120.089, 70.226, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.21, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[38.785, 0], [0, 0], [0, -38.785], [0, 0], [-38.785, 0], [0, 0], [0, 0]], "o": [[0, 0], [-38.785, 0], [0, 0], [0, 38.785], [0, 0], [0, 0], [0, -38.785]], "v": [[49.863, -70.226], [-49.863, -70.226], [-120.089, 0], [-120.089, 0], [-49.863, 70.226], [120.089, 70.226], [120.089, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.79, "y": 0}, "t": 30, "s": [{"i": [[55.696, 0], [0, 0], [0, -55.696], [0, 0], [-55.696, 0], [0, 0], [0, 0]], "o": [[0, 0], [-55.696, 0], [0, 0], [0, 55.696], [0, 0], [0, 0], [0, -55.696]], "v": [[19.243, -131.467], [-123.966, -131.467], [-224.812, -30.62], [-224.812, -30.62], [-123.966, 70.226], [120.089, 70.226], [120.089, -30.62]], "c": true}]}, {"i": {"x": 0.21, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 76, "s": [{"i": [[33.723, 0], [0, 0], [0, -33.723], [0, 0], [-33.723, 0], [0, 0], [0, 0]], "o": [[0, 0], [-33.723, 0], [0, 0], [0, 33.723], [0, 0], [0, 0], [0, -33.723]], "v": [[59.029, -51.895], [-27.681, -51.895], [-88.742, 9.166], [-88.742, 9.166], [-27.681, 70.226], [120.089, 70.226], [120.089, 9.166]], "c": true}]}, {"t": 103, "s": [{"i": [[38.785, 0], [0, 0], [0, -38.785], [0, 0], [-38.785, 0], [0, 0], [0, 0]], "o": [[0, 0], [-38.785, 0], [0, 0], [0, 38.785], [0, 0], [0, 0], [0, -38.785]], "v": [[49.863, -70.226], [-49.863, -70.226], [-120.089, 0], [-120.089, 0], [-49.863, 70.226], [120.089, 70.226], [120.089, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-gradient-953-complaint').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "stroke", "np": 3, "mn": "Pseudo/@@G+h7I8mQTrOKjdgRerajvQ", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "<PERSON><PERSON>", "mn": "Pseudo/@@G+h7I8mQTrOKjdgRerajvQ-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}, {"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.294, 0.882, 0.925], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.796, 0.369, 0.933], "ix": 1}}]}], "ip": 0, "op": 470, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "hover-slide", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 190, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:hover-slide", "dr": 180}], "props": {}}